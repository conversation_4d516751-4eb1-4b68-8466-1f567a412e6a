import eslintConfigBase from '@lazycuh/eslint-config-base';
import sortClassMembers from 'eslint-plugin-sort-class-members';

import { createTypeScriptImportResolver } from 'eslint-import-resolver-typescript';

export default [
    { ignores: ['**/build/**', '**/*.js'] },
    sortClassMembers.configs["flat/recommended"],
    ...eslintConfigBase.map(config => ({
        ...config,

        files: ['src/**/*.ts'],
        settings: {
            'node': {
                tryExtensions: ['.ts', '.tsx']
            },
            'import/resolver': {
                typescript: {}
            },
            'import/resolver-next': [
                createTypeScriptImportResolver({
                    alwaysTryTypes: true,
                    typescript: {
                        project: 'tsconfig.json'
                    }
                })
            ]
        },
        rules: {
            ...config.rules,
            // Your rule overrides go here
            "sort-class-members/sort-class-members": [
                "error",
                {
                    "accessorPairPositioning": "together",
                    "stopAfterFirstProblem": true
                }
            ],
            "@stylistic/no-multiple-empty-lines": ["error", { "max": 1, "maxEOF": 0 }],
            "@stylistic/max-len": [
                "error", {
                    "code": 120, "tabWidth": 3,
                    "ignoreTrailingComments": true,
                    "ignoreUrls": true,
                    "ignoreTemplateLiterals": true
                }
            ],
            "@stylistic/indent": ["error", 3]
        }
    }))
];