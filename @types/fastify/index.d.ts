/* eslint-disable @typescript-eslint/no-unused-vars */
import * as http from 'http';

declare module 'fastify' {
   export interface FastifyInstance<
      HttpServer = http.Server,
      HttpRequest = http.IncomingMessage,
      HttpResponse = http.ServerResponse
   > {
      lookupTenantId: (clientId: string) => Promise<string | unknown>;
      authenticate: (requiredScope: string[]) => (request: FastifyRequest, reply: FastifyReply) => void;
   }

   export interface FastifyRequest {
      tenantId: string;
      user: string;
   }
}
