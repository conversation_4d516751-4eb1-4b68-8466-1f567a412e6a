{"name": "messaging-channel-sender", "version": "1.0.0", "description": "", "scripts": {"build": "tsc", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "license-report": "npx license-report --only=prod --output=table --exclude=@x-signal-inc/messaging-common --fields=name --fields=licenseType --fields=installedVersion --fields=link", "prestart": "npm run clean && npm run build", "start": "func start --port  7072", "test": "echo \"No tests yet...\""}, "dependencies": {"@azure/communication-sms": "^1.2.0-beta.3", "@azure/functions": "^4.7.0", "@azure/identity": "^4.9.1", "@azure/service-bus": "^7.9.5", "@azure/storage-blob": "^12.27.0", "@sendgrid/mail": "^8.1.5", "@types/html-to-text": "^9.0.4", "@x-signal-inc/messaging-common": "^0.1.73", "botbuilder": "^4.23.2", "botbuilder-core": "^4.23.2", "html-to-text": "^9.0.5", "nodemailer": "^6.10.1", "serialize-error": "^12.0.0", "turndown": "^7.2.0", "twilio": "^5.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.1", "@lazycuh/eslint-config-base": "^3.0.7", "@types/node": "^22.x", "@types/nodemailer": "^6.4.17", "@types/turndown": "^5.0.5", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-plugin-sort-class-members": "^1.21.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}, "main": "dist/{index.js,functions/*.js}", "type": "module"}