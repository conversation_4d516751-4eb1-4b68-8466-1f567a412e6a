{"name": "messaging-channel-processor", "version": "1.0.0", "description": "", "scripts": {"build": "npm run clean && npm run generate && tsc", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "generate": "prisma generate --schema node_modules/@x-signal-inc/messaging-common/prisma", "license-report": "npx license-report --only=prod --output=table --exclude=@x-signal-inc/messaging-common --fields=name --fields=licenseType --fields=installedVersion --fields=link", "prestart": "npm run clean && npm run build", "start": "func start", "test": "echo \"No tests yet...\""}, "dependencies": {"@azure/functions": "^4.7.0", "@azure/identity": "^4.9.1", "@azure/service-bus": "^7.9.5", "@azure/storage-blob": "^12.27.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@types/html-to-text": "^9.0.4", "@x-signal-inc/messaging-common": "^0.1.86", "botbuilder": "^4.23.2", "botframework-connector": "^4.23.2", "html-to-mrkdwn-ts": "^1.1.0", "html-to-text": "^9.0.5", "serialize-error": "^12.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@lazycuh/eslint-config-base": "^3.0.7", "@types/node": "^22.x", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-plugin-sort-class-members": "^1.21.0", "prettier": "^3.5.3", "prisma": "6.5.0", "rimraf": "^6.0.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "main": "dist/{index.js,functions/*.js}", "type": "module", "prisma.schema": "node_modules/@x-signal-inc/messaging-common/prisma/schema/"}