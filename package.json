{"name": "messaging-api", "version": "1.0.0", "main": "server.js", "scripts": {"clean": "rimraf ./build", "start": "npm run build && cp .env ./build/src && node build/src/server.js", "quick-start": "npm run clean && tsc && cp .env ./build/src && node build/src/server.js", "build": "npm run clean && npm run generate && tsc", "test": "npm run build && dotenv -e .env.test --  node --import tsx  --test ./tests/**/*.test.ts", "coverage": "", "generate": "prisma generate --schema node_modules/@x-signal-inc/messaging-common/prisma", "license-report": "npx license-report --only=prod --output=table --exclude=@x-signal-inc/messaging-common --fields=name --fields=licenseType --fields=installedVersion --fields=link"}, "dependencies": {"@azure/app-configuration": "^1.9.0", "@azure/data-tables": "^13.3.0", "@azure/identity": "^4.10.0", "@azure/keyvault-secrets": "^4.9.0", "@azure/monitor-ingestion": "^1.1.0", "@azure/service-bus": "^7.9.5", "@fastify/compress": "^8.0.1", "@fastify/express": "^4.0.2", "@fastify/formbody": "^8.0.2", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^9.1.0", "@fastify/sensible": "^6.0.3", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.2", "@fastify/type-provider-typebox": "^5.1.0", "@loglayer/plugin-redaction": "^2.1.0", "@loglayer/transport-consola": "^2.1.0", "@loglayer/transport-datadog": "^3.1.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/teamsfx": "^3.0.2", "@prisma/client": "6.5.0", "@sinclair/typebox": "^0.34.33", "@x-signal-inc/messaging-common": "^0.1.88", "base-x": "^5.0.1", "botbuilder": "^4.23.2", "consola": "^3.4.2", "dotenv": "^16.5.0", "fastify": "^5.3.3", "fastify-plugin": "^5.0.1", "jwt-decode": "^4.0.0", "loglayer": "^6.4.0", "lru-cache": "^11.1.0", "serialize-error": "^12.0.0", "stytch": "^12.19.0", "twilio": "^5.6.1", "uuid": "^11.1.0", "validator": "^13.15.0"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.26.0", "@lazycuh/eslint-config-base": "^3.0.7", "@stylistic/eslint-plugin": "^4.2.0", "@types/eslint": "^9.6.1", "@types/node": "^22.15.18", "@types/uuid": "^10.0.0", "@types/validator": "^13.15.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-plugin-sort-class-members": "^1.21.0", "prettier": "^3.5.3", "prisma": "6.5.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "type": "module", "prisma.schema": "node_modules/@x-signal-inc/messaging-common/prisma/schema/"}