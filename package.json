{"name": "@x-signal-inc/messaging-common", "version": "0.1.88", "description": "Messaging Common Code", "scripts": {"build": "npm run clean && npm run generate && tsc", "clean-build": "npm run clean && tsc", "watch": "npm run clean && tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "db-reset1": "dotenv -e .env.test1 -- npx prisma db push --schema prisma/schema  --force-reset", "db-reset2": "dotenv -e .env.test2 -- npx prisma db push --schema prisma/schema  --force-reset", "db-push": "dotenv -e .env.test -- npx prisma db push", "license-report": "npx license-report --only=prod --output=table --exclude=@x-signal-inc/messaging-common --fields=name --fields=licenseType --fields=installedVersion --fields=link", "test-model": "npm run build && npm run db-reset1 && npm run db-reset2 && dotenv -e .env.test -- node --import tsx  --test ./tests/model/suite.test.ts", "test-config": "npm run build && dotenv -e .env.test -- node --import tsx  --test ./tests/config/app-config.test.ts", "test-crypto": "npm run build && dotenv -e .env.test -- node --import tsx  --test ./tests/crypto/crypto.test.ts", "test-logger": "npm run build && dotenv -e .env.test -- node --import tsx  --test ./tests/logger/logger.test.ts", "test-secret-vault": "npm run build && dotenv -e .env.test -- node --import tsx --test --test-concurrency=1 ./tests/secrets/secret-vault.test.ts", "generate": "prisma generate --schema prisma/schema"}, "dependencies": {"@azure/app-configuration": "^1.9.0", "@azure/identity": "^4.10.0", "@azure/keyvault-secrets": "^4.9.0", "@azure/service-bus": "^7.9.5", "@loglayer/plugin-redaction": "^2.1.0", "@loglayer/transport": "^2.2.0", "@loglayer/transport-consola": "^2.1.0", "@loglayer/transport-datadog": "^3.1.0", "@loglayer/transport-pretty-terminal": "^3.1.0", "@prisma/client": "6.8.2", "@prisma/extension-optimize": "^1.1.8", "consola": "^3.4.2", "loglayer": "^6.4.2", "lru-cache": "^11.1.0", "serialize-error": "^12.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.27.0", "@lazycuh/eslint-config-base": "^3.0.7", "@types/eslint": "^9.6.1", "@types/fast-redact": "^3.0.4", "@types/json5": "^2.2.0", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "@typescript-eslint/parser": "^8.32.1", "dotenv": "^16.5.0", "eslint": "^9.27.0", "eslint-import-resolver-typescript": "^4.4.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-sort-class-members": "^1.21.0", "json5": "^2.2.3", "prettier": "^3.5.3", "prisma": "6.8.2", "rimraf": "^6.0.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "publishConfig": {"@x-signal-inc:registry": "https://npm.pkg.github.com"}, "assets": [{"input": "prisma", "glob": "**/*.prisma", "ignore": []}], "main": "dist/src/index.js", "module": "dist/src/index.js", "types": "dist/types/src/index.d.ts", "type": "module", "files": ["dist", "prisma"]}