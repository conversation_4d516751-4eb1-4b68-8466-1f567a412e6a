/* eslint-disable @typescript-eslint/no-floating-promises */
// eslint-disable-next-line simple-import-sort/imports
import { describe, it } from 'node:test';

import assert from 'assert';
import { TrackedLink } from '@prisma/client';
import { getPrismaClient } from '@x-signal-inc/messaging-common';

export const trackedLinkTests = async (baseUrl: string, tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   let linkId: string;
   let hash: string;
   describe('Tracked Links', () => {
      it('POST /tracked-link returns status 201', async () => {
         const response = await fetch(`${baseUrl}/v1/tracked-link`, {
            body: JSON.stringify({ url: 'https://xsignal.inc' }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 201);

         const json = (await response.json()) as { hash: string; id: string };
         hash = json.hash;
         const data = await prisma.trackedLink.findFirst({
            where: {
               hash
            }
         });

         assert.ok(data);

         linkId = data.id;
         assert.strictEqual(data.id, json.id);
      });

      it('GET /:hash returns status 302', async () => {
         const response = await fetch(`${baseUrl}/${hash}`, {
            method: 'GET',
            redirect: 'manual'
         });

         assert.strictEqual(response.status, 302);

         const data = await prisma.trackedLink.findFirst({
            where: {
               id: linkId
            }
         });

         assert.ok(data);
         assert.strictEqual(data.clickCount, 1);
         assert.ok(data.lastClick);
         assert.ok(data.firstClick);
      });

      it('GET /tracked-link/:id returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/tracked-link/${linkId}`, {
            method: 'GET'
         });

         assert.strictEqual(response.status, 200);

         const data = (await response.json()) as TrackedLink;

         assert.strictEqual(data.url, 'https://xsignal.inc');
         assert.strictEqual(data.clickCount, 1);
         assert.strictEqual(data.hash, hash);
         assert.strictEqual(data.id, linkId);
         assert.ok(data.lastClick);
         assert.ok(data.firstClick);
      });
   });
};
