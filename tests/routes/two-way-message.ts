/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable simple-import-sort/imports */

import { describe, it } from 'node:test';

import assert from 'assert';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import {
   BulkCreatedTwoWayMessage,
   GetTwoWayMessage,
   GetTwoWayMessages
} from '../../src/modules/two-way-message/two-way-message.interface.js';

export const twoWayMessageTests = (baseUrl: string, tenantId: string) => {
   describe('Two Way Messages', () => {
      let lastId: string;
      let updated: Date;

      const message = {
         category: 'uncategorized',
         channel: 'test-channel',
         message: 'test message',
         recipientServiceId: '15555555555'
      };

      it('POST /two-way-message returns status 201', async () => {
         const response = await fetch(`${baseUrl}/v1/two-way-message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as GetTwoWayMessage;
         lastId = body.id;
         updated = new Date(body.updatedAt);
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.recipientServiceId, message.recipientServiceId);
         assert.strictEqual(body.channel, message.channel);
         assert.strictEqual(body.message, message.message);
         assert.strictEqual(body.category, 'uncategorized');
         assert.ok(body.createdAt);
      });

      it('GET /two-way-message/:id returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/two-way-message/${lastId}`);

         const body = (await response.json()) as GetTwoWayMessage;
         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         assert.strictEqual(body.recipientServiceId, message.recipientServiceId);
         assert.strictEqual(body.channel, message.channel);
         assert.strictEqual(body.message, message.message);
         assert.strictEqual(body.category, 'uncategorized');
         assert.strictEqual(body.direction, 'O');
         assert.ok(body.createdAt);
         assert.ok(body.updatedAt);
      });

      it('GET /two-way-messages returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/two-way-message?since=2021-01-01T00:00:00Z`);
         const body = (await response.json()) as GetTwoWayMessages;

         assert.strictEqual(response.status, 200);
         assert.ok(body.items);
         assert.strictEqual(body.items.length, 1);

         const item = body.items[0];
         assert.ok(item.id);
         assert.strictEqual(item.recipientServiceId, message.recipientServiceId);
         assert.strictEqual(item.channel, message.channel);
         assert.strictEqual(item.message, message.message);
         assert.strictEqual(item.category, 'uncategorized');
         assert.strictEqual(item.direction, 'O');
         assert.ok(item.createdAt);
         assert.ok(item.updatedAt);
         assert.ok(body.pagination);
         assert.strictEqual(body.pagination.total_records, 1);
         assert.strictEqual(body.pagination.page_size, 50);
      });

      it('GET /two-way-message/:id returns status 200 when error set', async () => {
         const prisma = await getPrismaClient(tenantId);
         await prisma.twoWayMessage.update({
            data: { error: 'error message' },
            where: { id: lastId }
         });
         const response = await fetch(`${baseUrl}/v1/two-way-message/${lastId}`);

         const body = (await response.json()) as GetTwoWayMessage;
         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         assert.strictEqual(body.error, 'error message');
         assert.ok(body.createdAt);
         assert.ok(body.updatedAt);
         assert.ok(updated < new Date(body.updatedAt));
      });

      it('PATCH /two-way-message/:id returns status 200', async () => {
         const update = { category: 'new category', conversationId: '1234', tenantMessageId: '1234' };
         const response = await fetch(`${baseUrl}/v1/two-way-message/${lastId}`, {
            body: JSON.stringify(update),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });
         const body = (await response.json()) as GetTwoWayMessage;
         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         assert.strictEqual(body.recipientServiceId, message.recipientServiceId);
         assert.strictEqual(body.channel, message.channel);
         assert.strictEqual(body.message, message.message);
         assert.strictEqual(body.category, update.category);
         assert.strictEqual(body.conversationId, update.conversationId);
         assert.strictEqual(body.tenantMessageId, update.tenantMessageId);
         assert.strictEqual(body.direction, 'O');
         assert.ok(body.createdAt);
         assert.ok(body.updatedAt);
         assert.ok(updated < new Date(body.updatedAt));
      });

      it('POST /two-way-message/bulk returns status 2010', async () => {
         const response = await fetch(`${baseUrl}/v1/two-way-message/bulk`, {
            body: JSON.stringify({
               items: [
                  { ...message, tenantMessageId: '1234' },
                  { ...message, tenantMessageId: '5678' }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as BulkCreatedTwoWayMessage;
         assert.strictEqual(response.status, 200);
         assert.ok(body.items);
         assert.strictEqual(body.items.length, 2);
         assert.ok(body.items[0].id);
         assert.ok(body.items[1].id);
         assert.ok(body.items[0].success);
         assert.ok(body.items[1].success);
         assert.strictEqual(body.items[0].error, '');
         assert.strictEqual(body.items[1].error, '');
         assert.strictEqual(body.items[0].tenantMessageId, '1234');
         assert.strictEqual(body.items[1].tenantMessageId, '5678');
         assert.ok(body.items[0].id !== body.items[1].id);
      });
   });
};
