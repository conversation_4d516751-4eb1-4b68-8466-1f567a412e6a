/* eslint-disable @typescript-eslint/no-floating-promises */
// eslint-disable-next-line simple-import-sort/imports
import { describe, it } from 'node:test';

import assert from 'assert';
import { TableClient } from '@azure/data-tables';

export const shortLinkTests =  (baseUrl: string, tenantId: string, tableClient: TableClient) => {
   describe('Short Links', () => {
      let hash: string;

      it('POST /short-link returns status 201', async () => {
         const response = await fetch(`${baseUrl}/v1/short-link`, {
            body: JSON.stringify({ url: 'https://xsignal.inc' }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 201);

         const json = (await response.json()) as { hash: string };
         hash = json.hash;

         const data = await tableClient.getEntity(hash, '');

         assert.ok(data);

         assert.strictEqual(data.partitionKey, hash);
         assert.ok(!data.tenantId);
      });

      it('GET /:hash returns status 302', async () => {
         const response = await fetch(`${baseUrl}/${hash}`, {
            method: 'GET',
            redirect: 'manual'
         });
         assert.strictEqual(response.status, 302);
      });
   });
};
