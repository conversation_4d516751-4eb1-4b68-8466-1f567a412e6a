/* eslint-disable @typescript-eslint/no-floating-promises */
import { after, before, suite } from 'node:test';

import { TableClient } from '@azure/data-tables';
import { DefaultAzureCredential } from '@azure/identity';
import { getPrismaClient, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { v7 as uuid } from 'uuid';
import { appOptions } from 'config/app-options.js';

import { AppMock } from '../mocks/app-mock.js';

import { channelTests } from './channel.js';
import { heartbeatTests } from './heartbeat.js';
import { MessageTests } from './message.js';
import { recipientTests } from './recipient.js';
import { shortLinkTests } from './short-link.js';
import { tagTests } from './tag.js';
import { topicTests } from './topic.js';
import { trackedLinkTests } from './tracked-link.js';
import { twoWayMessageTests } from './two-way-message.js';

const tenantId = '12345';

const seedDatabase = async (tableClient: TableClient) => {
   const vault = SecretVaultFactory.getSecretVault(true);
   vault.setSecret(`${tenantId}-db-url`, process.env.DATABASE_URL ?? '');

   const prisma = await getPrismaClient(tenantId);

   let tenant = await prisma.tenant.findUnique({ where: { id: tenantId } });

   if (tenant) {
      await prisma.tenant.delete({ where: { id: tenant.id } });
   }

   tenant = await prisma.tenant.create({
      data: {
         enabled: true,
         id: tenantId,
         name: 'Test Tenant'
      }
   });

   vault.setSecret(`${tenantId}-${tenant.encryptionKeyId}-encryption-key`, uuid());

   await prisma.channel.create({
      data: {
         channelType: 'test-1',
         description: 'Test Channel',
         displayName: 'Test-Channel',
         enabled: true,
         externalId: 'test-channel',
         service: 'test',
         tenantId: tenant.id
      }
   });

   await prisma.channel.create({
      data: {
         channelType: 'test-2',
         description: 'Other Channel',
         displayName: 'Other Channel',
         enabled: true,
         externalId: 'other-channel',
         service: 'test',
         tenantId: tenant.id
      }
   });

   await prisma.tag.create({
      data: {
         description: 'Test Tag',
         displayName: 'Test Tag',
         enabled: true,
         externalId: 'test-tag',
         tenantId: tenant.id
      }
   });

   await prisma.tag.create({
      data: {
         description: 'Other Tag',
         displayName: 'Other Tag',
         enabled: true,
         externalId: 'other-tag',
         tenantId: tenant.id
      }
   });

   await prisma.topic.create({
      data: {
         channelType: 'test',
         defaultCategory: 'test-topic',
         defaultService: 'test',
         displayName: 'Test Topic',
         externalId: 'test-topic',
         tenantId: tenant.id
      }
   });

   await prisma.topic.create({
      data: {
         channelType: 'test',
         defaultCategory: 'other-topic',
         defaultService: 'test',
         displayName: 'Other Topic',
         externalId: 'other-topic',
         tenantId: tenant.id
      }
   });

   await prisma.recipient.create({
      data: {
         identifiers: {
            create: [{ type: 'email', value: '<EMAIL>' }]
         },
         tenantId: tenant.id
      }
   });

   await prisma.recipient.create({
      data: {
         identifiers: {
            create: [{ type: 'email', value: '<EMAIL>' }]
         },
         tenantId: tenant.id
      }
   });

   // Delete test hash
   try{
      await tableClient.deleteEntity('NkHJDMPr9kbYQP57COaC4lNow3NpmFTF', '');
   } catch  {
      // Ignore error if entity does not exist
   }

};

suite('routes', async () => {
   let app: AppMock;

   before(async () => {
      app = new AppMock(appOptions);

      await app.initialize();
      await app.start();
   });

   after(async () => {
      await app.close();
   });

   const credential = new DefaultAzureCredential();
   const tableClient = new TableClient(
      process.env.SHORT_LINK_TABLE_URL ?? '',
      process.env.SHORT_LINK_TABLE_NAME ?? '',
      credential
   );

   const baseUrl = process.env.BASE_URL ?? 'http://localhost:3000';
   await seedDatabase(tableClient);
   heartbeatTests(baseUrl);
   twoWayMessageTests(baseUrl, tenantId);
   await channelTests(baseUrl, tenantId);
   await MessageTests(baseUrl, tenantId);
   await recipientTests(baseUrl, tenantId);
   shortLinkTests(baseUrl, tenantId, tableClient);
   await tagTests(baseUrl, tenantId);
   await topicTests(baseUrl, tenantId);
   await trackedLinkTests(baseUrl, tenantId);
});
