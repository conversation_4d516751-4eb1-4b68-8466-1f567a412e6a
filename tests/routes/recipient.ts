/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { v7 as uuid } from 'uuid';
import { BulkResponses, CreatedRecipient, GetRecipients } from '../../src/modules/recipient/recipient.interface.js';

import { ErrorResponse } from '../../src/modules/shared/error/error.schema.js';

type ConnectionAttribute = {
   id: string;
   name: string;
   value: string;
};

type Connection = {
   attributes: ConnectionAttribute[];
   enabled?: boolean;
   id: string;
   service: string;
};

type Identifier = {
   id?: string;
   type: string;
   value: string;
};

type RecipientAttribute = {
   id?: string;
   name: string;
   value?: string;
};

export const recipientTests = async (baseUrl: string, tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);
   describe('Recipients', () => {
      let fullId: string;
      let minimalId: string;

      it('POST minimum data to /recipient returns status 201', async () => {
         const minimalRecipient = {
            identifiers: [
               {
                  type: 'email',
                  value: '<EMAIL>'
               },
               { type: 'emplid', value: '12345' },
               { type: 'oprid', value: 'bob.smith' }
            ]
         };

         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify(minimalRecipient),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedRecipient;
         minimalId = body.id;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.timezone, 'Etc/UTC');
         assert.strictEqual(body.locale, 'en-US');
         assert.strictEqual(body.enabled, true);

         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 0);
         assert.ok(body.identifiers);
         assert.ok(body.identifiers.length > 0);
         assert.ok(body.identifiers[0].id);

         assert.ok(body.connections);
         assert.strictEqual(body.connections.length, 0);

         assert.ok(body.tags);
         assert.strictEqual(body.tags.length, 0);

         assert.ok(body.preferences);
         assert.strictEqual(body.preferences.length, 0);
      });

      it('POST all data to /recipient returns status 201', async () => {
         const testTag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'test-tag' } });
         const otherTag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'other-tag' } });

         const recipient = {
            attributes: [
               { name: 'test-attribute1', value: 'test value1' },
               { name: 'test-attribute2', value: 'test value2' },
               { name: 'test-attribute3', value: 'test value3' }
            ],
            connections: [
               {
                  attributes: [{ name: 'phone', value: '************' }],
                  service: 'sms'
               },
               {
                  attributes: [{ name: 'upn', value: '************' }],
                  enabled: false,
                  service: 'ms-teams',
                  showInPreferences: false
               }
            ],
            identifiers: [
               {
                  type: 'email',
                  value: '<EMAIL>'
               },
               { type: 'emplid', value: '67890' },
               { type: 'oprid', value: 'alice.smith' }
            ],
            tags: [testTag.id, otherTag.id]
         };

         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify(recipient),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedRecipient;
         fullId = body.id;

         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.timezone, 'Etc/UTC');
         assert.strictEqual(body.locale, 'en-US');
         assert.strictEqual(body.enabled, true);

         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 3);

         assert.ok(body.identifiers);
         assert.strictEqual(body.identifiers.length, 3);

         assert.strictEqual(body.connections.length, 2);
         assert.strictEqual(body.connections[0].attributes.length, 1);
         assert.strictEqual(body.connections[1].attributes.length, 1);
         body.connections.sort((a: Connection, b: Connection) => a.service.localeCompare(b.service));

         assert.strictEqual(body.connections[0].service, 'ms-teams');
         assert.strictEqual(body.connections[0].attributes[0].name, 'upn');
         assert.strictEqual(body.connections[0].attributes[0].value, '************');
         assert.strictEqual(body.connections[0].enabled, false);
         assert.strictEqual(body.connections[0].showInPreferences, false);

         assert.strictEqual(body.connections[1].service, 'sms');
         assert.strictEqual(body.connections[1].attributes[0].name, 'phone');
         assert.strictEqual(body.connections[1].attributes[0].value, '************');
         assert.strictEqual(body.connections[1].enabled, true);
         assert.strictEqual(body.connections[1].showInPreferences, true);

         assert.ok(body.tags);
         assert.strictEqual(body.tags.length, 2);
         assert.strictEqual(body.tags.includes(testTag.id), true);
         assert.strictEqual(body.tags.includes(otherTag.id), true);

         assert.ok(body.preferences);
         assert.strictEqual(body.preferences.length, 0);
      });

      it('GET /recipient} returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`);
         const body = (await response.json()) as GetRecipients;
         assert.strictEqual(response.status, 200);
         assert.ok(Array.isArray(body.items));
         assert.ok(body.items.length > 0);
         assert.ok(body.pagination);
      });

      it('GET /recipient/{id} returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${fullId}`);
         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, fullId);
      });

      it('GET /recipient/{id} with identifier as ID returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/<EMAIL>`);
         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, fullId);
      });

      it('POST /recipient as a service account returns status 201', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               enabled: false,
               identifiers: [
                  {
                     type: 'service-account',
                     value: 'administrators'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });
         assert.strictEqual(response.status, 201);
         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(body.enabled, false);
      });

      it('GET /recipient as a service account returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/service-accounts`);
         const body = (await response.json()) as GetRecipients;
         assert.strictEqual(response.status, 200);
         assert.ok(Array.isArray(body.items));
         assert.strictEqual(body.items.length, 1);
         assert.ok(body.pagination);
      });

      it('POST /recipient with existing identifier returns status 409', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(body.statusCode, 409);
         assert.strictEqual(body.code, 'FST_ERR_CONFLICT');
         assert.strictEqual(body.error, 'Conflict');
         assert.ok(body.message?.includes('Recipient already exists'));
      });

      it('POST /recipient with duplicate identifier types returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  },
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.strictEqual(body.message, 'Duplicate identifier types');
         assert.strictEqual(body.statusCode, 400);
      });

      it('POST /recipient with duplicate identifier types returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  },
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.strictEqual(body.message, 'Duplicate identifier types');
         assert.strictEqual(body.statusCode, 400);
      });

      it('POST /recipient with a service-account ID and another ID type returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  },
                  {
                     type: 'service-account',
                     value: 'registrar'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.strictEqual(body.message, 'Service account identifier cannot be mixed with other identifier types');
         assert.strictEqual(body.statusCode, 400);
      });

      it('PATCH /recipient with duplicate topics in preferences returns status 400', async () => {
         const topic = await prisma.topic.findFirstOrThrow({ where: { externalId: 'test-topic' } });

         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ],
               preferences: [
                  {
                     connections: [uuid()],
                     topic: topic.id
                  },
                  {
                     connections: [uuid()],
                     topic: topic.id
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.ok(body.message?.includes('Duplicate topic preference'));
         assert.strictEqual(body.statusCode, 400);
      });

      it('Patch /recipient with invalid connection returns status 400', async () => {
         const topic = await prisma.topic.findFirstOrThrow({ where: { externalId: 'test-topic' } });

         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ],
               preferences: [
                  {
                     connections: [uuid()],
                     topic: topic.id
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.ok(body.message?.includes('does not exist for recipient'));
         assert.strictEqual(body.statusCode, 400);
      });

      it('Patch /recipient can add preference returns 200', async () => {
         const topic = await prisma.topic.findFirstOrThrow({ where: { externalId: 'test-topic' } });
         const connection = await prisma.connection.findFirstOrThrow({ where: { recipientId: fullId } });

         const response = await fetch(`${baseUrl}/v1/recipient/${fullId}`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ],
               preferences: [
                  {
                     connections: [connection.id],
                     topic: topic.id
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, fullId);
         assert.strictEqual(body.preferences.length, 1);
         assert.strictEqual(body.preferences[0].topic, topic.id);
         assert.strictEqual(body.preferences[0].connections.length, 1);
         assert.strictEqual(body.preferences[0].connections[0], connection.id);
      });

      it('POST /recipient/{id}/preferences can replace recipient preferences returns 200', async () => {
         let response = await fetch(`${baseUrl}/v1/recipient/${fullId}/preferences`, {
            body: JSON.stringify({
               preferences: []
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         let body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, fullId);
         assert.strictEqual(body.preferences.length, 0);

         const topic = await prisma.topic.findFirstOrThrow({ where: { externalId: 'test-topic' } });
         const connection = await prisma.connection.findFirstOrThrow({ where: { recipientId: fullId } });

         response = await fetch(`${baseUrl}/v1/recipient/${fullId}/preferences`, {
            body: JSON.stringify({
               preferences: [
                  {
                     connections: [connection.id],
                     topic: topic.id
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, fullId);
         assert.strictEqual(body.preferences.length, 1);
         assert.strictEqual(body.preferences[0].topic, topic.id);
         assert.strictEqual(body.preferences[0].connections.length, 1);
         assert.strictEqual(body.preferences[0].connections[0], connection.id);
      });

      it('POST /recipient with duplicate service returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               connections: [
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     service: 'email'
                  },
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     service: 'email'
                  }
               ],
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.ok(body.message?.includes('Duplicate service'));
         assert.strictEqual(body.statusCode, 400);
      });

      it('POST /recipient/upsert-identifiers returns status 200', async () => {
         const currentCount = (
            await prisma.recipient.findMany({
               where: {
                  tenantId: '12345'
               }
            })
         ).length;

         const data = {
            items: [
               {
                  correlationId: '1',
                  identifiers: [
                     {
                        type: 'email',
                        value: '<EMAIL>'
                     },
                     {
                        type: 'phone',
                        value: '************'
                     }
                  ]
               },
               {
                  // doesn't do anything
                  correlationId: '2',
                  identifiers: [
                     {
                        type: 'phone',
                        value: '************'
                     }
                  ]
               },
               {
                  correlationId: '3',
                  identifiers: [
                     { type: 'email', value: '<EMAIL>' },
                     { type: 'emplid', value: '67890' },
                     { type: 'oprid', value: 'bob.smith' }
                  ]
               },
               {
                  correlationId: '4',
                  identifiers: [
                     { type: 'emplid', value: '67890' },
                     { type: 'oprid', value: 'sam.smith' },
                     { type: 'test', value: 'test' }
                  ]
               }
            ]
         };

         const response = await fetch(`${baseUrl}/v1/recipient/upsert-identifiers`, {
            body: JSON.stringify(data),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as BulkResponses;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.items.length, 4);

         const number3 = body.items.find((item: { correlationId: string }) => item.correlationId === '3');
         assert.ok(number3);
         assert.strictEqual(number3.success, false);
         assert.ok(number3.error);

         const updatedCount = (
            await prisma.recipient.findMany({
               include: { identifiers: true },
               where: {
                  tenantId: '12345'
               }
            })
         ).length;

         assert.strictEqual(updatedCount, currentCount + 2);
      });

      it('PATCH /recipient/{id} for a non-existent ID returns status 404', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/xxxx`, {
            body: JSON.stringify({
               enabled: false
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 404);
         assert.strictEqual(body.code, 'FST_ERR_NOT_FOUND');
         assert.strictEqual(body.error, 'Not Found');
         assert.strictEqual(body.message, 'Recipient not found');
         assert.strictEqual(body.statusCode, 404);
      });

      it('PATCH /recipient/{id} returns status 200', async () => {
         const tag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'test-tag' } });

         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               attributes: [
                  {
                     name: 'name',
                     value: 'value'
                  }
               ],

               connections: [
                  {
                     attributes: [
                        {
                           name: 'name',
                           value: 'value'
                        }
                     ],
                     enabled: false,
                     service: 'test'
                  }
               ],
               enabled: true,

               identifiers: [
                  {
                     type: 'service-account',
                     value: 'financial-aid'
                  }
               ],
               tags: [tag.id]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, minimalId);
         assert.strictEqual(body.enabled, true);
         assert.strictEqual(body.locale, 'en-US');
         assert.strictEqual(body.timezone, 'Etc/UTC');
         assert.strictEqual(body.identifiers.length, 1);
         assert.strictEqual(body.identifiers[0].type, 'service-account');
         assert.strictEqual(body.identifiers[0].value, 'financial-aid');
         assert.strictEqual(body.attributes.length, 1);
         assert.strictEqual(body.attributes[0].name, 'name');
         assert.strictEqual(body.attributes[0].value, 'value');
         assert.strictEqual(body.preferences.length, 0);
         assert.strictEqual(body.connections.length, 1);
         assert.strictEqual(body.connections[0].enabled, false);
         assert.strictEqual(body.connections[0].service, 'test');
         assert.strictEqual(body.connections[0].attributes.length, 1);
         assert.strictEqual(body.connections[0].attributes[0].name, 'name');
         assert.strictEqual(body.connections[0].attributes[0].value, 'value');
         assert.strictEqual(body.tags.length, 1);
         assert.strictEqual(body.tags[0], tag.id);

         const sameBody = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify(body),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const sameBodyCheck = (await sameBody.json()) as CreatedRecipient;
         assert.strictEqual(sameBody.status, 200);
         assert.strictEqual(sameBodyCheck.id, body.id);
         assert.strictEqual(sameBodyCheck.enabled, body.enabled);
         assert.strictEqual(sameBodyCheck.locale, body.locale);
         assert.strictEqual(sameBodyCheck.timezone, body.timezone);
         assert.strictEqual(sameBodyCheck.identifiers.length, 1);
         sameBodyCheck.identifiers.sort((a: Identifier, b: Identifier) => a.type.localeCompare(b.type));
         assert.strictEqual(sameBodyCheck.identifiers[0].type, body.identifiers[0].type);
         assert.strictEqual(sameBodyCheck.identifiers[0].value, body.identifiers[0].value);
         assert.strictEqual(sameBodyCheck.attributes.length, 1);
         sameBodyCheck.attributes.sort((a: RecipientAttribute, b: RecipientAttribute) => a.name.localeCompare(b.name));
         assert.strictEqual(sameBodyCheck.attributes[0].name, body.attributes[0].name);
         assert.strictEqual(sameBodyCheck.attributes[0].value, body.attributes[0].value);
         assert.strictEqual(sameBodyCheck.preferences.length, 0);
         assert.strictEqual(sameBodyCheck.connections.length, 1);
         assert.strictEqual(sameBodyCheck.connections[0].id, body.connections[0].id);
         assert.strictEqual(sameBodyCheck.connections[0].attributes.length, 1);
         assert.strictEqual(sameBodyCheck.connections[0].attributes[0].name, body.connections[0].attributes[0].name);
         assert.strictEqual(sameBodyCheck.connections[0].attributes[0].value, body.connections[0].attributes[0].value);

         body.connections.push({
            attributes: [{ id: uuid(), name: 'phone', value: '************' }],
            enabled: false,
            id: uuid(),
            service: 'sms',
            showInPreferences: true
         });

         const bodyWithAnotherConnection = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify(body),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const bodyWithAnotherConnectionCheck = (await bodyWithAnotherConnection.json()) as CreatedRecipient;
         assert.strictEqual(bodyWithAnotherConnection.status, 200);
         bodyWithAnotherConnectionCheck.connections.sort((a: Connection, b: Connection) => a.id.localeCompare(b.id));
         assert.strictEqual(bodyWithAnotherConnectionCheck.connections.length, 2);
         assert.strictEqual(bodyWithAnotherConnectionCheck.connections[0].id, body.connections[0].id);
         assert.strictEqual(bodyWithAnotherConnectionCheck.connections[0].attributes.length, 1);
         assert.strictEqual(
            bodyWithAnotherConnectionCheck.connections[0].attributes[0].name,
            body.connections[0].attributes[0].name
         );
         assert.strictEqual(
            bodyWithAnotherConnectionCheck.connections[0].attributes[0].value,
            body.connections[0].attributes[0].value
         );

         assert.strictEqual(bodyWithAnotherConnectionCheck.connections[1].attributes.length, 1);
         bodyWithAnotherConnectionCheck.connections[1].attributes.sort((a: RecipientAttribute, b: RecipientAttribute) =>
            a.name.localeCompare(b.name)
         );
         assert.strictEqual(bodyWithAnotherConnectionCheck.connections[1].attributes[0].name, 'phone');
         assert.strictEqual(bodyWithAnotherConnectionCheck.connections[1].attributes[0].value, '************');
      });

      it('PATCH /recipient/{id} with everything empty returns status 200', async () => {
         const tag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'test-tag' } });

         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({}),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, minimalId);
         assert.strictEqual(body.enabled, true);
         assert.strictEqual(body.locale, 'en-US');
         assert.strictEqual(body.timezone, 'Etc/UTC');
         assert.strictEqual(body.identifiers.length, 1);
         assert.strictEqual(body.identifiers[0].type, 'service-account');
         assert.strictEqual(body.identifiers[0].value, 'financial-aid');
         assert.strictEqual(body.attributes.length, 1);
         assert.strictEqual(body.attributes[0].name, 'name');
         assert.strictEqual(body.attributes[0].value, 'value');
         assert.strictEqual(body.preferences.length, 0);
         assert.strictEqual(body.connections.length, 2);
         assert.strictEqual(body.connections[0].enabled, false);
         assert.strictEqual(body.connections[0].service, 'test');
         assert.strictEqual(body.connections[0].enabled, false);
         assert.strictEqual(body.connections[0].attributes.length, 1);
         assert.strictEqual(body.connections[0].attributes[0].name, 'name');
         assert.strictEqual(body.connections[0].attributes[0].value, 'value');
         assert.strictEqual(body.connections[1].service, 'sms');
         assert.strictEqual(body.connections[1].attributes.length, 1);
         assert.strictEqual(body.connections[1].attributes[0].name, 'phone');
         assert.strictEqual(body.connections[1].attributes[0].value, '************');
         assert.strictEqual(body.tags.length, 1);
         assert.strictEqual(body.tags[0], tag.id);
      });

      it('PATCH /recipient/{id} with empty array for identifiers returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               id: minimalId,
               identifiers: []
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;

         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.strictEqual(body.message, 'No identifiers provided');
         assert.strictEqual(body.statusCode, 400);
      });

      it('PATCH /recipient/{id} deleting all relations (except identifiers) returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               attributes: [],
               connections: [],
               id: minimalId,
               preferences: [],
               tags: []
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, minimalId);
         assert.strictEqual(body.enabled, true);
         assert.strictEqual(body.locale, 'en-US');
         assert.strictEqual(body.timezone, 'Etc/UTC');
         assert.strictEqual(body.identifiers.length, 1);
         assert.strictEqual(body.attributes.length, 0);
         assert.strictEqual(body.preferences.length, 0);
         assert.strictEqual(body.connections.length, 0);
         assert.strictEqual(body.tags.length, 0);
      });

      it('PATCH /recipient with existing identifier returns status 409', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });
         assert.strictEqual(response.status, 409);

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 409);
         assert.strictEqual(body.statusCode, 409);
         assert.strictEqual(body.code, 'FST_ERR_CONFLICT');
         assert.strictEqual(body.error, 'Conflict');
         assert.ok(body.message?.includes('Recipient already exists'));
      });

      it('PATCH /recipient with duplicate identifier types returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  },
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.strictEqual(body.message, 'Duplicate identifier types');
         assert.strictEqual(body.statusCode, 400);
      });

      it('PATCH /recipient with duplicate attribute names returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               attributes: [
                  {
                     name: 'test',
                     value: 'test'
                  },
                  {
                     name: 'test',
                     value: 'test'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_CONFLICT');
         assert.strictEqual(body.error, 'Bad Request');
         assert.strictEqual(body.message, 'Duplicate attribute name "test"');
         assert.strictEqual(body.statusCode, 400);
      });

      it('PATCH /recipient with duplicate topics returns status 400', async () => {
         const topic = await prisma.topic.findFirstOrThrow({ where: { externalId: 'test-topic' } });
         const connections = await prisma.connection.findMany({ where: { recipientId: fullId } });
         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               preferences: [
                  {
                     connections: [connections[0].id],
                     topic: topic.id
                  },
                  {
                     connections: [connections[1].id],
                     topic: topic.id
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.ok(body.message?.includes('Duplicate topic preference'));
         assert.strictEqual(body.statusCode, 400);
      });

      it('PATCH /recipient with duplicate channels in connections returns status 400', async () => {
         const channel = await prisma.channel.findFirstOrThrow({ where: { externalId: 'test-channel' } });

         const response = await fetch(`${baseUrl}/v1/recipient/${minimalId}`, {
            body: JSON.stringify({
               connections: [
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     service: channel.service
                  },
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     service: channel.service
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.ok(body.message?.includes('Duplicate service'));
      });

      it('POST /recipient/bulk-upsert returns status 200', async () => {
         const topic = await prisma.topic.findFirstOrThrow({ where: { externalId: 'test-topic' } });
         const connection = await prisma.channel.findFirstOrThrow({ where: { externalId: 'test-channel' } });
         const tag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'test-tag' } });
         const testTag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'test-tag' } });
         const otherTag = await prisma.tag.findFirstOrThrow({ where: { externalId: 'other-tag' } });

         const response = await fetch(`${baseUrl}/v1/recipient/bulk-upsert`, {
            body: JSON.stringify({
               items: [
                  {
                     attributes: [
                        { name: 'test-attribute1', value: 'test value1' },
                        { name: 'test-attribute2', value: 'test value2' },
                        { name: 'test-attribute3', value: 'test value3' }
                     ],
                     connections: [
                        {
                           attributes: [{ name: 'phone', value: '************' }],
                           enabled: true,
                           service: 'sms'
                        },
                        {
                           attributes: [{ name: 'phone', value: '************' }],
                           enabled: true,
                           service: 'test'
                        }
                     ],
                     correlationId: '1',
                     identifiers: [
                        {
                           type: 'email',
                           value: '<EMAIL>'
                        },
                        { type: 'emplid', value: 'abcd' },
                        { type: 'oprid', value: 'ted.smith' }
                     ],
                     tags: [testTag.id, otherTag.id]
                  },
                  {
                     correlationId: '2',
                     identifiers: [
                        {
                           type: 'email',
                           value: 'yy'
                        }
                     ]
                  },
                  {
                     attributes: [
                        {
                           name: 'name',
                           value: 'value'
                        }
                     ],
                     connections: [
                        {
                           attributes: [
                              {
                                 name: 'name',
                                 value: 'value'
                              }
                           ],
                           enabled: false,
                           service: 'sms'
                        }
                     ],
                     correlationId: '3',
                     enabled: true,
                     id: minimalId,
                     identifiers: [
                        {
                           type: 'service-account',
                           value: 'financial-aid'
                        }
                     ],
                     locale: 'en-US',
                     preferences: [
                        {
                           connections: [connection.id],
                           topic: topic.id
                        }
                     ],
                     tags: [tag.id],
                     timezone: 'Etc/UTC'
                  },
                  {
                     correlationId: '4',
                     identifiers: [
                        {
                           type: 'email',
                           value: 'yy'
                        },
                        { type: 'emplid', value: 'abcd' }
                     ]
                  },
                  {
                     attributes: [
                        {
                           name: 'name',
                           value: 'value'
                        }
                     ],
                     connections: [
                        {
                           attributes: [],
                           enabled: false,
                           service: 'test'
                        }
                     ],
                     correlationId: '5',
                     enabled: true,
                     id: minimalId,
                     identifiers: [
                        {
                           type: 'service-account',
                           value: 'financial-aid'
                        }
                     ],
                     locale: 'en-US',
                     tags: [tag.id],
                     timezone: 'Etc/UTC'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as BulkResponses;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.items.length, 5);
         assert.ok(body.items[0].id);
         body.items.sort((a: { correlationId: string }, b: { correlationId: string }) =>
            a.correlationId.localeCompare(b.correlationId)
         );
         assert.strictEqual(body.items[0].success, true);
         assert.strictEqual(body.items[0].correlationId, '1');
         assert.ok(body.items[1].id);
         assert.strictEqual(body.items[1].success, true);
         assert.strictEqual(body.items[1].correlationId, '2');
         assert.ok(!body.items[2].id);
         assert.strictEqual(body.items[2].success, false);
         assert.strictEqual(body.items[2].correlationId, '3');
         assert.ok(!body.items[3].id);
         assert.strictEqual(body.items[3].success, false);
         assert.strictEqual(body.items[3].correlationId, '4');
         assert.strictEqual(body.items[3].error, 'Identifiers are not unique across recipients');
         assert.ok(body.items[4].id);
         assert.strictEqual(body.items[4].success, true);
         assert.strictEqual(body.items[4].correlationId, '5');

         const created = await prisma.recipient.findFirstOrThrow({
            include: {
               attributes: true,
               connections: {
                  include: {
                     attributes: true
                  }
               },
               identifiers: true,
               preferences: {
                  include: {
                     connections: { select: { id: true } },
                     topic: { select: { id: true } }
                  }
               },
               tags: { select: { id: true } }
            },
            where: { id: body.items[0].id }
         });

         assert.strictEqual(created.timezone, 'Etc/UTC');
         assert.strictEqual(created.locale, 'en-US');
         assert.strictEqual(created.enabled, true);

         assert.ok(created.attributes);
         assert.strictEqual(created.attributes.length, 3);

         assert.ok(created.identifiers);
         assert.strictEqual(created.identifiers.length, 3);

         assert.strictEqual(created.connections.length, 2);
         assert.strictEqual(created.connections[0].attributes.length, 1);
         assert.strictEqual(created.connections[1].attributes.length, 1);

         assert.ok(created.tags);
         assert.strictEqual(created.tags.length, 2);
         assert.strictEqual(created.tags.map((t) => t.id).includes(testTag.id), true);
         assert.strictEqual(created.tags.map((t) => t.id).includes(otherTag.id), true);

         assert.ok(created.preferences);
         assert.strictEqual(created.preferences.length, 0);

         const recipient = await prisma.recipient.findFirstOrThrow({
            include: {
               attributes: true,
               connections: {
                  include: {
                     attributes: true
                  }
               },
               identifiers: true,
               preferences: {
                  include: {
                     connections: { select: { id: true } },
                     topic: { select: { id: true } }
                  }
               },
               tags: { select: { id: true } }
            },
            where: { id: minimalId }
         });

         assert.strictEqual(recipient.id, minimalId);
         assert.strictEqual(recipient.enabled, true);
         assert.strictEqual(recipient.locale, 'en-US');
         assert.strictEqual(recipient.timezone, 'Etc/UTC');
         assert.strictEqual(recipient.identifiers.length, 1);
         assert.strictEqual(recipient.identifiers[0].type, 'service-account');
         assert.strictEqual(recipient.identifiers[0].value, 'financial-aid');
         assert.strictEqual(recipient.attributes.length, 1);
         assert.strictEqual(recipient.attributes[0].name, 'name');
         assert.strictEqual(recipient.attributes[0].value, 'value');
         assert.strictEqual(recipient.preferences.length, 0);
         assert.strictEqual(recipient.connections.length, 1);
         assert.strictEqual(recipient.connections[0].attributes.length, 0);
         assert.strictEqual(recipient.tags.length, 1);
         assert.strictEqual(recipient.tags[0].id, tag.id);
      });

      it('DELETE /recipient/{id} returns status 204', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${fullId}`, {
            method: 'DELETE'
         });
         assert.strictEqual(response.status, 204);
      });

      it('GET /recipient/{id} after delete returns status 404', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${fullId}`);
         assert.strictEqual(response.status, 404);
      });

      it('POST /recipient when connection channel ID is not valid returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               connections: [
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     channel: '01954e99-a354-7887-8181-d3500a8ed3e4',
                     service: 'email'
                  }
               ],
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(body.statusCode, 400);
         assert.strictEqual(body.code, 'FST_ERR_VALIDATION');
         assert.strictEqual(body.error, 'Bad Request');
         assert.ok(body.message?.includes('Channel not found'));
      });

      let recipientId: string;
      let connectionId: string;
      it('POST /recipient with connection channel ID returns status 201', async () => {
         const testChannel = await prisma.channel.findFirstOrThrow({
            where: { externalId: 'test-channel' }
         });

         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               connections: [
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     channel: testChannel.id,
                     service: 'test'
                  }
               ],
               identifiers: [
                  {
                     type: 'email',
                     value: '<EMAIL>'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedRecipient;
         recipientId = body.id;
         connectionId = body.connections[0].id;
         assert.strictEqual(response.status, 201);
      });

      it('PATCH /recipient remove connection channel returns 200', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient/${recipientId}`, {
            body: JSON.stringify({
               connections: [
                  {
                     attributes: [
                        {
                           name: 'email',
                           value: '<EMAIL>'
                        }
                     ],
                     id: connectionId,
                     service: 'test'
                  }
               ]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedRecipient;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.connections.length, 1);
         assert.strictEqual(body.connections[0].id, connectionId);
         assert.ok(!body.connections[0].channel);
      });

      // eslint-disable-next-line @stylistic/max-len, max-len
      it('POST /recipient with a ID in a different case that belongs to another recipient returns status 409', async () => {
         const response = await fetch(`${baseUrl}/v1/recipient`, {
            body: JSON.stringify({
               identifiers: [{ type: 'oprid', value: 'SAM.SMITH' }]
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 409);
         assert.strictEqual(body.statusCode, 409);
         assert.strictEqual(body.code, 'FST_ERR_CONFLICT');
         assert.strictEqual(body.error, 'Conflict');
         assert.ok(body.message?.includes('Recipient already exists with one of the given identifiers'));
      });
   });
};
