/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { CreatedTag, GetTags } from '../../src/modules/tag/tag.interface.js';

export const tagTests = async (baseUrl: string, tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);
   describe('Tags', () => {
      let lastId: string;

      const tag = {
         description: 'test description',
         displayName: 'created-tag',
         enabled: true,
         externalId: 'created-tag',
         selectable: false
      };

      it('POST /tag returns status 201', async () => {
         const response = await fetch(`${baseUrl}/v1/tag`, {
            body: JSON.stringify(tag),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedTag;
         lastId = body.id;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, tag.externalId);
         assert.strictEqual(body.displayName, tag.displayName);
         assert.strictEqual(body.description, tag.description);
         assert.strictEqual(body.enabled, tag.enabled);
         assert.strictEqual(body.selectable, tag.selectable);
      });

      it('POST /tag returns status 409 when externalId already exists', async () => {
         const replace = {
            description: 'replaced description',
            displayName: 'replaced-tag',
            enabled: true,
            externalId: 'CREATED-tag',
            selectable: false
         };
         const response = await fetch(`${baseUrl}/v1/tag`, {
            body: JSON.stringify(replace),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 409);
      });

      it('GET /tag/:id returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/tag/${lastId}`);
         const body = (await response.json()) as CreatedTag;

         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, lastId);
         assert.strictEqual(body.displayName, tag.displayName);
         assert.strictEqual(body.description, tag.description);
         assert.strictEqual(body.enabled, tag.enabled);
         assert.strictEqual(body.selectable, tag.selectable);
      });

      it('GET /tag returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/tag`);
         const body = (await response.json()) as GetTags;

         assert.strictEqual(response.status, 200);
         assert.ok(Array.isArray(body.items));
         assert.ok(body.items.length > 0);
         assert.ok(body.pagination);
      });

      it('PATCH /tag/:id returns status 200', async () => {
         const updatedTag = {
            description: null,
            displayName: 'updated-test-tag',
            enabled: false,
            selectable: false
         };

         const response = await fetch(`${baseUrl}/v1/tag/${lastId}`, {
            body: JSON.stringify(updatedTag),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedTag;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, lastId);
         assert.strictEqual(body.displayName, updatedTag.displayName);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.enabled, updatedTag.enabled);
         assert.strictEqual(body.selectable, updatedTag.selectable);
      });

      it('POST /tag/:id/recipients/add returns status 200', async () => {
         const testTag = await prisma.tag.findFirstOrThrow({
            include: { recipients: true },
            where: { externalId: 'test-tag' }
         });

         const count = testTag.recipients.length;

         const recipient = await prisma.recipient.findFirstOrThrow({
            where: { identifiers: { some: { value: '<EMAIL>' } } }
         });
         const recipients = { items: [recipient.id] };

         const response = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients/add`, {
            body: JSON.stringify(recipients),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 200);

         const tagResponse = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients`);
         const body = (await tagResponse.json()) as GetTags;
         assert.strictEqual(tagResponse.status, 200);
         assert.strictEqual(body.items.length, count + 1);
         assert.ok(body.pagination);
      });

      it('POST /tag/:id/recipients/remove returns status 200', async () => {
         const testTag = await prisma.tag.findFirstOrThrow({
            include: { recipients: true },
            where: { externalId: 'test-tag' }
         });
         const count = testTag.recipients.length;

         const recipient = await prisma.recipient.findFirstOrThrow({
            where: { identifiers: { some: { value: '<EMAIL>' } } }
         });
         const recipients = { items: [recipient.id] };

         const response = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients/remove`, {
            body: JSON.stringify(recipients),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 200);

         const tagResponse = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients`);
         const body = (await tagResponse.json()) as GetTags;
         assert.strictEqual(tagResponse.status, 200);
         assert.strictEqual(body.items.length, count - 1);
         assert.ok(body.pagination);
      });

      it('PUT /tag/:id/recipients returns status 200', async () => {
         const testTag = await prisma.tag.findFirstOrThrow({
            where: { externalId: 'test-tag' }
         });

         const recipient = await prisma.recipient.findFirstOrThrow({
            where: { identifiers: { some: { value: '<EMAIL>' } } }
         });
         const recipients = { items: [recipient.id] };

         const addOne = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients`, {
            body: JSON.stringify(recipients),
            headers: { 'Content-Type': 'application/json' },
            method: 'PUT'
         });

         assert.strictEqual(addOne.status, 200);

         let tagResponse = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients`);
         let body = (await tagResponse.json()) as GetTags;
         assert.strictEqual(tagResponse.status, 200);
         assert.strictEqual(body.items.length, 1);
         assert.ok(body.pagination);

         const removeAll = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients`, {
            body: JSON.stringify({ items: [] }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PUT'
         });

         assert.strictEqual(removeAll.status, 200);

         tagResponse = await fetch(`${baseUrl}/v1/tag/${testTag.id}/recipients`);
         body = (await tagResponse.json()) as GetTags;
         assert.strictEqual(tagResponse.status, 200);
         assert.strictEqual(body.items.length, 0);
         assert.ok(body.pagination);
      });

      it('DELETE /tag/:id returns status 204', async () => {
         const response = await fetch(`${baseUrl}/v1/tag/${lastId}`, {
            method: 'DELETE'
         });
         assert.strictEqual(response.status, 204);
      });

      it('GET /tag/:id returns status 404 after deletion', async () => {
         const response = await fetch(`${baseUrl}/v1/tag/${lastId}`);
         assert.strictEqual(response.status, 404);
      });
   });
};
