/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */
import { describe, it } from 'node:test';

import assert from 'assert';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { CreatedTopic, GetTopics } from '../../src/modules/topic/topic.interface.js';

export const topicTests = async (baseUrl: string, tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('Topics', async () => {
      let lastId: string;

      const testChannel = await prisma.channel.findFirstOrThrow({ where: { externalId: 'test-channel' } });

      const topic = {
         channelType: 'default',
         defaultCategory: 'created-topic',
         defaultService: 'test',
         displayName: 'created-topic',
         externalId: 'created-topic'
      };

      it('POST /topic returns status 201', async () => {
         const response = await fetch(`${baseUrl}/v1/topic`, {
            body: JSON.stringify(topic),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedTopic;
         lastId = body.id;
         assert.strictEqual(response.status, 201);
         assert.strictEqual(body.id, lastId);
         assert.strictEqual(body.displayName, topic.displayName);
         assert.strictEqual(body.defaultService, 'test');
         assert.strictEqual(body.externalId, topic.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.channelAlwaysOn, false);
         assert.strictEqual(body.defaultCategory, 'created-topic');
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.visibleInPreferences, true);
         assert.strictEqual(body.userMustOptIn, false);
         assert.strictEqual(body.orderSequence, 1);
         assert.deepStrictEqual(body.userPreferenceRoles, []);
         assert.deepStrictEqual(body.roles, []);
      });

      it('POST /topic with white space name returns status 400', async () => {
         const response = await fetch(`${baseUrl}/v1/topic`, {
            body: JSON.stringify({
               channelType: 'default',
               defaultChannelId: testChannel.id,
               displayName: '   ',
               externalId: 'whitespace-topic'
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 400);
      });

      it('POST /topic returns status 409 when externalId already exists', async () => {
         const replace = {
            channelType: 'default',
            defaultCategory: 'replace-topic',
            defaultService: 'test',
            displayName: 'replace-topic',
            externalId: 'CREATED-topic'
         };

         const response = await fetch(`${baseUrl}/v1/topic`, {
            body: JSON.stringify(replace),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 409);
      });

      it('POST /topic returns status 400 when externalId has a space', async () => {
         const response = await fetch(`${baseUrl}/v1/topic`, {
            body: JSON.stringify({
               channelType: 'default',
               defaultChannelId: testChannel.id,
               displayName: ' some topic',
               externalId: 'has space'
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 400);
      });

      it('GET /topic/:id returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/topic/${lastId}`);
         const body = (await response.json()) as CreatedTopic;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, lastId);
         assert.strictEqual(body.displayName, topic.displayName);
         assert.strictEqual(body.defaultService, 'test');
         assert.strictEqual(body.externalId, topic.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.channelAlwaysOn, false);
         assert.strictEqual(body.defaultCategory, topic.defaultCategory);
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.visibleInPreferences, true);
         assert.strictEqual(body.userMustOptIn, false);
         assert.strictEqual(body.orderSequence, 1);
         assert.deepStrictEqual(body.userPreferenceRoles, []);
         assert.deepStrictEqual(body.roles, []);
      });

      it('GET /topic returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/topic`);
         const body = (await response.json()) as GetTopics;

         assert.strictEqual(response.status, 200);
         assert.ok(Array.isArray(body.items));
         assert.ok(body.items.length > 0);
         assert.ok(body.pagination);
      });

      it('PATCH /topic/:id returns status 200', async () => {
         const updatedTag = {
            channelAlwaysOn: true,
            defaultCategory: 'test-category',
            description: null,
            displayName: 'updated-test-topic',
            enabled: false,
            externalId: 'updated-test-topic',
            orderSequence: 2,
            roles: ['role-1', 'role-2'],
            type: 'work',
            userMustOptIn: true,
            userPreferenceRoles: ['role1', 'role2'],
            visibleInPreferences: false
         };

         const response = await fetch(`${baseUrl}/v1/topic/${lastId}`, {
            body: JSON.stringify(updatedTag),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });

         const body = (await response.json()) as CreatedTopic;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, lastId);
         assert.strictEqual(body.displayName, updatedTag.displayName);
         assert.strictEqual(body.defaultService, 'test');
         assert.strictEqual(body.externalId, updatedTag.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.channelAlwaysOn, true);
         assert.strictEqual(body.defaultCategory, 'test-category');
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.visibleInPreferences, false);
         assert.strictEqual(body.userMustOptIn, true);
         assert.strictEqual(body.orderSequence, 2);
         assert.deepStrictEqual(body.userPreferenceRoles, ['role1', 'role2']);
         assert.deepStrictEqual(body.roles, ['role-1', 'role-2']);
      });

      it('PATCH /topic/:id returns status 400 with white space name', async () => {
         const response = await fetch(`${baseUrl}/v1/topic/${lastId}`, {
            body: JSON.stringify({
               displayName: '   '
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'PATCH'
         });
         assert.strictEqual(response.status, 400);
      });

      it('DELETE /topic/:id returns status 204', async () => {
         const response = await fetch(`${baseUrl}/v1/topic/${lastId}`, {
            method: 'DELETE'
         });
         assert.strictEqual(response.status, 204);
      });

      it('GET /topic/:id returns status 404 after deletion', async () => {
         const response = await fetch(`${baseUrl}/v1/tag/${lastId}`);
         assert.strictEqual(response.status, 404);
      });
   });
};
