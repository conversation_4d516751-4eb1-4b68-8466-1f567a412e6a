/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { AttributeConstants, getPrismaClient } from '@x-signal-inc/messaging-common';
import { CreatedChannel } from '../../src/modules/channel/channel.interface.js';

type Attribute = {
   id: string;
   name: string;
   secure: boolean;
   value?: string;
};

export const channelTests = async (baseUrl: string, tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('Channels', () => {
      let lastId: string;

      const esendexChannel = {
         attributes: [
            {
               name: AttributeConstants.ESENDEX_KEY,
               value: 'abcdefghi'
            },
            {
               name: AttributeConstants.PHONE,
               value: '**************'
            }
         ],
         channelType: 'default',
         displayName: 'SMS',
         externalId: 'esendex-channel',
         provider: AttributeConstants.ESENDEX,
         service: AttributeConstants.SMS
      };

      it('POST /channel Esendex channel returns status 201', async () => {
         const channel = esendexChannel;

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(channel),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedChannel;
         lastId = body.id;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, channel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, channel.service);
         assert.strictEqual(body.provider, channel.provider);
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.displayName, channel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 2);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[0].name);
         assert.ok(attribute.value?.includes('******'));
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[1].name);
         assert.strictEqual(attribute.value, channel.attributes[1].value);
      });

      it('POST /channel Twilio channel returns status 201', async () => {
         const channel = {
            attributes: [
               {
                  name: AttributeConstants.PHONE,
                  value: '+15555555555'
               },
               {
                  name: AttributeConstants.TWILIO_SID,
                  value: 'abcdefghi'
               },
               {
                  name: AttributeConstants.TWILIO_TOKEN,
                  value: 'abcdefghi'
               }
            ],
            channelType: 'registrar',
            displayName: 'SMS',
            externalId: 'twilio-channel',
            name: 'twilio-channel',
            provider: AttributeConstants.TWILIO,
            service: AttributeConstants.SMS
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(channel),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedChannel;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, channel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, channel.service);
         assert.strictEqual(body.provider, channel.provider);
         assert.strictEqual(body.channelType, 'registrar');
         assert.strictEqual(body.displayName, channel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 3);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[0].name);
         assert.strictEqual(attribute.value, channel.attributes[0].value);
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[1].name);
         assert.strictEqual(attribute.value, channel.attributes[1].value);
         attribute = body.attributes[2];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[2].name);
         assert.ok(attribute.value?.includes('******'));
      });

      it('POST /channel Modo channel returns status 201', async () => {
         const channel = {
            attributes: [
               {
                  name: AttributeConstants.MODO_APPLICATION_ID,
                  value: 'abcdefghi'
               },
               {
                  name: AttributeConstants.MODO_AUTHORIZATION,
                  value: 'jwtxxxxxxxxx'
               },
               {
                  name: AttributeConstants.MODO_BANNER,
                  value: '!ALL'
               },
               {
                  name: AttributeConstants.MODO_CHANNEL,
                  value: '12345'
               },
               {
                  name: AttributeConstants.MODO_FILTER_KEY,
                  value: 'emplid'
               },
               {
                  name: AttributeConstants.MODO_GROUP_ATTRIBUTE,
                  value: 'groups'
               },
               {
                  name: AttributeConstants.MODO_PUSH,
                  value: 'true'
               },
               {
                  name: AttributeConstants.MODO_STYLE,
                  value: 'alarm'
               },
               {
                  name: AttributeConstants.MODO_TARGET,
                  value: 'test'
               }
            ],
            channelType: 'registrar',
            displayName: 'SMS',
            externalId: 'modo-channel',
            name: 'modo-channel',
            service: AttributeConstants.MODO
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(channel),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedChannel;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, channel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, channel.service);
         assert.strictEqual(body.provider, AttributeConstants.MODO);
         assert.strictEqual(body.channelType, 'registrar');
         assert.strictEqual(body.displayName, channel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 9);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[0].name);
         assert.strictEqual(attribute.value, channel.attributes[0].value);
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[1].name);
         assert.ok(attribute.value?.includes('******'));
         attribute = body.attributes[2];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[2].name);
         assert.strictEqual(attribute.value, channel.attributes[2].value);
         attribute = body.attributes[3];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[3].name);
         assert.strictEqual(attribute.value, channel.attributes[3].value);
         attribute = body.attributes[4];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[4].name);
         assert.strictEqual(attribute.value, channel.attributes[4].value);
         attribute = body.attributes[5];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[5].name);
         assert.strictEqual(attribute.value, channel.attributes[5].value);
         attribute = body.attributes[6];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[6].name);
         assert.strictEqual(attribute.value, channel.attributes[6].value);
         attribute = body.attributes[7];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[7].name);
         assert.strictEqual(attribute.value, channel.attributes[7].value);
         attribute = body.attributes[8];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[8].name);
         assert.strictEqual(attribute.value, channel.attributes[8].value);
      });

      it('POST /channel Canvas channel returns status 201', async () => {
         const channel = {
            attributes: [
               {
                  name: AttributeConstants.CLIENT_ID,
                  value: '12345'
               },
               {
                  name: AttributeConstants.CLIENT_SECRET,
                  value: 'abcdefghi'
               },
               {
                  name: AttributeConstants.HOSTNAME,
                  value: 'canvas.example.com'
               },
               {
                  name: AttributeConstants.REFRESH_TOKEN,
                  value: 'abcdefghi'
               }
            ],
            channelType: 'registrar',
            displayName: 'Canvas',
            externalId: 'canvas-channel',
            service: AttributeConstants.CANVAS
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(channel),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedChannel;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, channel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, channel.service);
         assert.strictEqual(body.channelType, 'registrar');
         assert.strictEqual(body.displayName, channel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 4);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[0].name);
         assert.strictEqual(attribute.value, channel.attributes[0].value);
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[1].name);
         assert.ok(attribute.value?.includes('******'));
         attribute = body.attributes[2];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[2].name);
         assert.strictEqual(attribute.value, channel.attributes[2].value);
         attribute = body.attributes[3];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[3].name);
         assert.ok(attribute.value?.includes('******'));
      });

      it('POST /channel Sendgrid channel returns status 201', async () => {
         const channel = {
            attributes: [
               {
                  name: AttributeConstants.EMAIL,
                  value: '<EMAIL>'
               },
               {
                  name: AttributeConstants.SENDGRID_KEY,
                  value: 'abcdefghi'
               }
            ],
            channelType: 'default',
            displayName: 'Sendgrid',
            externalId: 'sendgrid-channel',
            provider: AttributeConstants.SENDGRID,
            service: AttributeConstants.EMAIL
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(channel),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedChannel;
         assert.strictEqual(response.status, 201);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, channel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, channel.service);
         assert.strictEqual(body.provider, channel.provider);
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.displayName, channel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 2);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[0].name);
         assert.strictEqual(attribute.value, channel.attributes[0].value);
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[1].name);
         assert.ok(attribute.value?.includes('******'));
      });

      it('POST /channel SMTP channel returns status 201', async () => {
         const channel = {
            attributes: [
               {
                  name: AttributeConstants.SMTP_FROM,
                  value: '<EMAIL>'
               },
               {
                  name: AttributeConstants.SMTP_HOST,
                  value: 'abcdefghi'
               },
               {
                  name: AttributeConstants.SMTP_PASSWORD,
                  value: 'secret'
               },
               {
                  name: AttributeConstants.SMTP_PORT,
                  value: '12345'
               },
               {
                  name: AttributeConstants.SMTP_SECURE,
                  value: 'true'
               },
               {
                  name: AttributeConstants.SMTP_USER,
                  value: '<EMAIL>'
               }
            ],
            channelType: 'staff',
            displayName: 'SMTP',
            externalId: 'smtp-channel',
            provider: AttributeConstants.SMTP,
            service: AttributeConstants.EMAIL
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(channel),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const body = (await response.json()) as CreatedChannel;
         assert.strictEqual(response.status, 201);
         assert.strictEqual(body.externalId, channel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, channel.service);
         assert.strictEqual(body.provider, channel.provider);
         assert.strictEqual(body.channelType, 'staff');
         assert.strictEqual(body.displayName, channel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 6);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[0].name);
         assert.strictEqual(attribute.value, channel.attributes[0].value);
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[1].name);
         assert.strictEqual(attribute.value, channel.attributes[1].value);
         attribute = body.attributes[2];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[2].name);
         assert.ok(attribute.value?.includes('******'));
         attribute = body.attributes[3];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[3].name);
         assert.strictEqual(attribute.value, channel.attributes[3].value);
         attribute = body.attributes[4];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[4].name);
         assert.strictEqual(attribute.value, channel.attributes[4].value);
         attribute = body.attributes[5];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, channel.attributes[5].name);
         assert.strictEqual(attribute.value, channel.attributes[5].value);
      });

      it('POST /channel existing channel external ID returns 409', async () => {
         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify({
               attributes: [],
               channelType: 'default',
               displayName: 'SMS',
               externalId: 'ESENDEX-channel',
               provider: AttributeConstants.ESENDEX,
               service: AttributeConstants.SMS
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 409);
      });

      it('POST /channel with same service and service type returns status 409', async () => {
         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify({
               attributes: [
                  {
                     name: AttributeConstants.PHONE,
                     value: '+15555555555'
                  },
                  {
                     name: AttributeConstants.TWILIO_SID,
                     value: 'abcdefghi'
                  },
                  {
                     name: AttributeConstants.TWILIO_TOKEN,
                     value: 'abcdefghi'
                  }
               ],
               channelType: 'default',
               displayName: 'SMS',
               externalId: 'duplicate-service',
               provider: AttributeConstants.TWILIO,
               service: AttributeConstants.SMS
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 409);
      });

      it('POST /channel external ID cannot be empty returns status 400', async () => {
         const data = {
            attributes: [],
            channelType: 'default',
            description: 'test description',
            displayName: 'SMS',
            enabled: true,
            externalId: '',
            service: 'sms'
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(data),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 400);
      });

      it('POST /channel external ID cannot have white space returns status 400', async () => {
         const data = {
            attributes: [
               {
                  name: 'test attribute',
                  value: 'test value'
               }
            ],
            channelType: 'default',
            description: 'test description',
            displayName: 'SMS',
            enabled: true,
            externalId: 'test id',
            service: 'sms'
         };

         const response = await fetch(`${baseUrl}/v1/channel`, {
            body: JSON.stringify(data),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         assert.strictEqual(response.status, 400);
      });

      it('GET /channel/:id returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}`);
         const body = (await response.json()) as CreatedChannel;

         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, esendexChannel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, esendexChannel.service);
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.displayName, esendexChannel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 2);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, esendexChannel.attributes[0].name);
         assert.ok(attribute.value?.includes('******'));
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, esendexChannel.attributes[1].name);
         assert.strictEqual(attribute.value, esendexChannel.attributes[1].value);
      });

      it('GET /channel/:externalId returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${esendexChannel.externalId}`);
         const body = (await response.json()) as CreatedChannel;

         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, esendexChannel.externalId);
         assert.strictEqual(body.description, '');
         assert.strictEqual(body.service, esendexChannel.service);
         assert.strictEqual(body.channelType, 'default');
         assert.strictEqual(body.displayName, esendexChannel.displayName);
         assert.strictEqual(body.enabled, true);
         assert.ok(body.attributes);
         assert.strictEqual(body.attributes.length, 2);

         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, esendexChannel.attributes[0].name);
         assert.ok(attribute.value?.includes('******'));
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, esendexChannel.attributes[1].name);
         assert.strictEqual(attribute.value, esendexChannel.attributes[1].value);
      });

      /* it('GET /channel/:channelId/attributes/:attributeId returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}/attribute/${attributeId}`);

         const body = await response.json();
         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         const attribute = esendexChannel.attributes[1];
         assert.strictEqual(body.name, attribute.name);
         assert.strictEqual(body.value, attribute.value);
      });

      it('PATCH /channel/:channelId/attributes/:attributeId returns status 200', async () => {
         const data = {
            id: attributeId,
            value: 'updated value3'
         };
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}/attribute/${attributeId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
         });

         const body = await response.json();
         assert.strictEqual(response.status, 200);
         assert.strictEqual(body.id, attributeId);
         assert.strictEqual(body.name, esendexChannel.attributes[1].name);
         assert.strictEqual(body.value, data.value);
      });

           it('DELETE /channel/:channelId/attributes/:attributeId returns status 204', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}/attribute/${attributeId}`, {
            method: 'DELETE'
         });

         assert.strictEqual(response.status, 204);
      }); */

      it('PUT /channel replace channel returns status 200', async () => {
         const data = {
            attributes: [
               {
                  name: AttributeConstants.PHONE,
                  value: '+15555555555'
               },
               {
                  name: AttributeConstants.TWILIO_SID,
                  value: 'abcdefghi'
               },
               {
                  name: AttributeConstants.TWILIO_TOKEN,
                  value: 'abcdefghi'
               }
            ],
            channelType: 'replaced-type',
            description: 'replaced-description',
            displayName: 'SMS',
            enabled: false,
            externalId: 'replaced-channel',
            provider: AttributeConstants.TWILIO,
            service: AttributeConstants.SMS
         };

         const response = await fetch(`${baseUrl}/v1/channel/${lastId}`, {
            body: JSON.stringify(data),
            headers: { 'Content-Type': 'application/json' },
            method: 'PUT'
         });

         const body = (await response.json()) as CreatedChannel;
         assert.strictEqual(response.status, 200);
         assert.ok(body.id);
         assert.strictEqual(body.externalId, data.externalId);
         assert.strictEqual(body.description, data.description);
         assert.strictEqual(body.service, data.service);
         assert.strictEqual(body.displayName, data.displayName);
         assert.strictEqual(body.channelType, data.channelType);
         assert.strictEqual(body.enabled, data.enabled);
         assert.ok(body.attributes);
         body.attributes.sort((a: Attribute, b: Attribute) => a.name.localeCompare(b.name));
         assert.strictEqual(body.attributes.length, 3);
         let attribute = body.attributes[0];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, data.attributes[0].name);
         assert.strictEqual(attribute.value, data.attributes[0].value);
         attribute = body.attributes[1];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, data.attributes[1].name);
         assert.strictEqual(attribute.value, data.attributes[1].value);
         attribute = body.attributes[2];
         assert.ok(attribute.id);
         assert.strictEqual(attribute.name, data.attributes[2].name);
         assert.ok(attribute.value?.includes('******'));
      });

      it('PUT /channel existing channel external ID returns 409', async () => {
         const otherChannel = await prisma.channel.findFirstOrThrow({
            include: { attributes: true },
            where: { externalId: 'other-channel' }
         });
         assert.ok(otherChannel);

         otherChannel.externalId = 'replaced-id';
         const response = await fetch(`${baseUrl}/v1/channel/${otherChannel.id}`, {
            body: JSON.stringify({
               attributes: [
                  {
                     name: AttributeConstants.ESENDEX_KEY,
                     value: 'abcdefghi'
                  },
                  {
                     name: AttributeConstants.PHONE,
                     value: '**************'
                  }
               ],
               channelType: 'default',
               displayName: 'SMS',
               externalId: 'REPLACED-channel',
               provider: AttributeConstants.ESENDEX,
               service: AttributeConstants.SMS
            }),
            headers: { 'Content-Type': 'application/json' },
            method: 'put'
         });

         assert.strictEqual(response.status, 409);
      });

      it('PUT /channel existing service and service type returns 409', async () => {
         const otherChannel = await prisma.channel.findFirstOrThrow({
            include: { attributes: true },
            where: { externalId: 'replaced-channel' }
         });
         assert.ok(otherChannel);
         otherChannel.service = AttributeConstants.SMS;
         otherChannel.provider = AttributeConstants.TWILIO;
         otherChannel.channelType = 'registrar';

         const response = await fetch(`${baseUrl}/v1/channel/${otherChannel.id}`, {
            body: JSON.stringify(otherChannel),
            headers: { 'Content-Type': 'application/json' },
            method: 'put'
         });

         assert.strictEqual(response.status, 409);
      });

      it('DELETE /channel delete channel returns 204', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}`, {
            method: 'DELETE'
         });

         assert.strictEqual(response.status, 204);
      });

      it('PUT /channel replace non-existent channel returns status 404', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}`, {
            body: JSON.stringify(esendexChannel),
            headers: { 'Content-Type': 'application/json' },
            method: 'PUT'
         });
         assert.strictEqual(response.status, 404);
      });

      it('DELETE /channel delete non-existent channel returns 404', async () => {
         const response = await fetch(`${baseUrl}/v1/channel/${lastId}`, {
            method: 'DELETE'
         });

         assert.strictEqual(response.status, 404);
      });
   });
};
