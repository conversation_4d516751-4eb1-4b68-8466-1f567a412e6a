/* eslint-disable @typescript-eslint/no-floating-promises */
// eslint-disable-next-line simple-import-sort/imports
import { describe, it } from 'node:test';

import assert from 'assert';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { CreatedMessage, CreatedMessages } from '../../src/modules/message/message.interface.js';
import { ErrorResponse } from '../../src/modules/shared/error/error.schema.js';

export const MessageTests = async (baseUrl: string, tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('Messages', () => {
      it('POST /message default values returns status 200', async () => {
         const message = {
            message: 'Hello, World!',
            recipients: ['user1'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as CreatedMessage;
         const topic = await prisma.topic.findFirstOrThrow({
            where: {
               externalId: message.topic,
               tenantId
            }
         });

         assert.strictEqual(response.status, 201);
         assert.ok(created.id);
         assert.strictEqual(created.importance, '1');
         assert.deepStrictEqual(created.recipients, message.recipients);
         assert.strictEqual(created.title, message.title);
         assert.strictEqual(created.topic, topic.id);
         assert.strictEqual(created.category, '');
         assert.strictEqual(created.error, '');
         assert.strictEqual(created.isNotificationMessage, false);
         assert.strictEqual(created.sendAt, '');
         assert.strictEqual(created.attributes.length, 0);
         assert.strictEqual(created.context.length, 0);
         assert.ok(created.createdAt);
         assert.ok(created.updatedAt);
      });

      let messageId: string;
      it('POST /message all values returns status 200', async () => {
         const message = {
            attributes: [{ name: 'name', value: 'value' }],
            category: 'category',
            context: [{ name: 'name', value: 'value' }],
            department: 'department',
            importance: '5',
            isNotificationMessage: true,
            message: 'Hello, World!',
            plainText: 'Hello, World!',
            sendAt: '2027-01-01T00:00:00.000Z',
            sender: 'sender',
            tags: ['tag1', 'tag2'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as CreatedMessage;
         assert.strictEqual(response.status, 201);
         assert.ok(created.id);
         messageId = created.id;

         const topic = await prisma.topic.findFirstOrThrow({
            where: {
               externalId: message.topic,
               tenantId
            }
         });

         assert.deepStrictEqual(created.recipients, []);
         assert.strictEqual(created.title, message.title);
         assert.strictEqual(created.topic, topic.id);
         assert.strictEqual(created.category, 'category');
         assert.strictEqual(created.error, '');
         assert.strictEqual(created.isNotificationMessage, true);
         assert.strictEqual(created.sendAt, '2027-01-01T00:00:00.000Z');
         assert.strictEqual(created.attributes.length, 1);
         assert.strictEqual(created.attributes[0].name, 'name');
         assert.strictEqual(created.attributes[0].value, 'value');
         assert.strictEqual(created.context.length, 1);
         assert.strictEqual(created.context[0].name, 'name');
         assert.strictEqual(created.context[0].value, 'value');
         assert.strictEqual(created.importance, '5');
         assert.strictEqual(created.tags.length, 2);
         assert.strictEqual(created.tags[0], 'tag1');
         assert.strictEqual(created.tags[1], 'tag2');
         assert.ok(created.createdAt);
         assert.ok(created.updatedAt);
      });

      it('POST /messages default values returns status 200', async () => {
         const message1 = {
            message: 'Message 1',
            recipients: ['user1'],
            title: 'Test Message 1',
            topic: 'test-topic'
         };

         const message2 = {
            message: 'Message 2',
            recipients: ['user2'],
            title: 'Test Message 2',
            topic: 'test-topic'
         };

         const message3 = {
            recipients: ['user3'],
            title: 'Test Message 3',
            topic: 'test-topic3'
         };

         const messages = {
            messages: [
               { correlationId: '1', message: message1 },
               { correlationId: '2', message: message2 },
               { correlationId: '3', message: message3 }
            ]
         };

         const response = await fetch(`${baseUrl}/v1/messages`, {
            body: JSON.stringify(messages),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as CreatedMessages;
         assert.strictEqual(response.status, 200);
         assert.strictEqual(created.messages.length, 3);
         assert.ok(created.messages[0].correlationId);
         assert.ok('id' in created.messages[0] && created.messages[0].id);
         assert.ok(created.messages[1].correlationId);
         assert.ok('id' in created.messages[1] && created.messages[1].id);
         assert.ok(created.messages[2].correlationId);

         const error = (
            created.messages[2] as {
               correlationId: string;
               error: { code: string; error: string; message: string; statusCode: number };
            }
         ).error;

         assert.ok(error.code);
         assert.ok(error.message);
         assert.ok(error.error);
         assert.ok(error.statusCode);
      });

      it('POST /message without message returns status 400', async () => {
         const message = {
            recipients: ['user1'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(created.error, 'Bad Request');
         assert.strictEqual(created.statusCode, 400);
         assert.strictEqual(created.message, 'Message, short message, or plain text is required.');
      });

      it('POST /message with recipients and tags returns status 400', async () => {
         const message = {
            message: 'Hello, World!',
            recipients: ['user1'],
            tags: ['test'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(created.error, 'Bad Request');
         assert.strictEqual(created.statusCode, 400);
         assert.strictEqual(created.message, 'Only one of recipients, service groups, or tags can be provided');
      });

      it('POST /message without message and isNotificationMessage returns status 400', async () => {
         const message = {
            isNotificationMessage: true,
            plainText: 'Hello, World!',
            recipients: ['user1'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(created.error, 'Bad Request');
         assert.strictEqual(created.statusCode, 400);
         assert.strictEqual(created.message, 'Message is required when notificationMessage is provided');
      });

      it('POST /message without message and isNotificationMessage returns status 400', async () => {
         const message = {
            isNotificationMessage: true,
            message: 'Hello, World!',
            recipients: ['user1'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(created.error, 'Bad Request');
         assert.strictEqual(created.statusCode, 400);
         assert.strictEqual(
            created.message,
            'Short message or plain text is required when notificationMessage is true'
         );
      });

      it('POST /message without message, plainText, shortMessage returns status 400', async () => {
         const message = {
            recipients: ['user1'],
            title: 'Test Message',
            topic: 'test-topic'
         };

         const response = await fetch(`${baseUrl}/v1/message`, {
            body: JSON.stringify(message),
            headers: { 'Content-Type': 'application/json' },
            method: 'POST'
         });

         const created = (await response.json()) as ErrorResponse;
         assert.strictEqual(response.status, 400);
         assert.strictEqual(created.error, 'Bad Request');
         assert.strictEqual(created.statusCode, 400);
         assert.strictEqual(created.message, 'Message, short message, or plain text is required.');
      });

      it('GET /message/:id returns status 200', async () => {
         const response = await fetch(`${baseUrl}/v1/message/${messageId}`, {
            method: 'GET'
         });

         const message = (await response.json()) as CreatedMessage;

         const topic = await prisma.topic.findFirstOrThrow({
            where: {
               externalId: 'test-topic',
               tenantId
            }
         });

         assert.strictEqual(response.status, 200);
         assert.strictEqual(message.id, messageId);

         assert.deepStrictEqual(message.recipients, []);
         assert.strictEqual(message.title, 'Test Message');
         assert.strictEqual(message.topic, topic.id);
         assert.strictEqual(message.category, 'category');
         assert.strictEqual(message.error, '');
         assert.strictEqual(message.isNotificationMessage, true);
         assert.strictEqual(message.sendAt, '2027-01-01T00:00:00.000Z');
         assert.strictEqual(message.attributes.length, 1);
         assert.strictEqual(message.attributes[0].name, 'name');
         assert.strictEqual(message.attributes[0].value, 'value');
         assert.strictEqual(message.context.length, 1);
         assert.strictEqual(message.context[0].name, 'name');
         assert.strictEqual(message.context[0].value, 'value');
         assert.strictEqual(message.importance, '5');
         assert.ok(message.createdAt);
         assert.ok(message.updatedAt);
      });
   });
};
