/* eslint-disable @typescript-eslint/no-floating-promises */
import { it } from 'node:test';

import assert from 'assert';

import { HeartbeatBody } from '../../src/modules/heartbeat/heartbeat.schema.js';

export const heartbeatTests = (baseUrl: string) => {
   it('GET /heartbeat returns status 200', async () => {
      const response = await fetch(`${baseUrl}/v1/heartbeat`);

      assert.strictEqual(response.status, 200);
      assert.strictEqual(((await response.json()) as HeartbeatBody).status, 'ok');
   });
};
