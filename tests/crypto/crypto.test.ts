/* eslint-disable @typescript-eslint/no-floating-promises */
import { describe, it, suite } from 'node:test';

import assert from 'assert';
import { v7 as uuid } from 'uuid';

import { SecretVaultFactory } from '../../src/secrets/index.js';
import { CryptoService } from '../../src/services/crypto.js';

// Mock data setup
const TENANT_ID = 'test-tenant';
const KEY_ID = 'test-key-id';
const PLAIN_TEXT = 'Hello, this is a secret message to encrypt!';

process.env.SECRET_VAULT_TYPE = 'LOCAL';

const vault = SecretVaultFactory.getSecretVault(false);
vault.setSecret(`${TENANT_ID}-${KEY_ID}-encryption-key`, uuid());

suite('Crypto Utilities', () => {
   const cryptoService = CryptoService.getInstance();
   describe('Encryption', () => {

      it('should encrypt plain text correctly', async () => {
         const encryptedData = await cryptoService.encrypt(TENANT_ID, KEY_ID, PLAIN_TEXT);
         const parts = encryptedData.split('.');
         assert.strictEqual(parts.length, 4, 'Encrypted data should be in 4 parts');
      });

      it('should produce different cipher texts for the same plaintext (due to random IV)', async () => {
         const encryptedData1 = await cryptoService.encrypt(TENANT_ID, KEY_ID, PLAIN_TEXT);
         const encryptedData2 = await cryptoService.encrypt(TENANT_ID, KEY_ID, PLAIN_TEXT);

         assert.notStrictEqual(
            encryptedData1,
            encryptedData2,
            'Multiple encryptions of the same plaintext should produce different cipher texts'
         );
      });
   });

   describe('Decryption', () => {
      it('should decrypt encrypted text back to the original plaintext', async () => {
         const encryptedData = await cryptoService.encrypt(TENANT_ID, KEY_ID, PLAIN_TEXT);
         const decryptedText = await cryptoService.decrypt(TENANT_ID, encryptedData);
         assert.strictEqual(decryptedText, PLAIN_TEXT, 'Decrypted text should match original plaintext');
      });

      it('should throw an error when decrypting invalid cipher text', async () => {
         const invalidCipherText = 'invalid.jwe.format.data.here';

         await assert.rejects(
            async () => {
               await cryptoService.decrypt(TENANT_ID, invalidCipherText);
            },
            'Decryption of invalid cipher text should throw an error'
         );
      });
   });

   describe('End-to-End Encryption and Decryption', () => {
      it('should correctly encrypt and decrypt various data types', async () => {
         // Test with various data types
         const testCases = [
            'Simple string',
            'Special chars: !@#$%^&*()',
            JSON.stringify({ data: { nested: true }, id: 123, user: 'test', utf: '😄' }),
            'Merhaba Dünya! İşte bazı Türkçe karakterler: ğ, ı, ş, ö, ü, ç, İ, Ğ, Ş, Ö, Ü, Ç.',
            '你好，世界',
            ' ' // Space character
         ];

         for (const testCase of testCases) {
            const encrypted = await cryptoService.encrypt(TENANT_ID, KEY_ID, testCase);
            const decrypted = await cryptoService.decrypt(TENANT_ID,    encrypted);

            assert.strictEqual(decrypted, testCase, `End-to-end encryption/decryption should work for: ${testCase}`);
         }
      });

      it('should handle large text content', async () => {
         // Generate a large string (50KB)
         const largeText = 'A'.repeat(50 * 1024);

         const encrypted = await cryptoService.encrypt(TENANT_ID, KEY_ID, largeText);
         const decrypted = await cryptoService.decrypt(TENANT_ID, encrypted);

         assert.strictEqual(decrypted, largeText, 'Should correctly handle large text content');
      });
   });
});
