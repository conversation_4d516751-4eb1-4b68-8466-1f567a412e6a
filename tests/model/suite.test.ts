/* eslint-disable @typescript-eslint/no-floating-promises */

import { before, suite } from 'node:test';

import { v7 as uuid7 } from 'uuid';

import { SecretVaultFactory } from '../../src/secrets';
import { getPrismaClient } from '../../src/utils/prisma';

import { channel } from './channel';
import { message } from './message';
import { recipient } from './recipient';
import { relations } from './relations';
import { tag } from './tag';
import { template } from './template';
import { tenant } from './tenant';
import { topic } from './topic';

// We want to also test that we can use multiple tenants
// on different databases at the same time
const firstTenant = 'xsignal-first';
const secondTenant = 'xsignal-second';

const vault = SecretVaultFactory.getSecretVault(true);
vault.setSecret(`${firstTenant}-db-url`, process.env.DATABASE_URL_1 ?? '');
vault.setSecret(`${secondTenant}-db-url`, process.env.DATABASE_URL_2 ?? '');

const seedDatabase = async () => {
   const prismaFirst = await getPrismaClient(firstTenant);
   const prismaSecond = await getPrismaClient(secondTenant);
   prismaFirst.tenant.delete({ where: { id: firstTenant } });
   prismaSecond.tenant.delete({ where: { id: secondTenant } });
};

suite('models', async () => {
   before(async () => {
      await seedDatabase();
   });

   const channelId1 = uuid7();
   const channelId2 = uuid7();
   const recipientId1 = uuid7();
   const recipientId2 = uuid7();
   const tagId1 = uuid7();
   const tagId2 = uuid7();
   const topicId1 = uuid7();
   const topicId2 = uuid7();

   await tenant(firstTenant);
   await tenant(secondTenant);

   await channel(firstTenant, channelId1, channelId2);
   await channel(secondTenant, channelId1, channelId2);

   await topic(firstTenant, channelId1, channelId2, topicId1, topicId2);
   await topic(secondTenant, channelId1, channelId2, topicId1, topicId2);

   await tag(firstTenant, tagId1, tagId2);
   await tag(secondTenant, tagId1, tagId2);

   await recipient(firstTenant, recipientId1, recipientId2, tagId1, tagId2, topicId1);
   await recipient(secondTenant, recipientId1, recipientId2, tagId1, tagId2, topicId1);

   await template(firstTenant);
   await template(secondTenant);

   await message(firstTenant, topicId1);
   await message(firstTenant, topicId1);

   await relations(firstTenant);
});
