/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';

import { getPrismaClient } from '../../src/utils/prisma';

export const tenant = async (tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('tenant', () => {
      const name = tenantId;

      it('should create a new tenant', async () => {
         const data = {
            id: tenantId,
            name
         };

         const createdTenant = await prisma.tenant.create({
            data
         });

         assert.strictEqual(createdTenant.id, tenantId);
         assert.strictEqual(createdTenant.name, name);
         assert.strictEqual(createdTenant.enabled, true);
         assert.strictEqual(createdTenant.language, 'en');
         assert.strictEqual(createdTenant.storeMessageForDays, 30);
         assert.strictEqual(createdTenant.archive, false);
         assert(createdTenant.encryptionKeyId);
         assert(createdTenant.createdAt);
         assert.ok(createdTenant.createdAt instanceof Date);
         assert(createdTenant.updatedAt);
         assert.ok(createdTenant.updatedAt instanceof Date);
      });

      it('should update a tenant', async () => {
         const data = {
            archive: true,
            enabled: false,
            language: 'fr',
            storeMessageForDays: 14
         };

         const updatedTenant = await prisma.tenant.update({
            data,
            where: { id: tenantId }
         });

         assert.strictEqual(updatedTenant.id, tenantId);
         assert.strictEqual(updatedTenant.name, name);
         assert.strictEqual(updatedTenant.enabled, false);
         assert.strictEqual(updatedTenant.language, 'fr');
         assert.strictEqual(updatedTenant.storeMessageForDays, 14);
         assert.strictEqual(updatedTenant.archive, true);
         assert(updatedTenant.encryptionKeyId);
         assert(updatedTenant.createdAt);
         assert.ok(updatedTenant.createdAt instanceof Date);
         assert(updatedTenant.updatedAt);
         assert.ok(updatedTenant.updatedAt instanceof Date);
      });
   });
};
