/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { v7 as uuid } from 'uuid';

import { getPrismaClient } from '../../src/utils/prisma';

export const tag = async (tenantId: string, tagId1: string, tagId2: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('tag', () => {
      const createData = {
         displayName: 'First Year',
         externalId: 'first-year',
         id: tagId1,
         tenantId
      };

      it('should create a new tag', async () => {
         const createdTag = await prisma.tag.create({
            data: createData,
            include: { recipients: true }
         });

         assert.strictEqual(createdTag.tenantId, tenantId);
         assert.strictEqual(createdTag.id, createData.id);
         assert.strictEqual(createdTag.externalId, createData.externalId);
         assert.strictEqual(createdTag.displayName, createData.displayName);
         assert.strictEqual(createdTag.description, '');
         assert.strictEqual(createdTag.enabled, true);
         assert.strictEqual(createdTag.selectable, false);
         assert.ok(createdTag.recipients instanceof Array);
         assert.strictEqual(createdTag.recipients.length, 0);
         assert(createdTag.createdAt);
         assert.ok(createdTag.createdAt instanceof Date);
         assert(createdTag.updatedAt);
         assert.ok(createdTag.updatedAt instanceof Date);
      });

      it('should create a another tag', async () => {
         const otherData = {
            description: 'Second Year',
            displayName: 'Second Year',
            enabled: false,
            externalId: 'second-year',
            id: tagId2,
            selectable: true,
            tenantId
         };
         const createdTag = await prisma.tag.create({
            data: otherData,
            include: { recipients: true }
         });

         assert.strictEqual(createdTag.tenantId, tenantId);
         assert.strictEqual(createdTag.id, otherData.id);
         assert.strictEqual(createdTag.externalId, otherData.externalId);
         assert.strictEqual(createdTag.displayName, otherData.displayName);
         assert.strictEqual(createdTag.description, otherData.description);
         assert.strictEqual(createdTag.enabled, otherData.enabled);
         assert.strictEqual(createdTag.selectable, otherData.selectable);
         assert.ok(createdTag.recipients instanceof Array);
         assert.strictEqual(createdTag.recipients.length, 0);
         assert(createdTag.createdAt);
         assert.ok(createdTag.createdAt instanceof Date);
         assert(createdTag.updatedAt);
         assert.ok(createdTag.updatedAt instanceof Date);
      });

      it('should update a tag', async () => {
         const updateData = {
            description: 'Third Year',
            displayName: 'Third Year',
            enabled: false,
            externalId: 'third-year',
            id: tagId1,
            selectable: true
         };

         const updatedTag = await prisma.tag.update({
            data: updateData,
            include: { recipients: true },
            where: { id: tagId1 }
         });

         assert.strictEqual(updatedTag.tenantId, tenantId);
         assert.strictEqual(updatedTag.id, updateData.id);
         assert.strictEqual(updatedTag.externalId, updateData.externalId);
         assert.strictEqual(updatedTag.displayName, updateData.displayName);
         assert.strictEqual(updatedTag.description, updateData.description);
         assert.strictEqual(updatedTag.enabled, updateData.enabled);
         assert.strictEqual(updatedTag.selectable, updateData.selectable);
         assert.ok(updatedTag.recipients instanceof Array);
         assert.strictEqual(updatedTag.recipients.length, 0);
         assert(updatedTag.createdAt);
         assert.ok(updatedTag.createdAt instanceof Date);
         assert(updatedTag.updatedAt);
         assert.ok(updatedTag.updatedAt instanceof Date);
      });

      it('can create two tags without an external id (test unique constraint)', async () => {
         const uniqueTag1 = await prisma.tag.create({
            data: {
               displayName: 'unique-tag-1',
               id: uuid(),
               tenantId
            }
         });

         const uniqueTag2 = await prisma.tag.create({
            data: {
               displayName: 'unique-tag-2',
               id: uuid(),
               tenantId
            }
         });
         assert.ok(uniqueTag1);
         assert.ok(uniqueTag2);
      });
   });
};
