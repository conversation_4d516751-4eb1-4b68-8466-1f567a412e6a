/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { Prisma } from '@prisma/client';

import { getPrismaClient } from '../../src/utils/prisma';

export const message = async (tenantId: string, topicId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('message', () => {
      const createData = Prisma.validator<Prisma.MessageCreateInput>()({
         attributes: {
            create: [
               {
                  name: 'test',
                  value: 'test'
               },
               {
                  name: 'test2',
                  value: 'test2'
               }
            ]
         },
         context: {
            create: [
               {
                  name: 'test',
                  value: 'test'
               },
               {
                  name: 'test2',
                  value: 'test2'
               }
            ]
         },
         message: 'Hello World',
         recipients: ['1234567890'],
         tenant: {
            connect: {
               id: tenantId
            }
         },
         topic: topicId
      });

      it('should create a new message', async () => {
         const createdMessage = await prisma.message.create({
            data: createData,
            include: {
               attributes: true,
               context: true
            }
         });

         assert.ok(createdMessage.id);
         assert.strictEqual(createdMessage.tenantId, tenantId);
         assert.strictEqual(createdMessage.recipients.length, 1);
         assert.strictEqual(createdMessage.recipients[0], createData.recipients[0]);
         assert.ok(createdMessage.tags instanceof Array);
         assert.strictEqual(createdMessage.tags.length, 0);
         assert.strictEqual(createdMessage.topic, createData.topic);

         assert.strictEqual(createdMessage.message, createData.message);
         assert.strictEqual(createdMessage.title, '');
         assert.strictEqual(createdMessage.shortMessage, '');
         assert.strictEqual(createdMessage.plainText, '');
         assert.strictEqual(createdMessage.importance, '1');
         assert.strictEqual(createdMessage.isNotificationMessage, false);
         assert.strictEqual(createdMessage.conversationId, '');
         assert.strictEqual(createdMessage.sendAt, null);
         assert.strictEqual(createdMessage.status, 'PROCESSING');

         assert.strictEqual(createdMessage.attributes.length, 2);
         createdMessage.attributes.sort((a, b) => a.name.localeCompare(b.name));
         assert.strictEqual(createdMessage.attributes[0].name, 'test');
         assert.strictEqual(createdMessage.attributes[0].value, 'test');
         assert.strictEqual(createdMessage.attributes[1].name, 'test2');
         assert.strictEqual(createdMessage.attributes[1].value, 'test2');

         assert.strictEqual(createdMessage.context.length, 2);
         createdMessage.context.sort((a, b) => a.name.localeCompare(b.name));
         assert.strictEqual(createdMessage.context[0].name, 'test');
         assert.strictEqual(createdMessage.context[0].value, 'test');
         assert.strictEqual(createdMessage.context[1].name, 'test2');
         assert.strictEqual(createdMessage.context[1].value, 'test2');

         assert(createdMessage.createdAt);
         assert.ok(createdMessage.createdAt instanceof Date);
         assert(createdMessage.updatedAt);
         assert.ok(createdMessage.updatedAt instanceof Date);
      });
   });
};
