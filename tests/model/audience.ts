
/* import { describe, it } from 'node:test';

import assert from 'assert';
import { Prisma } from '@prisma/client';

import { getPrismaClient } from '../../src/utils/prisma';

export const audience = async (tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('audience', () => {
      it('should create a new audience', async () => {
         const data = {
            description: 'Work',
            name: 'work',
            tenantId
         };

         const createdAudience = await prisma.audience.create({
            data
         });

         assert.strictEqual(createdAudience.tenantId, tenantId);
         assert.strictEqual(createdAudience.name, data.name);
         assert.strictEqual(createdAudience.description, data.description);
      });

      it('should update a audience', async () => {
         const data = {
            description: 'Workforce'
         };

         const updatedAudience = await prisma.audience.update({
            data,
            where: { unique_audience_name: { name: 'work', tenantId } }
         });

         assert.strictEqual(updatedAudience.tenantId, tenantId);
         assert.strictEqual(updatedAudience.name, 'work');
         assert.strictEqual(updatedAudience.description, data.description);
      });

      it('can create another audience', async () => {
         const data = {
            description: 'Guardian',
            name: 'guardian',
            tenantId
         };

         const createdAudience = await prisma.audience.create({
            data
         });

         assert.strictEqual(createdAudience.tenantId, tenantId);
         assert.strictEqual(createdAudience.name, data.name);
         assert.strictEqual(createdAudience.description, data.description);
      });

      it('can get all audiences', async () => {
         const audiences = await prisma.audience.findMany();

         assert.strictEqual(audiences.length, 2);
      });

      it('cannot create a duplicate audience', async () => {
         const data = {
            description: 'Work',
            name: 'work',
            tenantId
         }; // duplicate

         try {
            await prisma.audience.create({
               data
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot update a duplicate audience name', async () => {
         const data = {
            name: 'guardian'
         };

         try {
            await prisma.audience.update({
               data,
               where: { unique_audience_name: { name: 'work', tenantId } }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('can delete an audience', async () => {
         await prisma.audience.delete({
            where: { unique_audience_name: { name: 'work', tenantId } }
         });

         const found = await prisma.audience.findUnique({
            where: { unique_audience_name: { name: 'work', tenantId } }
         });

         assert.strictEqual(found, null);
      });
   });
};
*/
