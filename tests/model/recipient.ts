/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { Prisma } from '@prisma/client';

import { v7 as uuid } from 'uuid';

import { getPrismaClient } from '../../src/utils/prisma';

export const recipient = async (
   tenantId: string,
   recipientId1: string,
   recipientId2: string,
   tagId1: string,
   tagId2: string,
   topicId1: string
) => {
   const prisma = await getPrismaClient(tenantId);

   describe('recipient', () => {
      const createData = {
         attributes: {
            create: [
               {
                  name: 'attr1',
                  value: 'test'
               },
               {
                  name: 'attr2',
                  value: 'test'
               }
            ]
         },
         id: recipientId1,
         identifiers: {
            create: [
               {
                  type: 'phone',
                  value: '1234567890'
               }
            ]
         },
         tags: {
            connect: [{ id: tagId1 }]
         },
         tenantId
      };

      it('should create a new recipient', async () => {
         const createdRecipient = await prisma.recipient.create({
            data: createData,
            include: { attributes: true, identifiers: true, tags: true }
         });

         assert.strictEqual(createdRecipient.tenantId, tenantId);
         assert.strictEqual(createdRecipient.id, createData.id);
         assert.strictEqual(createdRecipient.timezone, 'Etc/UTC');
         assert.strictEqual(createdRecipient.locale, 'en-US');
         assert.ok(createdRecipient.identifiers instanceof Array);
         assert.strictEqual(createdRecipient.identifiers.length, 1);
         assert.strictEqual(createdRecipient.identifiers[0].type, 'phone');
         assert.strictEqual(createdRecipient.identifiers[0].value, '1234567890');
         assert.strictEqual(createdRecipient.tags.length, 1);
         assert.strictEqual(createdRecipient.tags[0].id, tagId1);
         assert(createdRecipient.createdAt);
         assert.ok(createdRecipient.createdAt instanceof Date);
         assert(createdRecipient.updatedAt);
         assert.ok(createdRecipient.updatedAt instanceof Date);
         assert.strictEqual(createdRecipient.attributes.length, 2);
         createdRecipient.attributes.sort((a, b) => a.name.localeCompare(b.name));
         assert.strictEqual(createdRecipient.attributes[0].name, 'attr1');
         assert.strictEqual(createdRecipient.attributes[0].value, 'test');
         assert.strictEqual(createdRecipient.attributes[1].name, 'attr2');
         assert.strictEqual(createdRecipient.attributes[1].value, 'test');
      });

      it('should create a another recipient', async () => {
         const otherData = {
            id: recipientId2,
            identifiers: {
               create: [
                  {
                     type: 'emplid',
                     value: '1234567890'
                  },
                  {
                     type: 'oprid',
                     value: '0987654321'
                  }
               ]
            },
            locale: 'fr-FR',
            tags: {
               connect: [{ id: tagId1 }, { id: tagId2 }]
            },
            tenantId,
            timezone: 'America/New_York'
         };

         const createdRecipient = await prisma.recipient.create({
            data: otherData,
            include: { identifiers: true, tags: true }
         });

         assert.strictEqual(createdRecipient.tenantId, tenantId);
         assert.strictEqual(createdRecipient.id, otherData.id);
         assert.strictEqual(createdRecipient.timezone, otherData.timezone);
         assert.strictEqual(createdRecipient.locale, otherData.locale);
         assert.ok(createdRecipient.identifiers instanceof Array);
         assert.strictEqual(createdRecipient.identifiers.length, 2);
         assert.strictEqual(createdRecipient.tags.length, 2);
         assert(createdRecipient.createdAt);
         assert.ok(createdRecipient.createdAt instanceof Date);
         assert(createdRecipient.updatedAt);
         assert.ok(createdRecipient.updatedAt instanceof Date);
      });

      it('should update a recipient', async () => {
         const updateData = {
            id: recipientId1,
            identifiers: {
               create: [
                  {
                     type: 'emplid',
                     value: '1234567890'
                  }
               ]
            },
            locale: 'fr-FR',
            tags: {
               connect: [{ id: tagId2 }]
            },
            tenantId,
            timezone: 'America/New_York'
         };

         const updatedRecipient = await prisma.recipient.update({
            data: updateData,
            include: { identifiers: true, tags: true },
            where: { id: recipientId1 }
         });

         assert.strictEqual(updatedRecipient.tenantId, tenantId);
         assert.strictEqual(updatedRecipient.id, updateData.id);
         assert.strictEqual(updatedRecipient.timezone, updateData.timezone);
         assert.strictEqual(updatedRecipient.locale, updateData.locale);
         assert.ok(updatedRecipient.identifiers instanceof Array);
         assert.strictEqual(updatedRecipient.identifiers.length, 2);
         assert.strictEqual(updatedRecipient.tags.length, 2);
         assert(updatedRecipient.createdAt);
         assert.ok(updatedRecipient.createdAt instanceof Date);
         assert(updatedRecipient.updatedAt);
         assert.ok(updatedRecipient.updatedAt instanceof Date);
      });

      it('can remove tags, add a tag, and then select recipient', async () => {
         const updateData = {
            identifiers: {
               deleteMany: {}
            },
            tags: {
               connect: [{ id: tagId2 }],
               disconnect: [{ id: tagId1 }]
            }
         };

         await prisma.recipient.update({
            data: updateData,
            where: { id: recipientId1 }
         });

         const found = await prisma.recipient.findUnique({
            include: { identifiers: true, tags: true },
            where: { id: recipientId1 }
         });
         assert.ok(found);
         assert.strictEqual(found.tenantId, tenantId);
         assert.strictEqual(found.id, recipientId1);
         assert.strictEqual(found.timezone, 'America/New_York');
         assert.strictEqual(found.locale, 'fr-FR');
         assert.ok(found.identifiers instanceof Array);
         assert.strictEqual(found.identifiers.length, 0);
         assert.strictEqual(found.tags.length, 1);
         assert(found.createdAt);
         assert.ok(found.createdAt instanceof Date);
         assert(found.updatedAt);
         assert.ok(found.updatedAt instanceof Date);
      });

      it('tag should contain recipient', async () => {
         const tag = await prisma.tag.findUnique({
            include: { recipients: true },
            where: { id: tagId2 }
         });
         const found = tag?.recipients.find((r) => r.id === recipientId1);
         assert.ok(found);
      });

      it('can get recipients by tag', async () => {
         const tag = await prisma.tag.findUnique({
            include: { recipients: true },
            where: { id: tagId1 }
         });
         assert.ok(tag);
         assert.ok(tag.recipients instanceof Array);
         assert.strictEqual(tag.recipients.length, 1);

         const updatedTag = await prisma.tag.update({
            data: {
               recipients: {
                  connect: [{ id: recipientId1 }, { id: recipientId2 }]
               }
            },
            include: { recipients: { select: { id: true } } },
            where: { id: tagId1 }
         });
         assert.ok(updatedTag.recipients instanceof Array);
         assert.strictEqual(updatedTag.recipients.length, 2);

         const updatedTag2 = await prisma.tag.update({
            data: {
               recipients: {
                  disconnect: [{ id: recipientId2 }]
               }
            },
            include: { recipients: { select: { id: true } } },
            where: { id: tagId1 }
         });
         assert.ok(updatedTag2.recipients instanceof Array);
         assert.strictEqual(updatedTag2.recipients.length, 1);
      });

      it('can add connection to recipient', async () => {
         const updatedRecipient = await prisma.recipient.update({
            data: {
               connections: {
                  create: {
                     enabled: true,
                     service: 'test'
                  }
               }
            },
            include: { connections: true },
            where: { id: recipientId1 }
         });
         assert.strictEqual(updatedRecipient.connections.length, 1);
         assert.strictEqual(updatedRecipient.connections[0].service, 'test');
         assert.strictEqual(updatedRecipient.connections[0].enabled, true);
         assert.strictEqual(updatedRecipient.connections[0].showInPreferences, true);
         assert.ok(!updatedRecipient.connections[0].channelId);
      });

      it('can add another connection to recipient', async () => {
         const updatedRecipient = await prisma.recipient.update({
            data: {
               connections: {
                  create: {
                     enabled: false,
                     service: 'sms',
                     showInPreferences: false
                  }
               }
            },
            include: { connections: true },
            where: { id: recipientId1 }
         });
         assert.strictEqual(updatedRecipient.connections.length, 2);
         const testConnection = updatedRecipient.connections.find((c) => c.service === 'test');
         assert.ok(testConnection);
         assert.strictEqual(testConnection.enabled, true);
         assert.strictEqual(testConnection.showInPreferences, true);
         assert.ok(!testConnection.channelId);

         const smsConnection = updatedRecipient.connections.find((c) => c.service === 'sms');
         assert.ok(smsConnection);
         assert.strictEqual(smsConnection.enabled, false);
         assert.strictEqual(smsConnection.showInPreferences, false);
         assert.ok(!smsConnection.channelId);
      });

      it('adding another test connection without a channelId throws an exception', async () => {
         try {
            await prisma.recipient.update({
               data: {
                  connections: {
                     create: {
                        enabled: true,
                        service: 'test'
                     }
                  }
               },
               include: { connections: true },
               where: { id: recipientId1 }
            });
            assert.fail('Expected an error to be thrown');
         } catch (e) {
            assert.ok(e instanceof Error);
         }
      });

      it('can delete connections', async () => {
         const updatedRecipient = await prisma.recipient.update({
            data: {
               connections: {
                  deleteMany: [
                     {
                        service: 'test'
                     }
                  ]
               }
            },
            include: { connections: true },
            where: { id: recipientId1 }
         });
         assert.strictEqual(updatedRecipient.connections.length, 1);
      });

      it('add topic preferences to recipient', async () => {
         const connection1 = await prisma.connection.create({
            data: {
               enabled: true,
               id: uuid(),
               recipientId: recipientId1,
               service: 'test'
            }
         });

         const connection2 = await prisma.connection.create({
            data: {
               enabled: true,
               id: uuid(),
               recipientId: recipientId1,
               service: 'msteams'
            }
         });

         await prisma.topicPreference.create({
            data: {
               connections: {
                  connect: [
                     {
                        id: connection1.id
                     },
                     {
                        id: connection2.id
                     }
                  ]
               },
               id: uuid(),
               optedIn: true,
               recipientId: recipientId1,
               topicId: topicId1
            }
         });

         const found = await prisma.recipient.findUniqueOrThrow({
            include: { preferences: { include: { connections: { select: { id: true } } } } },
            where: { id: recipientId1 }
         });

         assert.strictEqual(found.preferences.length, 1);
         assert.strictEqual(found.preferences[0].connections.length, 2);
         assert.strictEqual(found.preferences[0].optedIn, true);
      });

      it('can remove connection from preference', async () => {
         const found = await prisma.recipient.findUnique({
            include: { preferences: { include: { connections: { select: { id: true } } } } },
            where: { id: recipientId1 }
         });

         const preferences = found?.preferences.sort((a, b) => a.id.localeCompare(b.id));
         assert.ok(preferences);
         const preference = preferences[0];
         assert.ok(preference);

         const connections = preference.connections;
         connections.sort((a, b) => a.id.localeCompare(b.id));

         const updatedRecipient = await prisma.recipient.update({
            data: {
               preferences: {
                  update: [
                     {
                        data: {
                           connections: {
                              disconnect: [
                                 {
                                    id: connections[0].id
                                 }
                              ]
                           }
                        },
                        where: {
                           id: preference.id
                        }
                     }
                  ]
               }
            },
            include: { preferences: { include: { connections: { select: { id: true } } } } },
            where: { id: recipientId1 }
         });

         assert.strictEqual(updatedRecipient.preferences.length, 1);
         assert.strictEqual(updatedRecipient.preferences[0].connections.length, 1);
         assert.strictEqual(updatedRecipient.preferences[0].connections[0].id, connections[1].id);
      });

      it('cannot create a recipient with duplicate attribute names', async () => {
         const otherData = {
            attributes: {
               create: [
                  {
                     name: 'attr1',
                     value: 'test'
                  },
                  {
                     // duplicate
                     name: 'attr1',
                     value: 'test'
                  }
               ]
            },
            id: uuid(),
            identifiers: {
               create: [
                  {
                     type: 'attr',
                     value: '1234567890'
                  }
               ]
            },
            locale: 'fr-FR',
            tenantId,
            timezone: 'America/New_York'
         };

         try {
            await prisma.recipient.create({
               data: otherData,
               include: { attributes: true, identifiers: true }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot update a recipient with duplicate attribute names', async () => {
         const data = {
            name: 'attr1',
            recipientId: recipientId1, // duplicate
            value: 'test2'
         };

         try {
            await prisma.recipientAttribute.create({
               data
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot add a connection attribute with a duplicate name', async () => {
         const found = await prisma.recipient.findUnique({
            include: { connections: { include: { attributes: true } } },
            where: { id: recipientId1 }
         });

         const connection = found?.connections.find((c) => c.service === 'test');
         assert.ok(connection);

         await prisma.connectionAttribute.create({
            data: {
               connectionId: connection.id,
               id: uuid(),
               name: 'attr1',
               value: 'test'
            }
         });

         await prisma.connectionAttribute.create({
            data: {
               connectionId: connection.id,
               id: uuid(),
               name: 'attr2',
               value: 'test'
            }
         });

         try {
            await prisma.connectionAttribute.create({
               data: {
                  connectionId: connection.id,
                  id: uuid(),
                  name: 'attr1',
                  value: 'test'
               }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot add connection to recipient with same service and audience', async () => {
         try {
            await prisma.recipient.update({
               data: {
                  connections: {
                     create: {
                        enabled: true,
                        service: 'test'
                     }
                  }
               },
               include: { connections: true },
               where: { id: recipientId1 }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });
   });
};
