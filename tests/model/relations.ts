/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';
import { v7 as uuid } from 'uuid';

import { getPrismaClient } from '../../src/utils/prisma';

export const relations = async (tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('relations', () => {
      const channelId1 = uuid();
      const channelId2 = uuid();
      const topicId1 = uuid();

      it('can delete topic when used in recipient preference', async () => {
         const channelData = {
            channelType: 'default',
            displayName: 'channel 1',
            externalId: 'channel-1',
            id: channelId1,
            service: 'test',
            tenantId
         };

         const channel = await prisma.channel.create({
            data: channelData
         });

         const topicData = {
            channelType: 'default',
            defaultCategory: 'test',
            defaultService: channel.service,
            displayName: 'topic 1',
            externalId: 'topic-1',
            id: topicId1,
            tenantId
         };

         const topic = await prisma.topic.create({
            data: topicData
         });

         const connection1Id = uuid();
         const connection2Id = uuid();

         await prisma.recipient.create({
            data: {
               connections: {
                  create: [
                     {
                        enabled: true,
                        id: connection1Id,
                        service: 'test-1'
                     },
                     {
                        enabled: true,
                        id: connection2Id,
                        service: 'test-2'
                     }
                  ]
               },
               id: uuid(),
               preferences: {
                  create: [
                     {
                        connections: {
                           connect: [
                              {
                                 id: connection1Id
                              },
                              {
                                 id: connection2Id
                              }
                           ]
                        },
                        optedIn: true,
                        topicId: topic.id
                     }
                  ]
               },
               tenantId
            },
            include: {
               connections: true,
               preferences: true
            }
         });

         let topicPreference = await prisma.topicPreference.findMany({
            where: {
               topicId: topic.id
            }
         });

         assert.strictEqual(topicPreference.length, 1);

         await prisma.topic.delete({
            where: {
               id: topicId1
            }
         });

         topicPreference = await prisma.topicPreference.findMany({
            where: {
               topicId: topic.id
            }
         });

         assert.strictEqual(topicPreference.length, 0);
      });

      it('can delete channel when used in recipient preference', async () => {
         const channel2Data = {
            channelType: 'default',
            displayName: 'channel 2',
            externalId: 'channel-2',
            id: channelId2,
            service: 'test',
            tenantId
         };

         const channel2 = await prisma.channel.create({
            data: channel2Data
         });

         const topicData = {
            channelType: 'default',
            defaultCategory: 'test',
            defaultService: 'test',
            displayName: 'topic 1',
            externalId: 'topic-1',
            id: uuid(),
            tenantId
         };

         const topic = await prisma.topic.create({
            data: topicData
         });

         const topicData2 = {
            channelType: 'default',
            defaultCategory: 'test',
            defaultService: channel2.service,
            displayName: 'topic 2',
            externalId: 'topic-2',
            id: uuid(),
            tenantId
         };

         const topic2 = await prisma.topic.create({
            data: topicData2
         });

         const connection1Id = uuid();
         const connection2Id = uuid();
         const connection3Id = uuid();
         const connection4Id = uuid();

         await prisma.recipient.create({
            data: {
               connections: {
                  create: [
                     {
                        enabled: true,
                        id: connection1Id,
                        service: 'test-1'
                     },
                     {
                        enabled: true,
                        id: connection2Id,
                        service: 'test-2'
                     }
                  ]
               },
               id: uuid(),
               preferences: {
                  create: [
                     {
                        connections: {
                           connect: [
                              {
                                 id: connection1Id
                              }
                           ]
                        },
                        optedIn: true,
                        topicId: topic.id
                     },
                     {
                        connections: {
                           connect: [
                              {
                                 id: connection2Id
                              }
                           ]
                        },
                        optedIn: true,
                        topicId: topic2.id
                     }
                  ]
               },
               tenantId
            },
            include: {
               connections: true,
               preferences: true
            }
         });

         await prisma.recipient.create({
            data: {
               connections: {
                  create: [
                     {
                        enabled: true,
                        id: connection3Id,
                        service: 'test-1'
                     },
                     {
                        enabled: true,
                        id: connection4Id,
                        service: 'test-2'
                     }
                  ]
               },
               id: uuid(),
               preferences: {
                  create: [
                     {
                        connections: {
                           connect: [
                              {
                                 id: connection3Id
                              },
                              {
                                 id: connection4Id
                              }
                           ]
                        },
                        optedIn: true,
                        topicId: topic.id
                     }
                  ]
               },
               tenantId
            },
            include: {
               connections: true,
               preferences: true
            }
         });

         let topicPreference = await prisma.topicPreference.findMany({
            include: {
               connections: true
            },
            where: {
               topicId: topic.id
            }
         });

         assert.strictEqual(topicPreference.length, 2);
         topicPreference[0].connections.sort((a, b) => a.id.localeCompare(b.id));
         assert.strictEqual(topicPreference[0].connections.length, 1);
         assert.strictEqual(topicPreference[1].connections.length, 2);

         await prisma.connection.delete({
            where: {
               id: connection1Id
            }
         });

         topicPreference = await prisma.topicPreference.findMany({
            include: {
               connections: true
            },
            where: {
               topicId: topic.id
            }
         });

         topicPreference[0].connections.sort((a, b) => a.id.localeCompare(b.id));
         assert.strictEqual(topicPreference[0].connections.length, 0);
         assert.strictEqual(topicPreference[1].connections.length, 2);
      });
   });
};
