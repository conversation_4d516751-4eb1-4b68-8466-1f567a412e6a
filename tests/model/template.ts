/* eslint-disable @typescript-eslint/no-floating-promises */

import { describe, it } from 'node:test';

import assert from 'assert';

import { getPrismaClient } from '../../src/utils/prisma';

export const template = async (tenantId: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('template', () => {
      const createData = {
         displayName: 'Enrollment',
         externalId: 'enrollment',
         localeTemplates: {
            create: [
               {
                  locale: 'en-US',
                  title: 'Enrollment'
               },
               {
                  locale: 'es-MX',
                  title: 'Matriculación'
               }
            ]
         },

         tenantId
      };

      let lastId: string;

      it('should create a new template', async () => {
         const createdTemplate = await prisma.template.create({
            data: createData,
            include: { localeTemplates: { orderBy: { locale: 'asc' } } }
         });

         lastId = createdTemplate.id;

         assert.ok(createdTemplate.id);
         assert.strictEqual(createdTemplate.tenantId, tenantId);
         assert.strictEqual(createdTemplate.externalId, createData.externalId);
         assert.strictEqual(createdTemplate.displayName, createData.displayName);
         assert.strictEqual(createdTemplate.description, '');
         assert.strictEqual(createdTemplate.isNotificationTemplate, false);
         assert.strictEqual(createdTemplate.localeTemplates.length, 2);
         assert.strictEqual(createdTemplate.localeTemplates[0].locale, 'en-US');
         assert.strictEqual(createdTemplate.localeTemplates[0].title, 'Enrollment');
         assert.strictEqual(createdTemplate.localeTemplates[0].fullMessage, '');
         assert.strictEqual(createdTemplate.localeTemplates[0].shortMessage, '');
         assert.strictEqual(createdTemplate.localeTemplates[0].plainText, '');
         assert.ok(createdTemplate.localeTemplates[0].createdAt);
         assert.ok(createdTemplate.localeTemplates[0].createdAt instanceof Date);
         assert.ok(createdTemplate.localeTemplates[0].updatedAt);
         assert.ok(createdTemplate.localeTemplates[0].updatedAt instanceof Date);

         assert.strictEqual(createdTemplate.localeTemplates[1].locale, 'es-MX');
         assert.strictEqual(createdTemplate.localeTemplates[1].title, 'Matriculación');
         assert.strictEqual(createdTemplate.localeTemplates[1].fullMessage, '');
         assert.strictEqual(createdTemplate.localeTemplates[1].shortMessage, '');
         assert.strictEqual(createdTemplate.localeTemplates[1].plainText, '');
         assert.ok(createdTemplate.localeTemplates[0].createdAt);
         assert.ok(createdTemplate.localeTemplates[1].createdAt instanceof Date);
         assert.ok(createdTemplate.localeTemplates[1].updatedAt);
         assert.ok(createdTemplate.localeTemplates[1].updatedAt instanceof Date);
      });

      it('should update a template', async () => {
         const updateData = {
            description: 'Enrollment description',
            isNotificationTemplate: true,
            localeTemplates: {
               updateMany: [
                  {
                     data: {
                        fullMessage: 'Full message',
                        plainText: 'Plain text',
                        shortMessage: 'Short message',
                        variables: ['{{firstName}}', '{{lastName}}', '{{email}}']
                     },
                     where: { locale: 'en-US' }
                  }
               ]
            }
         };

         const base = await prisma.template.update({
            data: updateData,
            include: { localeTemplates: { include: { actionButtons: true }, orderBy: { locale: 'asc' } } },
            where: { id: lastId }
         });

         await prisma.localeTemplate.update({
            data: {
               actionButtons: {
                  create: [
                     {
                        label: 'Link',
                        url: 'https://example.com'
                     }
                  ]
               }
            },
            where: { id: base.localeTemplates[0].id }
         });

         const found = await prisma.template.findUniqueOrThrow({
            include: { localeTemplates: { include: { actionButtons: true }, orderBy: { locale: 'asc' } } },
            where: { id: lastId }
         });

         assert.ok(found.id);
         assert.strictEqual(found.tenantId, tenantId);
         assert.strictEqual(found.id, lastId);
         assert.strictEqual(found.externalId, createData.externalId);
         assert.strictEqual(found.displayName, createData.displayName);
         assert.strictEqual(found.description, updateData.description);
         assert.strictEqual(found.isNotificationTemplate, true);
         assert.strictEqual(found.localeTemplates.length, 2);
         assert.strictEqual(found.localeTemplates[0].locale, 'en-US');
         assert.strictEqual(found.localeTemplates[0].title, 'Enrollment');
         assert.strictEqual(found.localeTemplates[0].fullMessage, 'Full message');
         assert.strictEqual(found.localeTemplates[0].shortMessage, 'Short message');
         assert.strictEqual(found.localeTemplates[0].plainText, 'Plain text');
         assert.deepStrictEqual(found.localeTemplates[0].variables, ['{{firstName}}', '{{lastName}}', '{{email}}']);

         assert.strictEqual(found.localeTemplates[0].actionButtons[0].label, 'Link');
         assert.strictEqual(found.localeTemplates[0].actionButtons[0].url, 'https://example.com');

         assert.strictEqual(found.localeTemplates[1].locale, 'es-MX');
         assert.strictEqual(found.localeTemplates[1].title, 'Matriculación');
         assert.strictEqual(found.localeTemplates[1].fullMessage, '');
         assert.strictEqual(found.localeTemplates[1].shortMessage, '');
         assert.strictEqual(found.localeTemplates[1].plainText, '');
      });
   });
};
