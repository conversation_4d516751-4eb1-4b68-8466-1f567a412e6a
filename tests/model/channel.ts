/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */
import { describe, it } from 'node:test';

import assert from 'assert';
import { Prisma } from '@prisma/client';
import { v7 as uuid7 } from 'uuid';

import { getPrismaClient } from '../../src/utils/prisma';

export const channel = async (tenantId: string, channelId1: string, channelId2: string) => {
   const prisma = await getPrismaClient(tenantId);

   describe('channel', () => {
      const createData = {
         attributes: {
            create: [
               {
                  name: 'test',
                  value: 'test'
               },
               {
                  name: 'test2',
                  value: 'test2'
               }
            ]
         },
         channelType: 'default',
         displayName: 'registrar sms',
         externalId: 'registrar-sms',
         id: channelId1,
         service: 'sms',
         tenantId
      };

      it('should create a new channel', async () => {
         const createdChannel = await prisma.channel.create({
            data: createData,
            include: { attributes: true }
         });

         assert.strictEqual(createdChannel.tenantId, tenantId);
         assert.strictEqual(createdChannel.id, createData.id);
         assert.strictEqual(createdChannel.externalId, createData.externalId);
         assert.strictEqual(createdChannel.service, createData.service);
         assert.strictEqual(createdChannel.channelType, createData.channelType);
         assert.strictEqual(createdChannel.displayName, createData.displayName);
         assert.strictEqual(createdChannel.enabled, true);
         assert.strictEqual(createdChannel.attributes.length, 2);
         createdChannel.attributes.sort((a, b) => a.name.localeCompare(b.name));
         assert.strictEqual(createdChannel.attributes[0].name, 'test');
         assert.strictEqual(createdChannel.attributes[0].value, 'test');
         assert.strictEqual(createdChannel.attributes[1].name, 'test2');
         assert.strictEqual(createdChannel.attributes[1].value, 'test2');
         assert.equal(createdChannel.description, '');
         assert(createdChannel.createdAt);
         assert.ok(createdChannel.createdAt instanceof Date);
         assert(createdChannel.updatedAt);
         assert.ok(createdChannel.updatedAt instanceof Date);
      });

      it('should create a another channel', async () => {
         const otherData = {
            channelType: 'staff',
            description: 'blah blah blah',
            displayName: 'admissions sms',
            enabled: false,
            externalId: 'admissions-sms',
            id: channelId2,
            service: 'sms',
            tenantId
         };

         const createdChannel = await prisma.channel.create({
            data: otherData
         });

         assert.strictEqual(createdChannel.tenantId, tenantId);
         assert.strictEqual(createdChannel.id, otherData.id);
         assert.strictEqual(createdChannel.externalId, otherData.externalId);
         assert.strictEqual(createdChannel.service, otherData.service);
         assert.strictEqual(createdChannel.channelType, otherData.channelType);
         assert.strictEqual(createdChannel.displayName, otherData.displayName);
         assert.strictEqual(createdChannel.enabled, otherData.enabled);
         assert.equal(createdChannel.description, otherData.description);
         assert(createdChannel.createdAt);
         assert.ok(createdChannel.createdAt instanceof Date);
         assert(createdChannel.updatedAt);
         assert.ok(createdChannel.updatedAt instanceof Date);
      });

      it('should update a channel', async () => {
         const updateData = {
            channelType: 'default',
            description: 'financial aid teams',
            displayName: 'finaid teams',
            enabled: false,
            externalId: 'finaid-teams',
            service: 'teams'
         };

         const updatedChannel = await prisma.channel.update({
            data: updateData,
            where: { id: channelId1 }
         });

         assert.strictEqual(updatedChannel.tenantId, tenantId);
         assert.strictEqual(updatedChannel.id, createData.id);
         assert.strictEqual(updatedChannel.channelType, updateData.channelType);
         assert.strictEqual(updatedChannel.externalId, updateData.externalId);
         assert.strictEqual(updatedChannel.service, updateData.service);
         assert.strictEqual(updatedChannel.displayName, updateData.displayName);
         assert.strictEqual(updatedChannel.enabled, updateData.enabled);
         assert.strictEqual(updatedChannel.description, updateData.description);
         assert(updatedChannel.createdAt);
         assert.ok(updatedChannel.createdAt instanceof Date);
         assert(updatedChannel.updatedAt);
         assert.ok(updatedChannel.updatedAt instanceof Date);
      });

      it('should get all channels', async () => {
         const channels = await prisma.channel.findMany();

         assert.strictEqual(channels.length, 2);
      });

      it('cannot create a channel with a duplicate service and audience', async () => {
         try {
            const data = {
               ...createData,
               id: uuid7()
            };

            await prisma.channel.create({
               data,
               include: { attributes: true }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot update a channel with a duplicate service and audience', async () => {
         const existing = await prisma.channel.findFirstOrThrow({
            where: {
               channelType: 'staff',
               service: createData.service
            }
         });

         try {
            const data = {
               ...createData,
               id: existing.id
            };
            await prisma.channel.update({
               data,
               include: { attributes: true },
               where: { id: existing.id }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot add a channel attribute with a duplicate name', async () => {
         try {
            await prisma.channelAttribute.create({
               data: {
                  channelId: createData.id,
                  id: uuid7(),
                  name: 'attr-4',
                  secure: false,
                  value: 'test'
               }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });

      it('cannot create a channel attribute with a duplicate name', async () => {
         try {
            await prisma.channelAttribute.create({
               data: {
                  channelId: createData.id,
                  id: uuid7(),
                  name: 'test',
                  secure: false,
                  value: 'test'
               }
            });
         } catch (error) {
            assert.ok(error instanceof Prisma.PrismaClientKnownRequestError);
            assert.strictEqual(error.code, 'P2002');
         }
      });
   });
};
