/* eslint-disable simple-import-sort/imports */
/* eslint-disable @typescript-eslint/no-floating-promises */

import { it, describe } from 'node:test';
import assert from 'assert';
import { getPrismaClient } from '../../src/utils/prisma';

export const topic = async (
   tenantId: string,
   channelId1: string,
   channelId2: string,
   topicId1: string,
   topicId2: string
) => {
   const prisma = await getPrismaClient(tenantId);

   describe('topic', () => {
      it('should create a new topic', async () => {
         const channel = await prisma.channel.findUniqueOrThrow({
            where: { id: channelId1 }
         });

         const createData = {
            channelType: 'default',
            defaultCategory: 'enrollment',
            defaultService: channel.service,
            displayName: 'Enrollment',
            externalId: 'enrollment',
            id: topicId1,
            tenantId
         };

         const createdTopic = await prisma.topic.create({
            data: createData
         });

         assert.strictEqual(createdTopic.tenantId, tenantId);
         assert.strictEqual(createdTopic.id, createData.id);
         assert.strictEqual(createdTopic.externalId, createData.externalId);
         assert.strictEqual(createdTopic.channelType, createData.channelType);
         assert.strictEqual(createdTopic.displayName, createData.displayName);
         assert.strictEqual(createdTopic.description, '');
         assert.strictEqual(createdTopic.defaultService, channel.service);
         assert.strictEqual(createdTopic.defaultCategory, 'enrollment');
         assert.strictEqual(createdTopic.channelAlwaysOn, false);
         assert.strictEqual(createdTopic.enabled, true);
         assert.strictEqual(createdTopic.visibleInPreferences, true);
         assert.strictEqual(createdTopic.orderSequence, 1);
         assert.ok(createdTopic.userPreferenceRoles instanceof Array);
         assert.strictEqual(createdTopic.userPreferenceRoles.length, 0);
         assert.ok(createdTopic.roles instanceof Array);
         assert.strictEqual(createdTopic.roles.length, 0);
         assert(createdTopic.createdAt);
         assert.ok(createdTopic.createdAt instanceof Date);
         assert(createdTopic.updatedAt);
         assert.ok(createdTopic.updatedAt instanceof Date);
      });

      it('should create a another topic', async () => {
         const channel = await prisma.channel.findUniqueOrThrow({
            where: { id: channelId2 }
         });

         const otherData = {
            channelAlwaysOn: true,
            channelType: 'default',
            defaultCategory: 'holds',
            defaultService: channel.service,
            description: 'Holds',
            displayName: 'Holds',
            enabled: false,
            externalId: 'holds',
            id: topicId2,
            orderSequence: 2,
            roles: ['student'],
            tenantId,
            userPreferenceRoles: ['student'],
            visibleInPreferences: false
         };

         const createdTopic = await prisma.topic.create({
            data: otherData
         });

         assert.strictEqual(createdTopic.tenantId, tenantId);
         assert.strictEqual(createdTopic.id, otherData.id);
         assert.strictEqual(createdTopic.externalId, otherData.externalId);
         assert.strictEqual(createdTopic.channelType, otherData.channelType);
         assert.strictEqual(createdTopic.displayName, otherData.displayName);
         assert.strictEqual(createdTopic.description, otherData.description);
         assert.strictEqual(createdTopic.defaultService, channel.service);
         assert.strictEqual(createdTopic.defaultCategory, otherData.defaultCategory);
         assert.strictEqual(createdTopic.channelAlwaysOn, true);
         assert.strictEqual(createdTopic.enabled, false);
         assert.strictEqual(createdTopic.visibleInPreferences, false);
         assert.strictEqual(createdTopic.orderSequence, 2);
         assert.ok(createdTopic.userPreferenceRoles instanceof Array);
         assert.strictEqual(createdTopic.userPreferenceRoles.length, 1);
         assert.strictEqual(createdTopic.userPreferenceRoles[0], 'student');
         assert.ok(createdTopic.roles instanceof Array);
         assert.strictEqual(createdTopic.roles.length, 1);
         assert.strictEqual(createdTopic.roles[0], 'student');
         assert(createdTopic.createdAt);
         assert.ok(createdTopic.createdAt instanceof Date);
         assert(createdTopic.updatedAt);
         assert.ok(createdTopic.updatedAt instanceof Date);
      });

      it('should create a third topic with same channels', async () => {
         const channel = await prisma.channel.findUniqueOrThrow({
            where: { id: channelId1 }
         });

         const data = {
            channelType: 'default',
            defaultCategory: 'registrar',
            defaultService: channel.service,
            description: 'Registrar',
            displayName: 'Registrar',
            enabled: false,
            externalId: 'registrar',
            orderSequence: 2,
            roles: ['student'],
            tenantId,
            userPreferenceRoles: ['student'],
            visibleInPreferences: false
         };

         const createdTopic = await prisma.topic.create({
            data
         });

         assert.strictEqual(createdTopic.tenantId, tenantId);
         assert.ok(createdTopic.id);
         assert.strictEqual(createdTopic.externalId, data.externalId);
         assert.strictEqual(createdTopic.displayName, data.displayName);
         assert.strictEqual(createdTopic.description, data.description);
         assert.strictEqual(createdTopic.defaultService, channel.service);
         assert.strictEqual(createdTopic.defaultCategory, data.defaultCategory);
         assert.strictEqual(createdTopic.channelAlwaysOn, false);
         assert.strictEqual(createdTopic.enabled, false);
         assert.strictEqual(createdTopic.visibleInPreferences, false);
         assert.strictEqual(createdTopic.orderSequence, 2);
         assert.ok(createdTopic.userPreferenceRoles instanceof Array);
         assert.strictEqual(createdTopic.userPreferenceRoles.length, 1);
         assert.strictEqual(createdTopic.userPreferenceRoles[0], 'student');
         assert.ok(createdTopic.roles instanceof Array);
         assert.strictEqual(createdTopic.roles.length, 1);
         assert.strictEqual(createdTopic.roles[0], 'student');
         assert(createdTopic.createdAt);
         assert.ok(createdTopic.createdAt instanceof Date);
         assert(createdTopic.updatedAt);
         assert.ok(createdTopic.updatedAt instanceof Date);
      });

      it('should update a topic', async () => {
         const channel = await prisma.channel.findUniqueOrThrow({
            where: { id: channelId2 }
         });

         const updateData = {
            defaultCategory: 'financial',
            defaultService: channel.service,
            description: 'Financial Aid',
            displayName: 'Financial Aid',
            enabled: false,
            externalId: 'financial',
            id: topicId1,
            orderSequence: 3,
            roles: ['student'],
            tenantId,
            userPreferenceRoles: ['student'],
            visibleInPreferences: false
         };

         const updatedTopic = await prisma.topic.update({
            data: updateData,
            where: { id: topicId1 }
         });

         assert.strictEqual(updatedTopic.tenantId, tenantId);
         assert.strictEqual(updatedTopic.id, updateData.id);
         assert.strictEqual(updatedTopic.externalId, updateData.externalId);
         assert.strictEqual(updatedTopic.displayName, updateData.displayName);
         assert.strictEqual(updatedTopic.description, updateData.description);
         assert.strictEqual(updatedTopic.defaultService, channel.service);
         assert.strictEqual(updatedTopic.defaultCategory, updateData.defaultCategory);
         assert.strictEqual(updatedTopic.channelAlwaysOn, false);
         assert.strictEqual(updatedTopic.enabled, false);
         assert.strictEqual(updatedTopic.visibleInPreferences, false);
         assert.strictEqual(updatedTopic.orderSequence, 3);
         assert.ok(updatedTopic.userPreferenceRoles instanceof Array);
         assert.strictEqual(updatedTopic.userPreferenceRoles.length, 1);
         assert.strictEqual(updatedTopic.userPreferenceRoles[0], 'student');
         assert.ok(updatedTopic.roles instanceof Array);
         assert.strictEqual(updatedTopic.roles.length, 1);
         assert.strictEqual(updatedTopic.roles[0], 'student');
         assert(updatedTopic.createdAt);
         assert.ok(updatedTopic.createdAt instanceof Date);
         assert(updatedTopic.updatedAt);
         assert.ok(updatedTopic.updatedAt instanceof Date);
      });
   });
};
