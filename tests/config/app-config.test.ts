/* eslint-disable @typescript-eslint/no-floating-promises */
import { describe, it, suite } from 'node:test';

import assert from 'assert';

import { AppConfigFactory } from '../../src/config/index.js';

suite('AppConfig', () => {
   describe('Azure getValue', () => {
      process.env.CONFIGURATION_TYPE = 'azure';
      const config = AppConfigFactory.create();

      it('should return the log level', async () => {
         const logLevel = await config.getValue('logLevel');
         assert.strictEqual(logLevel, 'debug');
      });

      it('call it again should return the log level', async () => {
         const logLevel = await config.getValue('logLevel');
         assert.strictEqual(logLevel, 'debug');
      });
   });

   describe('Local getValue', () => {
      AppConfigFactory.clear();
      process.env.CONFIGURATION_TYPE = 'local';

      const config = AppConfigFactory.create();
      process.env.LOCAL_VALUE = 'debug';

      it('should return the log level', async () => {
         const logLevel = await config.getValue('LOCAL_VALUE');
         assert.strictEqual(logLevel, 'debug');
      });

      it('call it again should return the log level', async () => {
         const logLevel = await config.getValue('LOCAL_VALUE');
         assert.strictEqual(logLevel, 'debug');
      });
   });

   describe('Local getValue using env to create', () => {
      process.env.USE_LOCAL_APP_CONFIG = 'true';
      AppConfigFactory.clear();
      const config = AppConfigFactory.create();

      process.env.OTHER_LOCAL_VALUE = 'debug';
      it('should return the log level', async () => {
         const logLevel = await config.getValue('OTHER_LOCAL_VALUE');
         assert.strictEqual(logLevel, 'debug');
      });

      it('call it again should return the log level', async () => {
         const logLevel = await config.getValue('OTHER_LOCAL_VALUE');
         assert.strictEqual(logLevel, 'debug');
      });
   });
});
