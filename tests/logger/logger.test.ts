/* eslint-disable @typescript-eslint/no-floating-promises */
import { describe, suite } from 'node:test';

import { LoggerFactory } from '../../src/logging';

suite('Logger', () => {
   process.env.USE_LOCAL_APP_CONFIG = 'true';

   describe('Manual check ', async () => {
      process.env.logLevel = 'trace';
      process.env.LOG_TRANSPORT = 'console';

      const logger = LoggerFactory.getLogger('test');
      await logger.trace({ data: 'trace' }, 'trace - trace level - no error');
      await logger.trace({ data: 'trace' }, 'trace - trace level - with error', new Error('test error'));
      await logger.debug({ data: 'debug' }, 'debug - trace level - no error');
      await logger.debug({ data: 'debug' }, 'debug - trace level - with error', new Error('test error'));
      await logger.info({ data: 'info' }, 'info - trace level - no error');
      await logger.info({ data: 'info' }, 'info - trace level - with error', new Error('test error'));
      await logger.warn({ data: 'warn' }, 'warn - trace level - no error');
      await logger.warn({ data: 'warn' }, 'warn - trace level - with error', new Error('test error'));
      await logger.error({ data: 'error' }, 'error - trace level - no error');
      await logger.error({ data: 'error' }, 'error - trace level - with error', new Error('test error'));
      await logger.fatal(
         { data: 'fatal', headers: { authorization: 'redact me', 'x-api-key': 'redact me' } },
         'fatal - trace level - no error'
      );
      await logger.fatal({ data: 'fatal' }, 'fatal - trace level - with error', new Error('test error'));

      process.env.logLevel = 'debug';

      await logger.trace({ data: 'trace' }, 'trace - debug level - no error - should not show');
      await logger.trace(
         { data: 'trace' },
         'trace - debug level - with error - should not show',
         new Error('test error')
      );
      await logger.debug({ data: 'debug' }, 'debug - debug level - no error');
      await logger.debug({ data: 'debug' }, 'debug - debug level - with error', new Error('test error'));
      await logger.info({ data: 'info' }, 'info - debug level - no error');
      await logger.info({ data: 'info' }, 'info - debug level - with error', new Error('test error'));
      await logger.warn({ data: 'warn' }, 'warn - debug level - no error');
      await logger.warn({ data: 'warn' }, 'warn - debug level - with error', new Error('test error'));
      await logger.error({ data: 'error' }, 'error - debug level - no error');
      await logger.error({ data: 'error' }, 'error - debug level - with error', new Error('test error'));
      await logger.fatal({ data: 'fatal' }, 'fatal - debug level - no error');
      await logger.fatal({ data: 'fatal' }, 'fatal - debug level - with error', new Error('test error'));

      process.env.logLevel = 'info';

      await logger.trace({ data: 'trace' }, 'trace - info level - no error - should not show');
      await logger.trace(
         { data: 'trace' },
         'trace - info level - with error - should not show',
         new Error('test error')
      );
      await logger.debug({ data: 'debug' }, 'debug - info level - no error - should not show');
      await logger.debug(
         { data: 'debug' },
         'debug - info level - with error - should not show',
         new Error('test error')
      );
      await logger.info({ data: 'info' }, 'info - info level - no error');
      await logger.info({ data: 'info' }, 'info - info level - with error', new Error('test error'));
      await logger.warn({ data: 'warn' }, 'warn - info level - no error');
      await logger.warn({ data: 'warn' }, 'warn - info level - with error', new Error('test error'));
      await logger.error({ data: 'error' }, 'error - info level - no error');
      await logger.error({ data: 'error' }, 'error - info level - with error', new Error('test error'));
      await logger.fatal({ data: 'fatal' }, 'fatal - info level - no error');
      await logger.fatal({ data: 'fatal' }, 'fatal - info level - with error', new Error('test error'));

      process.env.logLevel = 'warn';

      await logger.trace({ data: 'trace' }, 'trace - warn level - no error - should not show');
      await logger.trace(
         { data: 'trace' },
         'trace - warn level - with error - should not show',
         new Error('test error')
      );
      await logger.debug({ data: 'debug' }, 'debug - warn level - no error - should not show');
      await logger.debug(
         { data: 'debug' },
         'debug - warn level - with error - should not show',
         new Error('test error')
      );
      await logger.info({ data: 'info' }, 'info - warn level - no error - should not show');
      await logger.info({ data: 'info' }, 'info - warn level - with error - should not show', new Error('test error'));
      await logger.warn({ data: 'warn' }, 'warn - warn level - no error');
      await logger.warn({ data: 'warn' }, 'warn - warn level - with error', new Error('test error'));
      await logger.error({ data: 'error' }, 'error - warn level - no error');
      await logger.error({ data: 'error' }, 'error - warn level - with error', new Error('test error'));
      await logger.fatal({ data: 'fatal' }, 'fatal - warn level - no error');
      await logger.fatal({ data: 'fatal' }, 'fatal - warn level - with error', new Error('test error'));

      process.env.logLevel = 'error';

      await logger.trace({ data: 'trace' }, 'trace - error level - no error - should not show');
      await logger.trace(
         { data: 'trace' },
         'trace - error level - with error - should not show',
         new Error('test error')
      );
      await logger.debug({ data: 'debug' }, 'debug - error level - no error - should not show');
      await logger.debug(
         { data: 'debug' },
         'debug - error level - with error - should not show',
         new Error('test error')
      );
      await logger.info({ data: 'info' }, 'info - error level - no error - should not show');
      await logger.info({ data: 'info' }, 'info - error level - with error - should not show', new Error('test error'));
      await logger.warn({ data: 'warn' }, 'warn - error level - no error - should not show');
      await logger.warn({ data: 'warn' }, 'warn - error level - with error - should not show', new Error('test error'));
      await logger.error({ data: 'error' }, 'error - error level - no error');
      await logger.error({ data: 'error' }, 'error - error level - with error', new Error('test error'));
      await logger.fatal({ data: 'fatal' }, 'fatal - error level - no error');
      await logger.fatal({ data: 'fatal' }, 'fatal - error level - with error', new Error('test error'));

      process.env.logLevel = 'fatal';

      await logger.trace({ data: 'trace' }, 'trace - fatal level - no error - should not show');
      await logger.trace(
         { data: 'trace' },
         'trace - fatal level - with error - should not show',
         new Error('test error')
      );
      await logger.debug({ data: 'debug' }, 'debug - fatal level - no error - should not show');
      await logger.debug(
         { data: 'debug' },
         'debug - fatal level - with error - should not show',
         new Error('test error')
      );
      await logger.info({ data: 'info' }, 'info - fatal level - no error - should not show');
      await logger.info({ data: 'info' }, 'info - fatal level - with error - should not show', new Error('test error'));
      await logger.warn({ data: 'warn' }, 'warn - fatal level - no error - should not show');
      await logger.warn({ data: 'warn' }, 'warn - fatal level - with error - should not show', new Error('test error'));
      await logger.error({ data: 'error' }, 'error - fatal level - no error');
      await logger.error({ data: 'error' }, 'error - fatal level - with error', new Error('test error'));
      await logger.fatal({ data: 'fatal' }, 'fatal - fatal level - no error');
      await logger.fatal(
         { data: 'fatal', headers: { authorization: 'redact me', donot: 'dont redact me', 'x-api-key': 'redact me' } },
         'fatal - fatal level - with error',
         new Error('test error')
      );
   });
});
