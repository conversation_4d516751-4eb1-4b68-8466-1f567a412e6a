/* eslint-disable @typescript-eslint/no-floating-promises */
import { describe, it, suite } from 'node:test';

import assert from 'assert';
import { v7 as uuid } from 'uuid';

import { AzureSecretVault } from '../../src/secrets/azure-secret-vault.js';
import { CachedSecretVault } from '../../src/secrets/cached-secret-vault.js';
import { LocalSecretVault } from '../../src/secrets/local-secret-vault.js';
import { SecretVaultFactory } from '../../src/secrets/secret-vault-factory.js';

suite('SecretVault', () => {
   describe('LocalSecretVault', () => {
      const testKey = 'test-key';
      const testSecret = 'test-secret';

      it('should set, get, and delete a secret', async () => {
         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const secretVault = SecretVaultFactory.getSecretVault(false);
         await secretVault.setSecret(testKey, testSecret);
         const retrievedSecret = await secretVault.getSecret(testKey);
         assert.strictEqual(retrievedSecret, testSecret);
         await secretVault.deleteSecret(testKey);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should return empty string for non-existent secret', async () => {
         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const secretVault = SecretVaultFactory.getSecretVault(false);
         const retrievedSecret = await secretVault.getSecret('non-existent-key');
         assert.strictEqual(retrievedSecret, '');

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });
   });

   describe('AzureSecretVault', () => {
      const testKey = uuid();
      const testSecret = 'test-azure-secret';

      it('should set, get, and delete a secret', async () => {
         process.env.SECRET_VAULT_TYPE = 'AZURE';
         const secretVault = SecretVaultFactory.getSecretVault(false);
         await secretVault.setSecret(testKey, testSecret);
         const retrievedSecret = await secretVault.getSecret(testKey);
         assert.strictEqual(retrievedSecret, testSecret);
         await secretVault.deleteSecret(testKey);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should return empty string for non-existent secret', async () => {
         process.env.SECRET_VAULT_TYPE = 'AZURE';
         const secretVault = SecretVaultFactory.getSecretVault(false);
         const retrievedSecret = await secretVault.getSecret('non-existent-key');
         assert.strictEqual(retrievedSecret, '');

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });
   });

   describe('CachedSecretVault', () => {

      it('should set, get, and delete a secret - Azure', async () => {
         const testKey = uuid();
         const testSecret = 'test-cached-secret';

         process.env.SECRET_VAULT_TYPE = 'AZURE';
         const cachedVault = SecretVaultFactory.getSecretVault(false);

         await cachedVault.setSecret(testKey, testSecret);
         const retrievedSecret = await cachedVault.getSecret(testKey);
         assert.strictEqual(retrievedSecret, testSecret);
         await cachedVault.deleteSecret(testKey);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should cache secrets after first retrieval - Azure', async () => {
         const testKey = uuid();
         const testSecret = 'test-cached-secret';

         process.env.SECRET_VAULT_TYPE = 'AZURE';
         const cachedVault = SecretVaultFactory.getSecretVault(false);

         await cachedVault.setSecret(testKey, testSecret);

         // First get should retrieve from underlying vault
         const firstGet = await cachedVault.getSecret(testKey);
         assert.strictEqual(firstGet, testSecret);

         // Second get should retrieve from cache, not reflect the changed value
         const secondGet = await cachedVault.getSecret(testKey);
         assert.strictEqual(secondGet, testSecret);

         await cachedVault.deleteSecret(testKey);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should set, get, and delete a secret - LOCAL', async () => {
         const testKey = uuid();
         const testSecret = 'test-cached-secret';

         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const cachedVault = SecretVaultFactory.getSecretVault(false);

         await cachedVault.setSecret(testKey, testSecret);
         const retrievedSecret = await cachedVault.getSecret(testKey);
         assert.strictEqual(retrievedSecret, testSecret);
         await cachedVault.deleteSecret(testKey);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should cache secrets after first retrieval - LOCAL', async () => {
         const testKey = uuid();
         const testSecret = 'test-cached-secret';

         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const cachedVault = SecretVaultFactory.getSecretVault(false);

         await cachedVault.setSecret(testKey, testSecret);

         // First get should retrieve from underlying vault
         const firstGet = await cachedVault.getSecret(testKey);
         assert.strictEqual(firstGet, testSecret);

         // Second get should retrieve from cache, not reflect the changed value
         const secondGet = await cachedVault.getSecret(testKey);
         assert.strictEqual(secondGet, testSecret);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });
   });

   describe('SecretVaultFactory', () => {
      it('should create a LocalSecretVault by default', () => {
         process.env.SECRET_VAULT_TYPE = 'unknown';
         const vault = SecretVaultFactory.getSecretVault(false);
         assert(vault instanceof LocalSecretVault);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should create a LocalSecretVault when configured', () => {
         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const vault = SecretVaultFactory.getSecretVault(false);
         assert(vault instanceof LocalSecretVault);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should create an AzureSecretVault when configured', () => {
         process.env.SECRET_VAULT_TYPE = 'AZURE';
         const vault = SecretVaultFactory.getSecretVault(false);
         assert(vault instanceof AzureSecretVault);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should create a CachedSecretVault when cache is enabled', () => {
         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const vault = SecretVaultFactory.getSecretVault(true);
         assert(vault instanceof CachedSecretVault);

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should reuse the same instance when called multiple times', () => {
         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const vault1 = SecretVaultFactory.getSecretVault(false);
         const vault2 = SecretVaultFactory.getSecretVault(false);
         assert.strictEqual(vault1, vault2, 'Should return the same instance');

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });

      it('should create a new instance after reset', () => {
         process.env.SECRET_VAULT_TYPE = 'LOCAL';
         const vault1 = SecretVaultFactory.getSecretVault(false);
         SecretVaultFactory.resetSecretVault();
         const vault2 = SecretVaultFactory.getSecretVault(false);
         assert.notStrictEqual(vault1, vault2, 'Should create a new instance after reset');

         delete process.env.SECRET_VAULT_TYPE;
         SecretVaultFactory.resetSecretVault();
      });
   });
});
