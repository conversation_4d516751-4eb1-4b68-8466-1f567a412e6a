import compress from '@fastify/compress';
import fastifyFormbody from '@fastify/formbody';
import fastifyHelmet from '@fastify/helmet';
import sensible from '@fastify/sensible';

import { App } from '../../src/app.js';
import { ShortLinkRoute } from '../../src/modules/short-link/short-link.route.js';
import { initializeRoutes } from '../../src/plugins/initialize-routes.js';

import { authenticate } from './jwt.plugin.mock.js';

export class AppMock extends App {
   constructor(opts = {}) {
      super(opts);
   }

   protected async initializePlugins() {
      await this.instance.register(compress, {});
      await this.instance.register(sensible, { sharedSchemaId: 'HttpError' });
      await this.instance.register(fastifyFormbody, {});
      await this.instance.register(fastifyHelmet, {});

      await this.instance.register(authenticate);
      const shortLinkRoute = new ShortLinkRoute();
      this.instance.register(shortLinkRoute.initializeRoutes.bind(shortLinkRoute));

      await this.instance.register(initializeRoutes, { prefix: 'v1' });
   }
}
