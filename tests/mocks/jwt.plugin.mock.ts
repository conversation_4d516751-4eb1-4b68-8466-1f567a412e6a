import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { fastifyPlugin } from 'fastify-plugin';

export const authenticate = fastifyPlugin((fastify: FastifyInstance, _: unknown, next: () => void) => {
   const auth = (): ((request: FastifyRequest, reply: FastifyReply) => void) => {
      // removing async cause http requests to hang
      // eslint-disable-next-line @typescript-eslint/require-await
      return async (request: FastifyRequest) => {
         request.tenantId = '12345';
      };
   };

   fastify.decorate('authenticate', auth);
   next();
});
