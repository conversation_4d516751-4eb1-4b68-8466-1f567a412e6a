{
   "compilerOptions": {
      "target": "ES2020",
      "module": "ES2020",
      "declarationDir": "dist/types",
      "outDir": "dist",
      "sourceMap": true,
      "strict": true,
      "esModuleInterop": true,
      "declaration": true,
      "declarationMap": true,
      "moduleResolution": "node",
      "skipLibCheck": false,
      "forceConsistentCasingInFileNames": true,
      "allowSyntheticDefaultImports": true,
      "resolveJsonModule": true
   },
   // "include": ["src"],
   "include": ["src", "tests"],
   "exclude": ["node_modules"]
}
