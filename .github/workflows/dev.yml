# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy container app to Azure Web App - xsignal-bot-dev

on:
   push:
      branches:
         - dev
   workflow_dispatch:

jobs:
   build:
      runs-on: 'ubuntu-latest'
      permissions:
         contents: read
         packages: write

      steps:
         - name: Check out repository code
           uses: actions/checkout@v4
           with:
              token: ${{ github.token }}
              fetch-depth: 0

         - name: Get Version
           id: version
           uses: paulhatch/semantic-version@v5.4.0
           with:
              bump_each_commit: true

         - name: 'Setup Node'
           uses: actions/setup-node@v4
           env:
              NODE_AUTH_TOKEN: ${{ secrets.NODE_AUTH_TOKEN }}
           with:
              node-version: 22
              registry-url: 'https://npm.pkg.github.com'
              scope: '@x-signal-inc'

         - name: 'Resolve Project Dependencies Using Npm'
           shell: bash
           run: |
              npm install
              npm run generate
              npm run build

         - name: Log in to GitHub Container Registry
           uses: docker/login-action@v2
           with:
              registry: ghcr.io
              username: ${{ github.actor }}
              password: ${{ secrets.GITHUB_TOKEN }}

         - name: Build and push API Container
           run: |
              docker build -t ghcr.io/${{ github.repository_owner }}/messaging-api:${{ steps.version.outputs.version }} . -f ./Dockerfile
              docker tag ghcr.io/${{ github.repository_owner }}/messaging-api:${{ steps.version.outputs.version }} ghcr.io/${{ github.repository_owner }}/messaging-api:latest
              docker push ghcr.io/${{ github.repository_owner }}/messaging-api:${{ steps.version.outputs.version }}
              docker push ghcr.io/${{ github.repository_owner }}/messaging-api:latest
