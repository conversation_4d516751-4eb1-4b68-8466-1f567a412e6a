import { App } from './app.js';
import { appOptions } from './config/app-options.js';

const start = async () => {
   const app = new App(appOptions);

   try {
      await app.initialize();
      await app.start();
      const listeners = ['SIGINT', 'SIGTERM'];
      listeners.forEach((signal) => {
         process.on(signal, async () => {
            console.info('\nGracefully shutting down');
            await app.close();
            process.exit(0);
         });
      });
   } catch (error) {
      console.error(error);
      process.exit(1);
   }
};

void start();
