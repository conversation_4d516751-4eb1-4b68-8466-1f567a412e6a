export interface Logger {
   debug(metadata: object, message: string, error?: Error): Promise<void>;
   error(metadata: object, message: string, error?: Error): Promise<void>;
   fatal(metadata: object, message: string, error?: Error): Promise<void>;
   info(metadata: object, message: string, error?: Error): Promise<void>;
   trace(metadata: object, message: string, error?: Error): Promise<void>;
   warn(metadata: object, message: string, error?: Error): Promise<void>;
}
