/* eslint-disable @typescript-eslint/require-await */
import { redactionPlugin } from '@loglayer/plugin-redaction';
import { LogLayerTransport } from '@loglayer/transport';
import { ConsolaTransport } from '@loglayer/transport-consola';
import { DataDogTransport } from '@loglayer/transport-datadog';
import { getPrettyTerminal } from '@loglayer/transport-pretty-terminal';
import { createConsola } from 'consola';
import { ConsoleTransport, ILogLayer, LogLayer } from 'loglayer';
import { serializeError } from 'serialize-error';

import { AppConfig, AppConfigFactory } from '../config/index.js';

import { Logger } from './logger.js';

export class LogLayerLogger implements Logger {
   private _logger: ILogLayer;
   private _config: AppConfig;

   private _getTransport = (): LogLayerTransport => {
      const type = process.env.LOG_TRANSPORT?.toLocaleLowerCase() ?? 'console';

      switch (type) {
         case 'consola':
            return new ConsolaTransport({
               logger: createConsola({
                  fancy: true,
                  level: 5
               })
            });
         case 'datadog':
            return new DataDogTransport({
               level: 'trace',
               options: {
                  ddClientConf: {
                     authMethods: {
                        apiKeyAuth: process.env.DATADOG_API_KEY ?? ''
                     }
                  },
                  ddServerConf: {
                     site: process.env.DATADOG_SITE ?? ''
                  },
                  ddsource: process.env.DATADOG_SOURCE ?? '',
                  ddtags: process.env.DATADOG_TAGS ?? '',
                  service: process.env.DATADOG_SERVICE ?? ''
               }
            });
         case 'development':
            return getPrettyTerminal({
               enabled: true,
               level: 'trace'
            });
         default:
            return new ConsoleTransport({ logger: console });
      }
   };

   constructor() {
      this._config = AppConfigFactory.create();

      this._logger = new LogLayer({
         errorFieldName: 'error',
         errorSerializer: serializeError,
         plugins: [
            redactionPlugin({
               paths: [
                  'api_key',
                  'authorization',
                  'client_secret',
                  'headers.authorization',
                  'headers.cookie',
                  'password',
                  'token',
                  'headers["x-api-key"]'
               ]
            })
         ],
         transport: this._getTransport()
      });
   }

   async fatal(data: object, message: string, error?: Error): Promise<void> {
      if (error) {
         this._logger.withError(error).withMetadata(data).fatal(message);
      } else {
         this._logger.withMetadata(data).fatal(message);
      }
   }

   async error(data: object, message: string, error?: Error): Promise<void> {
      if (error) {
         this._logger.withError(error).withMetadata(data).error(message);
      } else {
         this._logger.withMetadata(data).error(message);
      }
   }

   async warn(data: object, message: string, error?: Error): Promise<void> {
      const logLevel = await this._config.getValue('logLevel');

      switch (logLevel) {
         case 'warn':
         case 'info':
         case 'debug':
         case 'trace':
            if (error) {
               this._logger.withError(error).withMetadata(data).warn(message);
            } else {
               this._logger.withMetadata(data).warn(message);
            }

            break;
         default:
            break;
      }
   }

   async info(data: object, message: string, error?: Error): Promise<void> {
      const logLevel = await this._config.getValue('logLevel');

      switch (logLevel) {
         case 'info':
         case 'debug':
         case 'trace':
            if (error) {
               this._logger.withError(error).withMetadata(data).info(message);
            } else {
               this._logger.withMetadata(data).info(message);
            }

            break;
         default:
            break;
      }
   }

   async debug(data: object, message: string, error?: Error): Promise<void> {
      const logLevel = await this._config.getValue('logLevel');

      switch (logLevel) {
         case 'debug':
         case 'trace':
            if (error) {
               this._logger.withError(error).withMetadata(data).debug(message);
            } else {
               this._logger.withMetadata(data).debug(message);
            }

            break;
         default:
            break;
      }
   }

   async trace(data: object, message: string, error?: Error): Promise<void> {
      const logLevel = await this._config.getValue('logLevel');

      if (logLevel !== 'trace') {
         return;
      }

      if (error) {
         this._logger.withError(error).withMetadata(data).debug(`[trace] ${message}`);
      } else {
         this._logger.withMetadata(data).debug(`[trace] ${message}`);
      }
   }
}
