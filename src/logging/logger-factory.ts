import { LogLayerLogger } from './log-layer-logger.js';
import { Logger } from './logger.js';

type LoggerType = 'LOG_LAYER';

export class LoggerFactory {
   private static _loggers: Map<string, Logger> = new Map<string, Logger>();

   static getLogger(name: string = 'default'): Logger {
      if (!name || name.trim().length === 0) {
         throw new Error('Logger name cannot be empty');
      }

      let logger = this._loggers.get(name) ?? null;
      const type: LoggerType = (process.env.LOGGER_TYPE ?? 'LOG_LAYER') as LoggerType;

      if (!logger) {
         switch (type.toLocaleUpperCase()) {
            case 'LOG_LAYER':
               logger = new LogLayerLogger();
               break;
            default:
               logger = new LogLayerLogger();
         }

         this._loggers.set(name, logger);
      }

      return logger;
   }

   static clearLoggers(): void {
      this._loggers.clear();
   }

   static removeLogger(name: string): boolean {
      return this._loggers.delete(name);
   }
}
