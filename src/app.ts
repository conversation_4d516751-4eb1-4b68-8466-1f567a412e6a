/* eslint-disable sort-keys-fix/sort-keys-fix */
import compress from '@fastify/compress';
import fastifyFormbody from '@fastify/formbody';
import fastifyHelmet from '@fastify/helmet';
import sensible from '@fastify/sensible';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUi from '@fastify/swagger-ui';
import { TypeBoxTypeProvider } from '@fastify/type-provider-typebox';
import { AppConfig, AppConfigFactory, AttributeConstants } from '@x-signal-inc/messaging-common';
import fastify from 'fastify';
import { FastifyInstance } from 'fastify';
import { jwtDecode, JwtPayload } from 'jwt-decode';

import { OAuth2TokenRoute } from './modules/oauth2-token/oauth2-token.route.js';
import { ShortLinkRoute } from './modules/short-link/short-link.route.js';
import { initializeRoutes } from './plugins/initialize-routes.js';
import { authenticate } from './plugins/jwt.plugin.js';
import { setLogger } from './utils/getLogger.js';

interface RequestBody {
   [key: string]: unknown;

   attributes?: Array<{
      name: string;
      value: string;
   }>;
}

export class App {
   readonly instance: FastifyInstance;
   readonly config: AppConfig;

   constructor(opts = {}) {
      this.instance = fastify(opts);
      setLogger(this.instance.log);
      this.config = AppConfigFactory.create();
   }

   public async initialize() {
      this.instance.withTypeProvider<TypeBoxTypeProvider>();
      await this._initializeSwagger();
      await this.initializePlugins();
   }

   protected async initializePlugins() {
      await this.instance.register(compress, {});
      await this.instance.register(sensible, { sharedSchemaId: 'HttpError' });
      await this.instance.register(fastifyFormbody, {});
      await this.instance.register(fastifyHelmet, {});
      await this.instance.register(authenticate);
      await this.instance.register(initializeRoutes, { prefix: 'v1' });

      // no prefix routes
      const shortLinkRoute = new ShortLinkRoute();
      this.instance.register(shortLinkRoute.initializeRoutes.bind(shortLinkRoute));

      const route = new OAuth2TokenRoute();
      this.instance.register(route.initializeRoutes.bind(route));

      this.instance.get('/', {}, (request, reply) => {
         reply.redirect('https://xsignal.inc');
      });

      this.instance.addHook('onResponse', async (request, reply) => {
         const logLevel = await this.config.getValue('logLevel');

         // Skip logging for heartbeat requests
         if (request.url === '/v1/heartbeat' || request.url === '/') {
            return;
         }

         if (logLevel === 'trace' || logLevel === 'debug') {
            const headers = { ...request.headers };
            let tenantId = '';

            if (headers.authorization) {
               // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
               const decoded = jwtDecode(headers.authorization) as JwtPayload & { tenant?: string };

               tenantId = decoded.tenant ?? '';
               headers.authorization = 'REDACTED';
            }

            let body = {} as RequestBody;

            if (request.body) {
               body = { ...request.body } as RequestBody;

               if (body.attributes) {
                  for (const attribute of body.attributes) {
                     switch (attribute.name) {
                        case AttributeConstants.CLIENT_SECRET:
                        case AttributeConstants.ESENDEX_KEY:
                        case AttributeConstants.MODO_AUTHORIZATION:
                        case AttributeConstants.REFRESH_TOKEN:
                        case AttributeConstants.SENDGRID_KEY:
                        case AttributeConstants.SMTP_PASSWORD:
                        case AttributeConstants.TWILIO_TOKEN:
                           attribute.value = '[REDACTED]';
                           break;
                        default:
                           break;
                     }
                  }
               }

               if (body.message) {
                  body.message = '[REDACTED]';
               }

               if (body.plainText) {
                  body.plainText = '[REDACTED]';
               }

               if (body.shortMessage) {
                  body.shortMessage = '[REDACTED]';
               }
            }

            reply.request.log.debug({
               'network.client.ip': request.ip,
               'http.method': request.method,
               'http.url': request.url,
               'http.headers': headers,
               'http.body': body,
               'http.status_code': reply.statusCode,
               duration: reply.elapsedTime,
               tenantId
            });
         }
      });
   }

   public async start() {
      const address = await this.instance.listen({ host: '0.0.0.0', port: parseInt(process.env.PORT ?? '3000', 10) });
      console.info(`Server listening at ${address}`);
   }

   public async close() {
      await this.instance.close();
   }

   private async _initializeSwagger() {
      await this.instance.register(fastifySwagger, {
         openapi: {
            components: {
               securitySchemes: {
                  bearerAuth: {
                     bearerFormat: 'JWT',
                     scheme: 'bearer',
                     type: 'http'
                  }
               }
            },
            info: {
               description: 'Messaging API Documentation',
               title: 'xSIGNAL Messaging API',
               version: '1.0.0'
            }
         }
      });

      await this.instance.register(fastifySwaggerUi, {
         routePrefix: '/api-docs',
         staticCSP: true,
         theme: { title: 'API Documentation' },
         transformSpecification: (swaggerObject) => {
            return swaggerObject;
         },
         transformSpecificationClone: true,
         transformStaticCSP: (header) => header,
         uiConfig: {
            deepLinking: false,
            docExpansion: 'full'
         },
         uiHooks: {
            onRequest(request, reply, next) {
               next();
            },
            preHandler(request, reply, next) {
               next();
            }
         }
      });
   }
}
