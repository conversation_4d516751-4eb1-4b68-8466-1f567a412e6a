import { InvocationContext } from '@azure/functions';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';
import twilio from 'twilio';

export const sendTwilioSms = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);
   const sid = message.attributes.find((a) => a.name === AttributeConstants.TWILIO_SID)?.value;

   const token = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.TWILIO_TOKEN}`
   );

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!token) {
      result.success = false;
      result.message = 'Twilio token not found';

      return result;
   }

   /* TODO: create a connection pool by SID?  and use keepAlive ? */
   const client: twilio.Twilio = twilio(sid, token, { autoRetry: true, lazyLoading: false, maxRetries: 3 });

   const toSend = {
      body: message.text,
      from: message.from,
      statusCallback: message.callbackUrl,
      to: message.to
   };

   context.debug({
      data: { ...toSend, body: '[REDACTED]', channelMessageId: message.channelMessageId },
      function: 'sendTwilioSms',
      message: 'twilio - sending',
      tenantId: message.tenantId
   });

   try {
      const response = await client.messages.create(toSend);
      context.debug({
         data: { channelMessageId: message.channelMessageId, response },
         function: 'sendTwilioSms',
         message: 'twilio - response',
         tenantId: message.tenantId
      });

      if (response.status !== 'failed' && response.status !== 'undelivered') {
         result.success = true;
         result.transactionId = response.sid;

         return result;
      }

      result.success = false;
      result.code = response.errorCode.toString() || '';

      if (response.errorCode === 21610) {
         context.info({
            data: {
               channelMessageId: message.channelMessageId,
               code: response.errorCode,
               error: 'Recipient has blocked SMS',
               phoneNumber: message.to
            },
            function: 'sendTwilioSms',
            message: 'twilio - error - 21610',
            tenantId: message.tenantId
         });

         result.message = 'Recipient has blocked SMS';
         result.disable = true;

         return result;
      }

      if (response.errorCode === 21408) {
         context.info({
            data: {
               channelMessageId: message.channelMessageId,
               code: response.errorCode,
               error: 'Invalid region',
               phoneNumber: message.to
            },
            function: 'sendTwilioSms',
            message: 'twilio - error - 21408',
            tenantId: message.tenantId
         });
         result.message = 'Invalid region';

         return result;
      }

      if (response.errorCode === 21211) {
         context.info({
            data: {
               channelMessageId: message.channelMessageId,
               code: response.errorCode,
               error: response.errorMessage,
               phoneNumber: message.to
            },
            function: 'sendTwilioSms',
            message: 'twilio - error - 21211',
            tenantId: message.tenantId
         });
         result.message = response.errorMessage;
         result.disable = true;

         return result;
      }

      context.info({
         data: {
            channelMessageId: message.channelMessageId,
            code: response.errorCode,
            error: response.errorMessage,
            phoneNumber: message.to
         },
         function: 'sendTwilioSms',
         message: 'twilio - error - ${response.errorCode}',
         tenantId: message.tenantId
      });

      result.message = response.errorMessage;
      result.disable = true;

      return result;
   } catch (err) {
      const error = err as Error;

      context.info({
         data: { channelMessageId: message.channelMessageId, phoneNumber: message.to },
         function: 'sendTwilioSms',
         message: 'twilio - error - no code, ${error.message}',
         tenantId: message.tenantId
      });

      result.success = false;
      result.message = error.message;

      if (error.message.toLowerCase().includes('authentication error')) {
         result.recoverable = false;
      } else {
         result.recoverable = true;
      }

      return result;
   }
};
