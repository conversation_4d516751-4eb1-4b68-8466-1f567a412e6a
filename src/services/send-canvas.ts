import { InvocationContext } from '@azure/functions';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';
import { htmlToText } from 'html-to-text';

enum AuthorizationStatus {
   Error = 'ERROR',
   NetworkError = 'NETWORK_ERROR',
   Success = 'SUCCESS'
}

type Authorization = {
   error?: string;
   oauthExpires?: number;
   oauthToken?: string;
   status: AuthorizationStatus;
};

const tokens: Map<string, Authorization> = new Map<string, Authorization>();

const refreshToken = async (
   message: ServiceMessage,
   hostname: string,
   clientSecret: string,
   token: string,
   context: InvocationContext
): Promise<Authorization> => {
   const clientId = message.attributes.find((a) => a.name === AttributeConstants.CLIENT_ID)?.value ?? '';

   context.debug({
      data: { clientId, hostname },
      function: 'sendCanvas',
      message: 'sendCanvas - refreshToken',
      tenantId: message.tenantId
   });

   // eslint-disable-next-line max-len
   const url = `https://${hostname}/login/oauth2/token?grant_type=refresh_token&client_id=${clientId}&client_secret=${clientSecret}&refresh_token=${token}`;

   try {
      const response = await fetch(url, {
         method: 'POST'
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const json = await response.json();

      if (response.status >= 400) {
         return { error: JSON.stringify(json), status: AuthorizationStatus.Error };
      }

      const authorization: Authorization = {
         oauthExpires: Date.now() + 50 * 60 * 1000,
         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
         oauthToken: json.access_token,
         status: AuthorizationStatus.Success
      };

      tokens.set(message.tenantId, authorization);

      return authorization;
   } catch (error) {
      let errorMessage = '';

      if (error instanceof Error) {
         errorMessage = error.message;

         // Type guard to check if error has cause property
         if ('cause' in error && error.cause && typeof error.cause === 'object') {
            const cause = error.cause as { code?: string; message?: string };

            if (cause.message || cause.code) {
               errorMessage = `${errorMessage}:${cause.message ?? ''}:${cause.code ?? ''}`;
            }
         }
      } else {
         // Handle case where error is not an Error object
         errorMessage = String(error);
      }

      context.info({
         data: { clientId, hostname },
         function: 'sendCanvas - refreshToken',
         message: errorMessage,
         tenantId: message.tenantId
      });

      return { error: errorMessage, status: AuthorizationStatus.NetworkError };
   }
};

export const sendCanvas = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const hostname = message.attributes.find((a) => a.name === AttributeConstants.HOSTNAME)?.value ?? '';
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);

   const clientSecret = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.CLIENT_SECRET}`
   );
   const token = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.REFRESH_TOKEN}`
   );

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!clientSecret || !token) {
      result.success = false;
      result.message = 'Canvas client secret or refresh token not found';

      return result;
   }

   try {
      let authorization: Authorization | undefined = tokens.get(message.tenantId);

      if (!authorization || (authorization.oauthExpires && authorization.oauthExpires < Date.now())) {
         authorization = await refreshToken(message, hostname, clientSecret, token, context);

         if (authorization.status === AuthorizationStatus.Error) {
            result.message = authorization.error ?? '';

            return result;
         }

         if (authorization.status === AuthorizationStatus.NetworkError) {
            result.recoverable = true;
            result.message = authorization.error ?? '';

            return result;
         }
      }

      const url = `https://${hostname}/api/v1/conversations`;

      const body = {
         body: htmlToText(message.text),
         // eslint-disable-next-line camelcase
         force_new: true,
         mode: 'sync',
         recipients: [message.to],
         scope: 'unread',
         subject: message.title
      };

      const response = await fetch(url, {
         body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${authorization.oauthToken}`,
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const json = await response.json();

      context.trace({
         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
         data: { canvasResponse: json },
         function: 'sendCanvas',
         message: 'sendCanvas - response',
         tenantId: message.tenantId
      });

      if (response.status >= 400) {
         result.message = JSON.stringify(json);

         return result;
      }

      result.success = true;

      return result;
   } catch (error) {
      let errorMessage = '';

      if (error instanceof Error) {
         errorMessage = error.message;
      } else {
         errorMessage = String(error);
      }

      result.message = errorMessage;

      context.error({
         channelMessageId: message.channelMessageId,
         function: 'sendCanvas',
         message: errorMessage,
         tenantId: message.tenantId
      });

      return result;
   }
};
