import { InvocationContext } from '@azure/functions';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';

type AttributeRule = {
   key: string;
   operator: 'string_equals';
   value: string;
};

type GroupRule = {
   key: string;
   operator: 'array_any';
   value: string[];
};

type ModoRequest = {
   personal_message: {
      banner?: string;
      body: string;
      end_at?: string;
      filter: {
         operator: 'all';
         rules?: [AttributeRule | GroupRule];
      };

      notifications: {
         push: boolean;
      };
      start_at?: string;
      style: string;
      title: string;
   };
};

// eslint-disable-next-line complexity
export const sendModo = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const baseUrl = 'https://communicate.modolabs.net/api/';
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);

   const authorization = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.MODO_AUTHORIZATION}`
   );

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!authorization) {
      result.success = false;
      result.message = 'Modo authorization not found';

      return result;
   }

   const application: string =
      message.attributes.find((a) => a.name === AttributeConstants.MODO_APPLICATION_ID)?.value ?? '';
   const target: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_TARGET)?.value ?? '';
   const channel: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_CHANNEL)?.value ?? '';
   const banner: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_BANNER)?.value ?? '';
   const startAt: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_START_AT)?.value ?? '';
   const endAt: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_END_AT)?.value ?? '';
   const style: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_STYLE)?.value ?? '';
   const push: boolean = message.attributes.find((a) => a.name === AttributeConstants.MODO_PUSH)?.value === 'true';
   const filter: string = message.attributes.find((a) => a.name === AttributeConstants.MODO_FILTER_KEY)?.value ?? '';
   const group: string =
      message.attributes.find((a) => a.name === AttributeConstants.MODO_GROUP_ATTRIBUTE)?.value ?? '';

   const url = `${baseUrl}applications/${application}/${target}/personal/channels/${channel}/messages`;
   const title = message.title.length > 120 ? `${message.title.substring(0, 117)}...` : message.title;
   const isRecipientMessage: boolean = message.isRecipientMessage;

   const body: ModoRequest = {
      // eslint-disable-next-line camelcase
      personal_message: {
         body: message.text,
         filter: {
            operator: 'all'
         },
         notifications: {
            push
         },
         style,
         title
      }
   };

   if (banner) {
      body.personal_message.banner = banner;
   }

   if (startAt) {
      // eslint-disable-next-line camelcase
      body.personal_message.start_at = startAt;
   }

   if (endAt) {
      // eslint-disable-next-line camelcase
      body.personal_message.end_at = endAt;
   }

   if (isRecipientMessage) {
      body.personal_message.filter.rules = [
         {
            key: filter,
            operator: 'string_equals',
            value: message.to
         }
      ];
   } else {
      body.personal_message.filter.rules = [
         {
            key: group,
            operator: 'array_any',
            value: [message.to]
         }
      ];
   }

   context.trace({
      data: { ...body, channelMessageId: message.channelMessageId, message: '[REDACTED]' },
      function: 'sendModo',
      message: 'sendModo - sending',
      tenantId: message.tenantId
   });

   try {
      const response = await fetch(url, {
         body: JSON.stringify(body),
         headers: {
            Accept: 'application/vnd.modo.communicate.v2',
            Authorization: `Bearer ${authorization}`,
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      const text = await response.text();
      let json: { data: { id: string } } = { data: { id: '' } };

      try {
         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
         json = JSON.parse(text);
      } catch {
         // skip
      }

      context.debug({
         data: { channelMessageId: message.channelMessageId, response: text },
         function: 'sendModo',
         message: 'sendModo - response',
         tenantId: message.tenantId
      });

      if (response.status >= 400) {
         result.message = text;

         return result;
      }

      result.transactionId = json.data.id;
      // eslint-disable-next-line max-len
      const submitUrl = `${baseUrl}applications/${application}/${target}/personal/messages/${result.transactionId}/dispatch`;

      context.trace({
         data: { channelMessageId: message.channelMessageId, modoUrl: submitUrl },
         function: 'sendModo',
         message: 'sendModo - submitting',
         tenantId: message.tenantId
      });

      const submitResponse = await fetch(submitUrl, {
         headers: {
            Accept: 'application/vnd.modo.communicate.v2',
            Authorization: `Bearer ${authorization}`,
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      const submitText = await submitResponse.text();

      context.debug({
         data: { channelMessageId: message.channelMessageId, response: submitText },
         function: 'sendModo',
         message: 'sendModo - response',
         tenantId: message.tenantId
      });

      if (submitResponse.status >= 400) {
         result.message = submitText;

         return result;
      } else {
         result.success = true;
      }

      return result;
   } catch (err) {
      const error = err as Error;

      context.info({
         channelMessageId: message.channelMessageId,
         function: 'sendModo',
         message: error.message,
         tenantId: message.tenantId
      });

      result.message = error.message;

      if (error.name === 'TimeoutError') {
         result.recoverable = true;
      }

      return result;
   }
};
