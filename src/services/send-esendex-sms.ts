import { InvocationContext } from '@azure/functions';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';

const url = 'https://messaging.esendex.us/Messaging.svc/SendMessage';

const Errors: Record<number, string> = {
   400: 'The number provided is invalid, wrong format, or it cannot send to the destination. (InvalidDID)',
   401: 'The provided license key is invalid. (InvalidLicenseKey)',
   402: 'The account has been suspended. (AccountSuspended)',
   403: 'No messages can be sent to the destination phone number from the license key. (StopOnPhoneNumber)',
   404: 'Proper number could not be established or MessageID provided is invalid. (MissingDIDOrInvalidMessageID)',
   405: 'ScheduledDateTime not in UTC. (InvalidDateTime)',
   406: 'Destination phone number invalid. (InvalidDestination)',
   411: 'No message body provided. (NoMessage)',
   413: 'Message is too long. (MessageExceedsLimit)',
   506: 'Message failed to submit to provider. (Failed)',
   507: 'Message cancelled. (Cancelled)',
   508: 'Message submitted to provider but failed by delivery receipt. (FailedByProvider)'
};

export const sendEsendexSms = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);
   const secretId = `${message.tenantId}-${message.channelId}-${AttributeConstants.ESENDEX_KEY}`;
   const key = await secretVault.getSecret(secretId);

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!key) {
      result.success = false;
      result.message = 'Esendex key not found';

      return result;
   }

   const to = message.to.replace('+', '');
   const from = message.from.replace('+', '');
   const data = {
      Body: message.text,
      Concatenate: true,
      From: from,
      IsUnicode: true,
      LicenseKey: key,
      To: [to],
      UseMMS: false
   };

   // remove license key from logs
   // eslint-disable-next-line no-unused-vars
   const { LicenseKey, Body, ...logData } = data;

   context.debug({
      data: { channelMessageId: message.channelMessageId, logData },
      function: 'sendEsendexSms',
      message: 'sendEsendexSms - sending',
      tenantId: message.tenantId
   });

   let response: Response;

   try {
      response = await fetch(url, {
         body: JSON.stringify(data),
         headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      const text = await response.text();
      context.debug({
         channelMessageId: message.channelMessageId,
         data: text,
         function: 'sendEsendexSms',
         message: 'sendEsendexSms - response',
         tenantId: message.tenantId
      });

      // bad request, or backend server issue, retry backend errors
      if (response.status >= 400) {
         result.message = `status:${response.status}: response: ${text}`;

         if (response.status >= 500) {
            result.recoverable = true;
            // don't retry backend errors that often
            result.data.retries = result.data.retries + 5;
         }

         context.info({
            channelMessageId: message.channelMessageId,
            code: response.status,
            data: result.message,
            function: 'sendEsendexSms',
            message: `status:${response.status}: response: ${text}`,
            tenantId: message.tenantId
         });

         return result;
      }

      // TODO: test this
      const responseData = JSON.parse(text) as Array<{ MessageID: string; MessageStatus: number }>;
      const messageStatus: number = responseData[0].MessageStatus;
      result.code = messageStatus.toString();

      if (messageStatus >= 400) {
         result.message = Errors[`${messageStatus}`];
         result.recoverable = false;

         if (messageStatus === 403) {
            result.disable = true;
         }

         return result;
      }

      result.success = true;
      result.transactionId = responseData[0].MessageID;
      result.message = 'Message sent successfully';
   } catch (err) {
      const error = err as Error;

      context.info({
         channelMessageId: message.channelMessageId,
         function: 'sendEsendexSms',
         message: error.message,
         tenantId: message.tenantId
      });

      result.message = error.message;

      if (error.name === 'TimeoutError') {
         result.recoverable = true;
      }
   }

   return result;
};
