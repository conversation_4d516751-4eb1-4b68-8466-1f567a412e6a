import { SmsClient } from '@azure/communication-sms';
import { InvocationContext } from '@azure/functions';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';

export const sendAzureSms = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);

   const connectionString = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.AZURE_SMS_CONNECTION}`
   );

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!connectionString) {
      result.success = false;
      result.message = 'AZURE SMS connection string not found';

      return result;
   }

   const toSend = {
      from: message.from,
      message: message.text,
      to: [message.to]
   };

   context.debug({
      channelMessageID: message.channelMessageId,
      data: { ...toSend, message: '[REDACTED]' },
      function: 'sendAzureSms',
      message: 'sendAzureSms - sending',
      tenantId: message.tenantId
   });

   try {
      const smsClient = new SmsClient(connectionString);

      const sendResults = await smsClient.send(toSend, {
         enableDeliveryReport: true
      });

      const response = sendResults[0];

      context.debug({
         channelMessageID: message.channelMessageId,
         data: response,
         function: 'sendAzureSms',
         message: 'sendAzureSms - response',
         tenantId: message.tenantId
      });

      if (response.successful) {
         result.success = true;
         result.transactionId = response.messageId ?? '';
      } else {
         result.success = false;
         result.message = response.errorMessage ?? '';
      }

      return result;
   } catch (err) {
      const error = err as Error;
      context.info({
         channelMessageID: message.channelMessageId,
         function: 'sendAzureSms',
         message: error.message,
         tenantId: message.tenantId
      });

      result.success = false;
      result.message = error.message;
      result.recoverable = true;

      return result;
   }
};
