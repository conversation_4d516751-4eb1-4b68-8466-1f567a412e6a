import { InvocationContext } from '@azure/functions';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';
import {
   CloudAdapter,
   ConfigurationBotFrameworkAuthentication,
   ConfigurationBotFrameworkAuthenticationOptions,
   ConfigurationServiceClientCredentialFactory
} from 'botbuilder';
import { Activity, ActivityFactory, CardFactory } from 'botbuilder-core';
import TurndownService from 'turndown';

const turndownService = new TurndownService();

const severityToColor = (severity: string): string => {
   switch (severity) {
      case '1':
         return 'Accent';
         break;
      case '5':
         return 'Warning';
      case '9':
         return 'Attention';
      default:
         return 'Default';
   }
};

export const sendTeams = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   // const serviceUrl = 'https://smba.trafficmanager.net/amer/';
   const serviceUrl = 'https://smba.trafficmanager.net/teams/';
   const botId = process.env.TEAMS_BOT_ID ?? '';
   const password = process.env.TEAMS_BOT_PASSWORD ?? '';

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!botId || !password) {
      result.success = false;
      result.message = 'Teams bot id or password not found';

      return result;
   }

   try {
      const credentialsFactory = new ConfigurationServiceClientCredentialFactory({
         MicrosoftAppId: botId,
         MicrosoftAppPassword: password,
         MicrosoftAppType: 'MultiTenant'
      });

      const botFrameworkAuthentication = new ConfigurationBotFrameworkAuthentication(
         {} as ConfigurationBotFrameworkAuthenticationOptions,
         credentialsFactory
      );

      const botAdapter = new CloudAdapter(botFrameworkAuthentication);
      const conversationReference: Partial<Activity> = ActivityFactory.fromObject({});
      conversationReference.serviceUrl = serviceUrl;

      conversationReference.conversation = {
         conversationType: 'personal',
         id: message.conversationId,
         isGroup: false,
         name: ''
      };

      let toSendMessage = message.text;

      if(!toSendMessage.startsWith('<')) {
         toSendMessage = toSendMessage.replaceAll('\n', '<br>');
      }

      toSendMessage = turndownService.turndown(toSendMessage);

      let card: object | undefined;

      if (message.title) {
         card = {
            $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
            body: [
               {
                  color: severityToColor(message.importance),
                  size: 'Medium',
                  text: turndownService.turndown(message.title),
                  type: 'TextBlock',
                  weight: 'Bolder',
                  wrap: true
               },
               {
                  separator: true,
                  text: toSendMessage,
                  type: 'TextBlock',
                  wrap: true
               }
            ],
            type: 'AdaptiveCard',
            version: '1.6'
         };
      }

      conversationReference.type = 'message';

      context.debug({
         channelMessageId: message.channelMessageId,
         function: 'sendTeams',
         message: 'sendTeams - sending',
         tenantId: message.tenantId
      });

      try {
         await botAdapter.continueConversationAsync(botId, conversationReference, async (turnContext) => {
            if (card) {
               await turnContext.sendActivity({ attachments: [CardFactory.adaptiveCard(card)] });
            } else {
               await turnContext.sendActivity(toSendMessage);
            }
         });
      } catch (err) {
         const error = err as Error;

         context.info({
            channelMessageId: message.channelMessageId,
            function: 'sendTeams',
            message: error.message,
            tenantId: message.tenantId
         });

         result.message = error.message;

         return result;
      }
   } catch (err) {
      const error = err as Error;

      context.info({
         channelMessageId: message.channelMessageId,
         function: 'sendTeams',
         message: error.message,
         tenantId: message.tenantId
      });

      result.message = error.message;

      return result;
   }

   result.success = true;

   return result;
};
