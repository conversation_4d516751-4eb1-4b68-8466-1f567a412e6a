import { InvocationContext } from '@azure/functions';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';
import TurndownService from 'turndown';

const turndownService = new TurndownService();

type Attachment = {
   color?: string;
   text?: string;
   title?: string;
};

const severityToColor = (severity: string): string => {
   let color: string;

   switch (severity) {
      case '1':
         color = '#008000';
         break;

      case '5':
         color = '#fff200';
         break;

      case '9':
         color = '#ba3f38';
         break;
      default:
         color = '#000000';
   }

   return color;
};

export const sendSlackWebhook = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   const attachment: Attachment = {
      color: severityToColor(message.importance),
      text: turndownService.turndown(message.text),
      title: message.title
   };

   context.debug({
      data: { channelMessageId: message.channelMessageId, ...attachment, text: '[REDACTED]' },
      function: 'sendSlackWebhook',
      message: 'slack-webhook - sending',
      tenantId: message.tenantId
   });

   try {
      const response = await fetch(message.webhook, {
         body: JSON.stringify({ attachments: [attachment] }),
         headers: {
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      const responseText = await response.text();

      context.debug({
         data: { channelMessageId: message.channelMessageId, response: responseText },
         function: 'slack-webhook',
         message: 'sendSlackWebhook - response',
         tenantId: message.tenantId
      });

      if (response.status >= 400) {
         result.message = responseText;
      } else {
         result.success = true;
         const transactionId = response.headers.get(AttributeConstants.X_SLACK_UNIQUE_ID);

         if (transactionId) {
            result.transactionId = transactionId;
         }
      }

      return result;
   } catch (err) {
      const error = err as Error;

      context.info({
         channelMessageId: message.channelMessageId,
         function: 'slack-webhook',
         message: error.message,
         tenantId: message.tenantId
      });

      result.message = error.message;

      if (error.name === 'TimeoutError') {
         result.recoverable = true;
      }

      return result;
   }
};
