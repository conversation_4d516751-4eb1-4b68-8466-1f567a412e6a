import { InvocationContext } from '@azure/functions';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';

export const sendTest = (message: ServiceMessage, context: InvocationContext): ServiceResult => {
   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   context.debug({
      channelMessageId: message.channelMessageId,
      data: message,
      function: 'sendTest',
      message: 'send-test - sending',
      tenantId: message.tenantId
   });

   if (message.attributes.find((a) => a.name === 'fail') && message.retries < 2) {
      context.error({
         channelMessageId: message.channelMessageId,
         message: 'simulated fail',
         name: 'send-test',
         type: 'channel'
      });
      result.message = 'simulated fail, retrying...';
      result.recoverable = true;

      return result;
   }

   result.success = true;

   return result;
};
