import { InvocationContext } from '@azure/functions';
import { AttributeConstants, ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';
import TurndownService from 'turndown';

const turndownService = new TurndownService();

type MessageCard = {
   '@context': string;
   '@type': string;
   text?: string;
   themeColor?: string;
   title?: string;
};

const severityToColor = (severity: string): string => {
   let color: string;

   switch (severity) {
      case '1':
         color = '#008000';
         break;

      case '5':
         color = '#fff200';
         break;

      case '9':
         color = '#ba3f38';
         break;
      default:
         color = '#000000';
   }

   return color;
};

export const sendTeamsWebhook = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const messageCard: MessageCard = {
      '@context': 'https://schema.org/extensions',
      '@type': 'MessageCard'
   };

   messageCard.themeColor = severityToColor(message.importance);
   messageCard.title = message.title;
   messageCard.text = turndownService.turndown(message.text);

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   context.debug({
      data: { channelMessageId: message.channelMessageId, ...messageCard, message: '[REDACTED]' },
      function: 'sendTeamsWebhook',
      message: 'teams-webhook - sending',
      tenantId: message.tenantId
   });

   try {
      const response = await fetch(message.webhook, {
         body: JSON.stringify(messageCard),
         headers: {
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      const responseText = await response.text();
      result.transactionId = response.headers.get(AttributeConstants.REQUEST_ID) ?? '';

      context.debug({
         data: { channelMessageId: message.channelMessageId, response: responseText },
         function: 'sendTeamsWebhook',
         message: 'teams-webhook - response',
         tenantId: message.tenantId
      });

      if (response.status >= 400) {
         result.message = responseText;
      } else {
         result.success = true;
      }

      return result;
   } catch (err) {
      const error = err as Error;

      context.info({
         channelMessageId: message.channelMessageId,
         function: 'sendTeamsWebhook',
         message: error.message,
         tenantId: message.tenantId
      });

      result.message = error.message;

      if (error.name === 'TimeoutError') {
         result.recoverable = true;
      }

      return result;
   }
};
