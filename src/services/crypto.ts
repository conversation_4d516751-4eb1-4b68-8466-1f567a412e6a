import { createC<PERSON>her<PERSON>, createDecipheriv, createHash, randomBytes } from 'crypto';
import { LRUCache } from 'lru-cache';

import { SecretVault, SecretVaultFactory} from '../secrets/index.js';
import { base64urlDecode, base64urlEncode } from '../utils/base64-url.js';

/**
 * CryptoService provides encryption and decryption functionality
 * using AES-256-GCM with tenant-specific encryption keys.
 */
export class CryptoService {
   private _keyCache: LRUCache<string, Buffer>;
   private _secretVault: SecretVault;
   private static _instance: CryptoService | null = null;

   /**
    * Creates a new CryptoService instance
    */
   constructor() {
      this._keyCache = new LRUCache<string, Buffer>({
         max: 500
      });
      this._secretVault = SecretVaultFactory.getSecretVault(true);
   }

   /**
    * Gets the singleton instance of CryptoService
    *
    * @returns The singleton instance
    */
   public static getInstance(): CryptoService {
      CryptoService._instance ??= new CryptoService();

      return CryptoService._instance;
   }

   /**
    * Retrieves an encryption key for the specified tenant and key ID
    *
    * @param tenantId The tenant identifier
    * @param keyId The key identifier
    * @returns The encryption key as a Buffer
    * @throws Error if the key is not found
    */
   private async _retrieveKey(tenantId: string, keyId: string): Promise<Buffer> {
      const keyName = `${tenantId}-${keyId}-encryption-key`;

      if (this._keyCache.has(keyName)) {
         return this._keyCache.get(keyName)!;
      }

      const encodedKey: string = await this._secretVault.getSecret(keyName);

      if (!encodedKey) {
         throw new Error(`Key not found: ${keyName}`);
      }

      const key = createHash('sha256').update(encodedKey).digest();
      this._keyCache.set(keyName, key);

      return key;
   }

   /**
    * Encrypts a plaintext string using AES-256-GCM
    *
    * @param tenantId The tenant identifier
    * @param keyId The key identifier to use for encryption
    * @param plainText The text to encrypt
    * @returns Encrypted data in the format keyId.iv.ciphertext.tag
    */
   public async encrypt(tenantId: string, keyId: string, plainText: string): Promise<string> {
      const key = await this._retrieveKey(tenantId, keyId);
      const iv = randomBytes(12);
      const cipher = createCipheriv('aes-256-gcm', key, iv);

      const ciphertext = Buffer.concat([
         cipher.update(plainText, 'utf8'),
         cipher.final()
      ]);

      const tag = cipher.getAuthTag();

      const ivString = base64urlEncode(iv);
      const ciphertextString = base64urlEncode(ciphertext);
      const tagString = base64urlEncode(tag);

      return `${keyId}.${ivString}.${ciphertextString}.${tagString}`;
   }

   /**
    * Decrypts an encrypted string using AES-256-GCM
    *
    * @param tenantId The tenant identifier
    * @param encryptedData The encrypted data in the format keyId.iv.ciphertext.tag
    * @returns The decrypted plaintext
    */
   public async decrypt(tenantId: string, encryptedData: string): Promise<string> {
      const [keyId, ivString, ciphertextString, tagString] = encryptedData.split('.');
      const key = await this._retrieveKey(tenantId, keyId);
      const iv = base64urlDecode(ivString);
      const ciphertext = base64urlDecode(ciphertextString);
      const tag = base64urlDecode(tagString);

      const decipher = createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(tag);

      const decrypted = Buffer.concat([
         decipher.update(ciphertext),
         decipher.final()
      ]);

      return decrypted.toString('utf8');
   }
}
