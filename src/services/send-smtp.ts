import { InvocationContext } from '@azure/functions';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';
import nodemailer from 'nodemailer';

// Define the SMTP error interface
interface SMTPError extends Error {
   code?: string;
}

// Type guard function to check if error is SMTPError
function isSMTPError(error: unknown): error is SMTPError {
   return error instanceof Error && 'code' in error;
}

export const sendSmtp = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);

   const password = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.SMTP_PASSWORD}`
   );

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!password) {
      result.success = false;
      result.message = 'SMTP password not found';

      return result;
   }

   const host = message.attributes.find((a) => a.name === AttributeConstants.SMTP_HOST)?.value;

   if (!host) {
      result.success = false;
      result.message = 'SMTP host not found';

      return result;
   }

   const username = message.attributes.find((a) => a.name === AttributeConstants.SMTP_USER)?.value;

   if (!username) {
      result.success = false;
      result.message = 'SMTP username not found';

      return result;
   }

   const portValue = message.attributes.find((a) => a.name === AttributeConstants.SMTP_PORT)?.value;
   const tlsValue = message.attributes.find((a) => a.name === AttributeConstants.SMTP_SECURE)?.value;

   const port = portValue ? Number(portValue) : 587;
   const tls = tlsValue ? tlsValue.toString().toLowerCase() === 'true' : false;

   try {
      const transporter: nodemailer.Transporter = nodemailer.createTransport({
         auth: {
            pass: password,
            user: username
         },
         host,
         port,
         secure: tls,
         tls: {
            rejectUnauthorized: false
         }
      });

      const smtpMessage = {
         attachDataUrls: true,
         from: message.from,
         html: message.text,
         subject: message.title,
         to: message.to
      };

      context.debug({
         data: { channelMessageId: message.channelMessageId, ...smtpMessage, html: '[REDACTED]' },
         function: 'sendSmtp',
         message: 'sendSmtp - sending',
         tenantId: message.tenantId
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const response = await transporter.sendMail(smtpMessage);
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
      const transactionId: string | undefined = response.messageId;

      if (transactionId) {
         result.transactionId = transactionId;
      }

      result.success = true;

      return result;
   } catch (error: unknown) {
      let code = '';

      if (isSMTPError(error)) {
         code = error.code ?? '';

         switch (error.code) {
            case 'ECONNECTION':
            case 'ETIMEDOUT':
            case 'ESOCKET':
               result.recoverable = true;
               break;
            case undefined:
               break;
         }
      }

      result.message = error instanceof Error ? error.message : 'An unknown error occurred';

      if (code) {
         result.code = code;
      }

      context.info({
         channelMessageId: message.channelMessageId,
         function: 'sendSmtp',
         message: `code: ${code}, message: ${result.message}`,
         tenantId: message.tenantId
      });

      return result;
   }
};
