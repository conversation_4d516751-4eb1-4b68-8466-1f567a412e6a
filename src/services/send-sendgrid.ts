import { InvocationContext } from '@azure/functions';
import ClientResponse from '@sendgrid/helpers/classes/response.js';
import ResponseError from '@sendgrid/helpers/classes/response-error.js';
import MailService from '@sendgrid/mail';
import { SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { AttributeConstants } from '@x-signal-inc/messaging-common';
import { ServiceMessage, ServiceResult } from '@x-signal-inc/messaging-common';

export const sendSendGrid = async (message: ServiceMessage, context: InvocationContext): Promise<ServiceResult> => {
   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);

   const key = await secretVault.getSecret(
      `${message.tenantId}-${message.channelId}-${AttributeConstants.SENDGRID_KEY}`
   );

   const result: ServiceResult = {
      code: '',
      data: message,
      disable: false,
      message: '',
      recoverable: false,
      sentAt: new Date(),
      success: false,
      transactionId: ''
   };

   if (!key) {
      result.success = false;
      result.message = 'SendGrid key not found';

      return result;
   }

   const toSend = {
      from: message.from,
      html: message.text,
      subject: message.title,
      to: message.to
   };

   context.debug({
      data: { ...toSend, channelMessageId: message.channelMessageId, html: '[REDACTED]' },
      function: 'sendSendGrid',
      message: 'sendgrid - sending',
      tenantId: message.tenantId
   });

   try {
      const sender = MailService;
      sender.setApiKey(key);
      const response: ClientResponse = (await sender.send(toSend))[0];

      context.debug({
         data: { channelMessageId: message.channelMessageId, response },
         function: 'sendSendGrid',
         message: 'sendgrid - response',
         tenantId: message.tenantId
      });

      if (response.headers) {
         // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
         result.transactionId = response.headers['x-message-id'];
      }

      result.success = true;

      return result;
   } catch (err) {
      const error = err as Error | ResponseError;

      context.info({
         data: { channelMessageId: message.channelMessageId, email: message.to },
         function: 'sendSendGrid',
         message: error.message,
         tenantId: message.tenantId
      });

      if (error instanceof ResponseError) {
         const responseError = error;
         result.success = false;
         result.code = responseError.code.toString() || '';

         result.message = `${responseError.code}:${error.message}:${JSON.stringify(responseError.response.body)}`;
         result.recoverable = false;

         return result;
      }

      result.success = false;
      result.message = error.message;

      if (error.message.toLowerCase().includes('authentication error')) {
         result.recoverable = false;
      } else {
         result.recoverable = true;
      }

      return result;
   }
};
