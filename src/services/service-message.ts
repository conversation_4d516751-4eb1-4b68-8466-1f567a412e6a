import { ServiceChannel } from '../services/index.js';

import { Status } from './status.js';

export type BlobText = {
   blob: string;
   container: string;
   url: string;
};

export type ChannelAttribute = {
   name: string;
   value: string;
};

export class ServiceMessage {
   tenantId = '';
   recipientId = '';
   messageId = '';
   channelMessageId = '';
   channelId = '';
   connectionId = '';
   logLevel = 'info';
   title = '';
   text = '';
   to = '';
   from = '';
   webhook = '';
   callbackUrl = '';
   conversationId = '';
   importance = '1';
   error = '';
   endpointIdentifier = '';
   deleteAfter = false;

   channel: ServiceChannel | null = null;
   blobText: BlobText | null = null;
   tableTextId: string | null = null;
   status: Status = Status.PROCESSING;
   attributes: ChannelAttribute[] = [];

   disable = false;
   isRecipientMessage = false;
   isTwoWay = false;
   retries = 0;

   constructor(partial?: Partial<ServiceMessage>) {
      if (partial) {
         Object.assign(this, partial);
      }
   }
}
