import { DefaultAzureCredential } from '@azure/identity';
import { ServiceBusClient, ServiceBusMessage } from '@azure/service-bus';

import { Queue } from './queue.js';

export class AzureServiceBus implements Queue {
   private _outgoingQueue: string;
   private _serviceBusClient: ServiceBusClient;

   constructor(queue: string, hostname: string) {
      this._outgoingQueue = queue;
      this._serviceBusClient = new ServiceBusClient(hostname, new DefaultAzureCredential());
   }

   async enqueue(message: object & { id?: string }, when: Date = new Date()): Promise<void> {
      const sender = this._serviceBusClient.createSender(this._outgoingQueue);
      const statusMessage: ServiceBusMessage = {
         body: message,
         contentType: 'application/json',
         scheduledEnqueueTimeUtc: when
      };

      if (message.id) {
         statusMessage.messageId = message.id;
      }

      try {
         await sender.sendMessages(statusMessage);
      } finally {
         await sender.close();
      }
   }
}
