import { AzureServiceBus } from './azure-service-bus.js';
import { Queue } from './queue.js';

type QueueType = 'AZURE_SERVICE_BUS' | 'TEST';

class TestQueue implements Queue {
   // eslint-disable-next-line @typescript-eslint/require-await
   async enqueue(message: object & { id?: string }): Promise<void> {
      console.info('fake queue, queued - ', message.id);
   }
}

export class QueueFactory {
   private static _queues: Map<string, Queue> = new Map<string, Queue>();

   static getQueue(name: string, hostname: string, queue: string): Queue {
      if (this._queues.has(name)) {
         return this._queues.get(name)!;
      }

      const queueType = (process.env.QUEUE_TYPE ?? 'TEST') as QueueType;

      switch (queueType.toLocaleUpperCase()) {
         case 'AZURE_SERVICE_BUS': {
            const serviceBus = new AzureServiceBus(queue, hostname);
            this._queues.set(name, serviceBus);

            return serviceBus;
         }

         case 'TEST': {
            const testQueue = new TestQueue();
            this._queues.set(name, testQueue);

            return testQueue;
         }

         default:
            throw new Error(`Unsupported queue type: ${process.env.QUEUE_TYPE}`);
      }
   }
}
