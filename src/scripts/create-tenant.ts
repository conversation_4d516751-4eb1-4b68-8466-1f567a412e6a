#!/usr/bin/env node
/* eslint-disable simple-import-sort/imports */
/* eslint-disable no-console */

import { exit } from 'process';
import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';
import { v7 as uuid7 } from 'uuid';

config();

void (async () => {
   const parameters: string[] = process.argv.slice(2);

   if (parameters.length < 1) {
      console.log('Usage: node create-tenant.js tenant-name');
      exit(1);
   }

   const tenantName = parameters[0].toLowerCase();
   const prisma = new PrismaClient();
   const tenant = await prisma.tenant.findUnique({ where: { name: tenantName.toLowerCase() } });

   if (tenant) {
      console.log(`A tenant ${tenant.id} already exists with that name`);
      exit(1);
   }

   const tenantId = uuid7();

   await prisma.tenant.create({
      data: {
         id: tenantId,
         name: tenantName
      }
   });

   console.log(`Tenant ${tenantName} created with id ${tenantId}`);
})();
