
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const Status = {
  AWAITING_APPROVAL: 'AWAITING_APPROVAL',
  APPROVAL_DENIED: 'APPROVAL_DENIED',
  ERROR: 'ERROR',
  PROCESSED: 'PROCESSED',
  PROCESSING: 'PROCESSING',
  RETRYING: 'RETRYING',
  SCHEDULED: 'SCHEDULED',
  SENT: 'SENT'
} as const

export type Status = (typeof Status)[keyof typeof Status]
