
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Connection` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Connection
 * 
 */
export type ConnectionModel = runtime.Types.Result.DefaultSelection<Prisma.$ConnectionPayload>

export type AggregateConnection = {
  _count: ConnectionCountAggregateOutputType | null
  _min: ConnectionMinAggregateOutputType | null
  _max: ConnectionMaxAggregateOutputType | null
}

export type ConnectionMinAggregateOutputType = {
  id: string | null
  recipientId: string | null
  service: string | null
  enabled: boolean | null
  showInPreferences: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  channelId: string | null
}

export type ConnectionMaxAggregateOutputType = {
  id: string | null
  recipientId: string | null
  service: string | null
  enabled: boolean | null
  showInPreferences: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  channelId: string | null
}

export type ConnectionCountAggregateOutputType = {
  id: number
  recipientId: number
  service: number
  enabled: number
  showInPreferences: number
  createdAt: number
  updatedAt: number
  channelId: number
  _all: number
}


export type ConnectionMinAggregateInputType = {
  id?: true
  recipientId?: true
  service?: true
  enabled?: true
  showInPreferences?: true
  createdAt?: true
  updatedAt?: true
  channelId?: true
}

export type ConnectionMaxAggregateInputType = {
  id?: true
  recipientId?: true
  service?: true
  enabled?: true
  showInPreferences?: true
  createdAt?: true
  updatedAt?: true
  channelId?: true
}

export type ConnectionCountAggregateInputType = {
  id?: true
  recipientId?: true
  service?: true
  enabled?: true
  showInPreferences?: true
  createdAt?: true
  updatedAt?: true
  channelId?: true
  _all?: true
}

export type ConnectionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Connection to aggregate.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Connections
  **/
  _count?: true | ConnectionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ConnectionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ConnectionMaxAggregateInputType
}

export type GetConnectionAggregateType<T extends ConnectionAggregateArgs> = {
      [P in keyof T & keyof AggregateConnection]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateConnection[P]>
    : Prisma.GetScalarType<T[P], AggregateConnection[P]>
}




export type ConnectionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithAggregationInput | Prisma.ConnectionOrderByWithAggregationInput[]
  by: Prisma.ConnectionScalarFieldEnum[] | Prisma.ConnectionScalarFieldEnum
  having?: Prisma.ConnectionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ConnectionCountAggregateInputType | true
  _min?: ConnectionMinAggregateInputType
  _max?: ConnectionMaxAggregateInputType
}

export type ConnectionGroupByOutputType = {
  id: string
  recipientId: string
  service: string
  enabled: boolean
  showInPreferences: boolean
  createdAt: Date
  updatedAt: Date
  channelId: string | null
  _count: ConnectionCountAggregateOutputType | null
  _min: ConnectionMinAggregateOutputType | null
  _max: ConnectionMaxAggregateOutputType | null
}

type GetConnectionGroupByPayload<T extends ConnectionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ConnectionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ConnectionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ConnectionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ConnectionGroupByOutputType[P]>
      }
    >
  > 



export type ConnectionWhereInput = {
  AND?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  OR?: Prisma.ConnectionWhereInput[]
  NOT?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  id?: Prisma.StringFilter<"Connection"> | string
  recipientId?: Prisma.StringFilter<"Connection"> | string
  service?: Prisma.StringFilter<"Connection"> | string
  enabled?: Prisma.BoolFilter<"Connection"> | boolean
  showInPreferences?: Prisma.BoolFilter<"Connection"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  channelId?: Prisma.StringNullableFilter<"Connection"> | string | null
  recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
  attributes?: Prisma.ConnectionAttributeListRelationFilter
  topicPreferences?: Prisma.TopicPreferenceListRelationFilter
  Channel?: Prisma.XOR<Prisma.ChannelNullableScalarRelationFilter, Prisma.ChannelWhereInput> | null
}

export type ConnectionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  showInPreferences?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  channelId?: Prisma.SortOrderInput | Prisma.SortOrder
  recipient?: Prisma.RecipientOrderByWithRelationInput
  attributes?: Prisma.ConnectionAttributeOrderByRelationAggregateInput
  topicPreferences?: Prisma.TopicPreferenceOrderByRelationAggregateInput
  Channel?: Prisma.ChannelOrderByWithRelationInput
}

export type ConnectionWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  idx_connection_recipient_service?: Prisma.ConnectionIdx_connection_recipient_serviceCompoundUniqueInput
  AND?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  OR?: Prisma.ConnectionWhereInput[]
  NOT?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  recipientId?: Prisma.StringFilter<"Connection"> | string
  service?: Prisma.StringFilter<"Connection"> | string
  enabled?: Prisma.BoolFilter<"Connection"> | boolean
  showInPreferences?: Prisma.BoolFilter<"Connection"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  channelId?: Prisma.StringNullableFilter<"Connection"> | string | null
  recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
  attributes?: Prisma.ConnectionAttributeListRelationFilter
  topicPreferences?: Prisma.TopicPreferenceListRelationFilter
  Channel?: Prisma.XOR<Prisma.ChannelNullableScalarRelationFilter, Prisma.ChannelWhereInput> | null
}, "id" | "idx_connection_recipient_service">

export type ConnectionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  showInPreferences?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  channelId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.ConnectionCountOrderByAggregateInput
  _max?: Prisma.ConnectionMaxOrderByAggregateInput
  _min?: Prisma.ConnectionMinOrderByAggregateInput
}

export type ConnectionScalarWhereWithAggregatesInput = {
  AND?: Prisma.ConnectionScalarWhereWithAggregatesInput | Prisma.ConnectionScalarWhereWithAggregatesInput[]
  OR?: Prisma.ConnectionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ConnectionScalarWhereWithAggregatesInput | Prisma.ConnectionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Connection"> | string
  recipientId?: Prisma.StringWithAggregatesFilter<"Connection"> | string
  service?: Prisma.StringWithAggregatesFilter<"Connection"> | string
  enabled?: Prisma.BoolWithAggregatesFilter<"Connection"> | boolean
  showInPreferences?: Prisma.BoolWithAggregatesFilter<"Connection"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Connection"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Connection"> | Date | string
  channelId?: Prisma.StringNullableWithAggregatesFilter<"Connection"> | string | null
}

export type ConnectionCreateInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipient: Prisma.RecipientCreateNestedOneWithoutConnectionsInput
  attributes?: Prisma.ConnectionAttributeCreateNestedManyWithoutConnectionInput
  topicPreferences?: Prisma.TopicPreferenceCreateNestedManyWithoutConnectionsInput
  Channel?: Prisma.ChannelCreateNestedOneWithoutConnectionInput
}

export type ConnectionUncheckedCreateInput = {
  id?: string
  recipientId: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelId?: string | null
  attributes?: Prisma.ConnectionAttributeUncheckedCreateNestedManyWithoutConnectionInput
  topicPreferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutConnectionsInput
}

export type ConnectionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.RecipientUpdateOneRequiredWithoutConnectionsNestedInput
  attributes?: Prisma.ConnectionAttributeUpdateManyWithoutConnectionNestedInput
  topicPreferences?: Prisma.TopicPreferenceUpdateManyWithoutConnectionsNestedInput
  Channel?: Prisma.ChannelUpdateOneWithoutConnectionNestedInput
}

export type ConnectionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  attributes?: Prisma.ConnectionAttributeUncheckedUpdateManyWithoutConnectionNestedInput
  topicPreferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutConnectionsNestedInput
}

export type ConnectionCreateManyInput = {
  id?: string
  recipientId: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelId?: string | null
}

export type ConnectionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type ConnectionListRelationFilter = {
  every?: Prisma.ConnectionWhereInput
  some?: Prisma.ConnectionWhereInput
  none?: Prisma.ConnectionWhereInput
}

export type ConnectionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ConnectionScalarRelationFilter = {
  is?: Prisma.ConnectionWhereInput
  isNot?: Prisma.ConnectionWhereInput
}

export type ConnectionIdx_connection_recipient_serviceCompoundUniqueInput = {
  recipientId: string
  service: string
}

export type ConnectionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  showInPreferences?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
}

export type ConnectionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  showInPreferences?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
}

export type ConnectionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  showInPreferences?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
}

export type ConnectionCreateNestedManyWithoutChannelInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutChannelInput, Prisma.ConnectionUncheckedCreateWithoutChannelInput> | Prisma.ConnectionCreateWithoutChannelInput[] | Prisma.ConnectionUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutChannelInput | Prisma.ConnectionCreateOrConnectWithoutChannelInput[]
  createMany?: Prisma.ConnectionCreateManyChannelInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUncheckedCreateNestedManyWithoutChannelInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutChannelInput, Prisma.ConnectionUncheckedCreateWithoutChannelInput> | Prisma.ConnectionCreateWithoutChannelInput[] | Prisma.ConnectionUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutChannelInput | Prisma.ConnectionCreateOrConnectWithoutChannelInput[]
  createMany?: Prisma.ConnectionCreateManyChannelInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUpdateManyWithoutChannelNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutChannelInput, Prisma.ConnectionUncheckedCreateWithoutChannelInput> | Prisma.ConnectionCreateWithoutChannelInput[] | Prisma.ConnectionUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutChannelInput | Prisma.ConnectionCreateOrConnectWithoutChannelInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutChannelInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutChannelInput[]
  createMany?: Prisma.ConnectionCreateManyChannelInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutChannelInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutChannelInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutChannelInput | Prisma.ConnectionUpdateManyWithWhereWithoutChannelInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionUncheckedUpdateManyWithoutChannelNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutChannelInput, Prisma.ConnectionUncheckedCreateWithoutChannelInput> | Prisma.ConnectionCreateWithoutChannelInput[] | Prisma.ConnectionUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutChannelInput | Prisma.ConnectionCreateOrConnectWithoutChannelInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutChannelInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutChannelInput[]
  createMany?: Prisma.ConnectionCreateManyChannelInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutChannelInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutChannelInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutChannelInput | Prisma.ConnectionUpdateManyWithWhereWithoutChannelInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionCreateNestedOneWithoutAttributesInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutAttributesInput, Prisma.ConnectionUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutAttributesInput
  connect?: Prisma.ConnectionWhereUniqueInput
}

export type ConnectionUpdateOneRequiredWithoutAttributesNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutAttributesInput, Prisma.ConnectionUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutAttributesInput
  upsert?: Prisma.ConnectionUpsertWithoutAttributesInput
  connect?: Prisma.ConnectionWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ConnectionUpdateToOneWithWhereWithoutAttributesInput, Prisma.ConnectionUpdateWithoutAttributesInput>, Prisma.ConnectionUncheckedUpdateWithoutAttributesInput>
}

export type ConnectionCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRecipientInput, Prisma.ConnectionUncheckedCreateWithoutRecipientInput> | Prisma.ConnectionCreateWithoutRecipientInput[] | Prisma.ConnectionUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRecipientInput | Prisma.ConnectionCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.ConnectionCreateManyRecipientInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUncheckedCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRecipientInput, Prisma.ConnectionUncheckedCreateWithoutRecipientInput> | Prisma.ConnectionCreateWithoutRecipientInput[] | Prisma.ConnectionUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRecipientInput | Prisma.ConnectionCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.ConnectionCreateManyRecipientInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRecipientInput, Prisma.ConnectionUncheckedCreateWithoutRecipientInput> | Prisma.ConnectionCreateWithoutRecipientInput[] | Prisma.ConnectionUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRecipientInput | Prisma.ConnectionCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutRecipientInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.ConnectionCreateManyRecipientInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutRecipientInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutRecipientInput | Prisma.ConnectionUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionUncheckedUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRecipientInput, Prisma.ConnectionUncheckedCreateWithoutRecipientInput> | Prisma.ConnectionCreateWithoutRecipientInput[] | Prisma.ConnectionUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRecipientInput | Prisma.ConnectionCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutRecipientInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.ConnectionCreateManyRecipientInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutRecipientInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutRecipientInput | Prisma.ConnectionUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionCreateNestedManyWithoutTopicPreferencesInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput> | Prisma.ConnectionCreateWithoutTopicPreferencesInput[] | Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput | Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUncheckedCreateNestedManyWithoutTopicPreferencesInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput> | Prisma.ConnectionCreateWithoutTopicPreferencesInput[] | Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput | Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUpdateManyWithoutTopicPreferencesNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput> | Prisma.ConnectionCreateWithoutTopicPreferencesInput[] | Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput | Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutTopicPreferencesInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutTopicPreferencesInput[]
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutTopicPreferencesInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutTopicPreferencesInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutTopicPreferencesInput | Prisma.ConnectionUpdateManyWithWhereWithoutTopicPreferencesInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionUncheckedUpdateManyWithoutTopicPreferencesNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput> | Prisma.ConnectionCreateWithoutTopicPreferencesInput[] | Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput | Prisma.ConnectionCreateOrConnectWithoutTopicPreferencesInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutTopicPreferencesInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutTopicPreferencesInput[]
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutTopicPreferencesInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutTopicPreferencesInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutTopicPreferencesInput | Prisma.ConnectionUpdateManyWithWhereWithoutTopicPreferencesInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionCreateWithoutChannelInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipient: Prisma.RecipientCreateNestedOneWithoutConnectionsInput
  attributes?: Prisma.ConnectionAttributeCreateNestedManyWithoutConnectionInput
  topicPreferences?: Prisma.TopicPreferenceCreateNestedManyWithoutConnectionsInput
}

export type ConnectionUncheckedCreateWithoutChannelInput = {
  id?: string
  recipientId: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ConnectionAttributeUncheckedCreateNestedManyWithoutConnectionInput
  topicPreferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutConnectionsInput
}

export type ConnectionCreateOrConnectWithoutChannelInput = {
  where: Prisma.ConnectionWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutChannelInput, Prisma.ConnectionUncheckedCreateWithoutChannelInput>
}

export type ConnectionCreateManyChannelInputEnvelope = {
  data: Prisma.ConnectionCreateManyChannelInput | Prisma.ConnectionCreateManyChannelInput[]
  skipDuplicates?: boolean
}

export type ConnectionUpsertWithWhereUniqueWithoutChannelInput = {
  where: Prisma.ConnectionWhereUniqueInput
  update: Prisma.XOR<Prisma.ConnectionUpdateWithoutChannelInput, Prisma.ConnectionUncheckedUpdateWithoutChannelInput>
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutChannelInput, Prisma.ConnectionUncheckedCreateWithoutChannelInput>
}

export type ConnectionUpdateWithWhereUniqueWithoutChannelInput = {
  where: Prisma.ConnectionWhereUniqueInput
  data: Prisma.XOR<Prisma.ConnectionUpdateWithoutChannelInput, Prisma.ConnectionUncheckedUpdateWithoutChannelInput>
}

export type ConnectionUpdateManyWithWhereWithoutChannelInput = {
  where: Prisma.ConnectionScalarWhereInput
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyWithoutChannelInput>
}

export type ConnectionScalarWhereInput = {
  AND?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
  OR?: Prisma.ConnectionScalarWhereInput[]
  NOT?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
  id?: Prisma.StringFilter<"Connection"> | string
  recipientId?: Prisma.StringFilter<"Connection"> | string
  service?: Prisma.StringFilter<"Connection"> | string
  enabled?: Prisma.BoolFilter<"Connection"> | boolean
  showInPreferences?: Prisma.BoolFilter<"Connection"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  channelId?: Prisma.StringNullableFilter<"Connection"> | string | null
}

export type ConnectionCreateWithoutAttributesInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipient: Prisma.RecipientCreateNestedOneWithoutConnectionsInput
  topicPreferences?: Prisma.TopicPreferenceCreateNestedManyWithoutConnectionsInput
  Channel?: Prisma.ChannelCreateNestedOneWithoutConnectionInput
}

export type ConnectionUncheckedCreateWithoutAttributesInput = {
  id?: string
  recipientId: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelId?: string | null
  topicPreferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutConnectionsInput
}

export type ConnectionCreateOrConnectWithoutAttributesInput = {
  where: Prisma.ConnectionWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutAttributesInput, Prisma.ConnectionUncheckedCreateWithoutAttributesInput>
}

export type ConnectionUpsertWithoutAttributesInput = {
  update: Prisma.XOR<Prisma.ConnectionUpdateWithoutAttributesInput, Prisma.ConnectionUncheckedUpdateWithoutAttributesInput>
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutAttributesInput, Prisma.ConnectionUncheckedCreateWithoutAttributesInput>
  where?: Prisma.ConnectionWhereInput
}

export type ConnectionUpdateToOneWithWhereWithoutAttributesInput = {
  where?: Prisma.ConnectionWhereInput
  data: Prisma.XOR<Prisma.ConnectionUpdateWithoutAttributesInput, Prisma.ConnectionUncheckedUpdateWithoutAttributesInput>
}

export type ConnectionUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.RecipientUpdateOneRequiredWithoutConnectionsNestedInput
  topicPreferences?: Prisma.TopicPreferenceUpdateManyWithoutConnectionsNestedInput
  Channel?: Prisma.ChannelUpdateOneWithoutConnectionNestedInput
}

export type ConnectionUncheckedUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  topicPreferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutConnectionsNestedInput
}

export type ConnectionCreateWithoutRecipientInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ConnectionAttributeCreateNestedManyWithoutConnectionInput
  topicPreferences?: Prisma.TopicPreferenceCreateNestedManyWithoutConnectionsInput
  Channel?: Prisma.ChannelCreateNestedOneWithoutConnectionInput
}

export type ConnectionUncheckedCreateWithoutRecipientInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelId?: string | null
  attributes?: Prisma.ConnectionAttributeUncheckedCreateNestedManyWithoutConnectionInput
  topicPreferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutConnectionsInput
}

export type ConnectionCreateOrConnectWithoutRecipientInput = {
  where: Prisma.ConnectionWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutRecipientInput, Prisma.ConnectionUncheckedCreateWithoutRecipientInput>
}

export type ConnectionCreateManyRecipientInputEnvelope = {
  data: Prisma.ConnectionCreateManyRecipientInput | Prisma.ConnectionCreateManyRecipientInput[]
  skipDuplicates?: boolean
}

export type ConnectionUpsertWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.ConnectionWhereUniqueInput
  update: Prisma.XOR<Prisma.ConnectionUpdateWithoutRecipientInput, Prisma.ConnectionUncheckedUpdateWithoutRecipientInput>
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutRecipientInput, Prisma.ConnectionUncheckedCreateWithoutRecipientInput>
}

export type ConnectionUpdateWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.ConnectionWhereUniqueInput
  data: Prisma.XOR<Prisma.ConnectionUpdateWithoutRecipientInput, Prisma.ConnectionUncheckedUpdateWithoutRecipientInput>
}

export type ConnectionUpdateManyWithWhereWithoutRecipientInput = {
  where: Prisma.ConnectionScalarWhereInput
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyWithoutRecipientInput>
}

export type ConnectionCreateWithoutTopicPreferencesInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipient: Prisma.RecipientCreateNestedOneWithoutConnectionsInput
  attributes?: Prisma.ConnectionAttributeCreateNestedManyWithoutConnectionInput
  Channel?: Prisma.ChannelCreateNestedOneWithoutConnectionInput
}

export type ConnectionUncheckedCreateWithoutTopicPreferencesInput = {
  id?: string
  recipientId: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelId?: string | null
  attributes?: Prisma.ConnectionAttributeUncheckedCreateNestedManyWithoutConnectionInput
}

export type ConnectionCreateOrConnectWithoutTopicPreferencesInput = {
  where: Prisma.ConnectionWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput>
}

export type ConnectionUpsertWithWhereUniqueWithoutTopicPreferencesInput = {
  where: Prisma.ConnectionWhereUniqueInput
  update: Prisma.XOR<Prisma.ConnectionUpdateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedUpdateWithoutTopicPreferencesInput>
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedCreateWithoutTopicPreferencesInput>
}

export type ConnectionUpdateWithWhereUniqueWithoutTopicPreferencesInput = {
  where: Prisma.ConnectionWhereUniqueInput
  data: Prisma.XOR<Prisma.ConnectionUpdateWithoutTopicPreferencesInput, Prisma.ConnectionUncheckedUpdateWithoutTopicPreferencesInput>
}

export type ConnectionUpdateManyWithWhereWithoutTopicPreferencesInput = {
  where: Prisma.ConnectionScalarWhereInput
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyWithoutTopicPreferencesInput>
}

export type ConnectionCreateManyChannelInput = {
  id?: string
  recipientId: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ConnectionUpdateWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.RecipientUpdateOneRequiredWithoutConnectionsNestedInput
  attributes?: Prisma.ConnectionAttributeUpdateManyWithoutConnectionNestedInput
  topicPreferences?: Prisma.TopicPreferenceUpdateManyWithoutConnectionsNestedInput
}

export type ConnectionUncheckedUpdateWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ConnectionAttributeUncheckedUpdateManyWithoutConnectionNestedInput
  topicPreferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutConnectionsNestedInput
}

export type ConnectionUncheckedUpdateManyWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionCreateManyRecipientInput = {
  id?: string
  service: string
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelId?: string | null
}

export type ConnectionUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ConnectionAttributeUpdateManyWithoutConnectionNestedInput
  topicPreferences?: Prisma.TopicPreferenceUpdateManyWithoutConnectionsNestedInput
  Channel?: Prisma.ChannelUpdateOneWithoutConnectionNestedInput
}

export type ConnectionUncheckedUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  attributes?: Prisma.ConnectionAttributeUncheckedUpdateManyWithoutConnectionNestedInput
  topicPreferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutConnectionsNestedInput
}

export type ConnectionUncheckedUpdateManyWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type ConnectionUpdateWithoutTopicPreferencesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.RecipientUpdateOneRequiredWithoutConnectionsNestedInput
  attributes?: Prisma.ConnectionAttributeUpdateManyWithoutConnectionNestedInput
  Channel?: Prisma.ChannelUpdateOneWithoutConnectionNestedInput
}

export type ConnectionUncheckedUpdateWithoutTopicPreferencesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  attributes?: Prisma.ConnectionAttributeUncheckedUpdateManyWithoutConnectionNestedInput
}

export type ConnectionUncheckedUpdateManyWithoutTopicPreferencesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}


/**
 * Count Type ConnectionCountOutputType
 */

export type ConnectionCountOutputType = {
  attributes: number
  topicPreferences: number
}

export type ConnectionCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  attributes?: boolean | ConnectionCountOutputTypeCountAttributesArgs
  topicPreferences?: boolean | ConnectionCountOutputTypeCountTopicPreferencesArgs
}

/**
 * ConnectionCountOutputType without action
 */
export type ConnectionCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionCountOutputType
   */
  select?: Prisma.ConnectionCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ConnectionCountOutputType without action
 */
export type ConnectionCountOutputTypeCountAttributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionAttributeWhereInput
}

/**
 * ConnectionCountOutputType without action
 */
export type ConnectionCountOutputTypeCountTopicPreferencesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TopicPreferenceWhereInput
}


export type ConnectionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  service?: boolean
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  channelId?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  attributes?: boolean | Prisma.Connection$attributesArgs<ExtArgs>
  topicPreferences?: boolean | Prisma.Connection$topicPreferencesArgs<ExtArgs>
  Channel?: boolean | Prisma.Connection$ChannelArgs<ExtArgs>
  _count?: boolean | Prisma.ConnectionCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["connection"]>

export type ConnectionSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  service?: boolean
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  channelId?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  Channel?: boolean | Prisma.Connection$ChannelArgs<ExtArgs>
}, ExtArgs["result"]["connection"]>

export type ConnectionSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  service?: boolean
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  channelId?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  Channel?: boolean | Prisma.Connection$ChannelArgs<ExtArgs>
}, ExtArgs["result"]["connection"]>

export type ConnectionSelectScalar = {
  id?: boolean
  recipientId?: boolean
  service?: boolean
  enabled?: boolean
  showInPreferences?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  channelId?: boolean
}

export type ConnectionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "recipientId" | "service" | "enabled" | "showInPreferences" | "createdAt" | "updatedAt" | "channelId", ExtArgs["result"]["connection"]>
export type ConnectionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  attributes?: boolean | Prisma.Connection$attributesArgs<ExtArgs>
  topicPreferences?: boolean | Prisma.Connection$topicPreferencesArgs<ExtArgs>
  Channel?: boolean | Prisma.Connection$ChannelArgs<ExtArgs>
  _count?: boolean | Prisma.ConnectionCountOutputTypeDefaultArgs<ExtArgs>
}
export type ConnectionIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  Channel?: boolean | Prisma.Connection$ChannelArgs<ExtArgs>
}
export type ConnectionIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  Channel?: boolean | Prisma.Connection$ChannelArgs<ExtArgs>
}

export type $ConnectionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Connection"
  objects: {
    recipient: Prisma.$RecipientPayload<ExtArgs>
    attributes: Prisma.$ConnectionAttributePayload<ExtArgs>[]
    topicPreferences: Prisma.$TopicPreferencePayload<ExtArgs>[]
    Channel: Prisma.$ChannelPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    recipientId: string
    service: string
    enabled: boolean
    showInPreferences: boolean
    createdAt: Date
    updatedAt: Date
    channelId: string | null
  }, ExtArgs["result"]["connection"]>
  composites: {}
}

export type ConnectionGetPayload<S extends boolean | null | undefined | ConnectionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ConnectionPayload, S>

export type ConnectionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ConnectionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: ConnectionCountAggregateInputType | true
  }

export interface ConnectionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Connection'], meta: { name: 'Connection' } }
  /**
   * Find zero or one Connection that matches the filter.
   * @param {ConnectionFindUniqueArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ConnectionFindUniqueArgs>(args: Prisma.SelectSubset<T, ConnectionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Connection that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ConnectionFindUniqueOrThrowArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ConnectionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ConnectionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Connection that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionFindFirstArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ConnectionFindFirstArgs>(args?: Prisma.SelectSubset<T, ConnectionFindFirstArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Connection that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionFindFirstOrThrowArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ConnectionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ConnectionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Connections that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Connections
   * const connections = await prisma.connection.findMany()
   * 
   * // Get first 10 Connections
   * const connections = await prisma.connection.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const connectionWithIdOnly = await prisma.connection.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ConnectionFindManyArgs>(args?: Prisma.SelectSubset<T, ConnectionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Connection.
   * @param {ConnectionCreateArgs} args - Arguments to create a Connection.
   * @example
   * // Create one Connection
   * const Connection = await prisma.connection.create({
   *   data: {
   *     // ... data to create a Connection
   *   }
   * })
   * 
   */
  create<T extends ConnectionCreateArgs>(args: Prisma.SelectSubset<T, ConnectionCreateArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Connections.
   * @param {ConnectionCreateManyArgs} args - Arguments to create many Connections.
   * @example
   * // Create many Connections
   * const connection = await prisma.connection.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ConnectionCreateManyArgs>(args?: Prisma.SelectSubset<T, ConnectionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Connections and returns the data saved in the database.
   * @param {ConnectionCreateManyAndReturnArgs} args - Arguments to create many Connections.
   * @example
   * // Create many Connections
   * const connection = await prisma.connection.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Connections and only return the `id`
   * const connectionWithIdOnly = await prisma.connection.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ConnectionCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ConnectionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Connection.
   * @param {ConnectionDeleteArgs} args - Arguments to delete one Connection.
   * @example
   * // Delete one Connection
   * const Connection = await prisma.connection.delete({
   *   where: {
   *     // ... filter to delete one Connection
   *   }
   * })
   * 
   */
  delete<T extends ConnectionDeleteArgs>(args: Prisma.SelectSubset<T, ConnectionDeleteArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Connection.
   * @param {ConnectionUpdateArgs} args - Arguments to update one Connection.
   * @example
   * // Update one Connection
   * const connection = await prisma.connection.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ConnectionUpdateArgs>(args: Prisma.SelectSubset<T, ConnectionUpdateArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Connections.
   * @param {ConnectionDeleteManyArgs} args - Arguments to filter Connections to delete.
   * @example
   * // Delete a few Connections
   * const { count } = await prisma.connection.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ConnectionDeleteManyArgs>(args?: Prisma.SelectSubset<T, ConnectionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Connections.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Connections
   * const connection = await prisma.connection.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ConnectionUpdateManyArgs>(args: Prisma.SelectSubset<T, ConnectionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Connections and returns the data updated in the database.
   * @param {ConnectionUpdateManyAndReturnArgs} args - Arguments to update many Connections.
   * @example
   * // Update many Connections
   * const connection = await prisma.connection.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Connections and only return the `id`
   * const connectionWithIdOnly = await prisma.connection.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ConnectionUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ConnectionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Connection.
   * @param {ConnectionUpsertArgs} args - Arguments to update or create a Connection.
   * @example
   * // Update or create a Connection
   * const connection = await prisma.connection.upsert({
   *   create: {
   *     // ... data to create a Connection
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Connection we want to update
   *   }
   * })
   */
  upsert<T extends ConnectionUpsertArgs>(args: Prisma.SelectSubset<T, ConnectionUpsertArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Connections.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionCountArgs} args - Arguments to filter Connections to count.
   * @example
   * // Count the number of Connections
   * const count = await prisma.connection.count({
   *   where: {
   *     // ... the filter for the Connections we want to count
   *   }
   * })
  **/
  count<T extends ConnectionCountArgs>(
    args?: Prisma.Subset<T, ConnectionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ConnectionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Connection.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ConnectionAggregateArgs>(args: Prisma.Subset<T, ConnectionAggregateArgs>): Prisma.PrismaPromise<GetConnectionAggregateType<T>>

  /**
   * Group by Connection.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ConnectionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ConnectionGroupByArgs['orderBy'] }
      : { orderBy?: ConnectionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ConnectionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetConnectionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Connection model
 */
readonly fields: ConnectionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Connection.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ConnectionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  recipient<T extends Prisma.RecipientDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientDefaultArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  attributes<T extends Prisma.Connection$attributesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Connection$attributesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  topicPreferences<T extends Prisma.Connection$topicPreferencesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Connection$topicPreferencesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  Channel<T extends Prisma.Connection$ChannelArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Connection$ChannelArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Connection model
 */
export interface ConnectionFieldRefs {
  readonly id: Prisma.FieldRef<"Connection", 'String'>
  readonly recipientId: Prisma.FieldRef<"Connection", 'String'>
  readonly service: Prisma.FieldRef<"Connection", 'String'>
  readonly enabled: Prisma.FieldRef<"Connection", 'Boolean'>
  readonly showInPreferences: Prisma.FieldRef<"Connection", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Connection", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Connection", 'DateTime'>
  readonly channelId: Prisma.FieldRef<"Connection", 'String'>
}
    

// Custom InputTypes
/**
 * Connection findUnique
 */
export type ConnectionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where: Prisma.ConnectionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection findUniqueOrThrow
 */
export type ConnectionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where: Prisma.ConnectionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection findFirst
 */
export type ConnectionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Connections.
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Connections.
   */
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection findFirstOrThrow
 */
export type ConnectionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Connections.
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Connections.
   */
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection findMany
 */
export type ConnectionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connections to fetch.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Connections.
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection create
 */
export type ConnectionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * The data needed to create a Connection.
   */
  data: Prisma.XOR<Prisma.ConnectionCreateInput, Prisma.ConnectionUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection createMany
 */
export type ConnectionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Connections.
   */
  data: Prisma.ConnectionCreateManyInput | Prisma.ConnectionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Connection createManyAndReturn
 */
export type ConnectionCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * The data used to create many Connections.
   */
  data: Prisma.ConnectionCreateManyInput | Prisma.ConnectionCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Connection update
 */
export type ConnectionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * The data needed to update a Connection.
   */
  data: Prisma.XOR<Prisma.ConnectionUpdateInput, Prisma.ConnectionUncheckedUpdateInput>
  /**
   * Choose, which Connection to update.
   */
  where: Prisma.ConnectionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection updateMany
 */
export type ConnectionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Connections.
   */
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyInput>
  /**
   * Filter which Connections to update
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * Limit how many Connections to update.
   */
  limit?: number
}

/**
 * Connection updateManyAndReturn
 */
export type ConnectionUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * The data used to update Connections.
   */
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyInput>
  /**
   * Filter which Connections to update
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * Limit how many Connections to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Connection upsert
 */
export type ConnectionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * The filter to search for the Connection to update in case it exists.
   */
  where: Prisma.ConnectionWhereUniqueInput
  /**
   * In case the Connection found by the `where` argument doesn't exist, create a new Connection with this data.
   */
  create: Prisma.XOR<Prisma.ConnectionCreateInput, Prisma.ConnectionUncheckedCreateInput>
  /**
   * In case the Connection was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ConnectionUpdateInput, Prisma.ConnectionUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection delete
 */
export type ConnectionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter which Connection to delete.
   */
  where: Prisma.ConnectionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Connection deleteMany
 */
export type ConnectionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Connections to delete
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * Limit how many Connections to delete.
   */
  limit?: number
}

/**
 * Connection.attributes
 */
export type Connection$attributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  where?: Prisma.ConnectionAttributeWhereInput
  orderBy?: Prisma.ConnectionAttributeOrderByWithRelationInput | Prisma.ConnectionAttributeOrderByWithRelationInput[]
  cursor?: Prisma.ConnectionAttributeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ConnectionAttributeScalarFieldEnum | Prisma.ConnectionAttributeScalarFieldEnum[]
}

/**
 * Connection.topicPreferences
 */
export type Connection$topicPreferencesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  where?: Prisma.TopicPreferenceWhereInput
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TopicPreferenceScalarFieldEnum | Prisma.TopicPreferenceScalarFieldEnum[]
}

/**
 * Connection.Channel
 */
export type Connection$ChannelArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  where?: Prisma.ChannelWhereInput
}

/**
 * Connection without action
 */
export type ConnectionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
}
