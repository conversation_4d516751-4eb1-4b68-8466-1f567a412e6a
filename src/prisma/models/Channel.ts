
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Channel` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Channel
 * 
 */
export type ChannelModel = runtime.Types.Result.DefaultSelection<Prisma.$ChannelPayload>

export type AggregateChannel = {
  _count: ChannelCountAggregateOutputType | null
  _min: ChannelMinAggregateOutputType | null
  _max: ChannelMaxAggregateOutputType | null
}

export type ChannelMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  description: string | null
  service: string | null
  channelType: string | null
  provider: string | null
  displayName: string | null
  enabled: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ChannelMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  description: string | null
  service: string | null
  channelType: string | null
  provider: string | null
  displayName: string | null
  enabled: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ChannelCountAggregateOutputType = {
  id: number
  tenantId: number
  externalId: number
  description: number
  service: number
  channelType: number
  provider: number
  displayName: number
  enabled: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ChannelMinAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  description?: true
  service?: true
  channelType?: true
  provider?: true
  displayName?: true
  enabled?: true
  createdAt?: true
  updatedAt?: true
}

export type ChannelMaxAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  description?: true
  service?: true
  channelType?: true
  provider?: true
  displayName?: true
  enabled?: true
  createdAt?: true
  updatedAt?: true
}

export type ChannelCountAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  description?: true
  service?: true
  channelType?: true
  provider?: true
  displayName?: true
  enabled?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ChannelAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Channel to aggregate.
   */
  where?: Prisma.ChannelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Channels to fetch.
   */
  orderBy?: Prisma.ChannelOrderByWithRelationInput | Prisma.ChannelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ChannelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Channels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Channels.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Channels
  **/
  _count?: true | ChannelCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ChannelMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ChannelMaxAggregateInputType
}

export type GetChannelAggregateType<T extends ChannelAggregateArgs> = {
      [P in keyof T & keyof AggregateChannel]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateChannel[P]>
    : Prisma.GetScalarType<T[P], AggregateChannel[P]>
}




export type ChannelGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelWhereInput
  orderBy?: Prisma.ChannelOrderByWithAggregationInput | Prisma.ChannelOrderByWithAggregationInput[]
  by: Prisma.ChannelScalarFieldEnum[] | Prisma.ChannelScalarFieldEnum
  having?: Prisma.ChannelScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ChannelCountAggregateInputType | true
  _min?: ChannelMinAggregateInputType
  _max?: ChannelMaxAggregateInputType
}

export type ChannelGroupByOutputType = {
  id: string
  tenantId: string
  externalId: string
  description: string
  service: string
  channelType: string
  provider: string
  displayName: string
  enabled: boolean
  createdAt: Date
  updatedAt: Date
  _count: ChannelCountAggregateOutputType | null
  _min: ChannelMinAggregateOutputType | null
  _max: ChannelMaxAggregateOutputType | null
}

type GetChannelGroupByPayload<T extends ChannelGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ChannelGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ChannelGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ChannelGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ChannelGroupByOutputType[P]>
      }
    >
  > 



export type ChannelWhereInput = {
  AND?: Prisma.ChannelWhereInput | Prisma.ChannelWhereInput[]
  OR?: Prisma.ChannelWhereInput[]
  NOT?: Prisma.ChannelWhereInput | Prisma.ChannelWhereInput[]
  id?: Prisma.StringFilter<"Channel"> | string
  tenantId?: Prisma.StringFilter<"Channel"> | string
  externalId?: Prisma.StringFilter<"Channel"> | string
  description?: Prisma.StringFilter<"Channel"> | string
  service?: Prisma.StringFilter<"Channel"> | string
  channelType?: Prisma.StringFilter<"Channel"> | string
  provider?: Prisma.StringFilter<"Channel"> | string
  displayName?: Prisma.StringFilter<"Channel"> | string
  enabled?: Prisma.BoolFilter<"Channel"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Channel"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Channel"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  attributes?: Prisma.ChannelAttributeListRelationFilter
  channelMessages?: Prisma.ChannelMessageListRelationFilter
  Connection?: Prisma.ConnectionListRelationFilter
}

export type ChannelOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  description?: Prisma.SortOrder
  service?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  provider?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  attributes?: Prisma.ChannelAttributeOrderByRelationAggregateInput
  channelMessages?: Prisma.ChannelMessageOrderByRelationAggregateInput
  Connection?: Prisma.ConnectionOrderByRelationAggregateInput
}

export type ChannelWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  idx_channel_tenant_id_service_channel_type?: Prisma.ChannelIdx_channel_tenant_id_service_channel_typeCompoundUniqueInput
  AND?: Prisma.ChannelWhereInput | Prisma.ChannelWhereInput[]
  OR?: Prisma.ChannelWhereInput[]
  NOT?: Prisma.ChannelWhereInput | Prisma.ChannelWhereInput[]
  tenantId?: Prisma.StringFilter<"Channel"> | string
  externalId?: Prisma.StringFilter<"Channel"> | string
  description?: Prisma.StringFilter<"Channel"> | string
  service?: Prisma.StringFilter<"Channel"> | string
  channelType?: Prisma.StringFilter<"Channel"> | string
  provider?: Prisma.StringFilter<"Channel"> | string
  displayName?: Prisma.StringFilter<"Channel"> | string
  enabled?: Prisma.BoolFilter<"Channel"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Channel"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Channel"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  attributes?: Prisma.ChannelAttributeListRelationFilter
  channelMessages?: Prisma.ChannelMessageListRelationFilter
  Connection?: Prisma.ConnectionListRelationFilter
}, "id" | "idx_channel_tenant_id_service_channel_type">

export type ChannelOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  description?: Prisma.SortOrder
  service?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  provider?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ChannelCountOrderByAggregateInput
  _max?: Prisma.ChannelMaxOrderByAggregateInput
  _min?: Prisma.ChannelMinOrderByAggregateInput
}

export type ChannelScalarWhereWithAggregatesInput = {
  AND?: Prisma.ChannelScalarWhereWithAggregatesInput | Prisma.ChannelScalarWhereWithAggregatesInput[]
  OR?: Prisma.ChannelScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ChannelScalarWhereWithAggregatesInput | Prisma.ChannelScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  externalId?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  description?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  service?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  channelType?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  provider?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  displayName?: Prisma.StringWithAggregatesFilter<"Channel"> | string
  enabled?: Prisma.BoolWithAggregatesFilter<"Channel"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Channel"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Channel"> | Date | string
}

export type ChannelCreateInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutChannelConfigurationsInput
  attributes?: Prisma.ChannelAttributeCreateNestedManyWithoutChannelInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionCreateNestedManyWithoutChannelInput
}

export type ChannelUncheckedCreateInput = {
  id?: string
  tenantId: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ChannelAttributeUncheckedCreateNestedManyWithoutChannelInput
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionUncheckedCreateNestedManyWithoutChannelInput
}

export type ChannelUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutChannelConfigurationsNestedInput
  attributes?: Prisma.ChannelAttributeUpdateManyWithoutChannelNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUpdateManyWithoutChannelNestedInput
}

export type ChannelUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ChannelAttributeUncheckedUpdateManyWithoutChannelNestedInput
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUncheckedUpdateManyWithoutChannelNestedInput
}

export type ChannelCreateManyInput = {
  id?: string
  tenantId: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelScalarRelationFilter = {
  is?: Prisma.ChannelWhereInput
  isNot?: Prisma.ChannelWhereInput
}

export type ChannelNullableScalarRelationFilter = {
  is?: Prisma.ChannelWhereInput | null
  isNot?: Prisma.ChannelWhereInput | null
}

export type ChannelIdx_channel_tenant_id_service_channel_typeCompoundUniqueInput = {
  tenantId: string
  id: string
  service: string
  channelType: string
}

export type ChannelCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  description?: Prisma.SortOrder
  service?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  provider?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ChannelMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  description?: Prisma.SortOrder
  service?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  provider?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ChannelMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  description?: Prisma.SortOrder
  service?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  provider?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ChannelListRelationFilter = {
  every?: Prisma.ChannelWhereInput
  some?: Prisma.ChannelWhereInput
  none?: Prisma.ChannelWhereInput
}

export type ChannelOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ChannelCreateNestedOneWithoutAttributesInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutAttributesInput, Prisma.ChannelUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutAttributesInput
  connect?: Prisma.ChannelWhereUniqueInput
}

export type ChannelUpdateOneRequiredWithoutAttributesNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutAttributesInput, Prisma.ChannelUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutAttributesInput
  upsert?: Prisma.ChannelUpsertWithoutAttributesInput
  connect?: Prisma.ChannelWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ChannelUpdateToOneWithWhereWithoutAttributesInput, Prisma.ChannelUpdateWithoutAttributesInput>, Prisma.ChannelUncheckedUpdateWithoutAttributesInput>
}

export type ChannelCreateNestedOneWithoutChannelMessagesInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutChannelMessagesInput, Prisma.ChannelUncheckedCreateWithoutChannelMessagesInput>
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutChannelMessagesInput
  connect?: Prisma.ChannelWhereUniqueInput
}

export type ChannelUpdateOneWithoutChannelMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutChannelMessagesInput, Prisma.ChannelUncheckedCreateWithoutChannelMessagesInput>
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutChannelMessagesInput
  upsert?: Prisma.ChannelUpsertWithoutChannelMessagesInput
  disconnect?: Prisma.ChannelWhereInput | boolean
  delete?: Prisma.ChannelWhereInput | boolean
  connect?: Prisma.ChannelWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ChannelUpdateToOneWithWhereWithoutChannelMessagesInput, Prisma.ChannelUpdateWithoutChannelMessagesInput>, Prisma.ChannelUncheckedUpdateWithoutChannelMessagesInput>
}

export type ChannelCreateNestedOneWithoutConnectionInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutConnectionInput, Prisma.ChannelUncheckedCreateWithoutConnectionInput>
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutConnectionInput
  connect?: Prisma.ChannelWhereUniqueInput
}

export type ChannelUpdateOneWithoutConnectionNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutConnectionInput, Prisma.ChannelUncheckedCreateWithoutConnectionInput>
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutConnectionInput
  upsert?: Prisma.ChannelUpsertWithoutConnectionInput
  disconnect?: Prisma.ChannelWhereInput | boolean
  delete?: Prisma.ChannelWhereInput | boolean
  connect?: Prisma.ChannelWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ChannelUpdateToOneWithWhereWithoutConnectionInput, Prisma.ChannelUpdateWithoutConnectionInput>, Prisma.ChannelUncheckedUpdateWithoutConnectionInput>
}

export type ChannelCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutTenantInput, Prisma.ChannelUncheckedCreateWithoutTenantInput> | Prisma.ChannelCreateWithoutTenantInput[] | Prisma.ChannelUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutTenantInput | Prisma.ChannelCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.ChannelCreateManyTenantInputEnvelope
  connect?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
}

export type ChannelUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutTenantInput, Prisma.ChannelUncheckedCreateWithoutTenantInput> | Prisma.ChannelCreateWithoutTenantInput[] | Prisma.ChannelUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutTenantInput | Prisma.ChannelCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.ChannelCreateManyTenantInputEnvelope
  connect?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
}

export type ChannelUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutTenantInput, Prisma.ChannelUncheckedCreateWithoutTenantInput> | Prisma.ChannelCreateWithoutTenantInput[] | Prisma.ChannelUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutTenantInput | Prisma.ChannelCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.ChannelUpsertWithWhereUniqueWithoutTenantInput | Prisma.ChannelUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.ChannelCreateManyTenantInputEnvelope
  set?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  disconnect?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  delete?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  connect?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  update?: Prisma.ChannelUpdateWithWhereUniqueWithoutTenantInput | Prisma.ChannelUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.ChannelUpdateManyWithWhereWithoutTenantInput | Prisma.ChannelUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.ChannelScalarWhereInput | Prisma.ChannelScalarWhereInput[]
}

export type ChannelUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelCreateWithoutTenantInput, Prisma.ChannelUncheckedCreateWithoutTenantInput> | Prisma.ChannelCreateWithoutTenantInput[] | Prisma.ChannelUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.ChannelCreateOrConnectWithoutTenantInput | Prisma.ChannelCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.ChannelUpsertWithWhereUniqueWithoutTenantInput | Prisma.ChannelUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.ChannelCreateManyTenantInputEnvelope
  set?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  disconnect?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  delete?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  connect?: Prisma.ChannelWhereUniqueInput | Prisma.ChannelWhereUniqueInput[]
  update?: Prisma.ChannelUpdateWithWhereUniqueWithoutTenantInput | Prisma.ChannelUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.ChannelUpdateManyWithWhereWithoutTenantInput | Prisma.ChannelUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.ChannelScalarWhereInput | Prisma.ChannelScalarWhereInput[]
}

export type ChannelCreateWithoutAttributesInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutChannelConfigurationsInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionCreateNestedManyWithoutChannelInput
}

export type ChannelUncheckedCreateWithoutAttributesInput = {
  id?: string
  tenantId: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionUncheckedCreateNestedManyWithoutChannelInput
}

export type ChannelCreateOrConnectWithoutAttributesInput = {
  where: Prisma.ChannelWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelCreateWithoutAttributesInput, Prisma.ChannelUncheckedCreateWithoutAttributesInput>
}

export type ChannelUpsertWithoutAttributesInput = {
  update: Prisma.XOR<Prisma.ChannelUpdateWithoutAttributesInput, Prisma.ChannelUncheckedUpdateWithoutAttributesInput>
  create: Prisma.XOR<Prisma.ChannelCreateWithoutAttributesInput, Prisma.ChannelUncheckedCreateWithoutAttributesInput>
  where?: Prisma.ChannelWhereInput
}

export type ChannelUpdateToOneWithWhereWithoutAttributesInput = {
  where?: Prisma.ChannelWhereInput
  data: Prisma.XOR<Prisma.ChannelUpdateWithoutAttributesInput, Prisma.ChannelUncheckedUpdateWithoutAttributesInput>
}

export type ChannelUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutChannelConfigurationsNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUpdateManyWithoutChannelNestedInput
}

export type ChannelUncheckedUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUncheckedUpdateManyWithoutChannelNestedInput
}

export type ChannelCreateWithoutChannelMessagesInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutChannelConfigurationsInput
  attributes?: Prisma.ChannelAttributeCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionCreateNestedManyWithoutChannelInput
}

export type ChannelUncheckedCreateWithoutChannelMessagesInput = {
  id?: string
  tenantId: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ChannelAttributeUncheckedCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionUncheckedCreateNestedManyWithoutChannelInput
}

export type ChannelCreateOrConnectWithoutChannelMessagesInput = {
  where: Prisma.ChannelWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelCreateWithoutChannelMessagesInput, Prisma.ChannelUncheckedCreateWithoutChannelMessagesInput>
}

export type ChannelUpsertWithoutChannelMessagesInput = {
  update: Prisma.XOR<Prisma.ChannelUpdateWithoutChannelMessagesInput, Prisma.ChannelUncheckedUpdateWithoutChannelMessagesInput>
  create: Prisma.XOR<Prisma.ChannelCreateWithoutChannelMessagesInput, Prisma.ChannelUncheckedCreateWithoutChannelMessagesInput>
  where?: Prisma.ChannelWhereInput
}

export type ChannelUpdateToOneWithWhereWithoutChannelMessagesInput = {
  where?: Prisma.ChannelWhereInput
  data: Prisma.XOR<Prisma.ChannelUpdateWithoutChannelMessagesInput, Prisma.ChannelUncheckedUpdateWithoutChannelMessagesInput>
}

export type ChannelUpdateWithoutChannelMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutChannelConfigurationsNestedInput
  attributes?: Prisma.ChannelAttributeUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUpdateManyWithoutChannelNestedInput
}

export type ChannelUncheckedUpdateWithoutChannelMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ChannelAttributeUncheckedUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUncheckedUpdateManyWithoutChannelNestedInput
}

export type ChannelCreateWithoutConnectionInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutChannelConfigurationsInput
  attributes?: Prisma.ChannelAttributeCreateNestedManyWithoutChannelInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutChannelInput
}

export type ChannelUncheckedCreateWithoutConnectionInput = {
  id?: string
  tenantId: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ChannelAttributeUncheckedCreateNestedManyWithoutChannelInput
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutChannelInput
}

export type ChannelCreateOrConnectWithoutConnectionInput = {
  where: Prisma.ChannelWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelCreateWithoutConnectionInput, Prisma.ChannelUncheckedCreateWithoutConnectionInput>
}

export type ChannelUpsertWithoutConnectionInput = {
  update: Prisma.XOR<Prisma.ChannelUpdateWithoutConnectionInput, Prisma.ChannelUncheckedUpdateWithoutConnectionInput>
  create: Prisma.XOR<Prisma.ChannelCreateWithoutConnectionInput, Prisma.ChannelUncheckedCreateWithoutConnectionInput>
  where?: Prisma.ChannelWhereInput
}

export type ChannelUpdateToOneWithWhereWithoutConnectionInput = {
  where?: Prisma.ChannelWhereInput
  data: Prisma.XOR<Prisma.ChannelUpdateWithoutConnectionInput, Prisma.ChannelUncheckedUpdateWithoutConnectionInput>
}

export type ChannelUpdateWithoutConnectionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutChannelConfigurationsNestedInput
  attributes?: Prisma.ChannelAttributeUpdateManyWithoutChannelNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutChannelNestedInput
}

export type ChannelUncheckedUpdateWithoutConnectionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ChannelAttributeUncheckedUpdateManyWithoutChannelNestedInput
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutChannelNestedInput
}

export type ChannelCreateWithoutTenantInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ChannelAttributeCreateNestedManyWithoutChannelInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionCreateNestedManyWithoutChannelInput
}

export type ChannelUncheckedCreateWithoutTenantInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.ChannelAttributeUncheckedCreateNestedManyWithoutChannelInput
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutChannelInput
  Connection?: Prisma.ConnectionUncheckedCreateNestedManyWithoutChannelInput
}

export type ChannelCreateOrConnectWithoutTenantInput = {
  where: Prisma.ChannelWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelCreateWithoutTenantInput, Prisma.ChannelUncheckedCreateWithoutTenantInput>
}

export type ChannelCreateManyTenantInputEnvelope = {
  data: Prisma.ChannelCreateManyTenantInput | Prisma.ChannelCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type ChannelUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.ChannelWhereUniqueInput
  update: Prisma.XOR<Prisma.ChannelUpdateWithoutTenantInput, Prisma.ChannelUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.ChannelCreateWithoutTenantInput, Prisma.ChannelUncheckedCreateWithoutTenantInput>
}

export type ChannelUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.ChannelWhereUniqueInput
  data: Prisma.XOR<Prisma.ChannelUpdateWithoutTenantInput, Prisma.ChannelUncheckedUpdateWithoutTenantInput>
}

export type ChannelUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.ChannelScalarWhereInput
  data: Prisma.XOR<Prisma.ChannelUpdateManyMutationInput, Prisma.ChannelUncheckedUpdateManyWithoutTenantInput>
}

export type ChannelScalarWhereInput = {
  AND?: Prisma.ChannelScalarWhereInput | Prisma.ChannelScalarWhereInput[]
  OR?: Prisma.ChannelScalarWhereInput[]
  NOT?: Prisma.ChannelScalarWhereInput | Prisma.ChannelScalarWhereInput[]
  id?: Prisma.StringFilter<"Channel"> | string
  tenantId?: Prisma.StringFilter<"Channel"> | string
  externalId?: Prisma.StringFilter<"Channel"> | string
  description?: Prisma.StringFilter<"Channel"> | string
  service?: Prisma.StringFilter<"Channel"> | string
  channelType?: Prisma.StringFilter<"Channel"> | string
  provider?: Prisma.StringFilter<"Channel"> | string
  displayName?: Prisma.StringFilter<"Channel"> | string
  enabled?: Prisma.BoolFilter<"Channel"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Channel"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Channel"> | Date | string
}

export type ChannelCreateManyTenantInput = {
  id?: string
  externalId?: string
  description?: string
  service: string
  channelType: string
  provider?: string
  displayName: string
  enabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ChannelAttributeUpdateManyWithoutChannelNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUpdateManyWithoutChannelNestedInput
}

export type ChannelUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.ChannelAttributeUncheckedUpdateManyWithoutChannelNestedInput
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutChannelNestedInput
  Connection?: Prisma.ConnectionUncheckedUpdateManyWithoutChannelNestedInput
}

export type ChannelUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  provider?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ChannelCountOutputType
 */

export type ChannelCountOutputType = {
  attributes: number
  channelMessages: number
  Connection: number
}

export type ChannelCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  attributes?: boolean | ChannelCountOutputTypeCountAttributesArgs
  channelMessages?: boolean | ChannelCountOutputTypeCountChannelMessagesArgs
  Connection?: boolean | ChannelCountOutputTypeCountConnectionArgs
}

/**
 * ChannelCountOutputType without action
 */
export type ChannelCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelCountOutputType
   */
  select?: Prisma.ChannelCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ChannelCountOutputType without action
 */
export type ChannelCountOutputTypeCountAttributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelAttributeWhereInput
}

/**
 * ChannelCountOutputType without action
 */
export type ChannelCountOutputTypeCountChannelMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelMessageWhereInput
}

/**
 * ChannelCountOutputType without action
 */
export type ChannelCountOutputTypeCountConnectionArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
}


export type ChannelSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  description?: boolean
  service?: boolean
  channelType?: boolean
  provider?: boolean
  displayName?: boolean
  enabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  attributes?: boolean | Prisma.Channel$attributesArgs<ExtArgs>
  channelMessages?: boolean | Prisma.Channel$channelMessagesArgs<ExtArgs>
  Connection?: boolean | Prisma.Channel$ConnectionArgs<ExtArgs>
  _count?: boolean | Prisma.ChannelCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["channel"]>

export type ChannelSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  description?: boolean
  service?: boolean
  channelType?: boolean
  provider?: boolean
  displayName?: boolean
  enabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["channel"]>

export type ChannelSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  description?: boolean
  service?: boolean
  channelType?: boolean
  provider?: boolean
  displayName?: boolean
  enabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["channel"]>

export type ChannelSelectScalar = {
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  description?: boolean
  service?: boolean
  channelType?: boolean
  provider?: boolean
  displayName?: boolean
  enabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ChannelOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "externalId" | "description" | "service" | "channelType" | "provider" | "displayName" | "enabled" | "createdAt" | "updatedAt", ExtArgs["result"]["channel"]>
export type ChannelInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  attributes?: boolean | Prisma.Channel$attributesArgs<ExtArgs>
  channelMessages?: boolean | Prisma.Channel$channelMessagesArgs<ExtArgs>
  Connection?: boolean | Prisma.Channel$ConnectionArgs<ExtArgs>
  _count?: boolean | Prisma.ChannelCountOutputTypeDefaultArgs<ExtArgs>
}
export type ChannelIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type ChannelIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $ChannelPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Channel"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    attributes: Prisma.$ChannelAttributePayload<ExtArgs>[]
    channelMessages: Prisma.$ChannelMessagePayload<ExtArgs>[]
    Connection: Prisma.$ConnectionPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    externalId: string
    description: string
    service: string
    channelType: string
    provider: string
    displayName: string
    enabled: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["channel"]>
  composites: {}
}

export type ChannelGetPayload<S extends boolean | null | undefined | ChannelDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ChannelPayload, S>

export type ChannelCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ChannelFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: ChannelCountAggregateInputType | true
  }

export interface ChannelDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Channel'], meta: { name: 'Channel' } }
  /**
   * Find zero or one Channel that matches the filter.
   * @param {ChannelFindUniqueArgs} args - Arguments to find a Channel
   * @example
   * // Get one Channel
   * const channel = await prisma.channel.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ChannelFindUniqueArgs>(args: Prisma.SelectSubset<T, ChannelFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Channel that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ChannelFindUniqueOrThrowArgs} args - Arguments to find a Channel
   * @example
   * // Get one Channel
   * const channel = await prisma.channel.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ChannelFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ChannelFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Channel that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelFindFirstArgs} args - Arguments to find a Channel
   * @example
   * // Get one Channel
   * const channel = await prisma.channel.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ChannelFindFirstArgs>(args?: Prisma.SelectSubset<T, ChannelFindFirstArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Channel that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelFindFirstOrThrowArgs} args - Arguments to find a Channel
   * @example
   * // Get one Channel
   * const channel = await prisma.channel.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ChannelFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ChannelFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Channels that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Channels
   * const channels = await prisma.channel.findMany()
   * 
   * // Get first 10 Channels
   * const channels = await prisma.channel.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const channelWithIdOnly = await prisma.channel.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ChannelFindManyArgs>(args?: Prisma.SelectSubset<T, ChannelFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Channel.
   * @param {ChannelCreateArgs} args - Arguments to create a Channel.
   * @example
   * // Create one Channel
   * const Channel = await prisma.channel.create({
   *   data: {
   *     // ... data to create a Channel
   *   }
   * })
   * 
   */
  create<T extends ChannelCreateArgs>(args: Prisma.SelectSubset<T, ChannelCreateArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Channels.
   * @param {ChannelCreateManyArgs} args - Arguments to create many Channels.
   * @example
   * // Create many Channels
   * const channel = await prisma.channel.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ChannelCreateManyArgs>(args?: Prisma.SelectSubset<T, ChannelCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Channels and returns the data saved in the database.
   * @param {ChannelCreateManyAndReturnArgs} args - Arguments to create many Channels.
   * @example
   * // Create many Channels
   * const channel = await prisma.channel.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Channels and only return the `id`
   * const channelWithIdOnly = await prisma.channel.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ChannelCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ChannelCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Channel.
   * @param {ChannelDeleteArgs} args - Arguments to delete one Channel.
   * @example
   * // Delete one Channel
   * const Channel = await prisma.channel.delete({
   *   where: {
   *     // ... filter to delete one Channel
   *   }
   * })
   * 
   */
  delete<T extends ChannelDeleteArgs>(args: Prisma.SelectSubset<T, ChannelDeleteArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Channel.
   * @param {ChannelUpdateArgs} args - Arguments to update one Channel.
   * @example
   * // Update one Channel
   * const channel = await prisma.channel.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ChannelUpdateArgs>(args: Prisma.SelectSubset<T, ChannelUpdateArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Channels.
   * @param {ChannelDeleteManyArgs} args - Arguments to filter Channels to delete.
   * @example
   * // Delete a few Channels
   * const { count } = await prisma.channel.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ChannelDeleteManyArgs>(args?: Prisma.SelectSubset<T, ChannelDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Channels.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Channels
   * const channel = await prisma.channel.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ChannelUpdateManyArgs>(args: Prisma.SelectSubset<T, ChannelUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Channels and returns the data updated in the database.
   * @param {ChannelUpdateManyAndReturnArgs} args - Arguments to update many Channels.
   * @example
   * // Update many Channels
   * const channel = await prisma.channel.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Channels and only return the `id`
   * const channelWithIdOnly = await prisma.channel.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ChannelUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ChannelUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Channel.
   * @param {ChannelUpsertArgs} args - Arguments to update or create a Channel.
   * @example
   * // Update or create a Channel
   * const channel = await prisma.channel.upsert({
   *   create: {
   *     // ... data to create a Channel
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Channel we want to update
   *   }
   * })
   */
  upsert<T extends ChannelUpsertArgs>(args: Prisma.SelectSubset<T, ChannelUpsertArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Channels.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelCountArgs} args - Arguments to filter Channels to count.
   * @example
   * // Count the number of Channels
   * const count = await prisma.channel.count({
   *   where: {
   *     // ... the filter for the Channels we want to count
   *   }
   * })
  **/
  count<T extends ChannelCountArgs>(
    args?: Prisma.Subset<T, ChannelCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ChannelCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Channel.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ChannelAggregateArgs>(args: Prisma.Subset<T, ChannelAggregateArgs>): Prisma.PrismaPromise<GetChannelAggregateType<T>>

  /**
   * Group by Channel.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ChannelGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ChannelGroupByArgs['orderBy'] }
      : { orderBy?: ChannelGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ChannelGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetChannelGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Channel model
 */
readonly fields: ChannelFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Channel.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ChannelClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  attributes<T extends Prisma.Channel$attributesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Channel$attributesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  channelMessages<T extends Prisma.Channel$channelMessagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Channel$channelMessagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  Connection<T extends Prisma.Channel$ConnectionArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Channel$ConnectionArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Channel model
 */
export interface ChannelFieldRefs {
  readonly id: Prisma.FieldRef<"Channel", 'String'>
  readonly tenantId: Prisma.FieldRef<"Channel", 'String'>
  readonly externalId: Prisma.FieldRef<"Channel", 'String'>
  readonly description: Prisma.FieldRef<"Channel", 'String'>
  readonly service: Prisma.FieldRef<"Channel", 'String'>
  readonly channelType: Prisma.FieldRef<"Channel", 'String'>
  readonly provider: Prisma.FieldRef<"Channel", 'String'>
  readonly displayName: Prisma.FieldRef<"Channel", 'String'>
  readonly enabled: Prisma.FieldRef<"Channel", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Channel", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Channel", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Channel findUnique
 */
export type ChannelFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * Filter, which Channel to fetch.
   */
  where: Prisma.ChannelWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel findUniqueOrThrow
 */
export type ChannelFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * Filter, which Channel to fetch.
   */
  where: Prisma.ChannelWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel findFirst
 */
export type ChannelFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * Filter, which Channel to fetch.
   */
  where?: Prisma.ChannelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Channels to fetch.
   */
  orderBy?: Prisma.ChannelOrderByWithRelationInput | Prisma.ChannelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Channels.
   */
  cursor?: Prisma.ChannelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Channels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Channels.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Channels.
   */
  distinct?: Prisma.ChannelScalarFieldEnum | Prisma.ChannelScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel findFirstOrThrow
 */
export type ChannelFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * Filter, which Channel to fetch.
   */
  where?: Prisma.ChannelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Channels to fetch.
   */
  orderBy?: Prisma.ChannelOrderByWithRelationInput | Prisma.ChannelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Channels.
   */
  cursor?: Prisma.ChannelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Channels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Channels.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Channels.
   */
  distinct?: Prisma.ChannelScalarFieldEnum | Prisma.ChannelScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel findMany
 */
export type ChannelFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * Filter, which Channels to fetch.
   */
  where?: Prisma.ChannelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Channels to fetch.
   */
  orderBy?: Prisma.ChannelOrderByWithRelationInput | Prisma.ChannelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Channels.
   */
  cursor?: Prisma.ChannelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Channels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Channels.
   */
  skip?: number
  distinct?: Prisma.ChannelScalarFieldEnum | Prisma.ChannelScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel create
 */
export type ChannelCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * The data needed to create a Channel.
   */
  data: Prisma.XOR<Prisma.ChannelCreateInput, Prisma.ChannelUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel createMany
 */
export type ChannelCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Channels.
   */
  data: Prisma.ChannelCreateManyInput | Prisma.ChannelCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Channel createManyAndReturn
 */
export type ChannelCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * The data used to create many Channels.
   */
  data: Prisma.ChannelCreateManyInput | Prisma.ChannelCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Channel update
 */
export type ChannelUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * The data needed to update a Channel.
   */
  data: Prisma.XOR<Prisma.ChannelUpdateInput, Prisma.ChannelUncheckedUpdateInput>
  /**
   * Choose, which Channel to update.
   */
  where: Prisma.ChannelWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel updateMany
 */
export type ChannelUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Channels.
   */
  data: Prisma.XOR<Prisma.ChannelUpdateManyMutationInput, Prisma.ChannelUncheckedUpdateManyInput>
  /**
   * Filter which Channels to update
   */
  where?: Prisma.ChannelWhereInput
  /**
   * Limit how many Channels to update.
   */
  limit?: number
}

/**
 * Channel updateManyAndReturn
 */
export type ChannelUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * The data used to update Channels.
   */
  data: Prisma.XOR<Prisma.ChannelUpdateManyMutationInput, Prisma.ChannelUncheckedUpdateManyInput>
  /**
   * Filter which Channels to update
   */
  where?: Prisma.ChannelWhereInput
  /**
   * Limit how many Channels to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Channel upsert
 */
export type ChannelUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * The filter to search for the Channel to update in case it exists.
   */
  where: Prisma.ChannelWhereUniqueInput
  /**
   * In case the Channel found by the `where` argument doesn't exist, create a new Channel with this data.
   */
  create: Prisma.XOR<Prisma.ChannelCreateInput, Prisma.ChannelUncheckedCreateInput>
  /**
   * In case the Channel was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ChannelUpdateInput, Prisma.ChannelUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel delete
 */
export type ChannelDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  /**
   * Filter which Channel to delete.
   */
  where: Prisma.ChannelWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Channel deleteMany
 */
export type ChannelDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Channels to delete
   */
  where?: Prisma.ChannelWhereInput
  /**
   * Limit how many Channels to delete.
   */
  limit?: number
}

/**
 * Channel.attributes
 */
export type Channel$attributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  where?: Prisma.ChannelAttributeWhereInput
  orderBy?: Prisma.ChannelAttributeOrderByWithRelationInput | Prisma.ChannelAttributeOrderByWithRelationInput[]
  cursor?: Prisma.ChannelAttributeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ChannelAttributeScalarFieldEnum | Prisma.ChannelAttributeScalarFieldEnum[]
}

/**
 * Channel.channelMessages
 */
export type Channel$channelMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  where?: Prisma.ChannelMessageWhereInput
  orderBy?: Prisma.ChannelMessageOrderByWithRelationInput | Prisma.ChannelMessageOrderByWithRelationInput[]
  cursor?: Prisma.ChannelMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ChannelMessageScalarFieldEnum | Prisma.ChannelMessageScalarFieldEnum[]
}

/**
 * Channel.Connection
 */
export type Channel$ConnectionArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  cursor?: Prisma.ConnectionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * Channel without action
 */
export type ChannelDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
}
