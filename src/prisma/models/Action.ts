
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Action` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Action
 * 
 */
export type ActionModel = runtime.Types.Result.DefaultSelection<Prisma.$ActionPayload>

export type AggregateAction = {
  _count: ActionCountAggregateOutputType | null
  _min: ActionMinAggregateOutputType | null
  _max: ActionMaxAggregateOutputType | null
}

export type ActionMinAggregateOutputType = {
  id: string | null
  label: string | null
  url: string | null
  localeTemplateId: string | null
}

export type ActionMaxAggregateOutputType = {
  id: string | null
  label: string | null
  url: string | null
  localeTemplateId: string | null
}

export type ActionCountAggregateOutputType = {
  id: number
  label: number
  url: number
  localeTemplateId: number
  _all: number
}


export type ActionMinAggregateInputType = {
  id?: true
  label?: true
  url?: true
  localeTemplateId?: true
}

export type ActionMaxAggregateInputType = {
  id?: true
  label?: true
  url?: true
  localeTemplateId?: true
}

export type ActionCountAggregateInputType = {
  id?: true
  label?: true
  url?: true
  localeTemplateId?: true
  _all?: true
}

export type ActionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Action to aggregate.
   */
  where?: Prisma.ActionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Actions to fetch.
   */
  orderBy?: Prisma.ActionOrderByWithRelationInput | Prisma.ActionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ActionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Actions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Actions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Actions
  **/
  _count?: true | ActionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ActionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ActionMaxAggregateInputType
}

export type GetActionAggregateType<T extends ActionAggregateArgs> = {
      [P in keyof T & keyof AggregateAction]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAction[P]>
    : Prisma.GetScalarType<T[P], AggregateAction[P]>
}




export type ActionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ActionWhereInput
  orderBy?: Prisma.ActionOrderByWithAggregationInput | Prisma.ActionOrderByWithAggregationInput[]
  by: Prisma.ActionScalarFieldEnum[] | Prisma.ActionScalarFieldEnum
  having?: Prisma.ActionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ActionCountAggregateInputType | true
  _min?: ActionMinAggregateInputType
  _max?: ActionMaxAggregateInputType
}

export type ActionGroupByOutputType = {
  id: string
  label: string
  url: string
  localeTemplateId: string | null
  _count: ActionCountAggregateOutputType | null
  _min: ActionMinAggregateOutputType | null
  _max: ActionMaxAggregateOutputType | null
}

type GetActionGroupByPayload<T extends ActionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ActionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ActionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ActionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ActionGroupByOutputType[P]>
      }
    >
  > 



export type ActionWhereInput = {
  AND?: Prisma.ActionWhereInput | Prisma.ActionWhereInput[]
  OR?: Prisma.ActionWhereInput[]
  NOT?: Prisma.ActionWhereInput | Prisma.ActionWhereInput[]
  id?: Prisma.StringFilter<"Action"> | string
  label?: Prisma.StringFilter<"Action"> | string
  url?: Prisma.StringFilter<"Action"> | string
  localeTemplateId?: Prisma.StringNullableFilter<"Action"> | string | null
  localeTemplate?: Prisma.XOR<Prisma.LocaleTemplateNullableScalarRelationFilter, Prisma.LocaleTemplateWhereInput> | null
}

export type ActionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  label?: Prisma.SortOrder
  url?: Prisma.SortOrder
  localeTemplateId?: Prisma.SortOrderInput | Prisma.SortOrder
  localeTemplate?: Prisma.LocaleTemplateOrderByWithRelationInput
}

export type ActionWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.ActionWhereInput | Prisma.ActionWhereInput[]
  OR?: Prisma.ActionWhereInput[]
  NOT?: Prisma.ActionWhereInput | Prisma.ActionWhereInput[]
  label?: Prisma.StringFilter<"Action"> | string
  url?: Prisma.StringFilter<"Action"> | string
  localeTemplateId?: Prisma.StringNullableFilter<"Action"> | string | null
  localeTemplate?: Prisma.XOR<Prisma.LocaleTemplateNullableScalarRelationFilter, Prisma.LocaleTemplateWhereInput> | null
}, "id">

export type ActionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  label?: Prisma.SortOrder
  url?: Prisma.SortOrder
  localeTemplateId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.ActionCountOrderByAggregateInput
  _max?: Prisma.ActionMaxOrderByAggregateInput
  _min?: Prisma.ActionMinOrderByAggregateInput
}

export type ActionScalarWhereWithAggregatesInput = {
  AND?: Prisma.ActionScalarWhereWithAggregatesInput | Prisma.ActionScalarWhereWithAggregatesInput[]
  OR?: Prisma.ActionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ActionScalarWhereWithAggregatesInput | Prisma.ActionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Action"> | string
  label?: Prisma.StringWithAggregatesFilter<"Action"> | string
  url?: Prisma.StringWithAggregatesFilter<"Action"> | string
  localeTemplateId?: Prisma.StringNullableWithAggregatesFilter<"Action"> | string | null
}

export type ActionCreateInput = {
  id?: string
  label: string
  url: string
  localeTemplate?: Prisma.LocaleTemplateCreateNestedOneWithoutActionButtonsInput
}

export type ActionUncheckedCreateInput = {
  id?: string
  label: string
  url: string
  localeTemplateId?: string | null
}

export type ActionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  localeTemplate?: Prisma.LocaleTemplateUpdateOneWithoutActionButtonsNestedInput
}

export type ActionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  localeTemplateId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type ActionCreateManyInput = {
  id?: string
  label: string
  url: string
  localeTemplateId?: string | null
}

export type ActionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ActionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  localeTemplateId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type ActionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  label?: Prisma.SortOrder
  url?: Prisma.SortOrder
  localeTemplateId?: Prisma.SortOrder
}

export type ActionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  label?: Prisma.SortOrder
  url?: Prisma.SortOrder
  localeTemplateId?: Prisma.SortOrder
}

export type ActionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  label?: Prisma.SortOrder
  url?: Prisma.SortOrder
  localeTemplateId?: Prisma.SortOrder
}

export type ActionListRelationFilter = {
  every?: Prisma.ActionWhereInput
  some?: Prisma.ActionWhereInput
  none?: Prisma.ActionWhereInput
}

export type ActionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ActionCreateNestedManyWithoutLocaleTemplateInput = {
  create?: Prisma.XOR<Prisma.ActionCreateWithoutLocaleTemplateInput, Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput> | Prisma.ActionCreateWithoutLocaleTemplateInput[] | Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput[]
  connectOrCreate?: Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput | Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput[]
  createMany?: Prisma.ActionCreateManyLocaleTemplateInputEnvelope
  connect?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
}

export type ActionUncheckedCreateNestedManyWithoutLocaleTemplateInput = {
  create?: Prisma.XOR<Prisma.ActionCreateWithoutLocaleTemplateInput, Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput> | Prisma.ActionCreateWithoutLocaleTemplateInput[] | Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput[]
  connectOrCreate?: Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput | Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput[]
  createMany?: Prisma.ActionCreateManyLocaleTemplateInputEnvelope
  connect?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
}

export type ActionUpdateManyWithoutLocaleTemplateNestedInput = {
  create?: Prisma.XOR<Prisma.ActionCreateWithoutLocaleTemplateInput, Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput> | Prisma.ActionCreateWithoutLocaleTemplateInput[] | Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput[]
  connectOrCreate?: Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput | Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput[]
  upsert?: Prisma.ActionUpsertWithWhereUniqueWithoutLocaleTemplateInput | Prisma.ActionUpsertWithWhereUniqueWithoutLocaleTemplateInput[]
  createMany?: Prisma.ActionCreateManyLocaleTemplateInputEnvelope
  set?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  disconnect?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  delete?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  connect?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  update?: Prisma.ActionUpdateWithWhereUniqueWithoutLocaleTemplateInput | Prisma.ActionUpdateWithWhereUniqueWithoutLocaleTemplateInput[]
  updateMany?: Prisma.ActionUpdateManyWithWhereWithoutLocaleTemplateInput | Prisma.ActionUpdateManyWithWhereWithoutLocaleTemplateInput[]
  deleteMany?: Prisma.ActionScalarWhereInput | Prisma.ActionScalarWhereInput[]
}

export type ActionUncheckedUpdateManyWithoutLocaleTemplateNestedInput = {
  create?: Prisma.XOR<Prisma.ActionCreateWithoutLocaleTemplateInput, Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput> | Prisma.ActionCreateWithoutLocaleTemplateInput[] | Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput[]
  connectOrCreate?: Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput | Prisma.ActionCreateOrConnectWithoutLocaleTemplateInput[]
  upsert?: Prisma.ActionUpsertWithWhereUniqueWithoutLocaleTemplateInput | Prisma.ActionUpsertWithWhereUniqueWithoutLocaleTemplateInput[]
  createMany?: Prisma.ActionCreateManyLocaleTemplateInputEnvelope
  set?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  disconnect?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  delete?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  connect?: Prisma.ActionWhereUniqueInput | Prisma.ActionWhereUniqueInput[]
  update?: Prisma.ActionUpdateWithWhereUniqueWithoutLocaleTemplateInput | Prisma.ActionUpdateWithWhereUniqueWithoutLocaleTemplateInput[]
  updateMany?: Prisma.ActionUpdateManyWithWhereWithoutLocaleTemplateInput | Prisma.ActionUpdateManyWithWhereWithoutLocaleTemplateInput[]
  deleteMany?: Prisma.ActionScalarWhereInput | Prisma.ActionScalarWhereInput[]
}

export type ActionCreateWithoutLocaleTemplateInput = {
  id?: string
  label: string
  url: string
}

export type ActionUncheckedCreateWithoutLocaleTemplateInput = {
  id?: string
  label: string
  url: string
}

export type ActionCreateOrConnectWithoutLocaleTemplateInput = {
  where: Prisma.ActionWhereUniqueInput
  create: Prisma.XOR<Prisma.ActionCreateWithoutLocaleTemplateInput, Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput>
}

export type ActionCreateManyLocaleTemplateInputEnvelope = {
  data: Prisma.ActionCreateManyLocaleTemplateInput | Prisma.ActionCreateManyLocaleTemplateInput[]
  skipDuplicates?: boolean
}

export type ActionUpsertWithWhereUniqueWithoutLocaleTemplateInput = {
  where: Prisma.ActionWhereUniqueInput
  update: Prisma.XOR<Prisma.ActionUpdateWithoutLocaleTemplateInput, Prisma.ActionUncheckedUpdateWithoutLocaleTemplateInput>
  create: Prisma.XOR<Prisma.ActionCreateWithoutLocaleTemplateInput, Prisma.ActionUncheckedCreateWithoutLocaleTemplateInput>
}

export type ActionUpdateWithWhereUniqueWithoutLocaleTemplateInput = {
  where: Prisma.ActionWhereUniqueInput
  data: Prisma.XOR<Prisma.ActionUpdateWithoutLocaleTemplateInput, Prisma.ActionUncheckedUpdateWithoutLocaleTemplateInput>
}

export type ActionUpdateManyWithWhereWithoutLocaleTemplateInput = {
  where: Prisma.ActionScalarWhereInput
  data: Prisma.XOR<Prisma.ActionUpdateManyMutationInput, Prisma.ActionUncheckedUpdateManyWithoutLocaleTemplateInput>
}

export type ActionScalarWhereInput = {
  AND?: Prisma.ActionScalarWhereInput | Prisma.ActionScalarWhereInput[]
  OR?: Prisma.ActionScalarWhereInput[]
  NOT?: Prisma.ActionScalarWhereInput | Prisma.ActionScalarWhereInput[]
  id?: Prisma.StringFilter<"Action"> | string
  label?: Prisma.StringFilter<"Action"> | string
  url?: Prisma.StringFilter<"Action"> | string
  localeTemplateId?: Prisma.StringNullableFilter<"Action"> | string | null
}

export type ActionCreateManyLocaleTemplateInput = {
  id?: string
  label: string
  url: string
}

export type ActionUpdateWithoutLocaleTemplateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ActionUncheckedUpdateWithoutLocaleTemplateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ActionUncheckedUpdateManyWithoutLocaleTemplateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  label?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
}



export type ActionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  label?: boolean
  url?: boolean
  localeTemplateId?: boolean
  localeTemplate?: boolean | Prisma.Action$localeTemplateArgs<ExtArgs>
}, ExtArgs["result"]["action"]>

export type ActionSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  label?: boolean
  url?: boolean
  localeTemplateId?: boolean
  localeTemplate?: boolean | Prisma.Action$localeTemplateArgs<ExtArgs>
}, ExtArgs["result"]["action"]>

export type ActionSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  label?: boolean
  url?: boolean
  localeTemplateId?: boolean
  localeTemplate?: boolean | Prisma.Action$localeTemplateArgs<ExtArgs>
}, ExtArgs["result"]["action"]>

export type ActionSelectScalar = {
  id?: boolean
  label?: boolean
  url?: boolean
  localeTemplateId?: boolean
}

export type ActionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "label" | "url" | "localeTemplateId", ExtArgs["result"]["action"]>
export type ActionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  localeTemplate?: boolean | Prisma.Action$localeTemplateArgs<ExtArgs>
}
export type ActionIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  localeTemplate?: boolean | Prisma.Action$localeTemplateArgs<ExtArgs>
}
export type ActionIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  localeTemplate?: boolean | Prisma.Action$localeTemplateArgs<ExtArgs>
}

export type $ActionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Action"
  objects: {
    localeTemplate: Prisma.$LocaleTemplatePayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    label: string
    url: string
    localeTemplateId: string | null
  }, ExtArgs["result"]["action"]>
  composites: {}
}

export type ActionGetPayload<S extends boolean | null | undefined | ActionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ActionPayload, S>

export type ActionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ActionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: ActionCountAggregateInputType | true
  }

export interface ActionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Action'], meta: { name: 'Action' } }
  /**
   * Find zero or one Action that matches the filter.
   * @param {ActionFindUniqueArgs} args - Arguments to find a Action
   * @example
   * // Get one Action
   * const action = await prisma.action.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ActionFindUniqueArgs>(args: Prisma.SelectSubset<T, ActionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Action that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ActionFindUniqueOrThrowArgs} args - Arguments to find a Action
   * @example
   * // Get one Action
   * const action = await prisma.action.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ActionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ActionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Action that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionFindFirstArgs} args - Arguments to find a Action
   * @example
   * // Get one Action
   * const action = await prisma.action.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ActionFindFirstArgs>(args?: Prisma.SelectSubset<T, ActionFindFirstArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Action that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionFindFirstOrThrowArgs} args - Arguments to find a Action
   * @example
   * // Get one Action
   * const action = await prisma.action.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ActionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ActionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Actions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Actions
   * const actions = await prisma.action.findMany()
   * 
   * // Get first 10 Actions
   * const actions = await prisma.action.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const actionWithIdOnly = await prisma.action.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ActionFindManyArgs>(args?: Prisma.SelectSubset<T, ActionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Action.
   * @param {ActionCreateArgs} args - Arguments to create a Action.
   * @example
   * // Create one Action
   * const Action = await prisma.action.create({
   *   data: {
   *     // ... data to create a Action
   *   }
   * })
   * 
   */
  create<T extends ActionCreateArgs>(args: Prisma.SelectSubset<T, ActionCreateArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Actions.
   * @param {ActionCreateManyArgs} args - Arguments to create many Actions.
   * @example
   * // Create many Actions
   * const action = await prisma.action.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ActionCreateManyArgs>(args?: Prisma.SelectSubset<T, ActionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Actions and returns the data saved in the database.
   * @param {ActionCreateManyAndReturnArgs} args - Arguments to create many Actions.
   * @example
   * // Create many Actions
   * const action = await prisma.action.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Actions and only return the `id`
   * const actionWithIdOnly = await prisma.action.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ActionCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ActionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Action.
   * @param {ActionDeleteArgs} args - Arguments to delete one Action.
   * @example
   * // Delete one Action
   * const Action = await prisma.action.delete({
   *   where: {
   *     // ... filter to delete one Action
   *   }
   * })
   * 
   */
  delete<T extends ActionDeleteArgs>(args: Prisma.SelectSubset<T, ActionDeleteArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Action.
   * @param {ActionUpdateArgs} args - Arguments to update one Action.
   * @example
   * // Update one Action
   * const action = await prisma.action.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ActionUpdateArgs>(args: Prisma.SelectSubset<T, ActionUpdateArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Actions.
   * @param {ActionDeleteManyArgs} args - Arguments to filter Actions to delete.
   * @example
   * // Delete a few Actions
   * const { count } = await prisma.action.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ActionDeleteManyArgs>(args?: Prisma.SelectSubset<T, ActionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Actions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Actions
   * const action = await prisma.action.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ActionUpdateManyArgs>(args: Prisma.SelectSubset<T, ActionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Actions and returns the data updated in the database.
   * @param {ActionUpdateManyAndReturnArgs} args - Arguments to update many Actions.
   * @example
   * // Update many Actions
   * const action = await prisma.action.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Actions and only return the `id`
   * const actionWithIdOnly = await prisma.action.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ActionUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ActionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Action.
   * @param {ActionUpsertArgs} args - Arguments to update or create a Action.
   * @example
   * // Update or create a Action
   * const action = await prisma.action.upsert({
   *   create: {
   *     // ... data to create a Action
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Action we want to update
   *   }
   * })
   */
  upsert<T extends ActionUpsertArgs>(args: Prisma.SelectSubset<T, ActionUpsertArgs<ExtArgs>>): Prisma.Prisma__ActionClient<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Actions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionCountArgs} args - Arguments to filter Actions to count.
   * @example
   * // Count the number of Actions
   * const count = await prisma.action.count({
   *   where: {
   *     // ... the filter for the Actions we want to count
   *   }
   * })
  **/
  count<T extends ActionCountArgs>(
    args?: Prisma.Subset<T, ActionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ActionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Action.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ActionAggregateArgs>(args: Prisma.Subset<T, ActionAggregateArgs>): Prisma.PrismaPromise<GetActionAggregateType<T>>

  /**
   * Group by Action.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ActionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ActionGroupByArgs['orderBy'] }
      : { orderBy?: ActionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ActionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetActionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Action model
 */
readonly fields: ActionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Action.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ActionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  localeTemplate<T extends Prisma.Action$localeTemplateArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Action$localeTemplateArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Action model
 */
export interface ActionFieldRefs {
  readonly id: Prisma.FieldRef<"Action", 'String'>
  readonly label: Prisma.FieldRef<"Action", 'String'>
  readonly url: Prisma.FieldRef<"Action", 'String'>
  readonly localeTemplateId: Prisma.FieldRef<"Action", 'String'>
}
    

// Custom InputTypes
/**
 * Action findUnique
 */
export type ActionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * Filter, which Action to fetch.
   */
  where: Prisma.ActionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action findUniqueOrThrow
 */
export type ActionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * Filter, which Action to fetch.
   */
  where: Prisma.ActionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action findFirst
 */
export type ActionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * Filter, which Action to fetch.
   */
  where?: Prisma.ActionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Actions to fetch.
   */
  orderBy?: Prisma.ActionOrderByWithRelationInput | Prisma.ActionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Actions.
   */
  cursor?: Prisma.ActionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Actions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Actions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Actions.
   */
  distinct?: Prisma.ActionScalarFieldEnum | Prisma.ActionScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action findFirstOrThrow
 */
export type ActionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * Filter, which Action to fetch.
   */
  where?: Prisma.ActionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Actions to fetch.
   */
  orderBy?: Prisma.ActionOrderByWithRelationInput | Prisma.ActionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Actions.
   */
  cursor?: Prisma.ActionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Actions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Actions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Actions.
   */
  distinct?: Prisma.ActionScalarFieldEnum | Prisma.ActionScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action findMany
 */
export type ActionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * Filter, which Actions to fetch.
   */
  where?: Prisma.ActionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Actions to fetch.
   */
  orderBy?: Prisma.ActionOrderByWithRelationInput | Prisma.ActionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Actions.
   */
  cursor?: Prisma.ActionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Actions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Actions.
   */
  skip?: number
  distinct?: Prisma.ActionScalarFieldEnum | Prisma.ActionScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action create
 */
export type ActionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * The data needed to create a Action.
   */
  data: Prisma.XOR<Prisma.ActionCreateInput, Prisma.ActionUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action createMany
 */
export type ActionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Actions.
   */
  data: Prisma.ActionCreateManyInput | Prisma.ActionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Action createManyAndReturn
 */
export type ActionCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * The data used to create many Actions.
   */
  data: Prisma.ActionCreateManyInput | Prisma.ActionCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Action update
 */
export type ActionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * The data needed to update a Action.
   */
  data: Prisma.XOR<Prisma.ActionUpdateInput, Prisma.ActionUncheckedUpdateInput>
  /**
   * Choose, which Action to update.
   */
  where: Prisma.ActionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action updateMany
 */
export type ActionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Actions.
   */
  data: Prisma.XOR<Prisma.ActionUpdateManyMutationInput, Prisma.ActionUncheckedUpdateManyInput>
  /**
   * Filter which Actions to update
   */
  where?: Prisma.ActionWhereInput
  /**
   * Limit how many Actions to update.
   */
  limit?: number
}

/**
 * Action updateManyAndReturn
 */
export type ActionUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * The data used to update Actions.
   */
  data: Prisma.XOR<Prisma.ActionUpdateManyMutationInput, Prisma.ActionUncheckedUpdateManyInput>
  /**
   * Filter which Actions to update
   */
  where?: Prisma.ActionWhereInput
  /**
   * Limit how many Actions to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Action upsert
 */
export type ActionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * The filter to search for the Action to update in case it exists.
   */
  where: Prisma.ActionWhereUniqueInput
  /**
   * In case the Action found by the `where` argument doesn't exist, create a new Action with this data.
   */
  create: Prisma.XOR<Prisma.ActionCreateInput, Prisma.ActionUncheckedCreateInput>
  /**
   * In case the Action was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ActionUpdateInput, Prisma.ActionUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action delete
 */
export type ActionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  /**
   * Filter which Action to delete.
   */
  where: Prisma.ActionWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Action deleteMany
 */
export type ActionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Actions to delete
   */
  where?: Prisma.ActionWhereInput
  /**
   * Limit how many Actions to delete.
   */
  limit?: number
}

/**
 * Action.localeTemplate
 */
export type Action$localeTemplateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  where?: Prisma.LocaleTemplateWhereInput
}

/**
 * Action without action
 */
export type ActionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
}
