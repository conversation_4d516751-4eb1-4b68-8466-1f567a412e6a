
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Template` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Template
 * 
 */
export type TemplateModel = runtime.Types.Result.DefaultSelection<Prisma.$TemplatePayload>

export type AggregateTemplate = {
  _count: TemplateCountAggregateOutputType | null
  _min: TemplateMinAggregateOutputType | null
  _max: TemplateMaxAggregateOutputType | null
}

export type TemplateMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  displayName: string | null
  description: string | null
  isNotificationTemplate: boolean | null
}

export type TemplateMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  displayName: string | null
  description: string | null
  isNotificationTemplate: boolean | null
}

export type TemplateCountAggregateOutputType = {
  id: number
  tenantId: number
  externalId: number
  displayName: number
  description: number
  isNotificationTemplate: number
  _all: number
}


export type TemplateMinAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  isNotificationTemplate?: true
}

export type TemplateMaxAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  isNotificationTemplate?: true
}

export type TemplateCountAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  isNotificationTemplate?: true
  _all?: true
}

export type TemplateAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Template to aggregate.
   */
  where?: Prisma.TemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Templates to fetch.
   */
  orderBy?: Prisma.TemplateOrderByWithRelationInput | Prisma.TemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Templates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Templates.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Templates
  **/
  _count?: true | TemplateCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TemplateMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TemplateMaxAggregateInputType
}

export type GetTemplateAggregateType<T extends TemplateAggregateArgs> = {
      [P in keyof T & keyof AggregateTemplate]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTemplate[P]>
    : Prisma.GetScalarType<T[P], AggregateTemplate[P]>
}




export type TemplateGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TemplateWhereInput
  orderBy?: Prisma.TemplateOrderByWithAggregationInput | Prisma.TemplateOrderByWithAggregationInput[]
  by: Prisma.TemplateScalarFieldEnum[] | Prisma.TemplateScalarFieldEnum
  having?: Prisma.TemplateScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TemplateCountAggregateInputType | true
  _min?: TemplateMinAggregateInputType
  _max?: TemplateMaxAggregateInputType
}

export type TemplateGroupByOutputType = {
  id: string
  tenantId: string
  externalId: string
  displayName: string
  description: string
  isNotificationTemplate: boolean
  _count: TemplateCountAggregateOutputType | null
  _min: TemplateMinAggregateOutputType | null
  _max: TemplateMaxAggregateOutputType | null
}

type GetTemplateGroupByPayload<T extends TemplateGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TemplateGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TemplateGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TemplateGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TemplateGroupByOutputType[P]>
      }
    >
  > 



export type TemplateWhereInput = {
  AND?: Prisma.TemplateWhereInput | Prisma.TemplateWhereInput[]
  OR?: Prisma.TemplateWhereInput[]
  NOT?: Prisma.TemplateWhereInput | Prisma.TemplateWhereInput[]
  id?: Prisma.StringFilter<"Template"> | string
  tenantId?: Prisma.StringFilter<"Template"> | string
  externalId?: Prisma.StringFilter<"Template"> | string
  displayName?: Prisma.StringFilter<"Template"> | string
  description?: Prisma.StringFilter<"Template"> | string
  isNotificationTemplate?: Prisma.BoolFilter<"Template"> | boolean
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  localeTemplates?: Prisma.LocaleTemplateListRelationFilter
}

export type TemplateOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  isNotificationTemplate?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  localeTemplates?: Prisma.LocaleTemplateOrderByRelationAggregateInput
}

export type TemplateWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TemplateWhereInput | Prisma.TemplateWhereInput[]
  OR?: Prisma.TemplateWhereInput[]
  NOT?: Prisma.TemplateWhereInput | Prisma.TemplateWhereInput[]
  tenantId?: Prisma.StringFilter<"Template"> | string
  externalId?: Prisma.StringFilter<"Template"> | string
  displayName?: Prisma.StringFilter<"Template"> | string
  description?: Prisma.StringFilter<"Template"> | string
  isNotificationTemplate?: Prisma.BoolFilter<"Template"> | boolean
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  localeTemplates?: Prisma.LocaleTemplateListRelationFilter
}, "id">

export type TemplateOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  isNotificationTemplate?: Prisma.SortOrder
  _count?: Prisma.TemplateCountOrderByAggregateInput
  _max?: Prisma.TemplateMaxOrderByAggregateInput
  _min?: Prisma.TemplateMinOrderByAggregateInput
}

export type TemplateScalarWhereWithAggregatesInput = {
  AND?: Prisma.TemplateScalarWhereWithAggregatesInput | Prisma.TemplateScalarWhereWithAggregatesInput[]
  OR?: Prisma.TemplateScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TemplateScalarWhereWithAggregatesInput | Prisma.TemplateScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Template"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"Template"> | string
  externalId?: Prisma.StringWithAggregatesFilter<"Template"> | string
  displayName?: Prisma.StringWithAggregatesFilter<"Template"> | string
  description?: Prisma.StringWithAggregatesFilter<"Template"> | string
  isNotificationTemplate?: Prisma.BoolWithAggregatesFilter<"Template"> | boolean
}

export type TemplateCreateInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
  tenant: Prisma.TenantCreateNestedOneWithoutTemplatesInput
  localeTemplates?: Prisma.LocaleTemplateCreateNestedManyWithoutTemplateInput
}

export type TemplateUncheckedCreateInput = {
  id?: string
  tenantId: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
  localeTemplates?: Prisma.LocaleTemplateUncheckedCreateNestedManyWithoutTemplateInput
}

export type TemplateUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTemplatesNestedInput
  localeTemplates?: Prisma.LocaleTemplateUpdateManyWithoutTemplateNestedInput
}

export type TemplateUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  localeTemplates?: Prisma.LocaleTemplateUncheckedUpdateManyWithoutTemplateNestedInput
}

export type TemplateCreateManyInput = {
  id?: string
  tenantId: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
}

export type TemplateUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TemplateUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TemplateScalarRelationFilter = {
  is?: Prisma.TemplateWhereInput
  isNot?: Prisma.TemplateWhereInput
}

export type TemplateCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  isNotificationTemplate?: Prisma.SortOrder
}

export type TemplateMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  isNotificationTemplate?: Prisma.SortOrder
}

export type TemplateMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  isNotificationTemplate?: Prisma.SortOrder
}

export type TemplateListRelationFilter = {
  every?: Prisma.TemplateWhereInput
  some?: Prisma.TemplateWhereInput
  none?: Prisma.TemplateWhereInput
}

export type TemplateOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TemplateCreateNestedOneWithoutLocaleTemplatesInput = {
  create?: Prisma.XOR<Prisma.TemplateCreateWithoutLocaleTemplatesInput, Prisma.TemplateUncheckedCreateWithoutLocaleTemplatesInput>
  connectOrCreate?: Prisma.TemplateCreateOrConnectWithoutLocaleTemplatesInput
  connect?: Prisma.TemplateWhereUniqueInput
}

export type TemplateUpdateOneRequiredWithoutLocaleTemplatesNestedInput = {
  create?: Prisma.XOR<Prisma.TemplateCreateWithoutLocaleTemplatesInput, Prisma.TemplateUncheckedCreateWithoutLocaleTemplatesInput>
  connectOrCreate?: Prisma.TemplateCreateOrConnectWithoutLocaleTemplatesInput
  upsert?: Prisma.TemplateUpsertWithoutLocaleTemplatesInput
  connect?: Prisma.TemplateWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TemplateUpdateToOneWithWhereWithoutLocaleTemplatesInput, Prisma.TemplateUpdateWithoutLocaleTemplatesInput>, Prisma.TemplateUncheckedUpdateWithoutLocaleTemplatesInput>
}

export type TemplateCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TemplateCreateWithoutTenantInput, Prisma.TemplateUncheckedCreateWithoutTenantInput> | Prisma.TemplateCreateWithoutTenantInput[] | Prisma.TemplateUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TemplateCreateOrConnectWithoutTenantInput | Prisma.TemplateCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TemplateCreateManyTenantInputEnvelope
  connect?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
}

export type TemplateUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TemplateCreateWithoutTenantInput, Prisma.TemplateUncheckedCreateWithoutTenantInput> | Prisma.TemplateCreateWithoutTenantInput[] | Prisma.TemplateUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TemplateCreateOrConnectWithoutTenantInput | Prisma.TemplateCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TemplateCreateManyTenantInputEnvelope
  connect?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
}

export type TemplateUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TemplateCreateWithoutTenantInput, Prisma.TemplateUncheckedCreateWithoutTenantInput> | Prisma.TemplateCreateWithoutTenantInput[] | Prisma.TemplateUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TemplateCreateOrConnectWithoutTenantInput | Prisma.TemplateCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TemplateUpsertWithWhereUniqueWithoutTenantInput | Prisma.TemplateUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TemplateCreateManyTenantInputEnvelope
  set?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  disconnect?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  delete?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  connect?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  update?: Prisma.TemplateUpdateWithWhereUniqueWithoutTenantInput | Prisma.TemplateUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TemplateUpdateManyWithWhereWithoutTenantInput | Prisma.TemplateUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TemplateScalarWhereInput | Prisma.TemplateScalarWhereInput[]
}

export type TemplateUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TemplateCreateWithoutTenantInput, Prisma.TemplateUncheckedCreateWithoutTenantInput> | Prisma.TemplateCreateWithoutTenantInput[] | Prisma.TemplateUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TemplateCreateOrConnectWithoutTenantInput | Prisma.TemplateCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TemplateUpsertWithWhereUniqueWithoutTenantInput | Prisma.TemplateUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TemplateCreateManyTenantInputEnvelope
  set?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  disconnect?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  delete?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  connect?: Prisma.TemplateWhereUniqueInput | Prisma.TemplateWhereUniqueInput[]
  update?: Prisma.TemplateUpdateWithWhereUniqueWithoutTenantInput | Prisma.TemplateUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TemplateUpdateManyWithWhereWithoutTenantInput | Prisma.TemplateUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TemplateScalarWhereInput | Prisma.TemplateScalarWhereInput[]
}

export type TemplateCreateWithoutLocaleTemplatesInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
  tenant: Prisma.TenantCreateNestedOneWithoutTemplatesInput
}

export type TemplateUncheckedCreateWithoutLocaleTemplatesInput = {
  id?: string
  tenantId: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
}

export type TemplateCreateOrConnectWithoutLocaleTemplatesInput = {
  where: Prisma.TemplateWhereUniqueInput
  create: Prisma.XOR<Prisma.TemplateCreateWithoutLocaleTemplatesInput, Prisma.TemplateUncheckedCreateWithoutLocaleTemplatesInput>
}

export type TemplateUpsertWithoutLocaleTemplatesInput = {
  update: Prisma.XOR<Prisma.TemplateUpdateWithoutLocaleTemplatesInput, Prisma.TemplateUncheckedUpdateWithoutLocaleTemplatesInput>
  create: Prisma.XOR<Prisma.TemplateCreateWithoutLocaleTemplatesInput, Prisma.TemplateUncheckedCreateWithoutLocaleTemplatesInput>
  where?: Prisma.TemplateWhereInput
}

export type TemplateUpdateToOneWithWhereWithoutLocaleTemplatesInput = {
  where?: Prisma.TemplateWhereInput
  data: Prisma.XOR<Prisma.TemplateUpdateWithoutLocaleTemplatesInput, Prisma.TemplateUncheckedUpdateWithoutLocaleTemplatesInput>
}

export type TemplateUpdateWithoutLocaleTemplatesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTemplatesNestedInput
}

export type TemplateUncheckedUpdateWithoutLocaleTemplatesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TemplateCreateWithoutTenantInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
  localeTemplates?: Prisma.LocaleTemplateCreateNestedManyWithoutTemplateInput
}

export type TemplateUncheckedCreateWithoutTenantInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
  localeTemplates?: Prisma.LocaleTemplateUncheckedCreateNestedManyWithoutTemplateInput
}

export type TemplateCreateOrConnectWithoutTenantInput = {
  where: Prisma.TemplateWhereUniqueInput
  create: Prisma.XOR<Prisma.TemplateCreateWithoutTenantInput, Prisma.TemplateUncheckedCreateWithoutTenantInput>
}

export type TemplateCreateManyTenantInputEnvelope = {
  data: Prisma.TemplateCreateManyTenantInput | Prisma.TemplateCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type TemplateUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TemplateWhereUniqueInput
  update: Prisma.XOR<Prisma.TemplateUpdateWithoutTenantInput, Prisma.TemplateUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.TemplateCreateWithoutTenantInput, Prisma.TemplateUncheckedCreateWithoutTenantInput>
}

export type TemplateUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TemplateWhereUniqueInput
  data: Prisma.XOR<Prisma.TemplateUpdateWithoutTenantInput, Prisma.TemplateUncheckedUpdateWithoutTenantInput>
}

export type TemplateUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.TemplateScalarWhereInput
  data: Prisma.XOR<Prisma.TemplateUpdateManyMutationInput, Prisma.TemplateUncheckedUpdateManyWithoutTenantInput>
}

export type TemplateScalarWhereInput = {
  AND?: Prisma.TemplateScalarWhereInput | Prisma.TemplateScalarWhereInput[]
  OR?: Prisma.TemplateScalarWhereInput[]
  NOT?: Prisma.TemplateScalarWhereInput | Prisma.TemplateScalarWhereInput[]
  id?: Prisma.StringFilter<"Template"> | string
  tenantId?: Prisma.StringFilter<"Template"> | string
  externalId?: Prisma.StringFilter<"Template"> | string
  displayName?: Prisma.StringFilter<"Template"> | string
  description?: Prisma.StringFilter<"Template"> | string
  isNotificationTemplate?: Prisma.BoolFilter<"Template"> | boolean
}

export type TemplateCreateManyTenantInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  isNotificationTemplate?: boolean
}

export type TemplateUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  localeTemplates?: Prisma.LocaleTemplateUpdateManyWithoutTemplateNestedInput
}

export type TemplateUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  localeTemplates?: Prisma.LocaleTemplateUncheckedUpdateManyWithoutTemplateNestedInput
}

export type TemplateUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationTemplate?: Prisma.BoolFieldUpdateOperationsInput | boolean
}


/**
 * Count Type TemplateCountOutputType
 */

export type TemplateCountOutputType = {
  localeTemplates: number
}

export type TemplateCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  localeTemplates?: boolean | TemplateCountOutputTypeCountLocaleTemplatesArgs
}

/**
 * TemplateCountOutputType without action
 */
export type TemplateCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TemplateCountOutputType
   */
  select?: Prisma.TemplateCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TemplateCountOutputType without action
 */
export type TemplateCountOutputTypeCountLocaleTemplatesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LocaleTemplateWhereInput
}


export type TemplateSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  isNotificationTemplate?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  localeTemplates?: boolean | Prisma.Template$localeTemplatesArgs<ExtArgs>
  _count?: boolean | Prisma.TemplateCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["template"]>

export type TemplateSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  isNotificationTemplate?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["template"]>

export type TemplateSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  isNotificationTemplate?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["template"]>

export type TemplateSelectScalar = {
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  isNotificationTemplate?: boolean
}

export type TemplateOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "externalId" | "displayName" | "description" | "isNotificationTemplate", ExtArgs["result"]["template"]>
export type TemplateInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  localeTemplates?: boolean | Prisma.Template$localeTemplatesArgs<ExtArgs>
  _count?: boolean | Prisma.TemplateCountOutputTypeDefaultArgs<ExtArgs>
}
export type TemplateIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TemplateIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $TemplatePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Template"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    localeTemplates: Prisma.$LocaleTemplatePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    externalId: string
    displayName: string
    description: string
    isNotificationTemplate: boolean
  }, ExtArgs["result"]["template"]>
  composites: {}
}

export type TemplateGetPayload<S extends boolean | null | undefined | TemplateDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TemplatePayload, S>

export type TemplateCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TemplateFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TemplateCountAggregateInputType | true
  }

export interface TemplateDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Template'], meta: { name: 'Template' } }
  /**
   * Find zero or one Template that matches the filter.
   * @param {TemplateFindUniqueArgs} args - Arguments to find a Template
   * @example
   * // Get one Template
   * const template = await prisma.template.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TemplateFindUniqueArgs>(args: Prisma.SelectSubset<T, TemplateFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Template that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TemplateFindUniqueOrThrowArgs} args - Arguments to find a Template
   * @example
   * // Get one Template
   * const template = await prisma.template.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TemplateFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TemplateFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Template that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateFindFirstArgs} args - Arguments to find a Template
   * @example
   * // Get one Template
   * const template = await prisma.template.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TemplateFindFirstArgs>(args?: Prisma.SelectSubset<T, TemplateFindFirstArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Template that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateFindFirstOrThrowArgs} args - Arguments to find a Template
   * @example
   * // Get one Template
   * const template = await prisma.template.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TemplateFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TemplateFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Templates that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Templates
   * const templates = await prisma.template.findMany()
   * 
   * // Get first 10 Templates
   * const templates = await prisma.template.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const templateWithIdOnly = await prisma.template.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TemplateFindManyArgs>(args?: Prisma.SelectSubset<T, TemplateFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Template.
   * @param {TemplateCreateArgs} args - Arguments to create a Template.
   * @example
   * // Create one Template
   * const Template = await prisma.template.create({
   *   data: {
   *     // ... data to create a Template
   *   }
   * })
   * 
   */
  create<T extends TemplateCreateArgs>(args: Prisma.SelectSubset<T, TemplateCreateArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Templates.
   * @param {TemplateCreateManyArgs} args - Arguments to create many Templates.
   * @example
   * // Create many Templates
   * const template = await prisma.template.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TemplateCreateManyArgs>(args?: Prisma.SelectSubset<T, TemplateCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Templates and returns the data saved in the database.
   * @param {TemplateCreateManyAndReturnArgs} args - Arguments to create many Templates.
   * @example
   * // Create many Templates
   * const template = await prisma.template.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Templates and only return the `id`
   * const templateWithIdOnly = await prisma.template.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TemplateCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TemplateCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Template.
   * @param {TemplateDeleteArgs} args - Arguments to delete one Template.
   * @example
   * // Delete one Template
   * const Template = await prisma.template.delete({
   *   where: {
   *     // ... filter to delete one Template
   *   }
   * })
   * 
   */
  delete<T extends TemplateDeleteArgs>(args: Prisma.SelectSubset<T, TemplateDeleteArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Template.
   * @param {TemplateUpdateArgs} args - Arguments to update one Template.
   * @example
   * // Update one Template
   * const template = await prisma.template.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TemplateUpdateArgs>(args: Prisma.SelectSubset<T, TemplateUpdateArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Templates.
   * @param {TemplateDeleteManyArgs} args - Arguments to filter Templates to delete.
   * @example
   * // Delete a few Templates
   * const { count } = await prisma.template.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TemplateDeleteManyArgs>(args?: Prisma.SelectSubset<T, TemplateDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Templates.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Templates
   * const template = await prisma.template.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TemplateUpdateManyArgs>(args: Prisma.SelectSubset<T, TemplateUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Templates and returns the data updated in the database.
   * @param {TemplateUpdateManyAndReturnArgs} args - Arguments to update many Templates.
   * @example
   * // Update many Templates
   * const template = await prisma.template.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Templates and only return the `id`
   * const templateWithIdOnly = await prisma.template.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TemplateUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TemplateUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Template.
   * @param {TemplateUpsertArgs} args - Arguments to update or create a Template.
   * @example
   * // Update or create a Template
   * const template = await prisma.template.upsert({
   *   create: {
   *     // ... data to create a Template
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Template we want to update
   *   }
   * })
   */
  upsert<T extends TemplateUpsertArgs>(args: Prisma.SelectSubset<T, TemplateUpsertArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Templates.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateCountArgs} args - Arguments to filter Templates to count.
   * @example
   * // Count the number of Templates
   * const count = await prisma.template.count({
   *   where: {
   *     // ... the filter for the Templates we want to count
   *   }
   * })
  **/
  count<T extends TemplateCountArgs>(
    args?: Prisma.Subset<T, TemplateCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TemplateCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Template.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TemplateAggregateArgs>(args: Prisma.Subset<T, TemplateAggregateArgs>): Prisma.PrismaPromise<GetTemplateAggregateType<T>>

  /**
   * Group by Template.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TemplateGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TemplateGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TemplateGroupByArgs['orderBy'] }
      : { orderBy?: TemplateGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TemplateGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTemplateGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Template model
 */
readonly fields: TemplateFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Template.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TemplateClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  localeTemplates<T extends Prisma.Template$localeTemplatesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Template$localeTemplatesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Template model
 */
export interface TemplateFieldRefs {
  readonly id: Prisma.FieldRef<"Template", 'String'>
  readonly tenantId: Prisma.FieldRef<"Template", 'String'>
  readonly externalId: Prisma.FieldRef<"Template", 'String'>
  readonly displayName: Prisma.FieldRef<"Template", 'String'>
  readonly description: Prisma.FieldRef<"Template", 'String'>
  readonly isNotificationTemplate: Prisma.FieldRef<"Template", 'Boolean'>
}
    

// Custom InputTypes
/**
 * Template findUnique
 */
export type TemplateFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * Filter, which Template to fetch.
   */
  where: Prisma.TemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template findUniqueOrThrow
 */
export type TemplateFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * Filter, which Template to fetch.
   */
  where: Prisma.TemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template findFirst
 */
export type TemplateFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * Filter, which Template to fetch.
   */
  where?: Prisma.TemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Templates to fetch.
   */
  orderBy?: Prisma.TemplateOrderByWithRelationInput | Prisma.TemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Templates.
   */
  cursor?: Prisma.TemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Templates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Templates.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Templates.
   */
  distinct?: Prisma.TemplateScalarFieldEnum | Prisma.TemplateScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template findFirstOrThrow
 */
export type TemplateFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * Filter, which Template to fetch.
   */
  where?: Prisma.TemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Templates to fetch.
   */
  orderBy?: Prisma.TemplateOrderByWithRelationInput | Prisma.TemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Templates.
   */
  cursor?: Prisma.TemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Templates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Templates.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Templates.
   */
  distinct?: Prisma.TemplateScalarFieldEnum | Prisma.TemplateScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template findMany
 */
export type TemplateFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * Filter, which Templates to fetch.
   */
  where?: Prisma.TemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Templates to fetch.
   */
  orderBy?: Prisma.TemplateOrderByWithRelationInput | Prisma.TemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Templates.
   */
  cursor?: Prisma.TemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Templates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Templates.
   */
  skip?: number
  distinct?: Prisma.TemplateScalarFieldEnum | Prisma.TemplateScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template create
 */
export type TemplateCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * The data needed to create a Template.
   */
  data: Prisma.XOR<Prisma.TemplateCreateInput, Prisma.TemplateUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template createMany
 */
export type TemplateCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Templates.
   */
  data: Prisma.TemplateCreateManyInput | Prisma.TemplateCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Template createManyAndReturn
 */
export type TemplateCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * The data used to create many Templates.
   */
  data: Prisma.TemplateCreateManyInput | Prisma.TemplateCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Template update
 */
export type TemplateUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * The data needed to update a Template.
   */
  data: Prisma.XOR<Prisma.TemplateUpdateInput, Prisma.TemplateUncheckedUpdateInput>
  /**
   * Choose, which Template to update.
   */
  where: Prisma.TemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template updateMany
 */
export type TemplateUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Templates.
   */
  data: Prisma.XOR<Prisma.TemplateUpdateManyMutationInput, Prisma.TemplateUncheckedUpdateManyInput>
  /**
   * Filter which Templates to update
   */
  where?: Prisma.TemplateWhereInput
  /**
   * Limit how many Templates to update.
   */
  limit?: number
}

/**
 * Template updateManyAndReturn
 */
export type TemplateUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * The data used to update Templates.
   */
  data: Prisma.XOR<Prisma.TemplateUpdateManyMutationInput, Prisma.TemplateUncheckedUpdateManyInput>
  /**
   * Filter which Templates to update
   */
  where?: Prisma.TemplateWhereInput
  /**
   * Limit how many Templates to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Template upsert
 */
export type TemplateUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * The filter to search for the Template to update in case it exists.
   */
  where: Prisma.TemplateWhereUniqueInput
  /**
   * In case the Template found by the `where` argument doesn't exist, create a new Template with this data.
   */
  create: Prisma.XOR<Prisma.TemplateCreateInput, Prisma.TemplateUncheckedCreateInput>
  /**
   * In case the Template was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TemplateUpdateInput, Prisma.TemplateUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template delete
 */
export type TemplateDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  /**
   * Filter which Template to delete.
   */
  where: Prisma.TemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Template deleteMany
 */
export type TemplateDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Templates to delete
   */
  where?: Prisma.TemplateWhereInput
  /**
   * Limit how many Templates to delete.
   */
  limit?: number
}

/**
 * Template.localeTemplates
 */
export type Template$localeTemplatesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  where?: Prisma.LocaleTemplateWhereInput
  orderBy?: Prisma.LocaleTemplateOrderByWithRelationInput | Prisma.LocaleTemplateOrderByWithRelationInput[]
  cursor?: Prisma.LocaleTemplateWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LocaleTemplateScalarFieldEnum | Prisma.LocaleTemplateScalarFieldEnum[]
}

/**
 * Template without action
 */
export type TemplateDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
}
