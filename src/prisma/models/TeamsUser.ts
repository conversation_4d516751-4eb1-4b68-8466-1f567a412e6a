
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TeamsUser` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TeamsUser
 * 
 */
export type TeamsUserModel = runtime.Types.Result.DefaultSelection<Prisma.$TeamsUserPayload>

export type AggregateTeamsUser = {
  _count: TeamsUserCountAggregateOutputType | null
  _min: TeamsUserMinAggregateOutputType | null
  _max: TeamsUserMaxAggregateOutputType | null
}

export type TeamsUserMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  userPrincipalName: string | null
  email: string | null
  conversationId: string | null
  msTenantId: string | null
}

export type TeamsUserMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  userPrincipalName: string | null
  email: string | null
  conversationId: string | null
  msTenantId: string | null
}

export type TeamsUserCountAggregateOutputType = {
  id: number
  tenantId: number
  userPrincipalName: number
  email: number
  conversationId: number
  msTenantId: number
  _all: number
}


export type TeamsUserMinAggregateInputType = {
  id?: true
  tenantId?: true
  userPrincipalName?: true
  email?: true
  conversationId?: true
  msTenantId?: true
}

export type TeamsUserMaxAggregateInputType = {
  id?: true
  tenantId?: true
  userPrincipalName?: true
  email?: true
  conversationId?: true
  msTenantId?: true
}

export type TeamsUserCountAggregateInputType = {
  id?: true
  tenantId?: true
  userPrincipalName?: true
  email?: true
  conversationId?: true
  msTenantId?: true
  _all?: true
}

export type TeamsUserAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TeamsUser to aggregate.
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TeamsUsers to fetch.
   */
  orderBy?: Prisma.TeamsUserOrderByWithRelationInput | Prisma.TeamsUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TeamsUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TeamsUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TeamsUsers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TeamsUsers
  **/
  _count?: true | TeamsUserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TeamsUserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TeamsUserMaxAggregateInputType
}

export type GetTeamsUserAggregateType<T extends TeamsUserAggregateArgs> = {
      [P in keyof T & keyof AggregateTeamsUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTeamsUser[P]>
    : Prisma.GetScalarType<T[P], AggregateTeamsUser[P]>
}




export type TeamsUserGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TeamsUserWhereInput
  orderBy?: Prisma.TeamsUserOrderByWithAggregationInput | Prisma.TeamsUserOrderByWithAggregationInput[]
  by: Prisma.TeamsUserScalarFieldEnum[] | Prisma.TeamsUserScalarFieldEnum
  having?: Prisma.TeamsUserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TeamsUserCountAggregateInputType | true
  _min?: TeamsUserMinAggregateInputType
  _max?: TeamsUserMaxAggregateInputType
}

export type TeamsUserGroupByOutputType = {
  id: string
  tenantId: string
  userPrincipalName: string
  email: string | null
  conversationId: string | null
  msTenantId: string
  _count: TeamsUserCountAggregateOutputType | null
  _min: TeamsUserMinAggregateOutputType | null
  _max: TeamsUserMaxAggregateOutputType | null
}

type GetTeamsUserGroupByPayload<T extends TeamsUserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TeamsUserGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TeamsUserGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TeamsUserGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TeamsUserGroupByOutputType[P]>
      }
    >
  > 



export type TeamsUserWhereInput = {
  AND?: Prisma.TeamsUserWhereInput | Prisma.TeamsUserWhereInput[]
  OR?: Prisma.TeamsUserWhereInput[]
  NOT?: Prisma.TeamsUserWhereInput | Prisma.TeamsUserWhereInput[]
  id?: Prisma.StringFilter<"TeamsUser"> | string
  tenantId?: Prisma.StringFilter<"TeamsUser"> | string
  userPrincipalName?: Prisma.StringFilter<"TeamsUser"> | string
  email?: Prisma.StringNullableFilter<"TeamsUser"> | string | null
  conversationId?: Prisma.StringNullableFilter<"TeamsUser"> | string | null
  msTenantId?: Prisma.StringFilter<"TeamsUser"> | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
}

export type TeamsUserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  userPrincipalName?: Prisma.SortOrder
  email?: Prisma.SortOrderInput | Prisma.SortOrder
  conversationId?: Prisma.SortOrderInput | Prisma.SortOrder
  msTenantId?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
}

export type TeamsUserWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TeamsUserWhereInput | Prisma.TeamsUserWhereInput[]
  OR?: Prisma.TeamsUserWhereInput[]
  NOT?: Prisma.TeamsUserWhereInput | Prisma.TeamsUserWhereInput[]
  tenantId?: Prisma.StringFilter<"TeamsUser"> | string
  userPrincipalName?: Prisma.StringFilter<"TeamsUser"> | string
  email?: Prisma.StringNullableFilter<"TeamsUser"> | string | null
  conversationId?: Prisma.StringNullableFilter<"TeamsUser"> | string | null
  msTenantId?: Prisma.StringFilter<"TeamsUser"> | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
}, "id">

export type TeamsUserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  userPrincipalName?: Prisma.SortOrder
  email?: Prisma.SortOrderInput | Prisma.SortOrder
  conversationId?: Prisma.SortOrderInput | Prisma.SortOrder
  msTenantId?: Prisma.SortOrder
  _count?: Prisma.TeamsUserCountOrderByAggregateInput
  _max?: Prisma.TeamsUserMaxOrderByAggregateInput
  _min?: Prisma.TeamsUserMinOrderByAggregateInput
}

export type TeamsUserScalarWhereWithAggregatesInput = {
  AND?: Prisma.TeamsUserScalarWhereWithAggregatesInput | Prisma.TeamsUserScalarWhereWithAggregatesInput[]
  OR?: Prisma.TeamsUserScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TeamsUserScalarWhereWithAggregatesInput | Prisma.TeamsUserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"TeamsUser"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"TeamsUser"> | string
  userPrincipalName?: Prisma.StringWithAggregatesFilter<"TeamsUser"> | string
  email?: Prisma.StringNullableWithAggregatesFilter<"TeamsUser"> | string | null
  conversationId?: Prisma.StringNullableWithAggregatesFilter<"TeamsUser"> | string | null
  msTenantId?: Prisma.StringWithAggregatesFilter<"TeamsUser"> | string
}

export type TeamsUserCreateInput = {
  id: string
  userPrincipalName: string
  email?: string | null
  conversationId?: string | null
  msTenantId: string
  tenant: Prisma.TenantCreateNestedOneWithoutTeamsUserInput
}

export type TeamsUserUncheckedCreateInput = {
  id: string
  tenantId: string
  userPrincipalName: string
  email?: string | null
  conversationId?: string | null
  msTenantId: string
}

export type TeamsUserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTeamsUserNestedInput
}

export type TeamsUserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TeamsUserCreateManyInput = {
  id: string
  tenantId: string
  userPrincipalName: string
  email?: string | null
  conversationId?: string | null
  msTenantId: string
}

export type TeamsUserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TeamsUserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TeamsUserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  userPrincipalName?: Prisma.SortOrder
  email?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  msTenantId?: Prisma.SortOrder
}

export type TeamsUserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  userPrincipalName?: Prisma.SortOrder
  email?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  msTenantId?: Prisma.SortOrder
}

export type TeamsUserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  userPrincipalName?: Prisma.SortOrder
  email?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  msTenantId?: Prisma.SortOrder
}

export type TeamsUserListRelationFilter = {
  every?: Prisma.TeamsUserWhereInput
  some?: Prisma.TeamsUserWhereInput
  none?: Prisma.TeamsUserWhereInput
}

export type TeamsUserOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TeamsUserCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TeamsUserCreateWithoutTenantInput, Prisma.TeamsUserUncheckedCreateWithoutTenantInput> | Prisma.TeamsUserCreateWithoutTenantInput[] | Prisma.TeamsUserUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TeamsUserCreateOrConnectWithoutTenantInput | Prisma.TeamsUserCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TeamsUserCreateManyTenantInputEnvelope
  connect?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
}

export type TeamsUserUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TeamsUserCreateWithoutTenantInput, Prisma.TeamsUserUncheckedCreateWithoutTenantInput> | Prisma.TeamsUserCreateWithoutTenantInput[] | Prisma.TeamsUserUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TeamsUserCreateOrConnectWithoutTenantInput | Prisma.TeamsUserCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TeamsUserCreateManyTenantInputEnvelope
  connect?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
}

export type TeamsUserUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TeamsUserCreateWithoutTenantInput, Prisma.TeamsUserUncheckedCreateWithoutTenantInput> | Prisma.TeamsUserCreateWithoutTenantInput[] | Prisma.TeamsUserUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TeamsUserCreateOrConnectWithoutTenantInput | Prisma.TeamsUserCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TeamsUserUpsertWithWhereUniqueWithoutTenantInput | Prisma.TeamsUserUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TeamsUserCreateManyTenantInputEnvelope
  set?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  disconnect?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  delete?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  connect?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  update?: Prisma.TeamsUserUpdateWithWhereUniqueWithoutTenantInput | Prisma.TeamsUserUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TeamsUserUpdateManyWithWhereWithoutTenantInput | Prisma.TeamsUserUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TeamsUserScalarWhereInput | Prisma.TeamsUserScalarWhereInput[]
}

export type TeamsUserUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TeamsUserCreateWithoutTenantInput, Prisma.TeamsUserUncheckedCreateWithoutTenantInput> | Prisma.TeamsUserCreateWithoutTenantInput[] | Prisma.TeamsUserUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TeamsUserCreateOrConnectWithoutTenantInput | Prisma.TeamsUserCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TeamsUserUpsertWithWhereUniqueWithoutTenantInput | Prisma.TeamsUserUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TeamsUserCreateManyTenantInputEnvelope
  set?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  disconnect?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  delete?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  connect?: Prisma.TeamsUserWhereUniqueInput | Prisma.TeamsUserWhereUniqueInput[]
  update?: Prisma.TeamsUserUpdateWithWhereUniqueWithoutTenantInput | Prisma.TeamsUserUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TeamsUserUpdateManyWithWhereWithoutTenantInput | Prisma.TeamsUserUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TeamsUserScalarWhereInput | Prisma.TeamsUserScalarWhereInput[]
}

export type TeamsUserCreateWithoutTenantInput = {
  id: string
  userPrincipalName: string
  email?: string | null
  conversationId?: string | null
  msTenantId: string
}

export type TeamsUserUncheckedCreateWithoutTenantInput = {
  id: string
  userPrincipalName: string
  email?: string | null
  conversationId?: string | null
  msTenantId: string
}

export type TeamsUserCreateOrConnectWithoutTenantInput = {
  where: Prisma.TeamsUserWhereUniqueInput
  create: Prisma.XOR<Prisma.TeamsUserCreateWithoutTenantInput, Prisma.TeamsUserUncheckedCreateWithoutTenantInput>
}

export type TeamsUserCreateManyTenantInputEnvelope = {
  data: Prisma.TeamsUserCreateManyTenantInput | Prisma.TeamsUserCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type TeamsUserUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TeamsUserWhereUniqueInput
  update: Prisma.XOR<Prisma.TeamsUserUpdateWithoutTenantInput, Prisma.TeamsUserUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.TeamsUserCreateWithoutTenantInput, Prisma.TeamsUserUncheckedCreateWithoutTenantInput>
}

export type TeamsUserUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TeamsUserWhereUniqueInput
  data: Prisma.XOR<Prisma.TeamsUserUpdateWithoutTenantInput, Prisma.TeamsUserUncheckedUpdateWithoutTenantInput>
}

export type TeamsUserUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.TeamsUserScalarWhereInput
  data: Prisma.XOR<Prisma.TeamsUserUpdateManyMutationInput, Prisma.TeamsUserUncheckedUpdateManyWithoutTenantInput>
}

export type TeamsUserScalarWhereInput = {
  AND?: Prisma.TeamsUserScalarWhereInput | Prisma.TeamsUserScalarWhereInput[]
  OR?: Prisma.TeamsUserScalarWhereInput[]
  NOT?: Prisma.TeamsUserScalarWhereInput | Prisma.TeamsUserScalarWhereInput[]
  id?: Prisma.StringFilter<"TeamsUser"> | string
  tenantId?: Prisma.StringFilter<"TeamsUser"> | string
  userPrincipalName?: Prisma.StringFilter<"TeamsUser"> | string
  email?: Prisma.StringNullableFilter<"TeamsUser"> | string | null
  conversationId?: Prisma.StringNullableFilter<"TeamsUser"> | string | null
  msTenantId?: Prisma.StringFilter<"TeamsUser"> | string
}

export type TeamsUserCreateManyTenantInput = {
  id: string
  userPrincipalName: string
  email?: string | null
  conversationId?: string | null
  msTenantId: string
}

export type TeamsUserUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TeamsUserUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TeamsUserUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userPrincipalName?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  msTenantId?: Prisma.StringFieldUpdateOperationsInput | string
}



export type TeamsUserSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  userPrincipalName?: boolean
  email?: boolean
  conversationId?: boolean
  msTenantId?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["teamsUser"]>

export type TeamsUserSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  userPrincipalName?: boolean
  email?: boolean
  conversationId?: boolean
  msTenantId?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["teamsUser"]>

export type TeamsUserSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  userPrincipalName?: boolean
  email?: boolean
  conversationId?: boolean
  msTenantId?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["teamsUser"]>

export type TeamsUserSelectScalar = {
  id?: boolean
  tenantId?: boolean
  userPrincipalName?: boolean
  email?: boolean
  conversationId?: boolean
  msTenantId?: boolean
}

export type TeamsUserOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "userPrincipalName" | "email" | "conversationId" | "msTenantId", ExtArgs["result"]["teamsUser"]>
export type TeamsUserInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TeamsUserIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TeamsUserIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $TeamsUserPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TeamsUser"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    userPrincipalName: string
    email: string | null
    conversationId: string | null
    msTenantId: string
  }, ExtArgs["result"]["teamsUser"]>
  composites: {}
}

export type TeamsUserGetPayload<S extends boolean | null | undefined | TeamsUserDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload, S>

export type TeamsUserCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TeamsUserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TeamsUserCountAggregateInputType | true
  }

export interface TeamsUserDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TeamsUser'], meta: { name: 'TeamsUser' } }
  /**
   * Find zero or one TeamsUser that matches the filter.
   * @param {TeamsUserFindUniqueArgs} args - Arguments to find a TeamsUser
   * @example
   * // Get one TeamsUser
   * const teamsUser = await prisma.teamsUser.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TeamsUserFindUniqueArgs>(args: Prisma.SelectSubset<T, TeamsUserFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TeamsUser that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TeamsUserFindUniqueOrThrowArgs} args - Arguments to find a TeamsUser
   * @example
   * // Get one TeamsUser
   * const teamsUser = await prisma.teamsUser.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TeamsUserFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TeamsUserFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TeamsUser that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserFindFirstArgs} args - Arguments to find a TeamsUser
   * @example
   * // Get one TeamsUser
   * const teamsUser = await prisma.teamsUser.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TeamsUserFindFirstArgs>(args?: Prisma.SelectSubset<T, TeamsUserFindFirstArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TeamsUser that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserFindFirstOrThrowArgs} args - Arguments to find a TeamsUser
   * @example
   * // Get one TeamsUser
   * const teamsUser = await prisma.teamsUser.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TeamsUserFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TeamsUserFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TeamsUsers that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TeamsUsers
   * const teamsUsers = await prisma.teamsUser.findMany()
   * 
   * // Get first 10 TeamsUsers
   * const teamsUsers = await prisma.teamsUser.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const teamsUserWithIdOnly = await prisma.teamsUser.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TeamsUserFindManyArgs>(args?: Prisma.SelectSubset<T, TeamsUserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TeamsUser.
   * @param {TeamsUserCreateArgs} args - Arguments to create a TeamsUser.
   * @example
   * // Create one TeamsUser
   * const TeamsUser = await prisma.teamsUser.create({
   *   data: {
   *     // ... data to create a TeamsUser
   *   }
   * })
   * 
   */
  create<T extends TeamsUserCreateArgs>(args: Prisma.SelectSubset<T, TeamsUserCreateArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TeamsUsers.
   * @param {TeamsUserCreateManyArgs} args - Arguments to create many TeamsUsers.
   * @example
   * // Create many TeamsUsers
   * const teamsUser = await prisma.teamsUser.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TeamsUserCreateManyArgs>(args?: Prisma.SelectSubset<T, TeamsUserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TeamsUsers and returns the data saved in the database.
   * @param {TeamsUserCreateManyAndReturnArgs} args - Arguments to create many TeamsUsers.
   * @example
   * // Create many TeamsUsers
   * const teamsUser = await prisma.teamsUser.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TeamsUsers and only return the `id`
   * const teamsUserWithIdOnly = await prisma.teamsUser.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TeamsUserCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TeamsUserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TeamsUser.
   * @param {TeamsUserDeleteArgs} args - Arguments to delete one TeamsUser.
   * @example
   * // Delete one TeamsUser
   * const TeamsUser = await prisma.teamsUser.delete({
   *   where: {
   *     // ... filter to delete one TeamsUser
   *   }
   * })
   * 
   */
  delete<T extends TeamsUserDeleteArgs>(args: Prisma.SelectSubset<T, TeamsUserDeleteArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TeamsUser.
   * @param {TeamsUserUpdateArgs} args - Arguments to update one TeamsUser.
   * @example
   * // Update one TeamsUser
   * const teamsUser = await prisma.teamsUser.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TeamsUserUpdateArgs>(args: Prisma.SelectSubset<T, TeamsUserUpdateArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TeamsUsers.
   * @param {TeamsUserDeleteManyArgs} args - Arguments to filter TeamsUsers to delete.
   * @example
   * // Delete a few TeamsUsers
   * const { count } = await prisma.teamsUser.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TeamsUserDeleteManyArgs>(args?: Prisma.SelectSubset<T, TeamsUserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TeamsUsers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TeamsUsers
   * const teamsUser = await prisma.teamsUser.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TeamsUserUpdateManyArgs>(args: Prisma.SelectSubset<T, TeamsUserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TeamsUsers and returns the data updated in the database.
   * @param {TeamsUserUpdateManyAndReturnArgs} args - Arguments to update many TeamsUsers.
   * @example
   * // Update many TeamsUsers
   * const teamsUser = await prisma.teamsUser.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TeamsUsers and only return the `id`
   * const teamsUserWithIdOnly = await prisma.teamsUser.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TeamsUserUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TeamsUserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TeamsUser.
   * @param {TeamsUserUpsertArgs} args - Arguments to update or create a TeamsUser.
   * @example
   * // Update or create a TeamsUser
   * const teamsUser = await prisma.teamsUser.upsert({
   *   create: {
   *     // ... data to create a TeamsUser
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TeamsUser we want to update
   *   }
   * })
   */
  upsert<T extends TeamsUserUpsertArgs>(args: Prisma.SelectSubset<T, TeamsUserUpsertArgs<ExtArgs>>): Prisma.Prisma__TeamsUserClient<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TeamsUsers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserCountArgs} args - Arguments to filter TeamsUsers to count.
   * @example
   * // Count the number of TeamsUsers
   * const count = await prisma.teamsUser.count({
   *   where: {
   *     // ... the filter for the TeamsUsers we want to count
   *   }
   * })
  **/
  count<T extends TeamsUserCountArgs>(
    args?: Prisma.Subset<T, TeamsUserCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TeamsUserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TeamsUser.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TeamsUserAggregateArgs>(args: Prisma.Subset<T, TeamsUserAggregateArgs>): Prisma.PrismaPromise<GetTeamsUserAggregateType<T>>

  /**
   * Group by TeamsUser.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeamsUserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TeamsUserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TeamsUserGroupByArgs['orderBy'] }
      : { orderBy?: TeamsUserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TeamsUserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTeamsUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TeamsUser model
 */
readonly fields: TeamsUserFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TeamsUser.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TeamsUserClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TeamsUser model
 */
export interface TeamsUserFieldRefs {
  readonly id: Prisma.FieldRef<"TeamsUser", 'String'>
  readonly tenantId: Prisma.FieldRef<"TeamsUser", 'String'>
  readonly userPrincipalName: Prisma.FieldRef<"TeamsUser", 'String'>
  readonly email: Prisma.FieldRef<"TeamsUser", 'String'>
  readonly conversationId: Prisma.FieldRef<"TeamsUser", 'String'>
  readonly msTenantId: Prisma.FieldRef<"TeamsUser", 'String'>
}
    

// Custom InputTypes
/**
 * TeamsUser findUnique
 */
export type TeamsUserFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * Filter, which TeamsUser to fetch.
   */
  where: Prisma.TeamsUserWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser findUniqueOrThrow
 */
export type TeamsUserFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * Filter, which TeamsUser to fetch.
   */
  where: Prisma.TeamsUserWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser findFirst
 */
export type TeamsUserFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * Filter, which TeamsUser to fetch.
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TeamsUsers to fetch.
   */
  orderBy?: Prisma.TeamsUserOrderByWithRelationInput | Prisma.TeamsUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TeamsUsers.
   */
  cursor?: Prisma.TeamsUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TeamsUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TeamsUsers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TeamsUsers.
   */
  distinct?: Prisma.TeamsUserScalarFieldEnum | Prisma.TeamsUserScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser findFirstOrThrow
 */
export type TeamsUserFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * Filter, which TeamsUser to fetch.
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TeamsUsers to fetch.
   */
  orderBy?: Prisma.TeamsUserOrderByWithRelationInput | Prisma.TeamsUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TeamsUsers.
   */
  cursor?: Prisma.TeamsUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TeamsUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TeamsUsers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TeamsUsers.
   */
  distinct?: Prisma.TeamsUserScalarFieldEnum | Prisma.TeamsUserScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser findMany
 */
export type TeamsUserFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * Filter, which TeamsUsers to fetch.
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TeamsUsers to fetch.
   */
  orderBy?: Prisma.TeamsUserOrderByWithRelationInput | Prisma.TeamsUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TeamsUsers.
   */
  cursor?: Prisma.TeamsUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TeamsUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TeamsUsers.
   */
  skip?: number
  distinct?: Prisma.TeamsUserScalarFieldEnum | Prisma.TeamsUserScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser create
 */
export type TeamsUserCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * The data needed to create a TeamsUser.
   */
  data: Prisma.XOR<Prisma.TeamsUserCreateInput, Prisma.TeamsUserUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser createMany
 */
export type TeamsUserCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TeamsUsers.
   */
  data: Prisma.TeamsUserCreateManyInput | Prisma.TeamsUserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TeamsUser createManyAndReturn
 */
export type TeamsUserCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * The data used to create many TeamsUsers.
   */
  data: Prisma.TeamsUserCreateManyInput | Prisma.TeamsUserCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TeamsUser update
 */
export type TeamsUserUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * The data needed to update a TeamsUser.
   */
  data: Prisma.XOR<Prisma.TeamsUserUpdateInput, Prisma.TeamsUserUncheckedUpdateInput>
  /**
   * Choose, which TeamsUser to update.
   */
  where: Prisma.TeamsUserWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser updateMany
 */
export type TeamsUserUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TeamsUsers.
   */
  data: Prisma.XOR<Prisma.TeamsUserUpdateManyMutationInput, Prisma.TeamsUserUncheckedUpdateManyInput>
  /**
   * Filter which TeamsUsers to update
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * Limit how many TeamsUsers to update.
   */
  limit?: number
}

/**
 * TeamsUser updateManyAndReturn
 */
export type TeamsUserUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * The data used to update TeamsUsers.
   */
  data: Prisma.XOR<Prisma.TeamsUserUpdateManyMutationInput, Prisma.TeamsUserUncheckedUpdateManyInput>
  /**
   * Filter which TeamsUsers to update
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * Limit how many TeamsUsers to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TeamsUser upsert
 */
export type TeamsUserUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * The filter to search for the TeamsUser to update in case it exists.
   */
  where: Prisma.TeamsUserWhereUniqueInput
  /**
   * In case the TeamsUser found by the `where` argument doesn't exist, create a new TeamsUser with this data.
   */
  create: Prisma.XOR<Prisma.TeamsUserCreateInput, Prisma.TeamsUserUncheckedCreateInput>
  /**
   * In case the TeamsUser was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TeamsUserUpdateInput, Prisma.TeamsUserUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser delete
 */
export type TeamsUserDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  /**
   * Filter which TeamsUser to delete.
   */
  where: Prisma.TeamsUserWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TeamsUser deleteMany
 */
export type TeamsUserDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TeamsUsers to delete
   */
  where?: Prisma.TeamsUserWhereInput
  /**
   * Limit how many TeamsUsers to delete.
   */
  limit?: number
}

/**
 * TeamsUser without action
 */
export type TeamsUserDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
}
