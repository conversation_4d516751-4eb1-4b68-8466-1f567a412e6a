
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TopicPreference` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TopicPreference
 * 
 */
export type TopicPreferenceModel = runtime.Types.Result.DefaultSelection<Prisma.$TopicPreferencePayload>

export type AggregateTopicPreference = {
  _count: TopicPreferenceCountAggregateOutputType | null
  _min: TopicPreferenceMinAggregateOutputType | null
  _max: TopicPreferenceMaxAggregateOutputType | null
}

export type TopicPreferenceMinAggregateOutputType = {
  id: string | null
  recipientId: string | null
  topicId: string | null
  optedIn: boolean | null
}

export type TopicPreferenceMaxAggregateOutputType = {
  id: string | null
  recipientId: string | null
  topicId: string | null
  optedIn: boolean | null
}

export type TopicPreferenceCountAggregateOutputType = {
  id: number
  recipientId: number
  topicId: number
  optedIn: number
  _all: number
}


export type TopicPreferenceMinAggregateInputType = {
  id?: true
  recipientId?: true
  topicId?: true
  optedIn?: true
}

export type TopicPreferenceMaxAggregateInputType = {
  id?: true
  recipientId?: true
  topicId?: true
  optedIn?: true
}

export type TopicPreferenceCountAggregateInputType = {
  id?: true
  recipientId?: true
  topicId?: true
  optedIn?: true
  _all?: true
}

export type TopicPreferenceAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TopicPreference to aggregate.
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TopicPreferences to fetch.
   */
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TopicPreferences from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TopicPreferences.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TopicPreferences
  **/
  _count?: true | TopicPreferenceCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TopicPreferenceMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TopicPreferenceMaxAggregateInputType
}

export type GetTopicPreferenceAggregateType<T extends TopicPreferenceAggregateArgs> = {
      [P in keyof T & keyof AggregateTopicPreference]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTopicPreference[P]>
    : Prisma.GetScalarType<T[P], AggregateTopicPreference[P]>
}




export type TopicPreferenceGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TopicPreferenceWhereInput
  orderBy?: Prisma.TopicPreferenceOrderByWithAggregationInput | Prisma.TopicPreferenceOrderByWithAggregationInput[]
  by: Prisma.TopicPreferenceScalarFieldEnum[] | Prisma.TopicPreferenceScalarFieldEnum
  having?: Prisma.TopicPreferenceScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TopicPreferenceCountAggregateInputType | true
  _min?: TopicPreferenceMinAggregateInputType
  _max?: TopicPreferenceMaxAggregateInputType
}

export type TopicPreferenceGroupByOutputType = {
  id: string
  recipientId: string
  topicId: string
  optedIn: boolean
  _count: TopicPreferenceCountAggregateOutputType | null
  _min: TopicPreferenceMinAggregateOutputType | null
  _max: TopicPreferenceMaxAggregateOutputType | null
}

type GetTopicPreferenceGroupByPayload<T extends TopicPreferenceGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TopicPreferenceGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TopicPreferenceGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TopicPreferenceGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TopicPreferenceGroupByOutputType[P]>
      }
    >
  > 



export type TopicPreferenceWhereInput = {
  AND?: Prisma.TopicPreferenceWhereInput | Prisma.TopicPreferenceWhereInput[]
  OR?: Prisma.TopicPreferenceWhereInput[]
  NOT?: Prisma.TopicPreferenceWhereInput | Prisma.TopicPreferenceWhereInput[]
  id?: Prisma.StringFilter<"TopicPreference"> | string
  recipientId?: Prisma.StringFilter<"TopicPreference"> | string
  topicId?: Prisma.StringFilter<"TopicPreference"> | string
  optedIn?: Prisma.BoolFilter<"TopicPreference"> | boolean
  Recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
  topic?: Prisma.XOR<Prisma.TopicScalarRelationFilter, Prisma.TopicWhereInput>
  connections?: Prisma.ConnectionListRelationFilter
}

export type TopicPreferenceOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  topicId?: Prisma.SortOrder
  optedIn?: Prisma.SortOrder
  Recipient?: Prisma.RecipientOrderByWithRelationInput
  topic?: Prisma.TopicOrderByWithRelationInput
  connections?: Prisma.ConnectionOrderByRelationAggregateInput
}

export type TopicPreferenceWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TopicPreferenceWhereInput | Prisma.TopicPreferenceWhereInput[]
  OR?: Prisma.TopicPreferenceWhereInput[]
  NOT?: Prisma.TopicPreferenceWhereInput | Prisma.TopicPreferenceWhereInput[]
  recipientId?: Prisma.StringFilter<"TopicPreference"> | string
  topicId?: Prisma.StringFilter<"TopicPreference"> | string
  optedIn?: Prisma.BoolFilter<"TopicPreference"> | boolean
  Recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
  topic?: Prisma.XOR<Prisma.TopicScalarRelationFilter, Prisma.TopicWhereInput>
  connections?: Prisma.ConnectionListRelationFilter
}, "id">

export type TopicPreferenceOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  topicId?: Prisma.SortOrder
  optedIn?: Prisma.SortOrder
  _count?: Prisma.TopicPreferenceCountOrderByAggregateInput
  _max?: Prisma.TopicPreferenceMaxOrderByAggregateInput
  _min?: Prisma.TopicPreferenceMinOrderByAggregateInput
}

export type TopicPreferenceScalarWhereWithAggregatesInput = {
  AND?: Prisma.TopicPreferenceScalarWhereWithAggregatesInput | Prisma.TopicPreferenceScalarWhereWithAggregatesInput[]
  OR?: Prisma.TopicPreferenceScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TopicPreferenceScalarWhereWithAggregatesInput | Prisma.TopicPreferenceScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"TopicPreference"> | string
  recipientId?: Prisma.StringWithAggregatesFilter<"TopicPreference"> | string
  topicId?: Prisma.StringWithAggregatesFilter<"TopicPreference"> | string
  optedIn?: Prisma.BoolWithAggregatesFilter<"TopicPreference"> | boolean
}

export type TopicPreferenceCreateInput = {
  id?: string
  optedIn?: boolean
  Recipient: Prisma.RecipientCreateNestedOneWithoutPreferencesInput
  topic: Prisma.TopicCreateNestedOneWithoutTopicPreferenceInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutTopicPreferencesInput
}

export type TopicPreferenceUncheckedCreateInput = {
  id?: string
  recipientId: string
  topicId: string
  optedIn?: boolean
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutTopicPreferencesInput
}

export type TopicPreferenceUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  Recipient?: Prisma.RecipientUpdateOneRequiredWithoutPreferencesNestedInput
  topic?: Prisma.TopicUpdateOneRequiredWithoutTopicPreferenceNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutTopicPreferencesNestedInput
}

export type TopicPreferenceUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  topicId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutTopicPreferencesNestedInput
}

export type TopicPreferenceCreateManyInput = {
  id?: string
  recipientId: string
  topicId: string
  optedIn?: boolean
}

export type TopicPreferenceUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TopicPreferenceUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  topicId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TopicPreferenceListRelationFilter = {
  every?: Prisma.TopicPreferenceWhereInput
  some?: Prisma.TopicPreferenceWhereInput
  none?: Prisma.TopicPreferenceWhereInput
}

export type TopicPreferenceOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TopicPreferenceCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  topicId?: Prisma.SortOrder
  optedIn?: Prisma.SortOrder
}

export type TopicPreferenceMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  topicId?: Prisma.SortOrder
  optedIn?: Prisma.SortOrder
}

export type TopicPreferenceMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  topicId?: Prisma.SortOrder
  optedIn?: Prisma.SortOrder
}

export type TopicPreferenceCreateNestedManyWithoutConnectionsInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput> | Prisma.TopicPreferenceCreateWithoutConnectionsInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput | Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
}

export type TopicPreferenceUncheckedCreateNestedManyWithoutConnectionsInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput> | Prisma.TopicPreferenceCreateWithoutConnectionsInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput | Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
}

export type TopicPreferenceUpdateManyWithoutConnectionsNestedInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput> | Prisma.TopicPreferenceCreateWithoutConnectionsInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput | Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput[]
  upsert?: Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutConnectionsInput | Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutConnectionsInput[]
  set?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  disconnect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  delete?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  update?: Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutConnectionsInput | Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutConnectionsInput[]
  updateMany?: Prisma.TopicPreferenceUpdateManyWithWhereWithoutConnectionsInput | Prisma.TopicPreferenceUpdateManyWithWhereWithoutConnectionsInput[]
  deleteMany?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
}

export type TopicPreferenceUncheckedUpdateManyWithoutConnectionsNestedInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput> | Prisma.TopicPreferenceCreateWithoutConnectionsInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput | Prisma.TopicPreferenceCreateOrConnectWithoutConnectionsInput[]
  upsert?: Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutConnectionsInput | Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutConnectionsInput[]
  set?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  disconnect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  delete?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  update?: Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutConnectionsInput | Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutConnectionsInput[]
  updateMany?: Prisma.TopicPreferenceUpdateManyWithWhereWithoutConnectionsInput | Prisma.TopicPreferenceUpdateManyWithWhereWithoutConnectionsInput[]
  deleteMany?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
}

export type TopicPreferenceCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput> | Prisma.TopicPreferenceCreateWithoutRecipientInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput | Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.TopicPreferenceCreateManyRecipientInputEnvelope
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
}

export type TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput> | Prisma.TopicPreferenceCreateWithoutRecipientInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput | Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.TopicPreferenceCreateManyRecipientInputEnvelope
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
}

export type TopicPreferenceUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput> | Prisma.TopicPreferenceCreateWithoutRecipientInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput | Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutRecipientInput | Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.TopicPreferenceCreateManyRecipientInputEnvelope
  set?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  disconnect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  delete?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  update?: Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutRecipientInput | Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.TopicPreferenceUpdateManyWithWhereWithoutRecipientInput | Prisma.TopicPreferenceUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
}

export type TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput> | Prisma.TopicPreferenceCreateWithoutRecipientInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput | Prisma.TopicPreferenceCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutRecipientInput | Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.TopicPreferenceCreateManyRecipientInputEnvelope
  set?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  disconnect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  delete?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  update?: Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutRecipientInput | Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.TopicPreferenceUpdateManyWithWhereWithoutRecipientInput | Prisma.TopicPreferenceUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
}

export type TopicPreferenceCreateNestedManyWithoutTopicInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutTopicInput, Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput> | Prisma.TopicPreferenceCreateWithoutTopicInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput | Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput[]
  createMany?: Prisma.TopicPreferenceCreateManyTopicInputEnvelope
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
}

export type TopicPreferenceUncheckedCreateNestedManyWithoutTopicInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutTopicInput, Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput> | Prisma.TopicPreferenceCreateWithoutTopicInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput | Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput[]
  createMany?: Prisma.TopicPreferenceCreateManyTopicInputEnvelope
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
}

export type TopicPreferenceUpdateManyWithoutTopicNestedInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutTopicInput, Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput> | Prisma.TopicPreferenceCreateWithoutTopicInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput | Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput[]
  upsert?: Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutTopicInput | Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutTopicInput[]
  createMany?: Prisma.TopicPreferenceCreateManyTopicInputEnvelope
  set?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  disconnect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  delete?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  update?: Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutTopicInput | Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutTopicInput[]
  updateMany?: Prisma.TopicPreferenceUpdateManyWithWhereWithoutTopicInput | Prisma.TopicPreferenceUpdateManyWithWhereWithoutTopicInput[]
  deleteMany?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
}

export type TopicPreferenceUncheckedUpdateManyWithoutTopicNestedInput = {
  create?: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutTopicInput, Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput> | Prisma.TopicPreferenceCreateWithoutTopicInput[] | Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput[]
  connectOrCreate?: Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput | Prisma.TopicPreferenceCreateOrConnectWithoutTopicInput[]
  upsert?: Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutTopicInput | Prisma.TopicPreferenceUpsertWithWhereUniqueWithoutTopicInput[]
  createMany?: Prisma.TopicPreferenceCreateManyTopicInputEnvelope
  set?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  disconnect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  delete?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  connect?: Prisma.TopicPreferenceWhereUniqueInput | Prisma.TopicPreferenceWhereUniqueInput[]
  update?: Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutTopicInput | Prisma.TopicPreferenceUpdateWithWhereUniqueWithoutTopicInput[]
  updateMany?: Prisma.TopicPreferenceUpdateManyWithWhereWithoutTopicInput | Prisma.TopicPreferenceUpdateManyWithWhereWithoutTopicInput[]
  deleteMany?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
}

export type TopicPreferenceCreateWithoutConnectionsInput = {
  id?: string
  optedIn?: boolean
  Recipient: Prisma.RecipientCreateNestedOneWithoutPreferencesInput
  topic: Prisma.TopicCreateNestedOneWithoutTopicPreferenceInput
}

export type TopicPreferenceUncheckedCreateWithoutConnectionsInput = {
  id?: string
  recipientId: string
  topicId: string
  optedIn?: boolean
}

export type TopicPreferenceCreateOrConnectWithoutConnectionsInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  create: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput>
}

export type TopicPreferenceUpsertWithWhereUniqueWithoutConnectionsInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  update: Prisma.XOR<Prisma.TopicPreferenceUpdateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedUpdateWithoutConnectionsInput>
  create: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedCreateWithoutConnectionsInput>
}

export type TopicPreferenceUpdateWithWhereUniqueWithoutConnectionsInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateWithoutConnectionsInput, Prisma.TopicPreferenceUncheckedUpdateWithoutConnectionsInput>
}

export type TopicPreferenceUpdateManyWithWhereWithoutConnectionsInput = {
  where: Prisma.TopicPreferenceScalarWhereInput
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateManyMutationInput, Prisma.TopicPreferenceUncheckedUpdateManyWithoutConnectionsInput>
}

export type TopicPreferenceScalarWhereInput = {
  AND?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
  OR?: Prisma.TopicPreferenceScalarWhereInput[]
  NOT?: Prisma.TopicPreferenceScalarWhereInput | Prisma.TopicPreferenceScalarWhereInput[]
  id?: Prisma.StringFilter<"TopicPreference"> | string
  recipientId?: Prisma.StringFilter<"TopicPreference"> | string
  topicId?: Prisma.StringFilter<"TopicPreference"> | string
  optedIn?: Prisma.BoolFilter<"TopicPreference"> | boolean
}

export type TopicPreferenceCreateWithoutRecipientInput = {
  id?: string
  optedIn?: boolean
  topic: Prisma.TopicCreateNestedOneWithoutTopicPreferenceInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutTopicPreferencesInput
}

export type TopicPreferenceUncheckedCreateWithoutRecipientInput = {
  id?: string
  topicId: string
  optedIn?: boolean
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutTopicPreferencesInput
}

export type TopicPreferenceCreateOrConnectWithoutRecipientInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  create: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput>
}

export type TopicPreferenceCreateManyRecipientInputEnvelope = {
  data: Prisma.TopicPreferenceCreateManyRecipientInput | Prisma.TopicPreferenceCreateManyRecipientInput[]
  skipDuplicates?: boolean
}

export type TopicPreferenceUpsertWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  update: Prisma.XOR<Prisma.TopicPreferenceUpdateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedUpdateWithoutRecipientInput>
  create: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedCreateWithoutRecipientInput>
}

export type TopicPreferenceUpdateWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateWithoutRecipientInput, Prisma.TopicPreferenceUncheckedUpdateWithoutRecipientInput>
}

export type TopicPreferenceUpdateManyWithWhereWithoutRecipientInput = {
  where: Prisma.TopicPreferenceScalarWhereInput
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateManyMutationInput, Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientInput>
}

export type TopicPreferenceCreateWithoutTopicInput = {
  id?: string
  optedIn?: boolean
  Recipient: Prisma.RecipientCreateNestedOneWithoutPreferencesInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutTopicPreferencesInput
}

export type TopicPreferenceUncheckedCreateWithoutTopicInput = {
  id?: string
  recipientId: string
  optedIn?: boolean
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutTopicPreferencesInput
}

export type TopicPreferenceCreateOrConnectWithoutTopicInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  create: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutTopicInput, Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput>
}

export type TopicPreferenceCreateManyTopicInputEnvelope = {
  data: Prisma.TopicPreferenceCreateManyTopicInput | Prisma.TopicPreferenceCreateManyTopicInput[]
  skipDuplicates?: boolean
}

export type TopicPreferenceUpsertWithWhereUniqueWithoutTopicInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  update: Prisma.XOR<Prisma.TopicPreferenceUpdateWithoutTopicInput, Prisma.TopicPreferenceUncheckedUpdateWithoutTopicInput>
  create: Prisma.XOR<Prisma.TopicPreferenceCreateWithoutTopicInput, Prisma.TopicPreferenceUncheckedCreateWithoutTopicInput>
}

export type TopicPreferenceUpdateWithWhereUniqueWithoutTopicInput = {
  where: Prisma.TopicPreferenceWhereUniqueInput
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateWithoutTopicInput, Prisma.TopicPreferenceUncheckedUpdateWithoutTopicInput>
}

export type TopicPreferenceUpdateManyWithWhereWithoutTopicInput = {
  where: Prisma.TopicPreferenceScalarWhereInput
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateManyMutationInput, Prisma.TopicPreferenceUncheckedUpdateManyWithoutTopicInput>
}

export type TopicPreferenceUpdateWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  Recipient?: Prisma.RecipientUpdateOneRequiredWithoutPreferencesNestedInput
  topic?: Prisma.TopicUpdateOneRequiredWithoutTopicPreferenceNestedInput
}

export type TopicPreferenceUncheckedUpdateWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  topicId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TopicPreferenceUncheckedUpdateManyWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  topicId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TopicPreferenceCreateManyRecipientInput = {
  id?: string
  topicId: string
  optedIn?: boolean
}

export type TopicPreferenceUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  topic?: Prisma.TopicUpdateOneRequiredWithoutTopicPreferenceNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutTopicPreferencesNestedInput
}

export type TopicPreferenceUncheckedUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  topicId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutTopicPreferencesNestedInput
}

export type TopicPreferenceUncheckedUpdateManyWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  topicId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TopicPreferenceCreateManyTopicInput = {
  id?: string
  recipientId: string
  optedIn?: boolean
}

export type TopicPreferenceUpdateWithoutTopicInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  Recipient?: Prisma.RecipientUpdateOneRequiredWithoutPreferencesNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutTopicPreferencesNestedInput
}

export type TopicPreferenceUncheckedUpdateWithoutTopicInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutTopicPreferencesNestedInput
}

export type TopicPreferenceUncheckedUpdateManyWithoutTopicInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  optedIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
}


/**
 * Count Type TopicPreferenceCountOutputType
 */

export type TopicPreferenceCountOutputType = {
  connections: number
}

export type TopicPreferenceCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  connections?: boolean | TopicPreferenceCountOutputTypeCountConnectionsArgs
}

/**
 * TopicPreferenceCountOutputType without action
 */
export type TopicPreferenceCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreferenceCountOutputType
   */
  select?: Prisma.TopicPreferenceCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TopicPreferenceCountOutputType without action
 */
export type TopicPreferenceCountOutputTypeCountConnectionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
}


export type TopicPreferenceSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  topicId?: boolean
  optedIn?: boolean
  Recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  topic?: boolean | Prisma.TopicDefaultArgs<ExtArgs>
  connections?: boolean | Prisma.TopicPreference$connectionsArgs<ExtArgs>
  _count?: boolean | Prisma.TopicPreferenceCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["topicPreference"]>

export type TopicPreferenceSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  topicId?: boolean
  optedIn?: boolean
  Recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  topic?: boolean | Prisma.TopicDefaultArgs<ExtArgs>
}, ExtArgs["result"]["topicPreference"]>

export type TopicPreferenceSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  topicId?: boolean
  optedIn?: boolean
  Recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  topic?: boolean | Prisma.TopicDefaultArgs<ExtArgs>
}, ExtArgs["result"]["topicPreference"]>

export type TopicPreferenceSelectScalar = {
  id?: boolean
  recipientId?: boolean
  topicId?: boolean
  optedIn?: boolean
}

export type TopicPreferenceOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "recipientId" | "topicId" | "optedIn", ExtArgs["result"]["topicPreference"]>
export type TopicPreferenceInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  Recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  topic?: boolean | Prisma.TopicDefaultArgs<ExtArgs>
  connections?: boolean | Prisma.TopicPreference$connectionsArgs<ExtArgs>
  _count?: boolean | Prisma.TopicPreferenceCountOutputTypeDefaultArgs<ExtArgs>
}
export type TopicPreferenceIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  Recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  topic?: boolean | Prisma.TopicDefaultArgs<ExtArgs>
}
export type TopicPreferenceIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  Recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
  topic?: boolean | Prisma.TopicDefaultArgs<ExtArgs>
}

export type $TopicPreferencePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TopicPreference"
  objects: {
    Recipient: Prisma.$RecipientPayload<ExtArgs>
    topic: Prisma.$TopicPayload<ExtArgs>
    connections: Prisma.$ConnectionPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    recipientId: string
    topicId: string
    optedIn: boolean
  }, ExtArgs["result"]["topicPreference"]>
  composites: {}
}

export type TopicPreferenceGetPayload<S extends boolean | null | undefined | TopicPreferenceDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload, S>

export type TopicPreferenceCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TopicPreferenceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TopicPreferenceCountAggregateInputType | true
  }

export interface TopicPreferenceDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TopicPreference'], meta: { name: 'TopicPreference' } }
  /**
   * Find zero or one TopicPreference that matches the filter.
   * @param {TopicPreferenceFindUniqueArgs} args - Arguments to find a TopicPreference
   * @example
   * // Get one TopicPreference
   * const topicPreference = await prisma.topicPreference.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TopicPreferenceFindUniqueArgs>(args: Prisma.SelectSubset<T, TopicPreferenceFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TopicPreference that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TopicPreferenceFindUniqueOrThrowArgs} args - Arguments to find a TopicPreference
   * @example
   * // Get one TopicPreference
   * const topicPreference = await prisma.topicPreference.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TopicPreferenceFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TopicPreferenceFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TopicPreference that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceFindFirstArgs} args - Arguments to find a TopicPreference
   * @example
   * // Get one TopicPreference
   * const topicPreference = await prisma.topicPreference.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TopicPreferenceFindFirstArgs>(args?: Prisma.SelectSubset<T, TopicPreferenceFindFirstArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TopicPreference that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceFindFirstOrThrowArgs} args - Arguments to find a TopicPreference
   * @example
   * // Get one TopicPreference
   * const topicPreference = await prisma.topicPreference.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TopicPreferenceFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TopicPreferenceFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TopicPreferences that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TopicPreferences
   * const topicPreferences = await prisma.topicPreference.findMany()
   * 
   * // Get first 10 TopicPreferences
   * const topicPreferences = await prisma.topicPreference.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const topicPreferenceWithIdOnly = await prisma.topicPreference.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TopicPreferenceFindManyArgs>(args?: Prisma.SelectSubset<T, TopicPreferenceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TopicPreference.
   * @param {TopicPreferenceCreateArgs} args - Arguments to create a TopicPreference.
   * @example
   * // Create one TopicPreference
   * const TopicPreference = await prisma.topicPreference.create({
   *   data: {
   *     // ... data to create a TopicPreference
   *   }
   * })
   * 
   */
  create<T extends TopicPreferenceCreateArgs>(args: Prisma.SelectSubset<T, TopicPreferenceCreateArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TopicPreferences.
   * @param {TopicPreferenceCreateManyArgs} args - Arguments to create many TopicPreferences.
   * @example
   * // Create many TopicPreferences
   * const topicPreference = await prisma.topicPreference.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TopicPreferenceCreateManyArgs>(args?: Prisma.SelectSubset<T, TopicPreferenceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TopicPreferences and returns the data saved in the database.
   * @param {TopicPreferenceCreateManyAndReturnArgs} args - Arguments to create many TopicPreferences.
   * @example
   * // Create many TopicPreferences
   * const topicPreference = await prisma.topicPreference.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TopicPreferences and only return the `id`
   * const topicPreferenceWithIdOnly = await prisma.topicPreference.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TopicPreferenceCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TopicPreferenceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TopicPreference.
   * @param {TopicPreferenceDeleteArgs} args - Arguments to delete one TopicPreference.
   * @example
   * // Delete one TopicPreference
   * const TopicPreference = await prisma.topicPreference.delete({
   *   where: {
   *     // ... filter to delete one TopicPreference
   *   }
   * })
   * 
   */
  delete<T extends TopicPreferenceDeleteArgs>(args: Prisma.SelectSubset<T, TopicPreferenceDeleteArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TopicPreference.
   * @param {TopicPreferenceUpdateArgs} args - Arguments to update one TopicPreference.
   * @example
   * // Update one TopicPreference
   * const topicPreference = await prisma.topicPreference.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TopicPreferenceUpdateArgs>(args: Prisma.SelectSubset<T, TopicPreferenceUpdateArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TopicPreferences.
   * @param {TopicPreferenceDeleteManyArgs} args - Arguments to filter TopicPreferences to delete.
   * @example
   * // Delete a few TopicPreferences
   * const { count } = await prisma.topicPreference.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TopicPreferenceDeleteManyArgs>(args?: Prisma.SelectSubset<T, TopicPreferenceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TopicPreferences.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TopicPreferences
   * const topicPreference = await prisma.topicPreference.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TopicPreferenceUpdateManyArgs>(args: Prisma.SelectSubset<T, TopicPreferenceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TopicPreferences and returns the data updated in the database.
   * @param {TopicPreferenceUpdateManyAndReturnArgs} args - Arguments to update many TopicPreferences.
   * @example
   * // Update many TopicPreferences
   * const topicPreference = await prisma.topicPreference.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TopicPreferences and only return the `id`
   * const topicPreferenceWithIdOnly = await prisma.topicPreference.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TopicPreferenceUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TopicPreferenceUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TopicPreference.
   * @param {TopicPreferenceUpsertArgs} args - Arguments to update or create a TopicPreference.
   * @example
   * // Update or create a TopicPreference
   * const topicPreference = await prisma.topicPreference.upsert({
   *   create: {
   *     // ... data to create a TopicPreference
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TopicPreference we want to update
   *   }
   * })
   */
  upsert<T extends TopicPreferenceUpsertArgs>(args: Prisma.SelectSubset<T, TopicPreferenceUpsertArgs<ExtArgs>>): Prisma.Prisma__TopicPreferenceClient<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TopicPreferences.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceCountArgs} args - Arguments to filter TopicPreferences to count.
   * @example
   * // Count the number of TopicPreferences
   * const count = await prisma.topicPreference.count({
   *   where: {
   *     // ... the filter for the TopicPreferences we want to count
   *   }
   * })
  **/
  count<T extends TopicPreferenceCountArgs>(
    args?: Prisma.Subset<T, TopicPreferenceCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TopicPreferenceCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TopicPreference.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TopicPreferenceAggregateArgs>(args: Prisma.Subset<T, TopicPreferenceAggregateArgs>): Prisma.PrismaPromise<GetTopicPreferenceAggregateType<T>>

  /**
   * Group by TopicPreference.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicPreferenceGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TopicPreferenceGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TopicPreferenceGroupByArgs['orderBy'] }
      : { orderBy?: TopicPreferenceGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TopicPreferenceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTopicPreferenceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TopicPreference model
 */
readonly fields: TopicPreferenceFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TopicPreference.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TopicPreferenceClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  Recipient<T extends Prisma.RecipientDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientDefaultArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  topic<T extends Prisma.TopicDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TopicDefaultArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  connections<T extends Prisma.TopicPreference$connectionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TopicPreference$connectionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TopicPreference model
 */
export interface TopicPreferenceFieldRefs {
  readonly id: Prisma.FieldRef<"TopicPreference", 'String'>
  readonly recipientId: Prisma.FieldRef<"TopicPreference", 'String'>
  readonly topicId: Prisma.FieldRef<"TopicPreference", 'String'>
  readonly optedIn: Prisma.FieldRef<"TopicPreference", 'Boolean'>
}
    

// Custom InputTypes
/**
 * TopicPreference findUnique
 */
export type TopicPreferenceFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * Filter, which TopicPreference to fetch.
   */
  where: Prisma.TopicPreferenceWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference findUniqueOrThrow
 */
export type TopicPreferenceFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * Filter, which TopicPreference to fetch.
   */
  where: Prisma.TopicPreferenceWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference findFirst
 */
export type TopicPreferenceFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * Filter, which TopicPreference to fetch.
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TopicPreferences to fetch.
   */
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TopicPreferences.
   */
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TopicPreferences from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TopicPreferences.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TopicPreferences.
   */
  distinct?: Prisma.TopicPreferenceScalarFieldEnum | Prisma.TopicPreferenceScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference findFirstOrThrow
 */
export type TopicPreferenceFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * Filter, which TopicPreference to fetch.
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TopicPreferences to fetch.
   */
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TopicPreferences.
   */
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TopicPreferences from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TopicPreferences.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TopicPreferences.
   */
  distinct?: Prisma.TopicPreferenceScalarFieldEnum | Prisma.TopicPreferenceScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference findMany
 */
export type TopicPreferenceFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * Filter, which TopicPreferences to fetch.
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TopicPreferences to fetch.
   */
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TopicPreferences.
   */
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TopicPreferences from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TopicPreferences.
   */
  skip?: number
  distinct?: Prisma.TopicPreferenceScalarFieldEnum | Prisma.TopicPreferenceScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference create
 */
export type TopicPreferenceCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * The data needed to create a TopicPreference.
   */
  data: Prisma.XOR<Prisma.TopicPreferenceCreateInput, Prisma.TopicPreferenceUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference createMany
 */
export type TopicPreferenceCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TopicPreferences.
   */
  data: Prisma.TopicPreferenceCreateManyInput | Prisma.TopicPreferenceCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TopicPreference createManyAndReturn
 */
export type TopicPreferenceCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * The data used to create many TopicPreferences.
   */
  data: Prisma.TopicPreferenceCreateManyInput | Prisma.TopicPreferenceCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TopicPreference update
 */
export type TopicPreferenceUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * The data needed to update a TopicPreference.
   */
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateInput, Prisma.TopicPreferenceUncheckedUpdateInput>
  /**
   * Choose, which TopicPreference to update.
   */
  where: Prisma.TopicPreferenceWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference updateMany
 */
export type TopicPreferenceUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TopicPreferences.
   */
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateManyMutationInput, Prisma.TopicPreferenceUncheckedUpdateManyInput>
  /**
   * Filter which TopicPreferences to update
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * Limit how many TopicPreferences to update.
   */
  limit?: number
}

/**
 * TopicPreference updateManyAndReturn
 */
export type TopicPreferenceUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * The data used to update TopicPreferences.
   */
  data: Prisma.XOR<Prisma.TopicPreferenceUpdateManyMutationInput, Prisma.TopicPreferenceUncheckedUpdateManyInput>
  /**
   * Filter which TopicPreferences to update
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * Limit how many TopicPreferences to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TopicPreference upsert
 */
export type TopicPreferenceUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * The filter to search for the TopicPreference to update in case it exists.
   */
  where: Prisma.TopicPreferenceWhereUniqueInput
  /**
   * In case the TopicPreference found by the `where` argument doesn't exist, create a new TopicPreference with this data.
   */
  create: Prisma.XOR<Prisma.TopicPreferenceCreateInput, Prisma.TopicPreferenceUncheckedCreateInput>
  /**
   * In case the TopicPreference was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TopicPreferenceUpdateInput, Prisma.TopicPreferenceUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference delete
 */
export type TopicPreferenceDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  /**
   * Filter which TopicPreference to delete.
   */
  where: Prisma.TopicPreferenceWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TopicPreference deleteMany
 */
export type TopicPreferenceDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TopicPreferences to delete
   */
  where?: Prisma.TopicPreferenceWhereInput
  /**
   * Limit how many TopicPreferences to delete.
   */
  limit?: number
}

/**
 * TopicPreference.connections
 */
export type TopicPreference$connectionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  cursor?: Prisma.ConnectionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * TopicPreference without action
 */
export type TopicPreferenceDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
}
