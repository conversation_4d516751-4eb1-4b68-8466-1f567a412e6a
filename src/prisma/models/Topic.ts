
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Topic` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Topic
 * 
 */
export type TopicModel = runtime.Types.Result.DefaultSelection<Prisma.$TopicPayload>

export type AggregateTopic = {
  _count: TopicCountAggregateOutputType | null
  _avg: TopicAvgAggregateOutputType | null
  _sum: TopicSumAggregateOutputType | null
  _min: TopicMinAggregateOutputType | null
  _max: TopicMaxAggregateOutputType | null
}

export type TopicAvgAggregateOutputType = {
  orderSequence: number | null
}

export type TopicSumAggregateOutputType = {
  orderSequence: number | null
}

export type TopicMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  displayName: string | null
  description: string | null
  enabled: boolean | null
  defaultCategory: string | null
  channelType: string | null
  defaultService: string | null
  visibleInPreferences: boolean | null
  orderSequence: number | null
  userMustOptIn: boolean | null
  channelAlwaysOn: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TopicMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  displayName: string | null
  description: string | null
  enabled: boolean | null
  defaultCategory: string | null
  channelType: string | null
  defaultService: string | null
  visibleInPreferences: boolean | null
  orderSequence: number | null
  userMustOptIn: boolean | null
  channelAlwaysOn: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TopicCountAggregateOutputType = {
  id: number
  tenantId: number
  externalId: number
  displayName: number
  description: number
  enabled: number
  defaultCategory: number
  channelType: number
  defaultService: number
  visibleInPreferences: number
  orderSequence: number
  userMustOptIn: number
  channelAlwaysOn: number
  userPreferenceRoles: number
  roles: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type TopicAvgAggregateInputType = {
  orderSequence?: true
}

export type TopicSumAggregateInputType = {
  orderSequence?: true
}

export type TopicMinAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  enabled?: true
  defaultCategory?: true
  channelType?: true
  defaultService?: true
  visibleInPreferences?: true
  orderSequence?: true
  userMustOptIn?: true
  channelAlwaysOn?: true
  createdAt?: true
  updatedAt?: true
}

export type TopicMaxAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  enabled?: true
  defaultCategory?: true
  channelType?: true
  defaultService?: true
  visibleInPreferences?: true
  orderSequence?: true
  userMustOptIn?: true
  channelAlwaysOn?: true
  createdAt?: true
  updatedAt?: true
}

export type TopicCountAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  enabled?: true
  defaultCategory?: true
  channelType?: true
  defaultService?: true
  visibleInPreferences?: true
  orderSequence?: true
  userMustOptIn?: true
  channelAlwaysOn?: true
  userPreferenceRoles?: true
  roles?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type TopicAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Topic to aggregate.
   */
  where?: Prisma.TopicWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Topics to fetch.
   */
  orderBy?: Prisma.TopicOrderByWithRelationInput | Prisma.TopicOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TopicWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Topics from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Topics.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Topics
  **/
  _count?: true | TopicCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TopicAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TopicSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TopicMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TopicMaxAggregateInputType
}

export type GetTopicAggregateType<T extends TopicAggregateArgs> = {
      [P in keyof T & keyof AggregateTopic]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTopic[P]>
    : Prisma.GetScalarType<T[P], AggregateTopic[P]>
}




export type TopicGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TopicWhereInput
  orderBy?: Prisma.TopicOrderByWithAggregationInput | Prisma.TopicOrderByWithAggregationInput[]
  by: Prisma.TopicScalarFieldEnum[] | Prisma.TopicScalarFieldEnum
  having?: Prisma.TopicScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TopicCountAggregateInputType | true
  _avg?: TopicAvgAggregateInputType
  _sum?: TopicSumAggregateInputType
  _min?: TopicMinAggregateInputType
  _max?: TopicMaxAggregateInputType
}

export type TopicGroupByOutputType = {
  id: string
  tenantId: string
  externalId: string
  displayName: string
  description: string
  enabled: boolean
  defaultCategory: string
  channelType: string
  defaultService: string
  visibleInPreferences: boolean
  orderSequence: number
  userMustOptIn: boolean
  channelAlwaysOn: boolean
  userPreferenceRoles: string[]
  roles: string[]
  createdAt: Date
  updatedAt: Date
  _count: TopicCountAggregateOutputType | null
  _avg: TopicAvgAggregateOutputType | null
  _sum: TopicSumAggregateOutputType | null
  _min: TopicMinAggregateOutputType | null
  _max: TopicMaxAggregateOutputType | null
}

type GetTopicGroupByPayload<T extends TopicGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TopicGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TopicGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TopicGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TopicGroupByOutputType[P]>
      }
    >
  > 



export type TopicWhereInput = {
  AND?: Prisma.TopicWhereInput | Prisma.TopicWhereInput[]
  OR?: Prisma.TopicWhereInput[]
  NOT?: Prisma.TopicWhereInput | Prisma.TopicWhereInput[]
  id?: Prisma.StringFilter<"Topic"> | string
  tenantId?: Prisma.StringFilter<"Topic"> | string
  externalId?: Prisma.StringFilter<"Topic"> | string
  displayName?: Prisma.StringFilter<"Topic"> | string
  description?: Prisma.StringFilter<"Topic"> | string
  enabled?: Prisma.BoolFilter<"Topic"> | boolean
  defaultCategory?: Prisma.StringFilter<"Topic"> | string
  channelType?: Prisma.StringFilter<"Topic"> | string
  defaultService?: Prisma.StringFilter<"Topic"> | string
  visibleInPreferences?: Prisma.BoolFilter<"Topic"> | boolean
  orderSequence?: Prisma.IntFilter<"Topic"> | number
  userMustOptIn?: Prisma.BoolFilter<"Topic"> | boolean
  channelAlwaysOn?: Prisma.BoolFilter<"Topic"> | boolean
  userPreferenceRoles?: Prisma.StringNullableListFilter<"Topic">
  roles?: Prisma.StringNullableListFilter<"Topic">
  createdAt?: Prisma.DateTimeFilter<"Topic"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Topic"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  topicPreference?: Prisma.TopicPreferenceListRelationFilter
}

export type TopicOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  defaultCategory?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  visibleInPreferences?: Prisma.SortOrder
  orderSequence?: Prisma.SortOrder
  userMustOptIn?: Prisma.SortOrder
  channelAlwaysOn?: Prisma.SortOrder
  userPreferenceRoles?: Prisma.SortOrder
  roles?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  topicPreference?: Prisma.TopicPreferenceOrderByRelationAggregateInput
}

export type TopicWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TopicWhereInput | Prisma.TopicWhereInput[]
  OR?: Prisma.TopicWhereInput[]
  NOT?: Prisma.TopicWhereInput | Prisma.TopicWhereInput[]
  tenantId?: Prisma.StringFilter<"Topic"> | string
  externalId?: Prisma.StringFilter<"Topic"> | string
  displayName?: Prisma.StringFilter<"Topic"> | string
  description?: Prisma.StringFilter<"Topic"> | string
  enabled?: Prisma.BoolFilter<"Topic"> | boolean
  defaultCategory?: Prisma.StringFilter<"Topic"> | string
  channelType?: Prisma.StringFilter<"Topic"> | string
  defaultService?: Prisma.StringFilter<"Topic"> | string
  visibleInPreferences?: Prisma.BoolFilter<"Topic"> | boolean
  orderSequence?: Prisma.IntFilter<"Topic"> | number
  userMustOptIn?: Prisma.BoolFilter<"Topic"> | boolean
  channelAlwaysOn?: Prisma.BoolFilter<"Topic"> | boolean
  userPreferenceRoles?: Prisma.StringNullableListFilter<"Topic">
  roles?: Prisma.StringNullableListFilter<"Topic">
  createdAt?: Prisma.DateTimeFilter<"Topic"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Topic"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  topicPreference?: Prisma.TopicPreferenceListRelationFilter
}, "id">

export type TopicOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  defaultCategory?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  visibleInPreferences?: Prisma.SortOrder
  orderSequence?: Prisma.SortOrder
  userMustOptIn?: Prisma.SortOrder
  channelAlwaysOn?: Prisma.SortOrder
  userPreferenceRoles?: Prisma.SortOrder
  roles?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.TopicCountOrderByAggregateInput
  _avg?: Prisma.TopicAvgOrderByAggregateInput
  _max?: Prisma.TopicMaxOrderByAggregateInput
  _min?: Prisma.TopicMinOrderByAggregateInput
  _sum?: Prisma.TopicSumOrderByAggregateInput
}

export type TopicScalarWhereWithAggregatesInput = {
  AND?: Prisma.TopicScalarWhereWithAggregatesInput | Prisma.TopicScalarWhereWithAggregatesInput[]
  OR?: Prisma.TopicScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TopicScalarWhereWithAggregatesInput | Prisma.TopicScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  externalId?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  displayName?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  description?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  enabled?: Prisma.BoolWithAggregatesFilter<"Topic"> | boolean
  defaultCategory?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  channelType?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  defaultService?: Prisma.StringWithAggregatesFilter<"Topic"> | string
  visibleInPreferences?: Prisma.BoolWithAggregatesFilter<"Topic"> | boolean
  orderSequence?: Prisma.IntWithAggregatesFilter<"Topic"> | number
  userMustOptIn?: Prisma.BoolWithAggregatesFilter<"Topic"> | boolean
  channelAlwaysOn?: Prisma.BoolWithAggregatesFilter<"Topic"> | boolean
  userPreferenceRoles?: Prisma.StringNullableListFilter<"Topic">
  roles?: Prisma.StringNullableListFilter<"Topic">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Topic"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Topic"> | Date | string
}

export type TopicCreateInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTopicsInput
  topicPreference?: Prisma.TopicPreferenceCreateNestedManyWithoutTopicInput
}

export type TopicUncheckedCreateInput = {
  id?: string
  tenantId: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  topicPreference?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutTopicInput
}

export type TopicUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTopicsNestedInput
  topicPreference?: Prisma.TopicPreferenceUpdateManyWithoutTopicNestedInput
}

export type TopicUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  topicPreference?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutTopicNestedInput
}

export type TopicCreateManyInput = {
  id?: string
  tenantId: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TopicUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TopicUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TopicListRelationFilter = {
  every?: Prisma.TopicWhereInput
  some?: Prisma.TopicWhereInput
  none?: Prisma.TopicWhereInput
}

export type TopicOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TopicScalarRelationFilter = {
  is?: Prisma.TopicWhereInput
  isNot?: Prisma.TopicWhereInput
}

export type TopicCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  defaultCategory?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  visibleInPreferences?: Prisma.SortOrder
  orderSequence?: Prisma.SortOrder
  userMustOptIn?: Prisma.SortOrder
  channelAlwaysOn?: Prisma.SortOrder
  userPreferenceRoles?: Prisma.SortOrder
  roles?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TopicAvgOrderByAggregateInput = {
  orderSequence?: Prisma.SortOrder
}

export type TopicMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  defaultCategory?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  visibleInPreferences?: Prisma.SortOrder
  orderSequence?: Prisma.SortOrder
  userMustOptIn?: Prisma.SortOrder
  channelAlwaysOn?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TopicMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  defaultCategory?: Prisma.SortOrder
  channelType?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  visibleInPreferences?: Prisma.SortOrder
  orderSequence?: Prisma.SortOrder
  userMustOptIn?: Prisma.SortOrder
  channelAlwaysOn?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TopicSumOrderByAggregateInput = {
  orderSequence?: Prisma.SortOrder
}

export type TopicCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TopicCreateWithoutTenantInput, Prisma.TopicUncheckedCreateWithoutTenantInput> | Prisma.TopicCreateWithoutTenantInput[] | Prisma.TopicUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TopicCreateOrConnectWithoutTenantInput | Prisma.TopicCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TopicCreateManyTenantInputEnvelope
  connect?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
}

export type TopicUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TopicCreateWithoutTenantInput, Prisma.TopicUncheckedCreateWithoutTenantInput> | Prisma.TopicCreateWithoutTenantInput[] | Prisma.TopicUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TopicCreateOrConnectWithoutTenantInput | Prisma.TopicCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TopicCreateManyTenantInputEnvelope
  connect?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
}

export type TopicUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TopicCreateWithoutTenantInput, Prisma.TopicUncheckedCreateWithoutTenantInput> | Prisma.TopicCreateWithoutTenantInput[] | Prisma.TopicUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TopicCreateOrConnectWithoutTenantInput | Prisma.TopicCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TopicUpsertWithWhereUniqueWithoutTenantInput | Prisma.TopicUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TopicCreateManyTenantInputEnvelope
  set?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  disconnect?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  delete?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  connect?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  update?: Prisma.TopicUpdateWithWhereUniqueWithoutTenantInput | Prisma.TopicUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TopicUpdateManyWithWhereWithoutTenantInput | Prisma.TopicUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TopicScalarWhereInput | Prisma.TopicScalarWhereInput[]
}

export type TopicUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TopicCreateWithoutTenantInput, Prisma.TopicUncheckedCreateWithoutTenantInput> | Prisma.TopicCreateWithoutTenantInput[] | Prisma.TopicUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TopicCreateOrConnectWithoutTenantInput | Prisma.TopicCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TopicUpsertWithWhereUniqueWithoutTenantInput | Prisma.TopicUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TopicCreateManyTenantInputEnvelope
  set?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  disconnect?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  delete?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  connect?: Prisma.TopicWhereUniqueInput | Prisma.TopicWhereUniqueInput[]
  update?: Prisma.TopicUpdateWithWhereUniqueWithoutTenantInput | Prisma.TopicUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TopicUpdateManyWithWhereWithoutTenantInput | Prisma.TopicUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TopicScalarWhereInput | Prisma.TopicScalarWhereInput[]
}

export type TopicCreateNestedOneWithoutTopicPreferenceInput = {
  create?: Prisma.XOR<Prisma.TopicCreateWithoutTopicPreferenceInput, Prisma.TopicUncheckedCreateWithoutTopicPreferenceInput>
  connectOrCreate?: Prisma.TopicCreateOrConnectWithoutTopicPreferenceInput
  connect?: Prisma.TopicWhereUniqueInput
}

export type TopicUpdateOneRequiredWithoutTopicPreferenceNestedInput = {
  create?: Prisma.XOR<Prisma.TopicCreateWithoutTopicPreferenceInput, Prisma.TopicUncheckedCreateWithoutTopicPreferenceInput>
  connectOrCreate?: Prisma.TopicCreateOrConnectWithoutTopicPreferenceInput
  upsert?: Prisma.TopicUpsertWithoutTopicPreferenceInput
  connect?: Prisma.TopicWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TopicUpdateToOneWithWhereWithoutTopicPreferenceInput, Prisma.TopicUpdateWithoutTopicPreferenceInput>, Prisma.TopicUncheckedUpdateWithoutTopicPreferenceInput>
}

export type TopicCreateuserPreferenceRolesInput = {
  set: string[]
}

export type TopicCreaterolesInput = {
  set: string[]
}

export type TopicUpdateuserPreferenceRolesInput = {
  set?: string[]
  push?: string | string[]
}

export type TopicUpdaterolesInput = {
  set?: string[]
  push?: string | string[]
}

export type TopicCreateWithoutTenantInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  topicPreference?: Prisma.TopicPreferenceCreateNestedManyWithoutTopicInput
}

export type TopicUncheckedCreateWithoutTenantInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  topicPreference?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutTopicInput
}

export type TopicCreateOrConnectWithoutTenantInput = {
  where: Prisma.TopicWhereUniqueInput
  create: Prisma.XOR<Prisma.TopicCreateWithoutTenantInput, Prisma.TopicUncheckedCreateWithoutTenantInput>
}

export type TopicCreateManyTenantInputEnvelope = {
  data: Prisma.TopicCreateManyTenantInput | Prisma.TopicCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type TopicUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TopicWhereUniqueInput
  update: Prisma.XOR<Prisma.TopicUpdateWithoutTenantInput, Prisma.TopicUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.TopicCreateWithoutTenantInput, Prisma.TopicUncheckedCreateWithoutTenantInput>
}

export type TopicUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TopicWhereUniqueInput
  data: Prisma.XOR<Prisma.TopicUpdateWithoutTenantInput, Prisma.TopicUncheckedUpdateWithoutTenantInput>
}

export type TopicUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.TopicScalarWhereInput
  data: Prisma.XOR<Prisma.TopicUpdateManyMutationInput, Prisma.TopicUncheckedUpdateManyWithoutTenantInput>
}

export type TopicScalarWhereInput = {
  AND?: Prisma.TopicScalarWhereInput | Prisma.TopicScalarWhereInput[]
  OR?: Prisma.TopicScalarWhereInput[]
  NOT?: Prisma.TopicScalarWhereInput | Prisma.TopicScalarWhereInput[]
  id?: Prisma.StringFilter<"Topic"> | string
  tenantId?: Prisma.StringFilter<"Topic"> | string
  externalId?: Prisma.StringFilter<"Topic"> | string
  displayName?: Prisma.StringFilter<"Topic"> | string
  description?: Prisma.StringFilter<"Topic"> | string
  enabled?: Prisma.BoolFilter<"Topic"> | boolean
  defaultCategory?: Prisma.StringFilter<"Topic"> | string
  channelType?: Prisma.StringFilter<"Topic"> | string
  defaultService?: Prisma.StringFilter<"Topic"> | string
  visibleInPreferences?: Prisma.BoolFilter<"Topic"> | boolean
  orderSequence?: Prisma.IntFilter<"Topic"> | number
  userMustOptIn?: Prisma.BoolFilter<"Topic"> | boolean
  channelAlwaysOn?: Prisma.BoolFilter<"Topic"> | boolean
  userPreferenceRoles?: Prisma.StringNullableListFilter<"Topic">
  roles?: Prisma.StringNullableListFilter<"Topic">
  createdAt?: Prisma.DateTimeFilter<"Topic"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Topic"> | Date | string
}

export type TopicCreateWithoutTopicPreferenceInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTopicsInput
}

export type TopicUncheckedCreateWithoutTopicPreferenceInput = {
  id?: string
  tenantId: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TopicCreateOrConnectWithoutTopicPreferenceInput = {
  where: Prisma.TopicWhereUniqueInput
  create: Prisma.XOR<Prisma.TopicCreateWithoutTopicPreferenceInput, Prisma.TopicUncheckedCreateWithoutTopicPreferenceInput>
}

export type TopicUpsertWithoutTopicPreferenceInput = {
  update: Prisma.XOR<Prisma.TopicUpdateWithoutTopicPreferenceInput, Prisma.TopicUncheckedUpdateWithoutTopicPreferenceInput>
  create: Prisma.XOR<Prisma.TopicCreateWithoutTopicPreferenceInput, Prisma.TopicUncheckedCreateWithoutTopicPreferenceInput>
  where?: Prisma.TopicWhereInput
}

export type TopicUpdateToOneWithWhereWithoutTopicPreferenceInput = {
  where?: Prisma.TopicWhereInput
  data: Prisma.XOR<Prisma.TopicUpdateWithoutTopicPreferenceInput, Prisma.TopicUncheckedUpdateWithoutTopicPreferenceInput>
}

export type TopicUpdateWithoutTopicPreferenceInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTopicsNestedInput
}

export type TopicUncheckedUpdateWithoutTopicPreferenceInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TopicCreateManyTenantInput = {
  id?: string
  externalId?: string
  displayName: string
  description?: string
  enabled?: boolean
  defaultCategory?: string
  channelType: string
  defaultService: string
  visibleInPreferences?: boolean
  orderSequence?: number
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: Prisma.TopicCreateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicCreaterolesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TopicUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  topicPreference?: Prisma.TopicPreferenceUpdateManyWithoutTopicNestedInput
}

export type TopicUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  topicPreference?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutTopicNestedInput
}

export type TopicUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultCategory?: Prisma.StringFieldUpdateOperationsInput | string
  channelType?: Prisma.StringFieldUpdateOperationsInput | string
  defaultService?: Prisma.StringFieldUpdateOperationsInput | string
  visibleInPreferences?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderSequence?: Prisma.IntFieldUpdateOperationsInput | number
  userMustOptIn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelAlwaysOn?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userPreferenceRoles?: Prisma.TopicUpdateuserPreferenceRolesInput | string[]
  roles?: Prisma.TopicUpdaterolesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type TopicCountOutputType
 */

export type TopicCountOutputType = {
  topicPreference: number
}

export type TopicCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  topicPreference?: boolean | TopicCountOutputTypeCountTopicPreferenceArgs
}

/**
 * TopicCountOutputType without action
 */
export type TopicCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicCountOutputType
   */
  select?: Prisma.TopicCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TopicCountOutputType without action
 */
export type TopicCountOutputTypeCountTopicPreferenceArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TopicPreferenceWhereInput
}


export type TopicSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  defaultCategory?: boolean
  channelType?: boolean
  defaultService?: boolean
  visibleInPreferences?: boolean
  orderSequence?: boolean
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: boolean
  roles?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  topicPreference?: boolean | Prisma.Topic$topicPreferenceArgs<ExtArgs>
  _count?: boolean | Prisma.TopicCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["topic"]>

export type TopicSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  defaultCategory?: boolean
  channelType?: boolean
  defaultService?: boolean
  visibleInPreferences?: boolean
  orderSequence?: boolean
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: boolean
  roles?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["topic"]>

export type TopicSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  defaultCategory?: boolean
  channelType?: boolean
  defaultService?: boolean
  visibleInPreferences?: boolean
  orderSequence?: boolean
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: boolean
  roles?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["topic"]>

export type TopicSelectScalar = {
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  defaultCategory?: boolean
  channelType?: boolean
  defaultService?: boolean
  visibleInPreferences?: boolean
  orderSequence?: boolean
  userMustOptIn?: boolean
  channelAlwaysOn?: boolean
  userPreferenceRoles?: boolean
  roles?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type TopicOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "externalId" | "displayName" | "description" | "enabled" | "defaultCategory" | "channelType" | "defaultService" | "visibleInPreferences" | "orderSequence" | "userMustOptIn" | "channelAlwaysOn" | "userPreferenceRoles" | "roles" | "createdAt" | "updatedAt", ExtArgs["result"]["topic"]>
export type TopicInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  topicPreference?: boolean | Prisma.Topic$topicPreferenceArgs<ExtArgs>
  _count?: boolean | Prisma.TopicCountOutputTypeDefaultArgs<ExtArgs>
}
export type TopicIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TopicIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $TopicPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Topic"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    topicPreference: Prisma.$TopicPreferencePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    externalId: string
    displayName: string
    description: string
    enabled: boolean
    defaultCategory: string
    channelType: string
    defaultService: string
    visibleInPreferences: boolean
    orderSequence: number
    userMustOptIn: boolean
    channelAlwaysOn: boolean
    userPreferenceRoles: string[]
    roles: string[]
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["topic"]>
  composites: {}
}

export type TopicGetPayload<S extends boolean | null | undefined | TopicDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TopicPayload, S>

export type TopicCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TopicFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TopicCountAggregateInputType | true
  }

export interface TopicDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Topic'], meta: { name: 'Topic' } }
  /**
   * Find zero or one Topic that matches the filter.
   * @param {TopicFindUniqueArgs} args - Arguments to find a Topic
   * @example
   * // Get one Topic
   * const topic = await prisma.topic.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TopicFindUniqueArgs>(args: Prisma.SelectSubset<T, TopicFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Topic that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TopicFindUniqueOrThrowArgs} args - Arguments to find a Topic
   * @example
   * // Get one Topic
   * const topic = await prisma.topic.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TopicFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TopicFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Topic that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicFindFirstArgs} args - Arguments to find a Topic
   * @example
   * // Get one Topic
   * const topic = await prisma.topic.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TopicFindFirstArgs>(args?: Prisma.SelectSubset<T, TopicFindFirstArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Topic that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicFindFirstOrThrowArgs} args - Arguments to find a Topic
   * @example
   * // Get one Topic
   * const topic = await prisma.topic.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TopicFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TopicFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Topics that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Topics
   * const topics = await prisma.topic.findMany()
   * 
   * // Get first 10 Topics
   * const topics = await prisma.topic.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const topicWithIdOnly = await prisma.topic.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TopicFindManyArgs>(args?: Prisma.SelectSubset<T, TopicFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Topic.
   * @param {TopicCreateArgs} args - Arguments to create a Topic.
   * @example
   * // Create one Topic
   * const Topic = await prisma.topic.create({
   *   data: {
   *     // ... data to create a Topic
   *   }
   * })
   * 
   */
  create<T extends TopicCreateArgs>(args: Prisma.SelectSubset<T, TopicCreateArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Topics.
   * @param {TopicCreateManyArgs} args - Arguments to create many Topics.
   * @example
   * // Create many Topics
   * const topic = await prisma.topic.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TopicCreateManyArgs>(args?: Prisma.SelectSubset<T, TopicCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Topics and returns the data saved in the database.
   * @param {TopicCreateManyAndReturnArgs} args - Arguments to create many Topics.
   * @example
   * // Create many Topics
   * const topic = await prisma.topic.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Topics and only return the `id`
   * const topicWithIdOnly = await prisma.topic.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TopicCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TopicCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Topic.
   * @param {TopicDeleteArgs} args - Arguments to delete one Topic.
   * @example
   * // Delete one Topic
   * const Topic = await prisma.topic.delete({
   *   where: {
   *     // ... filter to delete one Topic
   *   }
   * })
   * 
   */
  delete<T extends TopicDeleteArgs>(args: Prisma.SelectSubset<T, TopicDeleteArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Topic.
   * @param {TopicUpdateArgs} args - Arguments to update one Topic.
   * @example
   * // Update one Topic
   * const topic = await prisma.topic.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TopicUpdateArgs>(args: Prisma.SelectSubset<T, TopicUpdateArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Topics.
   * @param {TopicDeleteManyArgs} args - Arguments to filter Topics to delete.
   * @example
   * // Delete a few Topics
   * const { count } = await prisma.topic.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TopicDeleteManyArgs>(args?: Prisma.SelectSubset<T, TopicDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Topics.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Topics
   * const topic = await prisma.topic.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TopicUpdateManyArgs>(args: Prisma.SelectSubset<T, TopicUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Topics and returns the data updated in the database.
   * @param {TopicUpdateManyAndReturnArgs} args - Arguments to update many Topics.
   * @example
   * // Update many Topics
   * const topic = await prisma.topic.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Topics and only return the `id`
   * const topicWithIdOnly = await prisma.topic.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TopicUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TopicUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Topic.
   * @param {TopicUpsertArgs} args - Arguments to update or create a Topic.
   * @example
   * // Update or create a Topic
   * const topic = await prisma.topic.upsert({
   *   create: {
   *     // ... data to create a Topic
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Topic we want to update
   *   }
   * })
   */
  upsert<T extends TopicUpsertArgs>(args: Prisma.SelectSubset<T, TopicUpsertArgs<ExtArgs>>): Prisma.Prisma__TopicClient<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Topics.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicCountArgs} args - Arguments to filter Topics to count.
   * @example
   * // Count the number of Topics
   * const count = await prisma.topic.count({
   *   where: {
   *     // ... the filter for the Topics we want to count
   *   }
   * })
  **/
  count<T extends TopicCountArgs>(
    args?: Prisma.Subset<T, TopicCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TopicCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Topic.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TopicAggregateArgs>(args: Prisma.Subset<T, TopicAggregateArgs>): Prisma.PrismaPromise<GetTopicAggregateType<T>>

  /**
   * Group by Topic.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TopicGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TopicGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TopicGroupByArgs['orderBy'] }
      : { orderBy?: TopicGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TopicGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTopicGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Topic model
 */
readonly fields: TopicFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Topic.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TopicClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  topicPreference<T extends Prisma.Topic$topicPreferenceArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Topic$topicPreferenceArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Topic model
 */
export interface TopicFieldRefs {
  readonly id: Prisma.FieldRef<"Topic", 'String'>
  readonly tenantId: Prisma.FieldRef<"Topic", 'String'>
  readonly externalId: Prisma.FieldRef<"Topic", 'String'>
  readonly displayName: Prisma.FieldRef<"Topic", 'String'>
  readonly description: Prisma.FieldRef<"Topic", 'String'>
  readonly enabled: Prisma.FieldRef<"Topic", 'Boolean'>
  readonly defaultCategory: Prisma.FieldRef<"Topic", 'String'>
  readonly channelType: Prisma.FieldRef<"Topic", 'String'>
  readonly defaultService: Prisma.FieldRef<"Topic", 'String'>
  readonly visibleInPreferences: Prisma.FieldRef<"Topic", 'Boolean'>
  readonly orderSequence: Prisma.FieldRef<"Topic", 'Int'>
  readonly userMustOptIn: Prisma.FieldRef<"Topic", 'Boolean'>
  readonly channelAlwaysOn: Prisma.FieldRef<"Topic", 'Boolean'>
  readonly userPreferenceRoles: Prisma.FieldRef<"Topic", 'String[]'>
  readonly roles: Prisma.FieldRef<"Topic", 'String[]'>
  readonly createdAt: Prisma.FieldRef<"Topic", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Topic", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Topic findUnique
 */
export type TopicFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * Filter, which Topic to fetch.
   */
  where: Prisma.TopicWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic findUniqueOrThrow
 */
export type TopicFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * Filter, which Topic to fetch.
   */
  where: Prisma.TopicWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic findFirst
 */
export type TopicFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * Filter, which Topic to fetch.
   */
  where?: Prisma.TopicWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Topics to fetch.
   */
  orderBy?: Prisma.TopicOrderByWithRelationInput | Prisma.TopicOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Topics.
   */
  cursor?: Prisma.TopicWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Topics from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Topics.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Topics.
   */
  distinct?: Prisma.TopicScalarFieldEnum | Prisma.TopicScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic findFirstOrThrow
 */
export type TopicFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * Filter, which Topic to fetch.
   */
  where?: Prisma.TopicWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Topics to fetch.
   */
  orderBy?: Prisma.TopicOrderByWithRelationInput | Prisma.TopicOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Topics.
   */
  cursor?: Prisma.TopicWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Topics from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Topics.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Topics.
   */
  distinct?: Prisma.TopicScalarFieldEnum | Prisma.TopicScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic findMany
 */
export type TopicFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * Filter, which Topics to fetch.
   */
  where?: Prisma.TopicWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Topics to fetch.
   */
  orderBy?: Prisma.TopicOrderByWithRelationInput | Prisma.TopicOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Topics.
   */
  cursor?: Prisma.TopicWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Topics from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Topics.
   */
  skip?: number
  distinct?: Prisma.TopicScalarFieldEnum | Prisma.TopicScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic create
 */
export type TopicCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * The data needed to create a Topic.
   */
  data: Prisma.XOR<Prisma.TopicCreateInput, Prisma.TopicUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic createMany
 */
export type TopicCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Topics.
   */
  data: Prisma.TopicCreateManyInput | Prisma.TopicCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Topic createManyAndReturn
 */
export type TopicCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * The data used to create many Topics.
   */
  data: Prisma.TopicCreateManyInput | Prisma.TopicCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Topic update
 */
export type TopicUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * The data needed to update a Topic.
   */
  data: Prisma.XOR<Prisma.TopicUpdateInput, Prisma.TopicUncheckedUpdateInput>
  /**
   * Choose, which Topic to update.
   */
  where: Prisma.TopicWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic updateMany
 */
export type TopicUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Topics.
   */
  data: Prisma.XOR<Prisma.TopicUpdateManyMutationInput, Prisma.TopicUncheckedUpdateManyInput>
  /**
   * Filter which Topics to update
   */
  where?: Prisma.TopicWhereInput
  /**
   * Limit how many Topics to update.
   */
  limit?: number
}

/**
 * Topic updateManyAndReturn
 */
export type TopicUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * The data used to update Topics.
   */
  data: Prisma.XOR<Prisma.TopicUpdateManyMutationInput, Prisma.TopicUncheckedUpdateManyInput>
  /**
   * Filter which Topics to update
   */
  where?: Prisma.TopicWhereInput
  /**
   * Limit how many Topics to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Topic upsert
 */
export type TopicUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * The filter to search for the Topic to update in case it exists.
   */
  where: Prisma.TopicWhereUniqueInput
  /**
   * In case the Topic found by the `where` argument doesn't exist, create a new Topic with this data.
   */
  create: Prisma.XOR<Prisma.TopicCreateInput, Prisma.TopicUncheckedCreateInput>
  /**
   * In case the Topic was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TopicUpdateInput, Prisma.TopicUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic delete
 */
export type TopicDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  /**
   * Filter which Topic to delete.
   */
  where: Prisma.TopicWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Topic deleteMany
 */
export type TopicDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Topics to delete
   */
  where?: Prisma.TopicWhereInput
  /**
   * Limit how many Topics to delete.
   */
  limit?: number
}

/**
 * Topic.topicPreference
 */
export type Topic$topicPreferenceArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  where?: Prisma.TopicPreferenceWhereInput
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TopicPreferenceScalarFieldEnum | Prisma.TopicPreferenceScalarFieldEnum[]
}

/**
 * Topic without action
 */
export type TopicDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
}
