
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ChannelAttribute` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ChannelAttribute
 * 
 */
export type ChannelAttributeModel = runtime.Types.Result.DefaultSelection<Prisma.$ChannelAttributePayload>

export type AggregateChannelAttribute = {
  _count: ChannelAttributeCountAggregateOutputType | null
  _min: ChannelAttributeMinAggregateOutputType | null
  _max: ChannelAttributeMaxAggregateOutputType | null
}

export type ChannelAttributeMinAggregateOutputType = {
  id: string | null
  channelId: string | null
  name: string | null
  value: string | null
  secure: boolean | null
}

export type ChannelAttributeMaxAggregateOutputType = {
  id: string | null
  channelId: string | null
  name: string | null
  value: string | null
  secure: boolean | null
}

export type ChannelAttributeCountAggregateOutputType = {
  id: number
  channelId: number
  name: number
  value: number
  secure: number
  _all: number
}


export type ChannelAttributeMinAggregateInputType = {
  id?: true
  channelId?: true
  name?: true
  value?: true
  secure?: true
}

export type ChannelAttributeMaxAggregateInputType = {
  id?: true
  channelId?: true
  name?: true
  value?: true
  secure?: true
}

export type ChannelAttributeCountAggregateInputType = {
  id?: true
  channelId?: true
  name?: true
  value?: true
  secure?: true
  _all?: true
}

export type ChannelAttributeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ChannelAttribute to aggregate.
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelAttributes to fetch.
   */
  orderBy?: Prisma.ChannelAttributeOrderByWithRelationInput | Prisma.ChannelAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ChannelAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ChannelAttributes
  **/
  _count?: true | ChannelAttributeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ChannelAttributeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ChannelAttributeMaxAggregateInputType
}

export type GetChannelAttributeAggregateType<T extends ChannelAttributeAggregateArgs> = {
      [P in keyof T & keyof AggregateChannelAttribute]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateChannelAttribute[P]>
    : Prisma.GetScalarType<T[P], AggregateChannelAttribute[P]>
}




export type ChannelAttributeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelAttributeWhereInput
  orderBy?: Prisma.ChannelAttributeOrderByWithAggregationInput | Prisma.ChannelAttributeOrderByWithAggregationInput[]
  by: Prisma.ChannelAttributeScalarFieldEnum[] | Prisma.ChannelAttributeScalarFieldEnum
  having?: Prisma.ChannelAttributeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ChannelAttributeCountAggregateInputType | true
  _min?: ChannelAttributeMinAggregateInputType
  _max?: ChannelAttributeMaxAggregateInputType
}

export type ChannelAttributeGroupByOutputType = {
  id: string
  channelId: string
  name: string
  value: string
  secure: boolean
  _count: ChannelAttributeCountAggregateOutputType | null
  _min: ChannelAttributeMinAggregateOutputType | null
  _max: ChannelAttributeMaxAggregateOutputType | null
}

type GetChannelAttributeGroupByPayload<T extends ChannelAttributeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ChannelAttributeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ChannelAttributeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ChannelAttributeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ChannelAttributeGroupByOutputType[P]>
      }
    >
  > 



export type ChannelAttributeWhereInput = {
  AND?: Prisma.ChannelAttributeWhereInput | Prisma.ChannelAttributeWhereInput[]
  OR?: Prisma.ChannelAttributeWhereInput[]
  NOT?: Prisma.ChannelAttributeWhereInput | Prisma.ChannelAttributeWhereInput[]
  id?: Prisma.StringFilter<"ChannelAttribute"> | string
  channelId?: Prisma.StringFilter<"ChannelAttribute"> | string
  name?: Prisma.StringFilter<"ChannelAttribute"> | string
  value?: Prisma.StringFilter<"ChannelAttribute"> | string
  secure?: Prisma.BoolFilter<"ChannelAttribute"> | boolean
  channel?: Prisma.XOR<Prisma.ChannelScalarRelationFilter, Prisma.ChannelWhereInput>
}

export type ChannelAttributeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  secure?: Prisma.SortOrder
  channel?: Prisma.ChannelOrderByWithRelationInput
}

export type ChannelAttributeWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  unique_channel_attribute_channel_id_name?: Prisma.ChannelAttributeUnique_channel_attribute_channel_id_nameCompoundUniqueInput
  AND?: Prisma.ChannelAttributeWhereInput | Prisma.ChannelAttributeWhereInput[]
  OR?: Prisma.ChannelAttributeWhereInput[]
  NOT?: Prisma.ChannelAttributeWhereInput | Prisma.ChannelAttributeWhereInput[]
  channelId?: Prisma.StringFilter<"ChannelAttribute"> | string
  name?: Prisma.StringFilter<"ChannelAttribute"> | string
  value?: Prisma.StringFilter<"ChannelAttribute"> | string
  secure?: Prisma.BoolFilter<"ChannelAttribute"> | boolean
  channel?: Prisma.XOR<Prisma.ChannelScalarRelationFilter, Prisma.ChannelWhereInput>
}, "id" | "unique_channel_attribute_channel_id_name">

export type ChannelAttributeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  secure?: Prisma.SortOrder
  _count?: Prisma.ChannelAttributeCountOrderByAggregateInput
  _max?: Prisma.ChannelAttributeMaxOrderByAggregateInput
  _min?: Prisma.ChannelAttributeMinOrderByAggregateInput
}

export type ChannelAttributeScalarWhereWithAggregatesInput = {
  AND?: Prisma.ChannelAttributeScalarWhereWithAggregatesInput | Prisma.ChannelAttributeScalarWhereWithAggregatesInput[]
  OR?: Prisma.ChannelAttributeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ChannelAttributeScalarWhereWithAggregatesInput | Prisma.ChannelAttributeScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"ChannelAttribute"> | string
  channelId?: Prisma.StringWithAggregatesFilter<"ChannelAttribute"> | string
  name?: Prisma.StringWithAggregatesFilter<"ChannelAttribute"> | string
  value?: Prisma.StringWithAggregatesFilter<"ChannelAttribute"> | string
  secure?: Prisma.BoolWithAggregatesFilter<"ChannelAttribute"> | boolean
}

export type ChannelAttributeCreateInput = {
  id?: string
  name: string
  value?: string
  secure?: boolean
  channel: Prisma.ChannelCreateNestedOneWithoutAttributesInput
}

export type ChannelAttributeUncheckedCreateInput = {
  id?: string
  channelId: string
  name: string
  value?: string
  secure?: boolean
}

export type ChannelAttributeUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channel?: Prisma.ChannelUpdateOneRequiredWithoutAttributesNestedInput
}

export type ChannelAttributeUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  channelId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type ChannelAttributeCreateManyInput = {
  id?: string
  channelId: string
  name: string
  value?: string
  secure?: boolean
}

export type ChannelAttributeUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type ChannelAttributeUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  channelId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type ChannelAttributeUnique_channel_attribute_channel_id_nameCompoundUniqueInput = {
  channelId: string
  name: string
}

export type ChannelAttributeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  secure?: Prisma.SortOrder
}

export type ChannelAttributeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  secure?: Prisma.SortOrder
}

export type ChannelAttributeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  secure?: Prisma.SortOrder
}

export type ChannelAttributeListRelationFilter = {
  every?: Prisma.ChannelAttributeWhereInput
  some?: Prisma.ChannelAttributeWhereInput
  none?: Prisma.ChannelAttributeWhereInput
}

export type ChannelAttributeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}

export type ChannelAttributeCreateNestedManyWithoutChannelInput = {
  create?: Prisma.XOR<Prisma.ChannelAttributeCreateWithoutChannelInput, Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput> | Prisma.ChannelAttributeCreateWithoutChannelInput[] | Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput | Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput[]
  createMany?: Prisma.ChannelAttributeCreateManyChannelInputEnvelope
  connect?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
}

export type ChannelAttributeUncheckedCreateNestedManyWithoutChannelInput = {
  create?: Prisma.XOR<Prisma.ChannelAttributeCreateWithoutChannelInput, Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput> | Prisma.ChannelAttributeCreateWithoutChannelInput[] | Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput | Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput[]
  createMany?: Prisma.ChannelAttributeCreateManyChannelInputEnvelope
  connect?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
}

export type ChannelAttributeUpdateManyWithoutChannelNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelAttributeCreateWithoutChannelInput, Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput> | Prisma.ChannelAttributeCreateWithoutChannelInput[] | Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput | Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput[]
  upsert?: Prisma.ChannelAttributeUpsertWithWhereUniqueWithoutChannelInput | Prisma.ChannelAttributeUpsertWithWhereUniqueWithoutChannelInput[]
  createMany?: Prisma.ChannelAttributeCreateManyChannelInputEnvelope
  set?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  disconnect?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  delete?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  connect?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  update?: Prisma.ChannelAttributeUpdateWithWhereUniqueWithoutChannelInput | Prisma.ChannelAttributeUpdateWithWhereUniqueWithoutChannelInput[]
  updateMany?: Prisma.ChannelAttributeUpdateManyWithWhereWithoutChannelInput | Prisma.ChannelAttributeUpdateManyWithWhereWithoutChannelInput[]
  deleteMany?: Prisma.ChannelAttributeScalarWhereInput | Prisma.ChannelAttributeScalarWhereInput[]
}

export type ChannelAttributeUncheckedUpdateManyWithoutChannelNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelAttributeCreateWithoutChannelInput, Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput> | Prisma.ChannelAttributeCreateWithoutChannelInput[] | Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput | Prisma.ChannelAttributeCreateOrConnectWithoutChannelInput[]
  upsert?: Prisma.ChannelAttributeUpsertWithWhereUniqueWithoutChannelInput | Prisma.ChannelAttributeUpsertWithWhereUniqueWithoutChannelInput[]
  createMany?: Prisma.ChannelAttributeCreateManyChannelInputEnvelope
  set?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  disconnect?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  delete?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  connect?: Prisma.ChannelAttributeWhereUniqueInput | Prisma.ChannelAttributeWhereUniqueInput[]
  update?: Prisma.ChannelAttributeUpdateWithWhereUniqueWithoutChannelInput | Prisma.ChannelAttributeUpdateWithWhereUniqueWithoutChannelInput[]
  updateMany?: Prisma.ChannelAttributeUpdateManyWithWhereWithoutChannelInput | Prisma.ChannelAttributeUpdateManyWithWhereWithoutChannelInput[]
  deleteMany?: Prisma.ChannelAttributeScalarWhereInput | Prisma.ChannelAttributeScalarWhereInput[]
}

export type ChannelAttributeCreateWithoutChannelInput = {
  id?: string
  name: string
  value?: string
  secure?: boolean
}

export type ChannelAttributeUncheckedCreateWithoutChannelInput = {
  id?: string
  name: string
  value?: string
  secure?: boolean
}

export type ChannelAttributeCreateOrConnectWithoutChannelInput = {
  where: Prisma.ChannelAttributeWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelAttributeCreateWithoutChannelInput, Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput>
}

export type ChannelAttributeCreateManyChannelInputEnvelope = {
  data: Prisma.ChannelAttributeCreateManyChannelInput | Prisma.ChannelAttributeCreateManyChannelInput[]
  skipDuplicates?: boolean
}

export type ChannelAttributeUpsertWithWhereUniqueWithoutChannelInput = {
  where: Prisma.ChannelAttributeWhereUniqueInput
  update: Prisma.XOR<Prisma.ChannelAttributeUpdateWithoutChannelInput, Prisma.ChannelAttributeUncheckedUpdateWithoutChannelInput>
  create: Prisma.XOR<Prisma.ChannelAttributeCreateWithoutChannelInput, Prisma.ChannelAttributeUncheckedCreateWithoutChannelInput>
}

export type ChannelAttributeUpdateWithWhereUniqueWithoutChannelInput = {
  where: Prisma.ChannelAttributeWhereUniqueInput
  data: Prisma.XOR<Prisma.ChannelAttributeUpdateWithoutChannelInput, Prisma.ChannelAttributeUncheckedUpdateWithoutChannelInput>
}

export type ChannelAttributeUpdateManyWithWhereWithoutChannelInput = {
  where: Prisma.ChannelAttributeScalarWhereInput
  data: Prisma.XOR<Prisma.ChannelAttributeUpdateManyMutationInput, Prisma.ChannelAttributeUncheckedUpdateManyWithoutChannelInput>
}

export type ChannelAttributeScalarWhereInput = {
  AND?: Prisma.ChannelAttributeScalarWhereInput | Prisma.ChannelAttributeScalarWhereInput[]
  OR?: Prisma.ChannelAttributeScalarWhereInput[]
  NOT?: Prisma.ChannelAttributeScalarWhereInput | Prisma.ChannelAttributeScalarWhereInput[]
  id?: Prisma.StringFilter<"ChannelAttribute"> | string
  channelId?: Prisma.StringFilter<"ChannelAttribute"> | string
  name?: Prisma.StringFilter<"ChannelAttribute"> | string
  value?: Prisma.StringFilter<"ChannelAttribute"> | string
  secure?: Prisma.BoolFilter<"ChannelAttribute"> | boolean
}

export type ChannelAttributeCreateManyChannelInput = {
  id?: string
  name: string
  value?: string
  secure?: boolean
}

export type ChannelAttributeUpdateWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type ChannelAttributeUncheckedUpdateWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type ChannelAttributeUncheckedUpdateManyWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  secure?: Prisma.BoolFieldUpdateOperationsInput | boolean
}



export type ChannelAttributeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  channelId?: boolean
  name?: boolean
  value?: boolean
  secure?: boolean
  channel?: boolean | Prisma.ChannelDefaultArgs<ExtArgs>
}, ExtArgs["result"]["channelAttribute"]>

export type ChannelAttributeSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  channelId?: boolean
  name?: boolean
  value?: boolean
  secure?: boolean
  channel?: boolean | Prisma.ChannelDefaultArgs<ExtArgs>
}, ExtArgs["result"]["channelAttribute"]>

export type ChannelAttributeSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  channelId?: boolean
  name?: boolean
  value?: boolean
  secure?: boolean
  channel?: boolean | Prisma.ChannelDefaultArgs<ExtArgs>
}, ExtArgs["result"]["channelAttribute"]>

export type ChannelAttributeSelectScalar = {
  id?: boolean
  channelId?: boolean
  name?: boolean
  value?: boolean
  secure?: boolean
}

export type ChannelAttributeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "channelId" | "name" | "value" | "secure", ExtArgs["result"]["channelAttribute"]>
export type ChannelAttributeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  channel?: boolean | Prisma.ChannelDefaultArgs<ExtArgs>
}
export type ChannelAttributeIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  channel?: boolean | Prisma.ChannelDefaultArgs<ExtArgs>
}
export type ChannelAttributeIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  channel?: boolean | Prisma.ChannelDefaultArgs<ExtArgs>
}

export type $ChannelAttributePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ChannelAttribute"
  objects: {
    channel: Prisma.$ChannelPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    channelId: string
    name: string
    value: string
    secure: boolean
  }, ExtArgs["result"]["channelAttribute"]>
  composites: {}
}

export type ChannelAttributeGetPayload<S extends boolean | null | undefined | ChannelAttributeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload, S>

export type ChannelAttributeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ChannelAttributeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: ChannelAttributeCountAggregateInputType | true
  }

export interface ChannelAttributeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ChannelAttribute'], meta: { name: 'ChannelAttribute' } }
  /**
   * Find zero or one ChannelAttribute that matches the filter.
   * @param {ChannelAttributeFindUniqueArgs} args - Arguments to find a ChannelAttribute
   * @example
   * // Get one ChannelAttribute
   * const channelAttribute = await prisma.channelAttribute.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ChannelAttributeFindUniqueArgs>(args: Prisma.SelectSubset<T, ChannelAttributeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ChannelAttribute that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ChannelAttributeFindUniqueOrThrowArgs} args - Arguments to find a ChannelAttribute
   * @example
   * // Get one ChannelAttribute
   * const channelAttribute = await prisma.channelAttribute.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ChannelAttributeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ChannelAttributeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ChannelAttribute that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeFindFirstArgs} args - Arguments to find a ChannelAttribute
   * @example
   * // Get one ChannelAttribute
   * const channelAttribute = await prisma.channelAttribute.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ChannelAttributeFindFirstArgs>(args?: Prisma.SelectSubset<T, ChannelAttributeFindFirstArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ChannelAttribute that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeFindFirstOrThrowArgs} args - Arguments to find a ChannelAttribute
   * @example
   * // Get one ChannelAttribute
   * const channelAttribute = await prisma.channelAttribute.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ChannelAttributeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ChannelAttributeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ChannelAttributes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ChannelAttributes
   * const channelAttributes = await prisma.channelAttribute.findMany()
   * 
   * // Get first 10 ChannelAttributes
   * const channelAttributes = await prisma.channelAttribute.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const channelAttributeWithIdOnly = await prisma.channelAttribute.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ChannelAttributeFindManyArgs>(args?: Prisma.SelectSubset<T, ChannelAttributeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ChannelAttribute.
   * @param {ChannelAttributeCreateArgs} args - Arguments to create a ChannelAttribute.
   * @example
   * // Create one ChannelAttribute
   * const ChannelAttribute = await prisma.channelAttribute.create({
   *   data: {
   *     // ... data to create a ChannelAttribute
   *   }
   * })
   * 
   */
  create<T extends ChannelAttributeCreateArgs>(args: Prisma.SelectSubset<T, ChannelAttributeCreateArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ChannelAttributes.
   * @param {ChannelAttributeCreateManyArgs} args - Arguments to create many ChannelAttributes.
   * @example
   * // Create many ChannelAttributes
   * const channelAttribute = await prisma.channelAttribute.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ChannelAttributeCreateManyArgs>(args?: Prisma.SelectSubset<T, ChannelAttributeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ChannelAttributes and returns the data saved in the database.
   * @param {ChannelAttributeCreateManyAndReturnArgs} args - Arguments to create many ChannelAttributes.
   * @example
   * // Create many ChannelAttributes
   * const channelAttribute = await prisma.channelAttribute.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ChannelAttributes and only return the `id`
   * const channelAttributeWithIdOnly = await prisma.channelAttribute.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ChannelAttributeCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ChannelAttributeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ChannelAttribute.
   * @param {ChannelAttributeDeleteArgs} args - Arguments to delete one ChannelAttribute.
   * @example
   * // Delete one ChannelAttribute
   * const ChannelAttribute = await prisma.channelAttribute.delete({
   *   where: {
   *     // ... filter to delete one ChannelAttribute
   *   }
   * })
   * 
   */
  delete<T extends ChannelAttributeDeleteArgs>(args: Prisma.SelectSubset<T, ChannelAttributeDeleteArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ChannelAttribute.
   * @param {ChannelAttributeUpdateArgs} args - Arguments to update one ChannelAttribute.
   * @example
   * // Update one ChannelAttribute
   * const channelAttribute = await prisma.channelAttribute.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ChannelAttributeUpdateArgs>(args: Prisma.SelectSubset<T, ChannelAttributeUpdateArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ChannelAttributes.
   * @param {ChannelAttributeDeleteManyArgs} args - Arguments to filter ChannelAttributes to delete.
   * @example
   * // Delete a few ChannelAttributes
   * const { count } = await prisma.channelAttribute.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ChannelAttributeDeleteManyArgs>(args?: Prisma.SelectSubset<T, ChannelAttributeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ChannelAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ChannelAttributes
   * const channelAttribute = await prisma.channelAttribute.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ChannelAttributeUpdateManyArgs>(args: Prisma.SelectSubset<T, ChannelAttributeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ChannelAttributes and returns the data updated in the database.
   * @param {ChannelAttributeUpdateManyAndReturnArgs} args - Arguments to update many ChannelAttributes.
   * @example
   * // Update many ChannelAttributes
   * const channelAttribute = await prisma.channelAttribute.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ChannelAttributes and only return the `id`
   * const channelAttributeWithIdOnly = await prisma.channelAttribute.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ChannelAttributeUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ChannelAttributeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ChannelAttribute.
   * @param {ChannelAttributeUpsertArgs} args - Arguments to update or create a ChannelAttribute.
   * @example
   * // Update or create a ChannelAttribute
   * const channelAttribute = await prisma.channelAttribute.upsert({
   *   create: {
   *     // ... data to create a ChannelAttribute
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ChannelAttribute we want to update
   *   }
   * })
   */
  upsert<T extends ChannelAttributeUpsertArgs>(args: Prisma.SelectSubset<T, ChannelAttributeUpsertArgs<ExtArgs>>): Prisma.Prisma__ChannelAttributeClient<runtime.Types.Result.GetResult<Prisma.$ChannelAttributePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ChannelAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeCountArgs} args - Arguments to filter ChannelAttributes to count.
   * @example
   * // Count the number of ChannelAttributes
   * const count = await prisma.channelAttribute.count({
   *   where: {
   *     // ... the filter for the ChannelAttributes we want to count
   *   }
   * })
  **/
  count<T extends ChannelAttributeCountArgs>(
    args?: Prisma.Subset<T, ChannelAttributeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ChannelAttributeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ChannelAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ChannelAttributeAggregateArgs>(args: Prisma.Subset<T, ChannelAttributeAggregateArgs>): Prisma.PrismaPromise<GetChannelAttributeAggregateType<T>>

  /**
   * Group by ChannelAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelAttributeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ChannelAttributeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ChannelAttributeGroupByArgs['orderBy'] }
      : { orderBy?: ChannelAttributeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ChannelAttributeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetChannelAttributeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ChannelAttribute model
 */
readonly fields: ChannelAttributeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ChannelAttribute.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ChannelAttributeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  channel<T extends Prisma.ChannelDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ChannelDefaultArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ChannelAttribute model
 */
export interface ChannelAttributeFieldRefs {
  readonly id: Prisma.FieldRef<"ChannelAttribute", 'String'>
  readonly channelId: Prisma.FieldRef<"ChannelAttribute", 'String'>
  readonly name: Prisma.FieldRef<"ChannelAttribute", 'String'>
  readonly value: Prisma.FieldRef<"ChannelAttribute", 'String'>
  readonly secure: Prisma.FieldRef<"ChannelAttribute", 'Boolean'>
}
    

// Custom InputTypes
/**
 * ChannelAttribute findUnique
 */
export type ChannelAttributeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ChannelAttribute to fetch.
   */
  where: Prisma.ChannelAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute findUniqueOrThrow
 */
export type ChannelAttributeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ChannelAttribute to fetch.
   */
  where: Prisma.ChannelAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute findFirst
 */
export type ChannelAttributeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ChannelAttribute to fetch.
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelAttributes to fetch.
   */
  orderBy?: Prisma.ChannelAttributeOrderByWithRelationInput | Prisma.ChannelAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ChannelAttributes.
   */
  cursor?: Prisma.ChannelAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ChannelAttributes.
   */
  distinct?: Prisma.ChannelAttributeScalarFieldEnum | Prisma.ChannelAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute findFirstOrThrow
 */
export type ChannelAttributeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ChannelAttribute to fetch.
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelAttributes to fetch.
   */
  orderBy?: Prisma.ChannelAttributeOrderByWithRelationInput | Prisma.ChannelAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ChannelAttributes.
   */
  cursor?: Prisma.ChannelAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ChannelAttributes.
   */
  distinct?: Prisma.ChannelAttributeScalarFieldEnum | Prisma.ChannelAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute findMany
 */
export type ChannelAttributeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ChannelAttributes to fetch.
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelAttributes to fetch.
   */
  orderBy?: Prisma.ChannelAttributeOrderByWithRelationInput | Prisma.ChannelAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ChannelAttributes.
   */
  cursor?: Prisma.ChannelAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelAttributes.
   */
  skip?: number
  distinct?: Prisma.ChannelAttributeScalarFieldEnum | Prisma.ChannelAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute create
 */
export type ChannelAttributeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * The data needed to create a ChannelAttribute.
   */
  data: Prisma.XOR<Prisma.ChannelAttributeCreateInput, Prisma.ChannelAttributeUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute createMany
 */
export type ChannelAttributeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ChannelAttributes.
   */
  data: Prisma.ChannelAttributeCreateManyInput | Prisma.ChannelAttributeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ChannelAttribute createManyAndReturn
 */
export type ChannelAttributeCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * The data used to create many ChannelAttributes.
   */
  data: Prisma.ChannelAttributeCreateManyInput | Prisma.ChannelAttributeCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ChannelAttribute update
 */
export type ChannelAttributeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * The data needed to update a ChannelAttribute.
   */
  data: Prisma.XOR<Prisma.ChannelAttributeUpdateInput, Prisma.ChannelAttributeUncheckedUpdateInput>
  /**
   * Choose, which ChannelAttribute to update.
   */
  where: Prisma.ChannelAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute updateMany
 */
export type ChannelAttributeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ChannelAttributes.
   */
  data: Prisma.XOR<Prisma.ChannelAttributeUpdateManyMutationInput, Prisma.ChannelAttributeUncheckedUpdateManyInput>
  /**
   * Filter which ChannelAttributes to update
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * Limit how many ChannelAttributes to update.
   */
  limit?: number
}

/**
 * ChannelAttribute updateManyAndReturn
 */
export type ChannelAttributeUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * The data used to update ChannelAttributes.
   */
  data: Prisma.XOR<Prisma.ChannelAttributeUpdateManyMutationInput, Prisma.ChannelAttributeUncheckedUpdateManyInput>
  /**
   * Filter which ChannelAttributes to update
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * Limit how many ChannelAttributes to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ChannelAttribute upsert
 */
export type ChannelAttributeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * The filter to search for the ChannelAttribute to update in case it exists.
   */
  where: Prisma.ChannelAttributeWhereUniqueInput
  /**
   * In case the ChannelAttribute found by the `where` argument doesn't exist, create a new ChannelAttribute with this data.
   */
  create: Prisma.XOR<Prisma.ChannelAttributeCreateInput, Prisma.ChannelAttributeUncheckedCreateInput>
  /**
   * In case the ChannelAttribute was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ChannelAttributeUpdateInput, Prisma.ChannelAttributeUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute delete
 */
export type ChannelAttributeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
  /**
   * Filter which ChannelAttribute to delete.
   */
  where: Prisma.ChannelAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelAttribute deleteMany
 */
export type ChannelAttributeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ChannelAttributes to delete
   */
  where?: Prisma.ChannelAttributeWhereInput
  /**
   * Limit how many ChannelAttributes to delete.
   */
  limit?: number
}

/**
 * ChannelAttribute without action
 */
export type ChannelAttributeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelAttribute
   */
  select?: Prisma.ChannelAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelAttribute
   */
  omit?: Prisma.ChannelAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelAttributeInclude<ExtArgs> | null
}
