
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `RecipientAttribute` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model RecipientAttribute
 * 
 */
export type RecipientAttributeModel = runtime.Types.Result.DefaultSelection<Prisma.$RecipientAttributePayload>

export type AggregateRecipientAttribute = {
  _count: RecipientAttributeCountAggregateOutputType | null
  _min: RecipientAttributeMinAggregateOutputType | null
  _max: RecipientAttributeMaxAggregateOutputType | null
}

export type RecipientAttributeMinAggregateOutputType = {
  id: string | null
  recipientId: string | null
  name: string | null
  value: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RecipientAttributeMaxAggregateOutputType = {
  id: string | null
  recipientId: string | null
  name: string | null
  value: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RecipientAttributeCountAggregateOutputType = {
  id: number
  recipientId: number
  name: number
  value: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type RecipientAttributeMinAggregateInputType = {
  id?: true
  recipientId?: true
  name?: true
  value?: true
  createdAt?: true
  updatedAt?: true
}

export type RecipientAttributeMaxAggregateInputType = {
  id?: true
  recipientId?: true
  name?: true
  value?: true
  createdAt?: true
  updatedAt?: true
}

export type RecipientAttributeCountAggregateInputType = {
  id?: true
  recipientId?: true
  name?: true
  value?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type RecipientAttributeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RecipientAttribute to aggregate.
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientAttributes to fetch.
   */
  orderBy?: Prisma.RecipientAttributeOrderByWithRelationInput | Prisma.RecipientAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.RecipientAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned RecipientAttributes
  **/
  _count?: true | RecipientAttributeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RecipientAttributeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RecipientAttributeMaxAggregateInputType
}

export type GetRecipientAttributeAggregateType<T extends RecipientAttributeAggregateArgs> = {
      [P in keyof T & keyof AggregateRecipientAttribute]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRecipientAttribute[P]>
    : Prisma.GetScalarType<T[P], AggregateRecipientAttribute[P]>
}




export type RecipientAttributeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientAttributeWhereInput
  orderBy?: Prisma.RecipientAttributeOrderByWithAggregationInput | Prisma.RecipientAttributeOrderByWithAggregationInput[]
  by: Prisma.RecipientAttributeScalarFieldEnum[] | Prisma.RecipientAttributeScalarFieldEnum
  having?: Prisma.RecipientAttributeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RecipientAttributeCountAggregateInputType | true
  _min?: RecipientAttributeMinAggregateInputType
  _max?: RecipientAttributeMaxAggregateInputType
}

export type RecipientAttributeGroupByOutputType = {
  id: string
  recipientId: string
  name: string
  value: string
  createdAt: Date
  updatedAt: Date
  _count: RecipientAttributeCountAggregateOutputType | null
  _min: RecipientAttributeMinAggregateOutputType | null
  _max: RecipientAttributeMaxAggregateOutputType | null
}

type GetRecipientAttributeGroupByPayload<T extends RecipientAttributeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RecipientAttributeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RecipientAttributeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RecipientAttributeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RecipientAttributeGroupByOutputType[P]>
      }
    >
  > 



export type RecipientAttributeWhereInput = {
  AND?: Prisma.RecipientAttributeWhereInput | Prisma.RecipientAttributeWhereInput[]
  OR?: Prisma.RecipientAttributeWhereInput[]
  NOT?: Prisma.RecipientAttributeWhereInput | Prisma.RecipientAttributeWhereInput[]
  id?: Prisma.StringFilter<"RecipientAttribute"> | string
  recipientId?: Prisma.StringFilter<"RecipientAttribute"> | string
  name?: Prisma.StringFilter<"RecipientAttribute"> | string
  value?: Prisma.StringFilter<"RecipientAttribute"> | string
  createdAt?: Prisma.DateTimeFilter<"RecipientAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"RecipientAttribute"> | Date | string
  recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
}

export type RecipientAttributeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipient?: Prisma.RecipientOrderByWithRelationInput
}

export type RecipientAttributeWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  unique_recipient_attribute_recipient_id_name?: Prisma.RecipientAttributeUnique_recipient_attribute_recipient_id_nameCompoundUniqueInput
  AND?: Prisma.RecipientAttributeWhereInput | Prisma.RecipientAttributeWhereInput[]
  OR?: Prisma.RecipientAttributeWhereInput[]
  NOT?: Prisma.RecipientAttributeWhereInput | Prisma.RecipientAttributeWhereInput[]
  recipientId?: Prisma.StringFilter<"RecipientAttribute"> | string
  name?: Prisma.StringFilter<"RecipientAttribute"> | string
  value?: Prisma.StringFilter<"RecipientAttribute"> | string
  createdAt?: Prisma.DateTimeFilter<"RecipientAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"RecipientAttribute"> | Date | string
  recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
}, "id" | "unique_recipient_attribute_recipient_id_name">

export type RecipientAttributeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.RecipientAttributeCountOrderByAggregateInput
  _max?: Prisma.RecipientAttributeMaxOrderByAggregateInput
  _min?: Prisma.RecipientAttributeMinOrderByAggregateInput
}

export type RecipientAttributeScalarWhereWithAggregatesInput = {
  AND?: Prisma.RecipientAttributeScalarWhereWithAggregatesInput | Prisma.RecipientAttributeScalarWhereWithAggregatesInput[]
  OR?: Prisma.RecipientAttributeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.RecipientAttributeScalarWhereWithAggregatesInput | Prisma.RecipientAttributeScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"RecipientAttribute"> | string
  recipientId?: Prisma.StringWithAggregatesFilter<"RecipientAttribute"> | string
  name?: Prisma.StringWithAggregatesFilter<"RecipientAttribute"> | string
  value?: Prisma.StringWithAggregatesFilter<"RecipientAttribute"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"RecipientAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"RecipientAttribute"> | Date | string
}

export type RecipientAttributeCreateInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  recipient: Prisma.RecipientCreateNestedOneWithoutAttributesInput
}

export type RecipientAttributeUncheckedCreateInput = {
  id?: string
  recipientId: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientAttributeUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.RecipientUpdateOneRequiredWithoutAttributesNestedInput
}

export type RecipientAttributeUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientAttributeCreateManyInput = {
  id?: string
  recipientId: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientAttributeUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientAttributeUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientAttributeUnique_recipient_attribute_recipient_id_nameCompoundUniqueInput = {
  recipientId: string
  name: string
}

export type RecipientAttributeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RecipientAttributeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RecipientAttributeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RecipientAttributeListRelationFilter = {
  every?: Prisma.RecipientAttributeWhereInput
  some?: Prisma.RecipientAttributeWhereInput
  none?: Prisma.RecipientAttributeWhereInput
}

export type RecipientAttributeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type RecipientAttributeCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.RecipientAttributeCreateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput> | Prisma.RecipientAttributeCreateWithoutRecipientInput[] | Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput | Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.RecipientAttributeCreateManyRecipientInputEnvelope
  connect?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
}

export type RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.RecipientAttributeCreateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput> | Prisma.RecipientAttributeCreateWithoutRecipientInput[] | Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput | Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.RecipientAttributeCreateManyRecipientInputEnvelope
  connect?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
}

export type RecipientAttributeUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientAttributeCreateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput> | Prisma.RecipientAttributeCreateWithoutRecipientInput[] | Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput | Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.RecipientAttributeUpsertWithWhereUniqueWithoutRecipientInput | Prisma.RecipientAttributeUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.RecipientAttributeCreateManyRecipientInputEnvelope
  set?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  disconnect?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  delete?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  connect?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  update?: Prisma.RecipientAttributeUpdateWithWhereUniqueWithoutRecipientInput | Prisma.RecipientAttributeUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.RecipientAttributeUpdateManyWithWhereWithoutRecipientInput | Prisma.RecipientAttributeUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.RecipientAttributeScalarWhereInput | Prisma.RecipientAttributeScalarWhereInput[]
}

export type RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientAttributeCreateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput> | Prisma.RecipientAttributeCreateWithoutRecipientInput[] | Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput | Prisma.RecipientAttributeCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.RecipientAttributeUpsertWithWhereUniqueWithoutRecipientInput | Prisma.RecipientAttributeUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.RecipientAttributeCreateManyRecipientInputEnvelope
  set?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  disconnect?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  delete?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  connect?: Prisma.RecipientAttributeWhereUniqueInput | Prisma.RecipientAttributeWhereUniqueInput[]
  update?: Prisma.RecipientAttributeUpdateWithWhereUniqueWithoutRecipientInput | Prisma.RecipientAttributeUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.RecipientAttributeUpdateManyWithWhereWithoutRecipientInput | Prisma.RecipientAttributeUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.RecipientAttributeScalarWhereInput | Prisma.RecipientAttributeScalarWhereInput[]
}

export type RecipientAttributeCreateWithoutRecipientInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientAttributeUncheckedCreateWithoutRecipientInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientAttributeCreateOrConnectWithoutRecipientInput = {
  where: Prisma.RecipientAttributeWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientAttributeCreateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput>
}

export type RecipientAttributeCreateManyRecipientInputEnvelope = {
  data: Prisma.RecipientAttributeCreateManyRecipientInput | Prisma.RecipientAttributeCreateManyRecipientInput[]
  skipDuplicates?: boolean
}

export type RecipientAttributeUpsertWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.RecipientAttributeWhereUniqueInput
  update: Prisma.XOR<Prisma.RecipientAttributeUpdateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedUpdateWithoutRecipientInput>
  create: Prisma.XOR<Prisma.RecipientAttributeCreateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedCreateWithoutRecipientInput>
}

export type RecipientAttributeUpdateWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.RecipientAttributeWhereUniqueInput
  data: Prisma.XOR<Prisma.RecipientAttributeUpdateWithoutRecipientInput, Prisma.RecipientAttributeUncheckedUpdateWithoutRecipientInput>
}

export type RecipientAttributeUpdateManyWithWhereWithoutRecipientInput = {
  where: Prisma.RecipientAttributeScalarWhereInput
  data: Prisma.XOR<Prisma.RecipientAttributeUpdateManyMutationInput, Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientInput>
}

export type RecipientAttributeScalarWhereInput = {
  AND?: Prisma.RecipientAttributeScalarWhereInput | Prisma.RecipientAttributeScalarWhereInput[]
  OR?: Prisma.RecipientAttributeScalarWhereInput[]
  NOT?: Prisma.RecipientAttributeScalarWhereInput | Prisma.RecipientAttributeScalarWhereInput[]
  id?: Prisma.StringFilter<"RecipientAttribute"> | string
  recipientId?: Prisma.StringFilter<"RecipientAttribute"> | string
  name?: Prisma.StringFilter<"RecipientAttribute"> | string
  value?: Prisma.StringFilter<"RecipientAttribute"> | string
  createdAt?: Prisma.DateTimeFilter<"RecipientAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"RecipientAttribute"> | Date | string
}

export type RecipientAttributeCreateManyRecipientInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientAttributeUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientAttributeUncheckedUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientAttributeUncheckedUpdateManyWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type RecipientAttributeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipientAttribute"]>

export type RecipientAttributeSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipientAttribute"]>

export type RecipientAttributeSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipientAttribute"]>

export type RecipientAttributeSelectScalar = {
  id?: boolean
  recipientId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type RecipientAttributeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "recipientId" | "name" | "value" | "createdAt" | "updatedAt", ExtArgs["result"]["recipientAttribute"]>
export type RecipientAttributeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}
export type RecipientAttributeIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}
export type RecipientAttributeIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}

export type $RecipientAttributePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "RecipientAttribute"
  objects: {
    recipient: Prisma.$RecipientPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    recipientId: string
    name: string
    value: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["recipientAttribute"]>
  composites: {}
}

export type RecipientAttributeGetPayload<S extends boolean | null | undefined | RecipientAttributeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload, S>

export type RecipientAttributeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<RecipientAttributeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: RecipientAttributeCountAggregateInputType | true
  }

export interface RecipientAttributeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RecipientAttribute'], meta: { name: 'RecipientAttribute' } }
  /**
   * Find zero or one RecipientAttribute that matches the filter.
   * @param {RecipientAttributeFindUniqueArgs} args - Arguments to find a RecipientAttribute
   * @example
   * // Get one RecipientAttribute
   * const recipientAttribute = await prisma.recipientAttribute.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RecipientAttributeFindUniqueArgs>(args: Prisma.SelectSubset<T, RecipientAttributeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one RecipientAttribute that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RecipientAttributeFindUniqueOrThrowArgs} args - Arguments to find a RecipientAttribute
   * @example
   * // Get one RecipientAttribute
   * const recipientAttribute = await prisma.recipientAttribute.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RecipientAttributeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, RecipientAttributeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RecipientAttribute that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeFindFirstArgs} args - Arguments to find a RecipientAttribute
   * @example
   * // Get one RecipientAttribute
   * const recipientAttribute = await prisma.recipientAttribute.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RecipientAttributeFindFirstArgs>(args?: Prisma.SelectSubset<T, RecipientAttributeFindFirstArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RecipientAttribute that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeFindFirstOrThrowArgs} args - Arguments to find a RecipientAttribute
   * @example
   * // Get one RecipientAttribute
   * const recipientAttribute = await prisma.recipientAttribute.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RecipientAttributeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, RecipientAttributeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more RecipientAttributes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all RecipientAttributes
   * const recipientAttributes = await prisma.recipientAttribute.findMany()
   * 
   * // Get first 10 RecipientAttributes
   * const recipientAttributes = await prisma.recipientAttribute.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const recipientAttributeWithIdOnly = await prisma.recipientAttribute.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends RecipientAttributeFindManyArgs>(args?: Prisma.SelectSubset<T, RecipientAttributeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a RecipientAttribute.
   * @param {RecipientAttributeCreateArgs} args - Arguments to create a RecipientAttribute.
   * @example
   * // Create one RecipientAttribute
   * const RecipientAttribute = await prisma.recipientAttribute.create({
   *   data: {
   *     // ... data to create a RecipientAttribute
   *   }
   * })
   * 
   */
  create<T extends RecipientAttributeCreateArgs>(args: Prisma.SelectSubset<T, RecipientAttributeCreateArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many RecipientAttributes.
   * @param {RecipientAttributeCreateManyArgs} args - Arguments to create many RecipientAttributes.
   * @example
   * // Create many RecipientAttributes
   * const recipientAttribute = await prisma.recipientAttribute.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends RecipientAttributeCreateManyArgs>(args?: Prisma.SelectSubset<T, RecipientAttributeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many RecipientAttributes and returns the data saved in the database.
   * @param {RecipientAttributeCreateManyAndReturnArgs} args - Arguments to create many RecipientAttributes.
   * @example
   * // Create many RecipientAttributes
   * const recipientAttribute = await prisma.recipientAttribute.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many RecipientAttributes and only return the `id`
   * const recipientAttributeWithIdOnly = await prisma.recipientAttribute.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends RecipientAttributeCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, RecipientAttributeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a RecipientAttribute.
   * @param {RecipientAttributeDeleteArgs} args - Arguments to delete one RecipientAttribute.
   * @example
   * // Delete one RecipientAttribute
   * const RecipientAttribute = await prisma.recipientAttribute.delete({
   *   where: {
   *     // ... filter to delete one RecipientAttribute
   *   }
   * })
   * 
   */
  delete<T extends RecipientAttributeDeleteArgs>(args: Prisma.SelectSubset<T, RecipientAttributeDeleteArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one RecipientAttribute.
   * @param {RecipientAttributeUpdateArgs} args - Arguments to update one RecipientAttribute.
   * @example
   * // Update one RecipientAttribute
   * const recipientAttribute = await prisma.recipientAttribute.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends RecipientAttributeUpdateArgs>(args: Prisma.SelectSubset<T, RecipientAttributeUpdateArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more RecipientAttributes.
   * @param {RecipientAttributeDeleteManyArgs} args - Arguments to filter RecipientAttributes to delete.
   * @example
   * // Delete a few RecipientAttributes
   * const { count } = await prisma.recipientAttribute.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends RecipientAttributeDeleteManyArgs>(args?: Prisma.SelectSubset<T, RecipientAttributeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RecipientAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many RecipientAttributes
   * const recipientAttribute = await prisma.recipientAttribute.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends RecipientAttributeUpdateManyArgs>(args: Prisma.SelectSubset<T, RecipientAttributeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RecipientAttributes and returns the data updated in the database.
   * @param {RecipientAttributeUpdateManyAndReturnArgs} args - Arguments to update many RecipientAttributes.
   * @example
   * // Update many RecipientAttributes
   * const recipientAttribute = await prisma.recipientAttribute.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more RecipientAttributes and only return the `id`
   * const recipientAttributeWithIdOnly = await prisma.recipientAttribute.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends RecipientAttributeUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, RecipientAttributeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one RecipientAttribute.
   * @param {RecipientAttributeUpsertArgs} args - Arguments to update or create a RecipientAttribute.
   * @example
   * // Update or create a RecipientAttribute
   * const recipientAttribute = await prisma.recipientAttribute.upsert({
   *   create: {
   *     // ... data to create a RecipientAttribute
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the RecipientAttribute we want to update
   *   }
   * })
   */
  upsert<T extends RecipientAttributeUpsertArgs>(args: Prisma.SelectSubset<T, RecipientAttributeUpsertArgs<ExtArgs>>): Prisma.Prisma__RecipientAttributeClient<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of RecipientAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeCountArgs} args - Arguments to filter RecipientAttributes to count.
   * @example
   * // Count the number of RecipientAttributes
   * const count = await prisma.recipientAttribute.count({
   *   where: {
   *     // ... the filter for the RecipientAttributes we want to count
   *   }
   * })
  **/
  count<T extends RecipientAttributeCountArgs>(
    args?: Prisma.Subset<T, RecipientAttributeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RecipientAttributeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a RecipientAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RecipientAttributeAggregateArgs>(args: Prisma.Subset<T, RecipientAttributeAggregateArgs>): Prisma.PrismaPromise<GetRecipientAttributeAggregateType<T>>

  /**
   * Group by RecipientAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAttributeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends RecipientAttributeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RecipientAttributeGroupByArgs['orderBy'] }
      : { orderBy?: RecipientAttributeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, RecipientAttributeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecipientAttributeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the RecipientAttribute model
 */
readonly fields: RecipientAttributeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for RecipientAttribute.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RecipientAttributeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  recipient<T extends Prisma.RecipientDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientDefaultArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the RecipientAttribute model
 */
export interface RecipientAttributeFieldRefs {
  readonly id: Prisma.FieldRef<"RecipientAttribute", 'String'>
  readonly recipientId: Prisma.FieldRef<"RecipientAttribute", 'String'>
  readonly name: Prisma.FieldRef<"RecipientAttribute", 'String'>
  readonly value: Prisma.FieldRef<"RecipientAttribute", 'String'>
  readonly createdAt: Prisma.FieldRef<"RecipientAttribute", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"RecipientAttribute", 'DateTime'>
}
    

// Custom InputTypes
/**
 * RecipientAttribute findUnique
 */
export type RecipientAttributeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * Filter, which RecipientAttribute to fetch.
   */
  where: Prisma.RecipientAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute findUniqueOrThrow
 */
export type RecipientAttributeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * Filter, which RecipientAttribute to fetch.
   */
  where: Prisma.RecipientAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute findFirst
 */
export type RecipientAttributeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * Filter, which RecipientAttribute to fetch.
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientAttributes to fetch.
   */
  orderBy?: Prisma.RecipientAttributeOrderByWithRelationInput | Prisma.RecipientAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RecipientAttributes.
   */
  cursor?: Prisma.RecipientAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RecipientAttributes.
   */
  distinct?: Prisma.RecipientAttributeScalarFieldEnum | Prisma.RecipientAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute findFirstOrThrow
 */
export type RecipientAttributeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * Filter, which RecipientAttribute to fetch.
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientAttributes to fetch.
   */
  orderBy?: Prisma.RecipientAttributeOrderByWithRelationInput | Prisma.RecipientAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RecipientAttributes.
   */
  cursor?: Prisma.RecipientAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RecipientAttributes.
   */
  distinct?: Prisma.RecipientAttributeScalarFieldEnum | Prisma.RecipientAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute findMany
 */
export type RecipientAttributeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * Filter, which RecipientAttributes to fetch.
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientAttributes to fetch.
   */
  orderBy?: Prisma.RecipientAttributeOrderByWithRelationInput | Prisma.RecipientAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing RecipientAttributes.
   */
  cursor?: Prisma.RecipientAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientAttributes.
   */
  skip?: number
  distinct?: Prisma.RecipientAttributeScalarFieldEnum | Prisma.RecipientAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute create
 */
export type RecipientAttributeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * The data needed to create a RecipientAttribute.
   */
  data: Prisma.XOR<Prisma.RecipientAttributeCreateInput, Prisma.RecipientAttributeUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute createMany
 */
export type RecipientAttributeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many RecipientAttributes.
   */
  data: Prisma.RecipientAttributeCreateManyInput | Prisma.RecipientAttributeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * RecipientAttribute createManyAndReturn
 */
export type RecipientAttributeCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * The data used to create many RecipientAttributes.
   */
  data: Prisma.RecipientAttributeCreateManyInput | Prisma.RecipientAttributeCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * RecipientAttribute update
 */
export type RecipientAttributeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * The data needed to update a RecipientAttribute.
   */
  data: Prisma.XOR<Prisma.RecipientAttributeUpdateInput, Prisma.RecipientAttributeUncheckedUpdateInput>
  /**
   * Choose, which RecipientAttribute to update.
   */
  where: Prisma.RecipientAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute updateMany
 */
export type RecipientAttributeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update RecipientAttributes.
   */
  data: Prisma.XOR<Prisma.RecipientAttributeUpdateManyMutationInput, Prisma.RecipientAttributeUncheckedUpdateManyInput>
  /**
   * Filter which RecipientAttributes to update
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * Limit how many RecipientAttributes to update.
   */
  limit?: number
}

/**
 * RecipientAttribute updateManyAndReturn
 */
export type RecipientAttributeUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * The data used to update RecipientAttributes.
   */
  data: Prisma.XOR<Prisma.RecipientAttributeUpdateManyMutationInput, Prisma.RecipientAttributeUncheckedUpdateManyInput>
  /**
   * Filter which RecipientAttributes to update
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * Limit how many RecipientAttributes to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * RecipientAttribute upsert
 */
export type RecipientAttributeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * The filter to search for the RecipientAttribute to update in case it exists.
   */
  where: Prisma.RecipientAttributeWhereUniqueInput
  /**
   * In case the RecipientAttribute found by the `where` argument doesn't exist, create a new RecipientAttribute with this data.
   */
  create: Prisma.XOR<Prisma.RecipientAttributeCreateInput, Prisma.RecipientAttributeUncheckedCreateInput>
  /**
   * In case the RecipientAttribute was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RecipientAttributeUpdateInput, Prisma.RecipientAttributeUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute delete
 */
export type RecipientAttributeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  /**
   * Filter which RecipientAttribute to delete.
   */
  where: Prisma.RecipientAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientAttribute deleteMany
 */
export type RecipientAttributeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RecipientAttributes to delete
   */
  where?: Prisma.RecipientAttributeWhereInput
  /**
   * Limit how many RecipientAttributes to delete.
   */
  limit?: number
}

/**
 * RecipientAttribute without action
 */
export type RecipientAttributeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
}
