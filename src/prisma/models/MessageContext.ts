
/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `MessageContext` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model MessageContext
 * 
 */
export type MessageContextModel = runtime.Types.Result.DefaultSelection<Prisma.$MessageContextPayload>

export type AggregateMessageContext = {
  _count: MessageContextCountAggregateOutputType | null
  _min: MessageContextMinAggregateOutputType | null
  _max: MessageContextMaxAggregateOutputType | null
}

export type MessageContextMinAggregateOutputType = {
  id: string | null
  messageId: string | null
  name: string | null
  value: string | null
}

export type MessageContextMaxAggregateOutputType = {
  id: string | null
  messageId: string | null
  name: string | null
  value: string | null
}

export type MessageContextCountAggregateOutputType = {
  id: number
  messageId: number
  name: number
  value: number
  _all: number
}


export type MessageContextMinAggregateInputType = {
  id?: true
  messageId?: true
  name?: true
  value?: true
}

export type MessageContextMaxAggregateInputType = {
  id?: true
  messageId?: true
  name?: true
  value?: true
}

export type MessageContextCountAggregateInputType = {
  id?: true
  messageId?: true
  name?: true
  value?: true
  _all?: true
}

export type MessageContextAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which MessageContext to aggregate.
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageContexts to fetch.
   */
  orderBy?: Prisma.MessageContextOrderByWithRelationInput | Prisma.MessageContextOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.MessageContextWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageContexts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageContexts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned MessageContexts
  **/
  _count?: true | MessageContextCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: MessageContextMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: MessageContextMaxAggregateInputType
}

export type GetMessageContextAggregateType<T extends MessageContextAggregateArgs> = {
      [P in keyof T & keyof AggregateMessageContext]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateMessageContext[P]>
    : Prisma.GetScalarType<T[P], AggregateMessageContext[P]>
}




export type MessageContextGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageContextWhereInput
  orderBy?: Prisma.MessageContextOrderByWithAggregationInput | Prisma.MessageContextOrderByWithAggregationInput[]
  by: Prisma.MessageContextScalarFieldEnum[] | Prisma.MessageContextScalarFieldEnum
  having?: Prisma.MessageContextScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: MessageContextCountAggregateInputType | true
  _min?: MessageContextMinAggregateInputType
  _max?: MessageContextMaxAggregateInputType
}

export type MessageContextGroupByOutputType = {
  id: string
  messageId: string
  name: string
  value: string
  _count: MessageContextCountAggregateOutputType | null
  _min: MessageContextMinAggregateOutputType | null
  _max: MessageContextMaxAggregateOutputType | null
}

type GetMessageContextGroupByPayload<T extends MessageContextGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<MessageContextGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof MessageContextGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], MessageContextGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], MessageContextGroupByOutputType[P]>
      }
    >
  > 



export type MessageContextWhereInput = {
  AND?: Prisma.MessageContextWhereInput | Prisma.MessageContextWhereInput[]
  OR?: Prisma.MessageContextWhereInput[]
  NOT?: Prisma.MessageContextWhereInput | Prisma.MessageContextWhereInput[]
  id?: Prisma.StringFilter<"MessageContext"> | string
  messageId?: Prisma.StringFilter<"MessageContext"> | string
  name?: Prisma.StringFilter<"MessageContext"> | string
  value?: Prisma.StringFilter<"MessageContext"> | string
  message?: Prisma.XOR<Prisma.MessageScalarRelationFilter, Prisma.MessageWhereInput>
}

export type MessageContextOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  message?: Prisma.MessageOrderByWithRelationInput
}

export type MessageContextWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.MessageContextWhereInput | Prisma.MessageContextWhereInput[]
  OR?: Prisma.MessageContextWhereInput[]
  NOT?: Prisma.MessageContextWhereInput | Prisma.MessageContextWhereInput[]
  messageId?: Prisma.StringFilter<"MessageContext"> | string
  name?: Prisma.StringFilter<"MessageContext"> | string
  value?: Prisma.StringFilter<"MessageContext"> | string
  message?: Prisma.XOR<Prisma.MessageScalarRelationFilter, Prisma.MessageWhereInput>
}, "id">

export type MessageContextOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  _count?: Prisma.MessageContextCountOrderByAggregateInput
  _max?: Prisma.MessageContextMaxOrderByAggregateInput
  _min?: Prisma.MessageContextMinOrderByAggregateInput
}

export type MessageContextScalarWhereWithAggregatesInput = {
  AND?: Prisma.MessageContextScalarWhereWithAggregatesInput | Prisma.MessageContextScalarWhereWithAggregatesInput[]
  OR?: Prisma.MessageContextScalarWhereWithAggregatesInput[]
  NOT?: Prisma.MessageContextScalarWhereWithAggregatesInput | Prisma.MessageContextScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"MessageContext"> | string
  messageId?: Prisma.StringWithAggregatesFilter<"MessageContext"> | string
  name?: Prisma.StringWithAggregatesFilter<"MessageContext"> | string
  value?: Prisma.StringWithAggregatesFilter<"MessageContext"> | string
}

export type MessageContextCreateInput = {
  id?: string
  name: string
  value?: string
  message: Prisma.MessageCreateNestedOneWithoutContextInput
}

export type MessageContextUncheckedCreateInput = {
  id?: string
  messageId: string
  name: string
  value?: string
}

export type MessageContextUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.MessageUpdateOneRequiredWithoutContextNestedInput
}

export type MessageContextUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageContextCreateManyInput = {
  id?: string
  messageId: string
  name: string
  value?: string
}

export type MessageContextUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageContextUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageContextCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
}

export type MessageContextMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
}

export type MessageContextMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
}

export type MessageContextListRelationFilter = {
  every?: Prisma.MessageContextWhereInput
  some?: Prisma.MessageContextWhereInput
  none?: Prisma.MessageContextWhereInput
}

export type MessageContextOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type MessageContextCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.MessageContextCreateWithoutMessageInput, Prisma.MessageContextUncheckedCreateWithoutMessageInput> | Prisma.MessageContextCreateWithoutMessageInput[] | Prisma.MessageContextUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageContextCreateOrConnectWithoutMessageInput | Prisma.MessageContextCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.MessageContextCreateManyMessageInputEnvelope
  connect?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
}

export type MessageContextUncheckedCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.MessageContextCreateWithoutMessageInput, Prisma.MessageContextUncheckedCreateWithoutMessageInput> | Prisma.MessageContextCreateWithoutMessageInput[] | Prisma.MessageContextUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageContextCreateOrConnectWithoutMessageInput | Prisma.MessageContextCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.MessageContextCreateManyMessageInputEnvelope
  connect?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
}

export type MessageContextUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.MessageContextCreateWithoutMessageInput, Prisma.MessageContextUncheckedCreateWithoutMessageInput> | Prisma.MessageContextCreateWithoutMessageInput[] | Prisma.MessageContextUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageContextCreateOrConnectWithoutMessageInput | Prisma.MessageContextCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.MessageContextUpsertWithWhereUniqueWithoutMessageInput | Prisma.MessageContextUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.MessageContextCreateManyMessageInputEnvelope
  set?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  disconnect?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  delete?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  connect?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  update?: Prisma.MessageContextUpdateWithWhereUniqueWithoutMessageInput | Prisma.MessageContextUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.MessageContextUpdateManyWithWhereWithoutMessageInput | Prisma.MessageContextUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.MessageContextScalarWhereInput | Prisma.MessageContextScalarWhereInput[]
}

export type MessageContextUncheckedUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.MessageContextCreateWithoutMessageInput, Prisma.MessageContextUncheckedCreateWithoutMessageInput> | Prisma.MessageContextCreateWithoutMessageInput[] | Prisma.MessageContextUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageContextCreateOrConnectWithoutMessageInput | Prisma.MessageContextCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.MessageContextUpsertWithWhereUniqueWithoutMessageInput | Prisma.MessageContextUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.MessageContextCreateManyMessageInputEnvelope
  set?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  disconnect?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  delete?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  connect?: Prisma.MessageContextWhereUniqueInput | Prisma.MessageContextWhereUniqueInput[]
  update?: Prisma.MessageContextUpdateWithWhereUniqueWithoutMessageInput | Prisma.MessageContextUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.MessageContextUpdateManyWithWhereWithoutMessageInput | Prisma.MessageContextUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.MessageContextScalarWhereInput | Prisma.MessageContextScalarWhereInput[]
}

export type MessageContextCreateWithoutMessageInput = {
  id?: string
  name: string
  value?: string
}

export type MessageContextUncheckedCreateWithoutMessageInput = {
  id?: string
  name: string
  value?: string
}

export type MessageContextCreateOrConnectWithoutMessageInput = {
  where: Prisma.MessageContextWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageContextCreateWithoutMessageInput, Prisma.MessageContextUncheckedCreateWithoutMessageInput>
}

export type MessageContextCreateManyMessageInputEnvelope = {
  data: Prisma.MessageContextCreateManyMessageInput | Prisma.MessageContextCreateManyMessageInput[]
  skipDuplicates?: boolean
}

export type MessageContextUpsertWithWhereUniqueWithoutMessageInput = {
  where: Prisma.MessageContextWhereUniqueInput
  update: Prisma.XOR<Prisma.MessageContextUpdateWithoutMessageInput, Prisma.MessageContextUncheckedUpdateWithoutMessageInput>
  create: Prisma.XOR<Prisma.MessageContextCreateWithoutMessageInput, Prisma.MessageContextUncheckedCreateWithoutMessageInput>
}

export type MessageContextUpdateWithWhereUniqueWithoutMessageInput = {
  where: Prisma.MessageContextWhereUniqueInput
  data: Prisma.XOR<Prisma.MessageContextUpdateWithoutMessageInput, Prisma.MessageContextUncheckedUpdateWithoutMessageInput>
}

export type MessageContextUpdateManyWithWhereWithoutMessageInput = {
  where: Prisma.MessageContextScalarWhereInput
  data: Prisma.XOR<Prisma.MessageContextUpdateManyMutationInput, Prisma.MessageContextUncheckedUpdateManyWithoutMessageInput>
}

export type MessageContextScalarWhereInput = {
  AND?: Prisma.MessageContextScalarWhereInput | Prisma.MessageContextScalarWhereInput[]
  OR?: Prisma.MessageContextScalarWhereInput[]
  NOT?: Prisma.MessageContextScalarWhereInput | Prisma.MessageContextScalarWhereInput[]
  id?: Prisma.StringFilter<"MessageContext"> | string
  messageId?: Prisma.StringFilter<"MessageContext"> | string
  name?: Prisma.StringFilter<"MessageContext"> | string
  value?: Prisma.StringFilter<"MessageContext"> | string
}

export type MessageContextCreateManyMessageInput = {
  id?: string
  name: string
  value?: string
}

export type MessageContextUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageContextUncheckedUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageContextUncheckedUpdateManyWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}



export type MessageContextSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}, ExtArgs["result"]["messageContext"]>

export type MessageContextSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}, ExtArgs["result"]["messageContext"]>

export type MessageContextSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}, ExtArgs["result"]["messageContext"]>

export type MessageContextSelectScalar = {
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
}

export type MessageContextOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "messageId" | "name" | "value", ExtArgs["result"]["messageContext"]>
export type MessageContextInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}
export type MessageContextIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}
export type MessageContextIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}

export type $MessageContextPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "MessageContext"
  objects: {
    message: Prisma.$MessagePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    messageId: string
    name: string
    value: string
  }, ExtArgs["result"]["messageContext"]>
  composites: {}
}

export type MessageContextGetPayload<S extends boolean | null | undefined | MessageContextDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$MessageContextPayload, S>

export type MessageContextCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<MessageContextFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: MessageContextCountAggregateInputType | true
  }

export interface MessageContextDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['MessageContext'], meta: { name: 'MessageContext' } }
  /**
   * Find zero or one MessageContext that matches the filter.
   * @param {MessageContextFindUniqueArgs} args - Arguments to find a MessageContext
   * @example
   * // Get one MessageContext
   * const messageContext = await prisma.messageContext.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends MessageContextFindUniqueArgs>(args: Prisma.SelectSubset<T, MessageContextFindUniqueArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one MessageContext that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {MessageContextFindUniqueOrThrowArgs} args - Arguments to find a MessageContext
   * @example
   * // Get one MessageContext
   * const messageContext = await prisma.messageContext.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends MessageContextFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, MessageContextFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first MessageContext that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextFindFirstArgs} args - Arguments to find a MessageContext
   * @example
   * // Get one MessageContext
   * const messageContext = await prisma.messageContext.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends MessageContextFindFirstArgs>(args?: Prisma.SelectSubset<T, MessageContextFindFirstArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first MessageContext that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextFindFirstOrThrowArgs} args - Arguments to find a MessageContext
   * @example
   * // Get one MessageContext
   * const messageContext = await prisma.messageContext.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends MessageContextFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, MessageContextFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more MessageContexts that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all MessageContexts
   * const messageContexts = await prisma.messageContext.findMany()
   * 
   * // Get first 10 MessageContexts
   * const messageContexts = await prisma.messageContext.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const messageContextWithIdOnly = await prisma.messageContext.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends MessageContextFindManyArgs>(args?: Prisma.SelectSubset<T, MessageContextFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a MessageContext.
   * @param {MessageContextCreateArgs} args - Arguments to create a MessageContext.
   * @example
   * // Create one MessageContext
   * const MessageContext = await prisma.messageContext.create({
   *   data: {
   *     // ... data to create a MessageContext
   *   }
   * })
   * 
   */
  create<T extends MessageContextCreateArgs>(args: Prisma.SelectSubset<T, MessageContextCreateArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many MessageContexts.
   * @param {MessageContextCreateManyArgs} args - Arguments to create many MessageContexts.
   * @example
   * // Create many MessageContexts
   * const messageContext = await prisma.messageContext.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends MessageContextCreateManyArgs>(args?: Prisma.SelectSubset<T, MessageContextCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many MessageContexts and returns the data saved in the database.
   * @param {MessageContextCreateManyAndReturnArgs} args - Arguments to create many MessageContexts.
   * @example
   * // Create many MessageContexts
   * const messageContext = await prisma.messageContext.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many MessageContexts and only return the `id`
   * const messageContextWithIdOnly = await prisma.messageContext.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends MessageContextCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, MessageContextCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a MessageContext.
   * @param {MessageContextDeleteArgs} args - Arguments to delete one MessageContext.
   * @example
   * // Delete one MessageContext
   * const MessageContext = await prisma.messageContext.delete({
   *   where: {
   *     // ... filter to delete one MessageContext
   *   }
   * })
   * 
   */
  delete<T extends MessageContextDeleteArgs>(args: Prisma.SelectSubset<T, MessageContextDeleteArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one MessageContext.
   * @param {MessageContextUpdateArgs} args - Arguments to update one MessageContext.
   * @example
   * // Update one MessageContext
   * const messageContext = await prisma.messageContext.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends MessageContextUpdateArgs>(args: Prisma.SelectSubset<T, MessageContextUpdateArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more MessageContexts.
   * @param {MessageContextDeleteManyArgs} args - Arguments to filter MessageContexts to delete.
   * @example
   * // Delete a few MessageContexts
   * const { count } = await prisma.messageContext.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends MessageContextDeleteManyArgs>(args?: Prisma.SelectSubset<T, MessageContextDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more MessageContexts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many MessageContexts
   * const messageContext = await prisma.messageContext.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends MessageContextUpdateManyArgs>(args: Prisma.SelectSubset<T, MessageContextUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more MessageContexts and returns the data updated in the database.
   * @param {MessageContextUpdateManyAndReturnArgs} args - Arguments to update many MessageContexts.
   * @example
   * // Update many MessageContexts
   * const messageContext = await prisma.messageContext.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more MessageContexts and only return the `id`
   * const messageContextWithIdOnly = await prisma.messageContext.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends MessageContextUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, MessageContextUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one MessageContext.
   * @param {MessageContextUpsertArgs} args - Arguments to update or create a MessageContext.
   * @example
   * // Update or create a MessageContext
   * const messageContext = await prisma.messageContext.upsert({
   *   create: {
   *     // ... data to create a MessageContext
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the MessageContext we want to update
   *   }
   * })
   */
  upsert<T extends MessageContextUpsertArgs>(args: Prisma.SelectSubset<T, MessageContextUpsertArgs<ExtArgs>>): Prisma.Prisma__MessageContextClient<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of MessageContexts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextCountArgs} args - Arguments to filter MessageContexts to count.
   * @example
   * // Count the number of MessageContexts
   * const count = await prisma.messageContext.count({
   *   where: {
   *     // ... the filter for the MessageContexts we want to count
   *   }
   * })
  **/
  count<T extends MessageContextCountArgs>(
    args?: Prisma.Subset<T, MessageContextCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], MessageContextCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a MessageContext.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends MessageContextAggregateArgs>(args: Prisma.Subset<T, MessageContextAggregateArgs>): Prisma.PrismaPromise<GetMessageContextAggregateType<T>>

  /**
   * Group by MessageContext.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageContextGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends MessageContextGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: MessageContextGroupByArgs['orderBy'] }
      : { orderBy?: MessageContextGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, MessageContextGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMessageContextGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the MessageContext model
 */
readonly fields: MessageContextFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for MessageContext.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__MessageContextClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  message<T extends Prisma.MessageDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.MessageDefaultArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the MessageContext model
 */
export interface MessageContextFieldRefs {
  readonly id: Prisma.FieldRef<"MessageContext", 'String'>
  readonly messageId: Prisma.FieldRef<"MessageContext", 'String'>
  readonly name: Prisma.FieldRef<"MessageContext", 'String'>
  readonly value: Prisma.FieldRef<"MessageContext", 'String'>
}
    

// Custom InputTypes
/**
 * MessageContext findUnique
 */
export type MessageContextFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * Filter, which MessageContext to fetch.
   */
  where: Prisma.MessageContextWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext findUniqueOrThrow
 */
export type MessageContextFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * Filter, which MessageContext to fetch.
   */
  where: Prisma.MessageContextWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext findFirst
 */
export type MessageContextFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * Filter, which MessageContext to fetch.
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageContexts to fetch.
   */
  orderBy?: Prisma.MessageContextOrderByWithRelationInput | Prisma.MessageContextOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for MessageContexts.
   */
  cursor?: Prisma.MessageContextWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageContexts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageContexts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of MessageContexts.
   */
  distinct?: Prisma.MessageContextScalarFieldEnum | Prisma.MessageContextScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext findFirstOrThrow
 */
export type MessageContextFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * Filter, which MessageContext to fetch.
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageContexts to fetch.
   */
  orderBy?: Prisma.MessageContextOrderByWithRelationInput | Prisma.MessageContextOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for MessageContexts.
   */
  cursor?: Prisma.MessageContextWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageContexts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageContexts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of MessageContexts.
   */
  distinct?: Prisma.MessageContextScalarFieldEnum | Prisma.MessageContextScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext findMany
 */
export type MessageContextFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * Filter, which MessageContexts to fetch.
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageContexts to fetch.
   */
  orderBy?: Prisma.MessageContextOrderByWithRelationInput | Prisma.MessageContextOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing MessageContexts.
   */
  cursor?: Prisma.MessageContextWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageContexts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageContexts.
   */
  skip?: number
  distinct?: Prisma.MessageContextScalarFieldEnum | Prisma.MessageContextScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext create
 */
export type MessageContextCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * The data needed to create a MessageContext.
   */
  data: Prisma.XOR<Prisma.MessageContextCreateInput, Prisma.MessageContextUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext createMany
 */
export type MessageContextCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many MessageContexts.
   */
  data: Prisma.MessageContextCreateManyInput | Prisma.MessageContextCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * MessageContext createManyAndReturn
 */
export type MessageContextCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * The data used to create many MessageContexts.
   */
  data: Prisma.MessageContextCreateManyInput | Prisma.MessageContextCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * MessageContext update
 */
export type MessageContextUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * The data needed to update a MessageContext.
   */
  data: Prisma.XOR<Prisma.MessageContextUpdateInput, Prisma.MessageContextUncheckedUpdateInput>
  /**
   * Choose, which MessageContext to update.
   */
  where: Prisma.MessageContextWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext updateMany
 */
export type MessageContextUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update MessageContexts.
   */
  data: Prisma.XOR<Prisma.MessageContextUpdateManyMutationInput, Prisma.MessageContextUncheckedUpdateManyInput>
  /**
   * Filter which MessageContexts to update
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * Limit how many MessageContexts to update.
   */
  limit?: number
}

/**
 * MessageContext updateManyAndReturn
 */
export type MessageContextUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * The data used to update MessageContexts.
   */
  data: Prisma.XOR<Prisma.MessageContextUpdateManyMutationInput, Prisma.MessageContextUncheckedUpdateManyInput>
  /**
   * Filter which MessageContexts to update
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * Limit how many MessageContexts to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * MessageContext upsert
 */
export type MessageContextUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * The filter to search for the MessageContext to update in case it exists.
   */
  where: Prisma.MessageContextWhereUniqueInput
  /**
   * In case the MessageContext found by the `where` argument doesn't exist, create a new MessageContext with this data.
   */
  create: Prisma.XOR<Prisma.MessageContextCreateInput, Prisma.MessageContextUncheckedCreateInput>
  /**
   * In case the MessageContext was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.MessageContextUpdateInput, Prisma.MessageContextUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext delete
 */
export type MessageContextDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  /**
   * Filter which MessageContext to delete.
   */
  where: Prisma.MessageContextWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageContext deleteMany
 */
export type MessageContextDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which MessageContexts to delete
   */
  where?: Prisma.MessageContextWhereInput
  /**
   * Limit how many MessageContexts to delete.
   */
  limit?: number
}

/**
 * MessageContext without action
 */
export type MessageContextDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
}
