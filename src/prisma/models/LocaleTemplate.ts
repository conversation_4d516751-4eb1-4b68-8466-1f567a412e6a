
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `LocaleTemplate` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model LocaleTemplate
 * 
 */
export type LocaleTemplateModel = runtime.Types.Result.DefaultSelection<Prisma.$LocaleTemplatePayload>

export type AggregateLocaleTemplate = {
  _count: LocaleTemplateCountAggregateOutputType | null
  _min: LocaleTemplateMinAggregateOutputType | null
  _max: LocaleTemplateMaxAggregateOutputType | null
}

export type LocaleTemplateMinAggregateOutputType = {
  id: string | null
  locale: string | null
  templateId: string | null
  title: string | null
  fullMessage: string | null
  shortMessage: string | null
  plainText: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type LocaleTemplateMaxAggregateOutputType = {
  id: string | null
  locale: string | null
  templateId: string | null
  title: string | null
  fullMessage: string | null
  shortMessage: string | null
  plainText: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type LocaleTemplateCountAggregateOutputType = {
  id: number
  locale: number
  templateId: number
  title: number
  fullMessage: number
  shortMessage: number
  plainText: number
  variables: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type LocaleTemplateMinAggregateInputType = {
  id?: true
  locale?: true
  templateId?: true
  title?: true
  fullMessage?: true
  shortMessage?: true
  plainText?: true
  createdAt?: true
  updatedAt?: true
}

export type LocaleTemplateMaxAggregateInputType = {
  id?: true
  locale?: true
  templateId?: true
  title?: true
  fullMessage?: true
  shortMessage?: true
  plainText?: true
  createdAt?: true
  updatedAt?: true
}

export type LocaleTemplateCountAggregateInputType = {
  id?: true
  locale?: true
  templateId?: true
  title?: true
  fullMessage?: true
  shortMessage?: true
  plainText?: true
  variables?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type LocaleTemplateAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which LocaleTemplate to aggregate.
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocaleTemplates to fetch.
   */
  orderBy?: Prisma.LocaleTemplateOrderByWithRelationInput | Prisma.LocaleTemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.LocaleTemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocaleTemplates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocaleTemplates.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned LocaleTemplates
  **/
  _count?: true | LocaleTemplateCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: LocaleTemplateMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: LocaleTemplateMaxAggregateInputType
}

export type GetLocaleTemplateAggregateType<T extends LocaleTemplateAggregateArgs> = {
      [P in keyof T & keyof AggregateLocaleTemplate]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLocaleTemplate[P]>
    : Prisma.GetScalarType<T[P], AggregateLocaleTemplate[P]>
}




export type LocaleTemplateGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LocaleTemplateWhereInput
  orderBy?: Prisma.LocaleTemplateOrderByWithAggregationInput | Prisma.LocaleTemplateOrderByWithAggregationInput[]
  by: Prisma.LocaleTemplateScalarFieldEnum[] | Prisma.LocaleTemplateScalarFieldEnum
  having?: Prisma.LocaleTemplateScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LocaleTemplateCountAggregateInputType | true
  _min?: LocaleTemplateMinAggregateInputType
  _max?: LocaleTemplateMaxAggregateInputType
}

export type LocaleTemplateGroupByOutputType = {
  id: string
  locale: string
  templateId: string
  title: string
  fullMessage: string
  shortMessage: string
  plainText: string
  variables: string[]
  createdAt: Date
  updatedAt: Date
  _count: LocaleTemplateCountAggregateOutputType | null
  _min: LocaleTemplateMinAggregateOutputType | null
  _max: LocaleTemplateMaxAggregateOutputType | null
}

type GetLocaleTemplateGroupByPayload<T extends LocaleTemplateGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LocaleTemplateGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof LocaleTemplateGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], LocaleTemplateGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], LocaleTemplateGroupByOutputType[P]>
      }
    >
  > 



export type LocaleTemplateWhereInput = {
  AND?: Prisma.LocaleTemplateWhereInput | Prisma.LocaleTemplateWhereInput[]
  OR?: Prisma.LocaleTemplateWhereInput[]
  NOT?: Prisma.LocaleTemplateWhereInput | Prisma.LocaleTemplateWhereInput[]
  id?: Prisma.StringFilter<"LocaleTemplate"> | string
  locale?: Prisma.StringFilter<"LocaleTemplate"> | string
  templateId?: Prisma.StringFilter<"LocaleTemplate"> | string
  title?: Prisma.StringFilter<"LocaleTemplate"> | string
  fullMessage?: Prisma.StringFilter<"LocaleTemplate"> | string
  shortMessage?: Prisma.StringFilter<"LocaleTemplate"> | string
  plainText?: Prisma.StringFilter<"LocaleTemplate"> | string
  variables?: Prisma.StringNullableListFilter<"LocaleTemplate">
  createdAt?: Prisma.DateTimeFilter<"LocaleTemplate"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"LocaleTemplate"> | Date | string
  template?: Prisma.XOR<Prisma.TemplateScalarRelationFilter, Prisma.TemplateWhereInput>
  actionButtons?: Prisma.ActionListRelationFilter
}

export type LocaleTemplateOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  templateId?: Prisma.SortOrder
  title?: Prisma.SortOrder
  fullMessage?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  variables?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  template?: Prisma.TemplateOrderByWithRelationInput
  actionButtons?: Prisma.ActionOrderByRelationAggregateInput
}

export type LocaleTemplateWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.LocaleTemplateWhereInput | Prisma.LocaleTemplateWhereInput[]
  OR?: Prisma.LocaleTemplateWhereInput[]
  NOT?: Prisma.LocaleTemplateWhereInput | Prisma.LocaleTemplateWhereInput[]
  locale?: Prisma.StringFilter<"LocaleTemplate"> | string
  templateId?: Prisma.StringFilter<"LocaleTemplate"> | string
  title?: Prisma.StringFilter<"LocaleTemplate"> | string
  fullMessage?: Prisma.StringFilter<"LocaleTemplate"> | string
  shortMessage?: Prisma.StringFilter<"LocaleTemplate"> | string
  plainText?: Prisma.StringFilter<"LocaleTemplate"> | string
  variables?: Prisma.StringNullableListFilter<"LocaleTemplate">
  createdAt?: Prisma.DateTimeFilter<"LocaleTemplate"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"LocaleTemplate"> | Date | string
  template?: Prisma.XOR<Prisma.TemplateScalarRelationFilter, Prisma.TemplateWhereInput>
  actionButtons?: Prisma.ActionListRelationFilter
}, "id">

export type LocaleTemplateOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  templateId?: Prisma.SortOrder
  title?: Prisma.SortOrder
  fullMessage?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  variables?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.LocaleTemplateCountOrderByAggregateInput
  _max?: Prisma.LocaleTemplateMaxOrderByAggregateInput
  _min?: Prisma.LocaleTemplateMinOrderByAggregateInput
}

export type LocaleTemplateScalarWhereWithAggregatesInput = {
  AND?: Prisma.LocaleTemplateScalarWhereWithAggregatesInput | Prisma.LocaleTemplateScalarWhereWithAggregatesInput[]
  OR?: Prisma.LocaleTemplateScalarWhereWithAggregatesInput[]
  NOT?: Prisma.LocaleTemplateScalarWhereWithAggregatesInput | Prisma.LocaleTemplateScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  locale?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  templateId?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  title?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  fullMessage?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  shortMessage?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  plainText?: Prisma.StringWithAggregatesFilter<"LocaleTemplate"> | string
  variables?: Prisma.StringNullableListFilter<"LocaleTemplate">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"LocaleTemplate"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"LocaleTemplate"> | Date | string
}

export type LocaleTemplateCreateInput = {
  id?: string
  locale: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  template: Prisma.TemplateCreateNestedOneWithoutLocaleTemplatesInput
  actionButtons?: Prisma.ActionCreateNestedManyWithoutLocaleTemplateInput
}

export type LocaleTemplateUncheckedCreateInput = {
  id?: string
  locale: string
  templateId: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  actionButtons?: Prisma.ActionUncheckedCreateNestedManyWithoutLocaleTemplateInput
}

export type LocaleTemplateUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  template?: Prisma.TemplateUpdateOneRequiredWithoutLocaleTemplatesNestedInput
  actionButtons?: Prisma.ActionUpdateManyWithoutLocaleTemplateNestedInput
}

export type LocaleTemplateUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  templateId?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  actionButtons?: Prisma.ActionUncheckedUpdateManyWithoutLocaleTemplateNestedInput
}

export type LocaleTemplateCreateManyInput = {
  id?: string
  locale: string
  templateId: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type LocaleTemplateUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocaleTemplateUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  templateId?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocaleTemplateNullableScalarRelationFilter = {
  is?: Prisma.LocaleTemplateWhereInput | null
  isNot?: Prisma.LocaleTemplateWhereInput | null
}

export type LocaleTemplateCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  templateId?: Prisma.SortOrder
  title?: Prisma.SortOrder
  fullMessage?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  variables?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type LocaleTemplateMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  templateId?: Prisma.SortOrder
  title?: Prisma.SortOrder
  fullMessage?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type LocaleTemplateMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  templateId?: Prisma.SortOrder
  title?: Prisma.SortOrder
  fullMessage?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type LocaleTemplateListRelationFilter = {
  every?: Prisma.LocaleTemplateWhereInput
  some?: Prisma.LocaleTemplateWhereInput
  none?: Prisma.LocaleTemplateWhereInput
}

export type LocaleTemplateOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type LocaleTemplateCreateNestedOneWithoutActionButtonsInput = {
  create?: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutActionButtonsInput, Prisma.LocaleTemplateUncheckedCreateWithoutActionButtonsInput>
  connectOrCreate?: Prisma.LocaleTemplateCreateOrConnectWithoutActionButtonsInput
  connect?: Prisma.LocaleTemplateWhereUniqueInput
}

export type LocaleTemplateUpdateOneWithoutActionButtonsNestedInput = {
  create?: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutActionButtonsInput, Prisma.LocaleTemplateUncheckedCreateWithoutActionButtonsInput>
  connectOrCreate?: Prisma.LocaleTemplateCreateOrConnectWithoutActionButtonsInput
  upsert?: Prisma.LocaleTemplateUpsertWithoutActionButtonsInput
  disconnect?: Prisma.LocaleTemplateWhereInput | boolean
  delete?: Prisma.LocaleTemplateWhereInput | boolean
  connect?: Prisma.LocaleTemplateWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LocaleTemplateUpdateToOneWithWhereWithoutActionButtonsInput, Prisma.LocaleTemplateUpdateWithoutActionButtonsInput>, Prisma.LocaleTemplateUncheckedUpdateWithoutActionButtonsInput>
}

export type LocaleTemplateCreatevariablesInput = {
  set: string[]
}

export type LocaleTemplateUpdatevariablesInput = {
  set?: string[]
  push?: string | string[]
}

export type LocaleTemplateCreateNestedManyWithoutTemplateInput = {
  create?: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput> | Prisma.LocaleTemplateCreateWithoutTemplateInput[] | Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput[]
  connectOrCreate?: Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput | Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput[]
  createMany?: Prisma.LocaleTemplateCreateManyTemplateInputEnvelope
  connect?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
}

export type LocaleTemplateUncheckedCreateNestedManyWithoutTemplateInput = {
  create?: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput> | Prisma.LocaleTemplateCreateWithoutTemplateInput[] | Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput[]
  connectOrCreate?: Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput | Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput[]
  createMany?: Prisma.LocaleTemplateCreateManyTemplateInputEnvelope
  connect?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
}

export type LocaleTemplateUpdateManyWithoutTemplateNestedInput = {
  create?: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput> | Prisma.LocaleTemplateCreateWithoutTemplateInput[] | Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput[]
  connectOrCreate?: Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput | Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput[]
  upsert?: Prisma.LocaleTemplateUpsertWithWhereUniqueWithoutTemplateInput | Prisma.LocaleTemplateUpsertWithWhereUniqueWithoutTemplateInput[]
  createMany?: Prisma.LocaleTemplateCreateManyTemplateInputEnvelope
  set?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  disconnect?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  delete?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  connect?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  update?: Prisma.LocaleTemplateUpdateWithWhereUniqueWithoutTemplateInput | Prisma.LocaleTemplateUpdateWithWhereUniqueWithoutTemplateInput[]
  updateMany?: Prisma.LocaleTemplateUpdateManyWithWhereWithoutTemplateInput | Prisma.LocaleTemplateUpdateManyWithWhereWithoutTemplateInput[]
  deleteMany?: Prisma.LocaleTemplateScalarWhereInput | Prisma.LocaleTemplateScalarWhereInput[]
}

export type LocaleTemplateUncheckedUpdateManyWithoutTemplateNestedInput = {
  create?: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput> | Prisma.LocaleTemplateCreateWithoutTemplateInput[] | Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput[]
  connectOrCreate?: Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput | Prisma.LocaleTemplateCreateOrConnectWithoutTemplateInput[]
  upsert?: Prisma.LocaleTemplateUpsertWithWhereUniqueWithoutTemplateInput | Prisma.LocaleTemplateUpsertWithWhereUniqueWithoutTemplateInput[]
  createMany?: Prisma.LocaleTemplateCreateManyTemplateInputEnvelope
  set?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  disconnect?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  delete?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  connect?: Prisma.LocaleTemplateWhereUniqueInput | Prisma.LocaleTemplateWhereUniqueInput[]
  update?: Prisma.LocaleTemplateUpdateWithWhereUniqueWithoutTemplateInput | Prisma.LocaleTemplateUpdateWithWhereUniqueWithoutTemplateInput[]
  updateMany?: Prisma.LocaleTemplateUpdateManyWithWhereWithoutTemplateInput | Prisma.LocaleTemplateUpdateManyWithWhereWithoutTemplateInput[]
  deleteMany?: Prisma.LocaleTemplateScalarWhereInput | Prisma.LocaleTemplateScalarWhereInput[]
}

export type LocaleTemplateCreateWithoutActionButtonsInput = {
  id?: string
  locale: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  template: Prisma.TemplateCreateNestedOneWithoutLocaleTemplatesInput
}

export type LocaleTemplateUncheckedCreateWithoutActionButtonsInput = {
  id?: string
  locale: string
  templateId: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type LocaleTemplateCreateOrConnectWithoutActionButtonsInput = {
  where: Prisma.LocaleTemplateWhereUniqueInput
  create: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutActionButtonsInput, Prisma.LocaleTemplateUncheckedCreateWithoutActionButtonsInput>
}

export type LocaleTemplateUpsertWithoutActionButtonsInput = {
  update: Prisma.XOR<Prisma.LocaleTemplateUpdateWithoutActionButtonsInput, Prisma.LocaleTemplateUncheckedUpdateWithoutActionButtonsInput>
  create: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutActionButtonsInput, Prisma.LocaleTemplateUncheckedCreateWithoutActionButtonsInput>
  where?: Prisma.LocaleTemplateWhereInput
}

export type LocaleTemplateUpdateToOneWithWhereWithoutActionButtonsInput = {
  where?: Prisma.LocaleTemplateWhereInput
  data: Prisma.XOR<Prisma.LocaleTemplateUpdateWithoutActionButtonsInput, Prisma.LocaleTemplateUncheckedUpdateWithoutActionButtonsInput>
}

export type LocaleTemplateUpdateWithoutActionButtonsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  template?: Prisma.TemplateUpdateOneRequiredWithoutLocaleTemplatesNestedInput
}

export type LocaleTemplateUncheckedUpdateWithoutActionButtonsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  templateId?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocaleTemplateCreateWithoutTemplateInput = {
  id?: string
  locale: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  actionButtons?: Prisma.ActionCreateNestedManyWithoutLocaleTemplateInput
}

export type LocaleTemplateUncheckedCreateWithoutTemplateInput = {
  id?: string
  locale: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
  actionButtons?: Prisma.ActionUncheckedCreateNestedManyWithoutLocaleTemplateInput
}

export type LocaleTemplateCreateOrConnectWithoutTemplateInput = {
  where: Prisma.LocaleTemplateWhereUniqueInput
  create: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput>
}

export type LocaleTemplateCreateManyTemplateInputEnvelope = {
  data: Prisma.LocaleTemplateCreateManyTemplateInput | Prisma.LocaleTemplateCreateManyTemplateInput[]
  skipDuplicates?: boolean
}

export type LocaleTemplateUpsertWithWhereUniqueWithoutTemplateInput = {
  where: Prisma.LocaleTemplateWhereUniqueInput
  update: Prisma.XOR<Prisma.LocaleTemplateUpdateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedUpdateWithoutTemplateInput>
  create: Prisma.XOR<Prisma.LocaleTemplateCreateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedCreateWithoutTemplateInput>
}

export type LocaleTemplateUpdateWithWhereUniqueWithoutTemplateInput = {
  where: Prisma.LocaleTemplateWhereUniqueInput
  data: Prisma.XOR<Prisma.LocaleTemplateUpdateWithoutTemplateInput, Prisma.LocaleTemplateUncheckedUpdateWithoutTemplateInput>
}

export type LocaleTemplateUpdateManyWithWhereWithoutTemplateInput = {
  where: Prisma.LocaleTemplateScalarWhereInput
  data: Prisma.XOR<Prisma.LocaleTemplateUpdateManyMutationInput, Prisma.LocaleTemplateUncheckedUpdateManyWithoutTemplateInput>
}

export type LocaleTemplateScalarWhereInput = {
  AND?: Prisma.LocaleTemplateScalarWhereInput | Prisma.LocaleTemplateScalarWhereInput[]
  OR?: Prisma.LocaleTemplateScalarWhereInput[]
  NOT?: Prisma.LocaleTemplateScalarWhereInput | Prisma.LocaleTemplateScalarWhereInput[]
  id?: Prisma.StringFilter<"LocaleTemplate"> | string
  locale?: Prisma.StringFilter<"LocaleTemplate"> | string
  templateId?: Prisma.StringFilter<"LocaleTemplate"> | string
  title?: Prisma.StringFilter<"LocaleTemplate"> | string
  fullMessage?: Prisma.StringFilter<"LocaleTemplate"> | string
  shortMessage?: Prisma.StringFilter<"LocaleTemplate"> | string
  plainText?: Prisma.StringFilter<"LocaleTemplate"> | string
  variables?: Prisma.StringNullableListFilter<"LocaleTemplate">
  createdAt?: Prisma.DateTimeFilter<"LocaleTemplate"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"LocaleTemplate"> | Date | string
}

export type LocaleTemplateCreateManyTemplateInput = {
  id?: string
  locale: string
  title?: string
  fullMessage?: string
  shortMessage?: string
  plainText?: string
  variables?: Prisma.LocaleTemplateCreatevariablesInput | string[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type LocaleTemplateUpdateWithoutTemplateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  actionButtons?: Prisma.ActionUpdateManyWithoutLocaleTemplateNestedInput
}

export type LocaleTemplateUncheckedUpdateWithoutTemplateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  actionButtons?: Prisma.ActionUncheckedUpdateManyWithoutLocaleTemplateNestedInput
}

export type LocaleTemplateUncheckedUpdateManyWithoutTemplateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  fullMessage?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  variables?: Prisma.LocaleTemplateUpdatevariablesInput | string[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type LocaleTemplateCountOutputType
 */

export type LocaleTemplateCountOutputType = {
  actionButtons: number
}

export type LocaleTemplateCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  actionButtons?: boolean | LocaleTemplateCountOutputTypeCountActionButtonsArgs
}

/**
 * LocaleTemplateCountOutputType without action
 */
export type LocaleTemplateCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplateCountOutputType
   */
  select?: Prisma.LocaleTemplateCountOutputTypeSelect<ExtArgs> | null
}

/**
 * LocaleTemplateCountOutputType without action
 */
export type LocaleTemplateCountOutputTypeCountActionButtonsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ActionWhereInput
}


export type LocaleTemplateSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  locale?: boolean
  templateId?: boolean
  title?: boolean
  fullMessage?: boolean
  shortMessage?: boolean
  plainText?: boolean
  variables?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  template?: boolean | Prisma.TemplateDefaultArgs<ExtArgs>
  actionButtons?: boolean | Prisma.LocaleTemplate$actionButtonsArgs<ExtArgs>
  _count?: boolean | Prisma.LocaleTemplateCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["localeTemplate"]>

export type LocaleTemplateSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  locale?: boolean
  templateId?: boolean
  title?: boolean
  fullMessage?: boolean
  shortMessage?: boolean
  plainText?: boolean
  variables?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  template?: boolean | Prisma.TemplateDefaultArgs<ExtArgs>
}, ExtArgs["result"]["localeTemplate"]>

export type LocaleTemplateSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  locale?: boolean
  templateId?: boolean
  title?: boolean
  fullMessage?: boolean
  shortMessage?: boolean
  plainText?: boolean
  variables?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  template?: boolean | Prisma.TemplateDefaultArgs<ExtArgs>
}, ExtArgs["result"]["localeTemplate"]>

export type LocaleTemplateSelectScalar = {
  id?: boolean
  locale?: boolean
  templateId?: boolean
  title?: boolean
  fullMessage?: boolean
  shortMessage?: boolean
  plainText?: boolean
  variables?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type LocaleTemplateOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "locale" | "templateId" | "title" | "fullMessage" | "shortMessage" | "plainText" | "variables" | "createdAt" | "updatedAt", ExtArgs["result"]["localeTemplate"]>
export type LocaleTemplateInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  template?: boolean | Prisma.TemplateDefaultArgs<ExtArgs>
  actionButtons?: boolean | Prisma.LocaleTemplate$actionButtonsArgs<ExtArgs>
  _count?: boolean | Prisma.LocaleTemplateCountOutputTypeDefaultArgs<ExtArgs>
}
export type LocaleTemplateIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  template?: boolean | Prisma.TemplateDefaultArgs<ExtArgs>
}
export type LocaleTemplateIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  template?: boolean | Prisma.TemplateDefaultArgs<ExtArgs>
}

export type $LocaleTemplatePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "LocaleTemplate"
  objects: {
    template: Prisma.$TemplatePayload<ExtArgs>
    actionButtons: Prisma.$ActionPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    locale: string
    templateId: string
    title: string
    fullMessage: string
    shortMessage: string
    plainText: string
    variables: string[]
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["localeTemplate"]>
  composites: {}
}

export type LocaleTemplateGetPayload<S extends boolean | null | undefined | LocaleTemplateDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload, S>

export type LocaleTemplateCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<LocaleTemplateFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: LocaleTemplateCountAggregateInputType | true
  }

export interface LocaleTemplateDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['LocaleTemplate'], meta: { name: 'LocaleTemplate' } }
  /**
   * Find zero or one LocaleTemplate that matches the filter.
   * @param {LocaleTemplateFindUniqueArgs} args - Arguments to find a LocaleTemplate
   * @example
   * // Get one LocaleTemplate
   * const localeTemplate = await prisma.localeTemplate.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends LocaleTemplateFindUniqueArgs>(args: Prisma.SelectSubset<T, LocaleTemplateFindUniqueArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one LocaleTemplate that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {LocaleTemplateFindUniqueOrThrowArgs} args - Arguments to find a LocaleTemplate
   * @example
   * // Get one LocaleTemplate
   * const localeTemplate = await prisma.localeTemplate.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends LocaleTemplateFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, LocaleTemplateFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first LocaleTemplate that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateFindFirstArgs} args - Arguments to find a LocaleTemplate
   * @example
   * // Get one LocaleTemplate
   * const localeTemplate = await prisma.localeTemplate.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends LocaleTemplateFindFirstArgs>(args?: Prisma.SelectSubset<T, LocaleTemplateFindFirstArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first LocaleTemplate that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateFindFirstOrThrowArgs} args - Arguments to find a LocaleTemplate
   * @example
   * // Get one LocaleTemplate
   * const localeTemplate = await prisma.localeTemplate.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends LocaleTemplateFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, LocaleTemplateFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more LocaleTemplates that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all LocaleTemplates
   * const localeTemplates = await prisma.localeTemplate.findMany()
   * 
   * // Get first 10 LocaleTemplates
   * const localeTemplates = await prisma.localeTemplate.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const localeTemplateWithIdOnly = await prisma.localeTemplate.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends LocaleTemplateFindManyArgs>(args?: Prisma.SelectSubset<T, LocaleTemplateFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a LocaleTemplate.
   * @param {LocaleTemplateCreateArgs} args - Arguments to create a LocaleTemplate.
   * @example
   * // Create one LocaleTemplate
   * const LocaleTemplate = await prisma.localeTemplate.create({
   *   data: {
   *     // ... data to create a LocaleTemplate
   *   }
   * })
   * 
   */
  create<T extends LocaleTemplateCreateArgs>(args: Prisma.SelectSubset<T, LocaleTemplateCreateArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many LocaleTemplates.
   * @param {LocaleTemplateCreateManyArgs} args - Arguments to create many LocaleTemplates.
   * @example
   * // Create many LocaleTemplates
   * const localeTemplate = await prisma.localeTemplate.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends LocaleTemplateCreateManyArgs>(args?: Prisma.SelectSubset<T, LocaleTemplateCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many LocaleTemplates and returns the data saved in the database.
   * @param {LocaleTemplateCreateManyAndReturnArgs} args - Arguments to create many LocaleTemplates.
   * @example
   * // Create many LocaleTemplates
   * const localeTemplate = await prisma.localeTemplate.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many LocaleTemplates and only return the `id`
   * const localeTemplateWithIdOnly = await prisma.localeTemplate.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends LocaleTemplateCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, LocaleTemplateCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a LocaleTemplate.
   * @param {LocaleTemplateDeleteArgs} args - Arguments to delete one LocaleTemplate.
   * @example
   * // Delete one LocaleTemplate
   * const LocaleTemplate = await prisma.localeTemplate.delete({
   *   where: {
   *     // ... filter to delete one LocaleTemplate
   *   }
   * })
   * 
   */
  delete<T extends LocaleTemplateDeleteArgs>(args: Prisma.SelectSubset<T, LocaleTemplateDeleteArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one LocaleTemplate.
   * @param {LocaleTemplateUpdateArgs} args - Arguments to update one LocaleTemplate.
   * @example
   * // Update one LocaleTemplate
   * const localeTemplate = await prisma.localeTemplate.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends LocaleTemplateUpdateArgs>(args: Prisma.SelectSubset<T, LocaleTemplateUpdateArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more LocaleTemplates.
   * @param {LocaleTemplateDeleteManyArgs} args - Arguments to filter LocaleTemplates to delete.
   * @example
   * // Delete a few LocaleTemplates
   * const { count } = await prisma.localeTemplate.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends LocaleTemplateDeleteManyArgs>(args?: Prisma.SelectSubset<T, LocaleTemplateDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more LocaleTemplates.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many LocaleTemplates
   * const localeTemplate = await prisma.localeTemplate.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends LocaleTemplateUpdateManyArgs>(args: Prisma.SelectSubset<T, LocaleTemplateUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more LocaleTemplates and returns the data updated in the database.
   * @param {LocaleTemplateUpdateManyAndReturnArgs} args - Arguments to update many LocaleTemplates.
   * @example
   * // Update many LocaleTemplates
   * const localeTemplate = await prisma.localeTemplate.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more LocaleTemplates and only return the `id`
   * const localeTemplateWithIdOnly = await prisma.localeTemplate.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends LocaleTemplateUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, LocaleTemplateUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one LocaleTemplate.
   * @param {LocaleTemplateUpsertArgs} args - Arguments to update or create a LocaleTemplate.
   * @example
   * // Update or create a LocaleTemplate
   * const localeTemplate = await prisma.localeTemplate.upsert({
   *   create: {
   *     // ... data to create a LocaleTemplate
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the LocaleTemplate we want to update
   *   }
   * })
   */
  upsert<T extends LocaleTemplateUpsertArgs>(args: Prisma.SelectSubset<T, LocaleTemplateUpsertArgs<ExtArgs>>): Prisma.Prisma__LocaleTemplateClient<runtime.Types.Result.GetResult<Prisma.$LocaleTemplatePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of LocaleTemplates.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateCountArgs} args - Arguments to filter LocaleTemplates to count.
   * @example
   * // Count the number of LocaleTemplates
   * const count = await prisma.localeTemplate.count({
   *   where: {
   *     // ... the filter for the LocaleTemplates we want to count
   *   }
   * })
  **/
  count<T extends LocaleTemplateCountArgs>(
    args?: Prisma.Subset<T, LocaleTemplateCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LocaleTemplateCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a LocaleTemplate.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends LocaleTemplateAggregateArgs>(args: Prisma.Subset<T, LocaleTemplateAggregateArgs>): Prisma.PrismaPromise<GetLocaleTemplateAggregateType<T>>

  /**
   * Group by LocaleTemplate.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocaleTemplateGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends LocaleTemplateGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: LocaleTemplateGroupByArgs['orderBy'] }
      : { orderBy?: LocaleTemplateGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, LocaleTemplateGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLocaleTemplateGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the LocaleTemplate model
 */
readonly fields: LocaleTemplateFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for LocaleTemplate.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__LocaleTemplateClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  template<T extends Prisma.TemplateDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TemplateDefaultArgs<ExtArgs>>): Prisma.Prisma__TemplateClient<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  actionButtons<T extends Prisma.LocaleTemplate$actionButtonsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LocaleTemplate$actionButtonsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ActionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the LocaleTemplate model
 */
export interface LocaleTemplateFieldRefs {
  readonly id: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly locale: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly templateId: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly title: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly fullMessage: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly shortMessage: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly plainText: Prisma.FieldRef<"LocaleTemplate", 'String'>
  readonly variables: Prisma.FieldRef<"LocaleTemplate", 'String[]'>
  readonly createdAt: Prisma.FieldRef<"LocaleTemplate", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"LocaleTemplate", 'DateTime'>
}
    

// Custom InputTypes
/**
 * LocaleTemplate findUnique
 */
export type LocaleTemplateFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * Filter, which LocaleTemplate to fetch.
   */
  where: Prisma.LocaleTemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate findUniqueOrThrow
 */
export type LocaleTemplateFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * Filter, which LocaleTemplate to fetch.
   */
  where: Prisma.LocaleTemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate findFirst
 */
export type LocaleTemplateFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * Filter, which LocaleTemplate to fetch.
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocaleTemplates to fetch.
   */
  orderBy?: Prisma.LocaleTemplateOrderByWithRelationInput | Prisma.LocaleTemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for LocaleTemplates.
   */
  cursor?: Prisma.LocaleTemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocaleTemplates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocaleTemplates.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of LocaleTemplates.
   */
  distinct?: Prisma.LocaleTemplateScalarFieldEnum | Prisma.LocaleTemplateScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate findFirstOrThrow
 */
export type LocaleTemplateFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * Filter, which LocaleTemplate to fetch.
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocaleTemplates to fetch.
   */
  orderBy?: Prisma.LocaleTemplateOrderByWithRelationInput | Prisma.LocaleTemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for LocaleTemplates.
   */
  cursor?: Prisma.LocaleTemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocaleTemplates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocaleTemplates.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of LocaleTemplates.
   */
  distinct?: Prisma.LocaleTemplateScalarFieldEnum | Prisma.LocaleTemplateScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate findMany
 */
export type LocaleTemplateFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * Filter, which LocaleTemplates to fetch.
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocaleTemplates to fetch.
   */
  orderBy?: Prisma.LocaleTemplateOrderByWithRelationInput | Prisma.LocaleTemplateOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing LocaleTemplates.
   */
  cursor?: Prisma.LocaleTemplateWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocaleTemplates from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocaleTemplates.
   */
  skip?: number
  distinct?: Prisma.LocaleTemplateScalarFieldEnum | Prisma.LocaleTemplateScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate create
 */
export type LocaleTemplateCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * The data needed to create a LocaleTemplate.
   */
  data: Prisma.XOR<Prisma.LocaleTemplateCreateInput, Prisma.LocaleTemplateUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate createMany
 */
export type LocaleTemplateCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many LocaleTemplates.
   */
  data: Prisma.LocaleTemplateCreateManyInput | Prisma.LocaleTemplateCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * LocaleTemplate createManyAndReturn
 */
export type LocaleTemplateCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * The data used to create many LocaleTemplates.
   */
  data: Prisma.LocaleTemplateCreateManyInput | Prisma.LocaleTemplateCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * LocaleTemplate update
 */
export type LocaleTemplateUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * The data needed to update a LocaleTemplate.
   */
  data: Prisma.XOR<Prisma.LocaleTemplateUpdateInput, Prisma.LocaleTemplateUncheckedUpdateInput>
  /**
   * Choose, which LocaleTemplate to update.
   */
  where: Prisma.LocaleTemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate updateMany
 */
export type LocaleTemplateUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update LocaleTemplates.
   */
  data: Prisma.XOR<Prisma.LocaleTemplateUpdateManyMutationInput, Prisma.LocaleTemplateUncheckedUpdateManyInput>
  /**
   * Filter which LocaleTemplates to update
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * Limit how many LocaleTemplates to update.
   */
  limit?: number
}

/**
 * LocaleTemplate updateManyAndReturn
 */
export type LocaleTemplateUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * The data used to update LocaleTemplates.
   */
  data: Prisma.XOR<Prisma.LocaleTemplateUpdateManyMutationInput, Prisma.LocaleTemplateUncheckedUpdateManyInput>
  /**
   * Filter which LocaleTemplates to update
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * Limit how many LocaleTemplates to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * LocaleTemplate upsert
 */
export type LocaleTemplateUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * The filter to search for the LocaleTemplate to update in case it exists.
   */
  where: Prisma.LocaleTemplateWhereUniqueInput
  /**
   * In case the LocaleTemplate found by the `where` argument doesn't exist, create a new LocaleTemplate with this data.
   */
  create: Prisma.XOR<Prisma.LocaleTemplateCreateInput, Prisma.LocaleTemplateUncheckedCreateInput>
  /**
   * In case the LocaleTemplate was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.LocaleTemplateUpdateInput, Prisma.LocaleTemplateUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate delete
 */
export type LocaleTemplateDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
  /**
   * Filter which LocaleTemplate to delete.
   */
  where: Prisma.LocaleTemplateWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * LocaleTemplate deleteMany
 */
export type LocaleTemplateDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which LocaleTemplates to delete
   */
  where?: Prisma.LocaleTemplateWhereInput
  /**
   * Limit how many LocaleTemplates to delete.
   */
  limit?: number
}

/**
 * LocaleTemplate.actionButtons
 */
export type LocaleTemplate$actionButtonsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Action
   */
  select?: Prisma.ActionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Action
   */
  omit?: Prisma.ActionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActionInclude<ExtArgs> | null
  where?: Prisma.ActionWhereInput
  orderBy?: Prisma.ActionOrderByWithRelationInput | Prisma.ActionOrderByWithRelationInput[]
  cursor?: Prisma.ActionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ActionScalarFieldEnum | Prisma.ActionScalarFieldEnum[]
}

/**
 * LocaleTemplate without action
 */
export type LocaleTemplateDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocaleTemplate
   */
  select?: Prisma.LocaleTemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocaleTemplate
   */
  omit?: Prisma.LocaleTemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocaleTemplateInclude<ExtArgs> | null
}
