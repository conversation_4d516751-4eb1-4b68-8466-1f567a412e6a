
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TrackedLink` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TrackedLink
 * 
 */
export type TrackedLinkModel = runtime.Types.Result.DefaultSelection<Prisma.$TrackedLinkPayload>

export type AggregateTrackedLink = {
  _count: TrackedLinkCountAggregateOutputType | null
  _avg: TrackedLinkAvgAggregateOutputType | null
  _sum: TrackedLinkSumAggregateOutputType | null
  _min: TrackedLinkMinAggregateOutputType | null
  _max: TrackedLinkMaxAggregateOutputType | null
}

export type TrackedLinkAvgAggregateOutputType = {
  clickCount: number | null
}

export type TrackedLinkSumAggregateOutputType = {
  clickCount: number | null
}

export type TrackedLinkMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  hash: string | null
  url: string | null
  clickCount: number | null
  firstClick: Date | null
  lastClick: Date | null
  messageId: string | null
  createdAt: Date | null
}

export type TrackedLinkMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  hash: string | null
  url: string | null
  clickCount: number | null
  firstClick: Date | null
  lastClick: Date | null
  messageId: string | null
  createdAt: Date | null
}

export type TrackedLinkCountAggregateOutputType = {
  id: number
  tenantId: number
  hash: number
  url: number
  clickCount: number
  firstClick: number
  lastClick: number
  messageId: number
  createdAt: number
  _all: number
}


export type TrackedLinkAvgAggregateInputType = {
  clickCount?: true
}

export type TrackedLinkSumAggregateInputType = {
  clickCount?: true
}

export type TrackedLinkMinAggregateInputType = {
  id?: true
  tenantId?: true
  hash?: true
  url?: true
  clickCount?: true
  firstClick?: true
  lastClick?: true
  messageId?: true
  createdAt?: true
}

export type TrackedLinkMaxAggregateInputType = {
  id?: true
  tenantId?: true
  hash?: true
  url?: true
  clickCount?: true
  firstClick?: true
  lastClick?: true
  messageId?: true
  createdAt?: true
}

export type TrackedLinkCountAggregateInputType = {
  id?: true
  tenantId?: true
  hash?: true
  url?: true
  clickCount?: true
  firstClick?: true
  lastClick?: true
  messageId?: true
  createdAt?: true
  _all?: true
}

export type TrackedLinkAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackedLink to aggregate.
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackedLinks to fetch.
   */
  orderBy?: Prisma.TrackedLinkOrderByWithRelationInput | Prisma.TrackedLinkOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TrackedLinkWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackedLinks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackedLinks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TrackedLinks
  **/
  _count?: true | TrackedLinkCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TrackedLinkAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TrackedLinkSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TrackedLinkMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TrackedLinkMaxAggregateInputType
}

export type GetTrackedLinkAggregateType<T extends TrackedLinkAggregateArgs> = {
      [P in keyof T & keyof AggregateTrackedLink]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTrackedLink[P]>
    : Prisma.GetScalarType<T[P], AggregateTrackedLink[P]>
}




export type TrackedLinkGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackedLinkWhereInput
  orderBy?: Prisma.TrackedLinkOrderByWithAggregationInput | Prisma.TrackedLinkOrderByWithAggregationInput[]
  by: Prisma.TrackedLinkScalarFieldEnum[] | Prisma.TrackedLinkScalarFieldEnum
  having?: Prisma.TrackedLinkScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TrackedLinkCountAggregateInputType | true
  _avg?: TrackedLinkAvgAggregateInputType
  _sum?: TrackedLinkSumAggregateInputType
  _min?: TrackedLinkMinAggregateInputType
  _max?: TrackedLinkMaxAggregateInputType
}

export type TrackedLinkGroupByOutputType = {
  id: string
  tenantId: string
  hash: string
  url: string
  clickCount: number
  firstClick: Date | null
  lastClick: Date | null
  messageId: string | null
  createdAt: Date
  _count: TrackedLinkCountAggregateOutputType | null
  _avg: TrackedLinkAvgAggregateOutputType | null
  _sum: TrackedLinkSumAggregateOutputType | null
  _min: TrackedLinkMinAggregateOutputType | null
  _max: TrackedLinkMaxAggregateOutputType | null
}

type GetTrackedLinkGroupByPayload<T extends TrackedLinkGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TrackedLinkGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TrackedLinkGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TrackedLinkGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TrackedLinkGroupByOutputType[P]>
      }
    >
  > 



export type TrackedLinkWhereInput = {
  AND?: Prisma.TrackedLinkWhereInput | Prisma.TrackedLinkWhereInput[]
  OR?: Prisma.TrackedLinkWhereInput[]
  NOT?: Prisma.TrackedLinkWhereInput | Prisma.TrackedLinkWhereInput[]
  id?: Prisma.StringFilter<"TrackedLink"> | string
  tenantId?: Prisma.StringFilter<"TrackedLink"> | string
  hash?: Prisma.StringFilter<"TrackedLink"> | string
  url?: Prisma.StringFilter<"TrackedLink"> | string
  clickCount?: Prisma.IntFilter<"TrackedLink"> | number
  firstClick?: Prisma.DateTimeNullableFilter<"TrackedLink"> | Date | string | null
  lastClick?: Prisma.DateTimeNullableFilter<"TrackedLink"> | Date | string | null
  messageId?: Prisma.StringNullableFilter<"TrackedLink"> | string | null
  createdAt?: Prisma.DateTimeFilter<"TrackedLink"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  message?: Prisma.XOR<Prisma.MessageNullableScalarRelationFilter, Prisma.MessageWhereInput> | null
}

export type TrackedLinkOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  hash?: Prisma.SortOrder
  url?: Prisma.SortOrder
  clickCount?: Prisma.SortOrder
  firstClick?: Prisma.SortOrderInput | Prisma.SortOrder
  lastClick?: Prisma.SortOrderInput | Prisma.SortOrder
  messageId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  message?: Prisma.MessageOrderByWithRelationInput
}

export type TrackedLinkWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  hash?: string
  AND?: Prisma.TrackedLinkWhereInput | Prisma.TrackedLinkWhereInput[]
  OR?: Prisma.TrackedLinkWhereInput[]
  NOT?: Prisma.TrackedLinkWhereInput | Prisma.TrackedLinkWhereInput[]
  tenantId?: Prisma.StringFilter<"TrackedLink"> | string
  url?: Prisma.StringFilter<"TrackedLink"> | string
  clickCount?: Prisma.IntFilter<"TrackedLink"> | number
  firstClick?: Prisma.DateTimeNullableFilter<"TrackedLink"> | Date | string | null
  lastClick?: Prisma.DateTimeNullableFilter<"TrackedLink"> | Date | string | null
  messageId?: Prisma.StringNullableFilter<"TrackedLink"> | string | null
  createdAt?: Prisma.DateTimeFilter<"TrackedLink"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  message?: Prisma.XOR<Prisma.MessageNullableScalarRelationFilter, Prisma.MessageWhereInput> | null
}, "id" | "uq_tracked_link_hash">

export type TrackedLinkOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  hash?: Prisma.SortOrder
  url?: Prisma.SortOrder
  clickCount?: Prisma.SortOrder
  firstClick?: Prisma.SortOrderInput | Prisma.SortOrder
  lastClick?: Prisma.SortOrderInput | Prisma.SortOrder
  messageId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.TrackedLinkCountOrderByAggregateInput
  _avg?: Prisma.TrackedLinkAvgOrderByAggregateInput
  _max?: Prisma.TrackedLinkMaxOrderByAggregateInput
  _min?: Prisma.TrackedLinkMinOrderByAggregateInput
  _sum?: Prisma.TrackedLinkSumOrderByAggregateInput
}

export type TrackedLinkScalarWhereWithAggregatesInput = {
  AND?: Prisma.TrackedLinkScalarWhereWithAggregatesInput | Prisma.TrackedLinkScalarWhereWithAggregatesInput[]
  OR?: Prisma.TrackedLinkScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TrackedLinkScalarWhereWithAggregatesInput | Prisma.TrackedLinkScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"TrackedLink"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"TrackedLink"> | string
  hash?: Prisma.StringWithAggregatesFilter<"TrackedLink"> | string
  url?: Prisma.StringWithAggregatesFilter<"TrackedLink"> | string
  clickCount?: Prisma.IntWithAggregatesFilter<"TrackedLink"> | number
  firstClick?: Prisma.DateTimeNullableWithAggregatesFilter<"TrackedLink"> | Date | string | null
  lastClick?: Prisma.DateTimeNullableWithAggregatesFilter<"TrackedLink"> | Date | string | null
  messageId?: Prisma.StringNullableWithAggregatesFilter<"TrackedLink"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"TrackedLink"> | Date | string
}

export type TrackedLinkCreateInput = {
  id?: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  createdAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTrackedLinksInput
  message?: Prisma.MessageCreateNestedOneWithoutTrackedLinksInput
}

export type TrackedLinkUncheckedCreateInput = {
  id?: string
  tenantId: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  messageId?: string | null
  createdAt?: Date | string
}

export type TrackedLinkUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTrackedLinksNestedInput
  message?: Prisma.MessageUpdateOneWithoutTrackedLinksNestedInput
}

export type TrackedLinkUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackedLinkCreateManyInput = {
  id?: string
  tenantId: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  messageId?: string | null
  createdAt?: Date | string
}

export type TrackedLinkUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackedLinkUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackedLinkListRelationFilter = {
  every?: Prisma.TrackedLinkWhereInput
  some?: Prisma.TrackedLinkWhereInput
  none?: Prisma.TrackedLinkWhereInput
}

export type TrackedLinkOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TrackedLinkCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  hash?: Prisma.SortOrder
  url?: Prisma.SortOrder
  clickCount?: Prisma.SortOrder
  firstClick?: Prisma.SortOrder
  lastClick?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type TrackedLinkAvgOrderByAggregateInput = {
  clickCount?: Prisma.SortOrder
}

export type TrackedLinkMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  hash?: Prisma.SortOrder
  url?: Prisma.SortOrder
  clickCount?: Prisma.SortOrder
  firstClick?: Prisma.SortOrder
  lastClick?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type TrackedLinkMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  hash?: Prisma.SortOrder
  url?: Prisma.SortOrder
  clickCount?: Prisma.SortOrder
  firstClick?: Prisma.SortOrder
  lastClick?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type TrackedLinkSumOrderByAggregateInput = {
  clickCount?: Prisma.SortOrder
}

export type TrackedLinkCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutMessageInput, Prisma.TrackedLinkUncheckedCreateWithoutMessageInput> | Prisma.TrackedLinkCreateWithoutMessageInput[] | Prisma.TrackedLinkUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutMessageInput | Prisma.TrackedLinkCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.TrackedLinkCreateManyMessageInputEnvelope
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
}

export type TrackedLinkUncheckedCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutMessageInput, Prisma.TrackedLinkUncheckedCreateWithoutMessageInput> | Prisma.TrackedLinkCreateWithoutMessageInput[] | Prisma.TrackedLinkUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutMessageInput | Prisma.TrackedLinkCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.TrackedLinkCreateManyMessageInputEnvelope
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
}

export type TrackedLinkUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutMessageInput, Prisma.TrackedLinkUncheckedCreateWithoutMessageInput> | Prisma.TrackedLinkCreateWithoutMessageInput[] | Prisma.TrackedLinkUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutMessageInput | Prisma.TrackedLinkCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.TrackedLinkUpsertWithWhereUniqueWithoutMessageInput | Prisma.TrackedLinkUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.TrackedLinkCreateManyMessageInputEnvelope
  set?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  disconnect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  delete?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  update?: Prisma.TrackedLinkUpdateWithWhereUniqueWithoutMessageInput | Prisma.TrackedLinkUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.TrackedLinkUpdateManyWithWhereWithoutMessageInput | Prisma.TrackedLinkUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.TrackedLinkScalarWhereInput | Prisma.TrackedLinkScalarWhereInput[]
}

export type TrackedLinkUncheckedUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutMessageInput, Prisma.TrackedLinkUncheckedCreateWithoutMessageInput> | Prisma.TrackedLinkCreateWithoutMessageInput[] | Prisma.TrackedLinkUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutMessageInput | Prisma.TrackedLinkCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.TrackedLinkUpsertWithWhereUniqueWithoutMessageInput | Prisma.TrackedLinkUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.TrackedLinkCreateManyMessageInputEnvelope
  set?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  disconnect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  delete?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  update?: Prisma.TrackedLinkUpdateWithWhereUniqueWithoutMessageInput | Prisma.TrackedLinkUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.TrackedLinkUpdateManyWithWhereWithoutMessageInput | Prisma.TrackedLinkUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.TrackedLinkScalarWhereInput | Prisma.TrackedLinkScalarWhereInput[]
}

export type TrackedLinkCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutTenantInput, Prisma.TrackedLinkUncheckedCreateWithoutTenantInput> | Prisma.TrackedLinkCreateWithoutTenantInput[] | Prisma.TrackedLinkUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutTenantInput | Prisma.TrackedLinkCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TrackedLinkCreateManyTenantInputEnvelope
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
}

export type TrackedLinkUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutTenantInput, Prisma.TrackedLinkUncheckedCreateWithoutTenantInput> | Prisma.TrackedLinkCreateWithoutTenantInput[] | Prisma.TrackedLinkUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutTenantInput | Prisma.TrackedLinkCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TrackedLinkCreateManyTenantInputEnvelope
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
}

export type TrackedLinkUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutTenantInput, Prisma.TrackedLinkUncheckedCreateWithoutTenantInput> | Prisma.TrackedLinkCreateWithoutTenantInput[] | Prisma.TrackedLinkUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutTenantInput | Prisma.TrackedLinkCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TrackedLinkUpsertWithWhereUniqueWithoutTenantInput | Prisma.TrackedLinkUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TrackedLinkCreateManyTenantInputEnvelope
  set?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  disconnect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  delete?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  update?: Prisma.TrackedLinkUpdateWithWhereUniqueWithoutTenantInput | Prisma.TrackedLinkUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TrackedLinkUpdateManyWithWhereWithoutTenantInput | Prisma.TrackedLinkUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TrackedLinkScalarWhereInput | Prisma.TrackedLinkScalarWhereInput[]
}

export type TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TrackedLinkCreateWithoutTenantInput, Prisma.TrackedLinkUncheckedCreateWithoutTenantInput> | Prisma.TrackedLinkCreateWithoutTenantInput[] | Prisma.TrackedLinkUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TrackedLinkCreateOrConnectWithoutTenantInput | Prisma.TrackedLinkCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TrackedLinkUpsertWithWhereUniqueWithoutTenantInput | Prisma.TrackedLinkUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TrackedLinkCreateManyTenantInputEnvelope
  set?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  disconnect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  delete?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  connect?: Prisma.TrackedLinkWhereUniqueInput | Prisma.TrackedLinkWhereUniqueInput[]
  update?: Prisma.TrackedLinkUpdateWithWhereUniqueWithoutTenantInput | Prisma.TrackedLinkUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TrackedLinkUpdateManyWithWhereWithoutTenantInput | Prisma.TrackedLinkUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TrackedLinkScalarWhereInput | Prisma.TrackedLinkScalarWhereInput[]
}

export type TrackedLinkCreateWithoutMessageInput = {
  id?: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  createdAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTrackedLinksInput
}

export type TrackedLinkUncheckedCreateWithoutMessageInput = {
  id?: string
  tenantId: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  createdAt?: Date | string
}

export type TrackedLinkCreateOrConnectWithoutMessageInput = {
  where: Prisma.TrackedLinkWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackedLinkCreateWithoutMessageInput, Prisma.TrackedLinkUncheckedCreateWithoutMessageInput>
}

export type TrackedLinkCreateManyMessageInputEnvelope = {
  data: Prisma.TrackedLinkCreateManyMessageInput | Prisma.TrackedLinkCreateManyMessageInput[]
  skipDuplicates?: boolean
}

export type TrackedLinkUpsertWithWhereUniqueWithoutMessageInput = {
  where: Prisma.TrackedLinkWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackedLinkUpdateWithoutMessageInput, Prisma.TrackedLinkUncheckedUpdateWithoutMessageInput>
  create: Prisma.XOR<Prisma.TrackedLinkCreateWithoutMessageInput, Prisma.TrackedLinkUncheckedCreateWithoutMessageInput>
}

export type TrackedLinkUpdateWithWhereUniqueWithoutMessageInput = {
  where: Prisma.TrackedLinkWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackedLinkUpdateWithoutMessageInput, Prisma.TrackedLinkUncheckedUpdateWithoutMessageInput>
}

export type TrackedLinkUpdateManyWithWhereWithoutMessageInput = {
  where: Prisma.TrackedLinkScalarWhereInput
  data: Prisma.XOR<Prisma.TrackedLinkUpdateManyMutationInput, Prisma.TrackedLinkUncheckedUpdateManyWithoutMessageInput>
}

export type TrackedLinkScalarWhereInput = {
  AND?: Prisma.TrackedLinkScalarWhereInput | Prisma.TrackedLinkScalarWhereInput[]
  OR?: Prisma.TrackedLinkScalarWhereInput[]
  NOT?: Prisma.TrackedLinkScalarWhereInput | Prisma.TrackedLinkScalarWhereInput[]
  id?: Prisma.StringFilter<"TrackedLink"> | string
  tenantId?: Prisma.StringFilter<"TrackedLink"> | string
  hash?: Prisma.StringFilter<"TrackedLink"> | string
  url?: Prisma.StringFilter<"TrackedLink"> | string
  clickCount?: Prisma.IntFilter<"TrackedLink"> | number
  firstClick?: Prisma.DateTimeNullableFilter<"TrackedLink"> | Date | string | null
  lastClick?: Prisma.DateTimeNullableFilter<"TrackedLink"> | Date | string | null
  messageId?: Prisma.StringNullableFilter<"TrackedLink"> | string | null
  createdAt?: Prisma.DateTimeFilter<"TrackedLink"> | Date | string
}

export type TrackedLinkCreateWithoutTenantInput = {
  id?: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  createdAt?: Date | string
  message?: Prisma.MessageCreateNestedOneWithoutTrackedLinksInput
}

export type TrackedLinkUncheckedCreateWithoutTenantInput = {
  id?: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  messageId?: string | null
  createdAt?: Date | string
}

export type TrackedLinkCreateOrConnectWithoutTenantInput = {
  where: Prisma.TrackedLinkWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackedLinkCreateWithoutTenantInput, Prisma.TrackedLinkUncheckedCreateWithoutTenantInput>
}

export type TrackedLinkCreateManyTenantInputEnvelope = {
  data: Prisma.TrackedLinkCreateManyTenantInput | Prisma.TrackedLinkCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type TrackedLinkUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TrackedLinkWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackedLinkUpdateWithoutTenantInput, Prisma.TrackedLinkUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.TrackedLinkCreateWithoutTenantInput, Prisma.TrackedLinkUncheckedCreateWithoutTenantInput>
}

export type TrackedLinkUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TrackedLinkWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackedLinkUpdateWithoutTenantInput, Prisma.TrackedLinkUncheckedUpdateWithoutTenantInput>
}

export type TrackedLinkUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.TrackedLinkScalarWhereInput
  data: Prisma.XOR<Prisma.TrackedLinkUpdateManyMutationInput, Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantInput>
}

export type TrackedLinkCreateManyMessageInput = {
  id?: string
  tenantId: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  createdAt?: Date | string
}

export type TrackedLinkUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTrackedLinksNestedInput
}

export type TrackedLinkUncheckedUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackedLinkUncheckedUpdateManyWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackedLinkCreateManyTenantInput = {
  id?: string
  hash: string
  url: string
  clickCount?: number
  firstClick?: Date | string | null
  lastClick?: Date | string | null
  messageId?: string | null
  createdAt?: Date | string
}

export type TrackedLinkUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  message?: Prisma.MessageUpdateOneWithoutTrackedLinksNestedInput
}

export type TrackedLinkUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackedLinkUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  hash?: Prisma.StringFieldUpdateOperationsInput | string
  url?: Prisma.StringFieldUpdateOperationsInput | string
  clickCount?: Prisma.IntFieldUpdateOperationsInput | number
  firstClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastClick?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type TrackedLinkSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  hash?: boolean
  url?: boolean
  clickCount?: boolean
  firstClick?: boolean
  lastClick?: boolean
  messageId?: boolean
  createdAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  message?: boolean | Prisma.TrackedLink$messageArgs<ExtArgs>
}, ExtArgs["result"]["trackedLink"]>

export type TrackedLinkSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  hash?: boolean
  url?: boolean
  clickCount?: boolean
  firstClick?: boolean
  lastClick?: boolean
  messageId?: boolean
  createdAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  message?: boolean | Prisma.TrackedLink$messageArgs<ExtArgs>
}, ExtArgs["result"]["trackedLink"]>

export type TrackedLinkSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  hash?: boolean
  url?: boolean
  clickCount?: boolean
  firstClick?: boolean
  lastClick?: boolean
  messageId?: boolean
  createdAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  message?: boolean | Prisma.TrackedLink$messageArgs<ExtArgs>
}, ExtArgs["result"]["trackedLink"]>

export type TrackedLinkSelectScalar = {
  id?: boolean
  tenantId?: boolean
  hash?: boolean
  url?: boolean
  clickCount?: boolean
  firstClick?: boolean
  lastClick?: boolean
  messageId?: boolean
  createdAt?: boolean
}

export type TrackedLinkOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "hash" | "url" | "clickCount" | "firstClick" | "lastClick" | "messageId" | "createdAt", ExtArgs["result"]["trackedLink"]>
export type TrackedLinkInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  message?: boolean | Prisma.TrackedLink$messageArgs<ExtArgs>
}
export type TrackedLinkIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  message?: boolean | Prisma.TrackedLink$messageArgs<ExtArgs>
}
export type TrackedLinkIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  message?: boolean | Prisma.TrackedLink$messageArgs<ExtArgs>
}

export type $TrackedLinkPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TrackedLink"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    message: Prisma.$MessagePayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    hash: string
    url: string
    clickCount: number
    firstClick: Date | null
    lastClick: Date | null
    messageId: string | null
    createdAt: Date
  }, ExtArgs["result"]["trackedLink"]>
  composites: {}
}

export type TrackedLinkGetPayload<S extends boolean | null | undefined | TrackedLinkDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload, S>

export type TrackedLinkCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TrackedLinkFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TrackedLinkCountAggregateInputType | true
  }

export interface TrackedLinkDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TrackedLink'], meta: { name: 'TrackedLink' } }
  /**
   * Find zero or one TrackedLink that matches the filter.
   * @param {TrackedLinkFindUniqueArgs} args - Arguments to find a TrackedLink
   * @example
   * // Get one TrackedLink
   * const trackedLink = await prisma.trackedLink.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TrackedLinkFindUniqueArgs>(args: Prisma.SelectSubset<T, TrackedLinkFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TrackedLink that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TrackedLinkFindUniqueOrThrowArgs} args - Arguments to find a TrackedLink
   * @example
   * // Get one TrackedLink
   * const trackedLink = await prisma.trackedLink.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TrackedLinkFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TrackedLinkFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackedLink that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkFindFirstArgs} args - Arguments to find a TrackedLink
   * @example
   * // Get one TrackedLink
   * const trackedLink = await prisma.trackedLink.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TrackedLinkFindFirstArgs>(args?: Prisma.SelectSubset<T, TrackedLinkFindFirstArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackedLink that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkFindFirstOrThrowArgs} args - Arguments to find a TrackedLink
   * @example
   * // Get one TrackedLink
   * const trackedLink = await prisma.trackedLink.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TrackedLinkFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TrackedLinkFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TrackedLinks that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TrackedLinks
   * const trackedLinks = await prisma.trackedLink.findMany()
   * 
   * // Get first 10 TrackedLinks
   * const trackedLinks = await prisma.trackedLink.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const trackedLinkWithIdOnly = await prisma.trackedLink.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TrackedLinkFindManyArgs>(args?: Prisma.SelectSubset<T, TrackedLinkFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TrackedLink.
   * @param {TrackedLinkCreateArgs} args - Arguments to create a TrackedLink.
   * @example
   * // Create one TrackedLink
   * const TrackedLink = await prisma.trackedLink.create({
   *   data: {
   *     // ... data to create a TrackedLink
   *   }
   * })
   * 
   */
  create<T extends TrackedLinkCreateArgs>(args: Prisma.SelectSubset<T, TrackedLinkCreateArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TrackedLinks.
   * @param {TrackedLinkCreateManyArgs} args - Arguments to create many TrackedLinks.
   * @example
   * // Create many TrackedLinks
   * const trackedLink = await prisma.trackedLink.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TrackedLinkCreateManyArgs>(args?: Prisma.SelectSubset<T, TrackedLinkCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TrackedLinks and returns the data saved in the database.
   * @param {TrackedLinkCreateManyAndReturnArgs} args - Arguments to create many TrackedLinks.
   * @example
   * // Create many TrackedLinks
   * const trackedLink = await prisma.trackedLink.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TrackedLinks and only return the `id`
   * const trackedLinkWithIdOnly = await prisma.trackedLink.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TrackedLinkCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TrackedLinkCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TrackedLink.
   * @param {TrackedLinkDeleteArgs} args - Arguments to delete one TrackedLink.
   * @example
   * // Delete one TrackedLink
   * const TrackedLink = await prisma.trackedLink.delete({
   *   where: {
   *     // ... filter to delete one TrackedLink
   *   }
   * })
   * 
   */
  delete<T extends TrackedLinkDeleteArgs>(args: Prisma.SelectSubset<T, TrackedLinkDeleteArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TrackedLink.
   * @param {TrackedLinkUpdateArgs} args - Arguments to update one TrackedLink.
   * @example
   * // Update one TrackedLink
   * const trackedLink = await prisma.trackedLink.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TrackedLinkUpdateArgs>(args: Prisma.SelectSubset<T, TrackedLinkUpdateArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TrackedLinks.
   * @param {TrackedLinkDeleteManyArgs} args - Arguments to filter TrackedLinks to delete.
   * @example
   * // Delete a few TrackedLinks
   * const { count } = await prisma.trackedLink.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TrackedLinkDeleteManyArgs>(args?: Prisma.SelectSubset<T, TrackedLinkDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackedLinks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TrackedLinks
   * const trackedLink = await prisma.trackedLink.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TrackedLinkUpdateManyArgs>(args: Prisma.SelectSubset<T, TrackedLinkUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackedLinks and returns the data updated in the database.
   * @param {TrackedLinkUpdateManyAndReturnArgs} args - Arguments to update many TrackedLinks.
   * @example
   * // Update many TrackedLinks
   * const trackedLink = await prisma.trackedLink.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TrackedLinks and only return the `id`
   * const trackedLinkWithIdOnly = await prisma.trackedLink.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TrackedLinkUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TrackedLinkUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TrackedLink.
   * @param {TrackedLinkUpsertArgs} args - Arguments to update or create a TrackedLink.
   * @example
   * // Update or create a TrackedLink
   * const trackedLink = await prisma.trackedLink.upsert({
   *   create: {
   *     // ... data to create a TrackedLink
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TrackedLink we want to update
   *   }
   * })
   */
  upsert<T extends TrackedLinkUpsertArgs>(args: Prisma.SelectSubset<T, TrackedLinkUpsertArgs<ExtArgs>>): Prisma.Prisma__TrackedLinkClient<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TrackedLinks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkCountArgs} args - Arguments to filter TrackedLinks to count.
   * @example
   * // Count the number of TrackedLinks
   * const count = await prisma.trackedLink.count({
   *   where: {
   *     // ... the filter for the TrackedLinks we want to count
   *   }
   * })
  **/
  count<T extends TrackedLinkCountArgs>(
    args?: Prisma.Subset<T, TrackedLinkCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TrackedLinkCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TrackedLink.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TrackedLinkAggregateArgs>(args: Prisma.Subset<T, TrackedLinkAggregateArgs>): Prisma.PrismaPromise<GetTrackedLinkAggregateType<T>>

  /**
   * Group by TrackedLink.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackedLinkGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TrackedLinkGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TrackedLinkGroupByArgs['orderBy'] }
      : { orderBy?: TrackedLinkGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TrackedLinkGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTrackedLinkGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TrackedLink model
 */
readonly fields: TrackedLinkFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TrackedLink.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TrackedLinkClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  message<T extends Prisma.TrackedLink$messageArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TrackedLink$messageArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TrackedLink model
 */
export interface TrackedLinkFieldRefs {
  readonly id: Prisma.FieldRef<"TrackedLink", 'String'>
  readonly tenantId: Prisma.FieldRef<"TrackedLink", 'String'>
  readonly hash: Prisma.FieldRef<"TrackedLink", 'String'>
  readonly url: Prisma.FieldRef<"TrackedLink", 'String'>
  readonly clickCount: Prisma.FieldRef<"TrackedLink", 'Int'>
  readonly firstClick: Prisma.FieldRef<"TrackedLink", 'DateTime'>
  readonly lastClick: Prisma.FieldRef<"TrackedLink", 'DateTime'>
  readonly messageId: Prisma.FieldRef<"TrackedLink", 'String'>
  readonly createdAt: Prisma.FieldRef<"TrackedLink", 'DateTime'>
}
    

// Custom InputTypes
/**
 * TrackedLink findUnique
 */
export type TrackedLinkFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * Filter, which TrackedLink to fetch.
   */
  where: Prisma.TrackedLinkWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink findUniqueOrThrow
 */
export type TrackedLinkFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * Filter, which TrackedLink to fetch.
   */
  where: Prisma.TrackedLinkWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink findFirst
 */
export type TrackedLinkFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * Filter, which TrackedLink to fetch.
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackedLinks to fetch.
   */
  orderBy?: Prisma.TrackedLinkOrderByWithRelationInput | Prisma.TrackedLinkOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackedLinks.
   */
  cursor?: Prisma.TrackedLinkWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackedLinks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackedLinks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackedLinks.
   */
  distinct?: Prisma.TrackedLinkScalarFieldEnum | Prisma.TrackedLinkScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink findFirstOrThrow
 */
export type TrackedLinkFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * Filter, which TrackedLink to fetch.
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackedLinks to fetch.
   */
  orderBy?: Prisma.TrackedLinkOrderByWithRelationInput | Prisma.TrackedLinkOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackedLinks.
   */
  cursor?: Prisma.TrackedLinkWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackedLinks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackedLinks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackedLinks.
   */
  distinct?: Prisma.TrackedLinkScalarFieldEnum | Prisma.TrackedLinkScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink findMany
 */
export type TrackedLinkFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * Filter, which TrackedLinks to fetch.
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackedLinks to fetch.
   */
  orderBy?: Prisma.TrackedLinkOrderByWithRelationInput | Prisma.TrackedLinkOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TrackedLinks.
   */
  cursor?: Prisma.TrackedLinkWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackedLinks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackedLinks.
   */
  skip?: number
  distinct?: Prisma.TrackedLinkScalarFieldEnum | Prisma.TrackedLinkScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink create
 */
export type TrackedLinkCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * The data needed to create a TrackedLink.
   */
  data: Prisma.XOR<Prisma.TrackedLinkCreateInput, Prisma.TrackedLinkUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink createMany
 */
export type TrackedLinkCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TrackedLinks.
   */
  data: Prisma.TrackedLinkCreateManyInput | Prisma.TrackedLinkCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TrackedLink createManyAndReturn
 */
export type TrackedLinkCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * The data used to create many TrackedLinks.
   */
  data: Prisma.TrackedLinkCreateManyInput | Prisma.TrackedLinkCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TrackedLink update
 */
export type TrackedLinkUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * The data needed to update a TrackedLink.
   */
  data: Prisma.XOR<Prisma.TrackedLinkUpdateInput, Prisma.TrackedLinkUncheckedUpdateInput>
  /**
   * Choose, which TrackedLink to update.
   */
  where: Prisma.TrackedLinkWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink updateMany
 */
export type TrackedLinkUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TrackedLinks.
   */
  data: Prisma.XOR<Prisma.TrackedLinkUpdateManyMutationInput, Prisma.TrackedLinkUncheckedUpdateManyInput>
  /**
   * Filter which TrackedLinks to update
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * Limit how many TrackedLinks to update.
   */
  limit?: number
}

/**
 * TrackedLink updateManyAndReturn
 */
export type TrackedLinkUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * The data used to update TrackedLinks.
   */
  data: Prisma.XOR<Prisma.TrackedLinkUpdateManyMutationInput, Prisma.TrackedLinkUncheckedUpdateManyInput>
  /**
   * Filter which TrackedLinks to update
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * Limit how many TrackedLinks to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TrackedLink upsert
 */
export type TrackedLinkUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * The filter to search for the TrackedLink to update in case it exists.
   */
  where: Prisma.TrackedLinkWhereUniqueInput
  /**
   * In case the TrackedLink found by the `where` argument doesn't exist, create a new TrackedLink with this data.
   */
  create: Prisma.XOR<Prisma.TrackedLinkCreateInput, Prisma.TrackedLinkUncheckedCreateInput>
  /**
   * In case the TrackedLink was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TrackedLinkUpdateInput, Prisma.TrackedLinkUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink delete
 */
export type TrackedLinkDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  /**
   * Filter which TrackedLink to delete.
   */
  where: Prisma.TrackedLinkWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TrackedLink deleteMany
 */
export type TrackedLinkDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackedLinks to delete
   */
  where?: Prisma.TrackedLinkWhereInput
  /**
   * Limit how many TrackedLinks to delete.
   */
  limit?: number
}

/**
 * TrackedLink.message
 */
export type TrackedLink$messageArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  where?: Prisma.MessageWhereInput
}

/**
 * TrackedLink without action
 */
export type TrackedLinkDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
}
