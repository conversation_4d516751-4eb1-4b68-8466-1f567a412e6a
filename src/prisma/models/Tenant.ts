
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Tenant` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Tenant
 * 
 */
export type TenantModel = runtime.Types.Result.DefaultSelection<Prisma.$TenantPayload>

export type AggregateTenant = {
  _count: TenantCountAggregateOutputType | null
  _avg: TenantAvgAggregateOutputType | null
  _sum: TenantSumAggregateOutputType | null
  _min: TenantMinAggregateOutputType | null
  _max: TenantMaxAggregateOutputType | null
}

export type TenantAvgAggregateOutputType = {
  storeMessageForDays: number | null
}

export type TenantSumAggregateOutputType = {
  storeMessageForDays: number | null
}

export type TenantMinAggregateOutputType = {
  id: string | null
  name: string | null
  enabled: boolean | null
  language: string | null
  storeMessageForDays: number | null
  archive: boolean | null
  encryptionKeyId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TenantMaxAggregateOutputType = {
  id: string | null
  name: string | null
  enabled: boolean | null
  language: string | null
  storeMessageForDays: number | null
  archive: boolean | null
  encryptionKeyId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TenantCountAggregateOutputType = {
  id: number
  name: number
  enabled: number
  language: number
  storeMessageForDays: number
  archive: number
  encryptionKeyId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type TenantAvgAggregateInputType = {
  storeMessageForDays?: true
}

export type TenantSumAggregateInputType = {
  storeMessageForDays?: true
}

export type TenantMinAggregateInputType = {
  id?: true
  name?: true
  enabled?: true
  language?: true
  storeMessageForDays?: true
  archive?: true
  encryptionKeyId?: true
  createdAt?: true
  updatedAt?: true
}

export type TenantMaxAggregateInputType = {
  id?: true
  name?: true
  enabled?: true
  language?: true
  storeMessageForDays?: true
  archive?: true
  encryptionKeyId?: true
  createdAt?: true
  updatedAt?: true
}

export type TenantCountAggregateInputType = {
  id?: true
  name?: true
  enabled?: true
  language?: true
  storeMessageForDays?: true
  archive?: true
  encryptionKeyId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type TenantAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Tenant to aggregate.
   */
  where?: Prisma.TenantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tenants to fetch.
   */
  orderBy?: Prisma.TenantOrderByWithRelationInput | Prisma.TenantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TenantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tenants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tenants.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Tenants
  **/
  _count?: true | TenantCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TenantAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TenantSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TenantMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TenantMaxAggregateInputType
}

export type GetTenantAggregateType<T extends TenantAggregateArgs> = {
      [P in keyof T & keyof AggregateTenant]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTenant[P]>
    : Prisma.GetScalarType<T[P], AggregateTenant[P]>
}




export type TenantGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TenantWhereInput
  orderBy?: Prisma.TenantOrderByWithAggregationInput | Prisma.TenantOrderByWithAggregationInput[]
  by: Prisma.TenantScalarFieldEnum[] | Prisma.TenantScalarFieldEnum
  having?: Prisma.TenantScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TenantCountAggregateInputType | true
  _avg?: TenantAvgAggregateInputType
  _sum?: TenantSumAggregateInputType
  _min?: TenantMinAggregateInputType
  _max?: TenantMaxAggregateInputType
}

export type TenantGroupByOutputType = {
  id: string
  name: string
  enabled: boolean
  language: string
  storeMessageForDays: number
  archive: boolean
  encryptionKeyId: string
  createdAt: Date
  updatedAt: Date
  _count: TenantCountAggregateOutputType | null
  _avg: TenantAvgAggregateOutputType | null
  _sum: TenantSumAggregateOutputType | null
  _min: TenantMinAggregateOutputType | null
  _max: TenantMaxAggregateOutputType | null
}

type GetTenantGroupByPayload<T extends TenantGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TenantGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TenantGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TenantGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TenantGroupByOutputType[P]>
      }
    >
  > 



export type TenantWhereInput = {
  AND?: Prisma.TenantWhereInput | Prisma.TenantWhereInput[]
  OR?: Prisma.TenantWhereInput[]
  NOT?: Prisma.TenantWhereInput | Prisma.TenantWhereInput[]
  id?: Prisma.StringFilter<"Tenant"> | string
  name?: Prisma.StringFilter<"Tenant"> | string
  enabled?: Prisma.BoolFilter<"Tenant"> | boolean
  language?: Prisma.StringFilter<"Tenant"> | string
  storeMessageForDays?: Prisma.IntFilter<"Tenant"> | number
  archive?: Prisma.BoolFilter<"Tenant"> | boolean
  encryptionKeyId?: Prisma.StringFilter<"Tenant"> | string
  createdAt?: Prisma.DateTimeFilter<"Tenant"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Tenant"> | Date | string
  twoWayMessages?: Prisma.TwoWayMessageListRelationFilter
  channelConfigurations?: Prisma.ChannelListRelationFilter
  messages?: Prisma.MessageListRelationFilter
  tags?: Prisma.TagListRelationFilter
  recipients?: Prisma.RecipientListRelationFilter
  topics?: Prisma.TopicListRelationFilter
  templates?: Prisma.TemplateListRelationFilter
  TeamsUser?: Prisma.TeamsUserListRelationFilter
  TrackedLinks?: Prisma.TrackedLinkListRelationFilter
}

export type TenantOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  language?: Prisma.SortOrder
  storeMessageForDays?: Prisma.SortOrder
  archive?: Prisma.SortOrder
  encryptionKeyId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  twoWayMessages?: Prisma.TwoWayMessageOrderByRelationAggregateInput
  channelConfigurations?: Prisma.ChannelOrderByRelationAggregateInput
  messages?: Prisma.MessageOrderByRelationAggregateInput
  tags?: Prisma.TagOrderByRelationAggregateInput
  recipients?: Prisma.RecipientOrderByRelationAggregateInput
  topics?: Prisma.TopicOrderByRelationAggregateInput
  templates?: Prisma.TemplateOrderByRelationAggregateInput
  TeamsUser?: Prisma.TeamsUserOrderByRelationAggregateInput
  TrackedLinks?: Prisma.TrackedLinkOrderByRelationAggregateInput
}

export type TenantWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  name?: string
  AND?: Prisma.TenantWhereInput | Prisma.TenantWhereInput[]
  OR?: Prisma.TenantWhereInput[]
  NOT?: Prisma.TenantWhereInput | Prisma.TenantWhereInput[]
  enabled?: Prisma.BoolFilter<"Tenant"> | boolean
  language?: Prisma.StringFilter<"Tenant"> | string
  storeMessageForDays?: Prisma.IntFilter<"Tenant"> | number
  archive?: Prisma.BoolFilter<"Tenant"> | boolean
  encryptionKeyId?: Prisma.StringFilter<"Tenant"> | string
  createdAt?: Prisma.DateTimeFilter<"Tenant"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Tenant"> | Date | string
  twoWayMessages?: Prisma.TwoWayMessageListRelationFilter
  channelConfigurations?: Prisma.ChannelListRelationFilter
  messages?: Prisma.MessageListRelationFilter
  tags?: Prisma.TagListRelationFilter
  recipients?: Prisma.RecipientListRelationFilter
  topics?: Prisma.TopicListRelationFilter
  templates?: Prisma.TemplateListRelationFilter
  TeamsUser?: Prisma.TeamsUserListRelationFilter
  TrackedLinks?: Prisma.TrackedLinkListRelationFilter
}, "id" | "name">

export type TenantOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  language?: Prisma.SortOrder
  storeMessageForDays?: Prisma.SortOrder
  archive?: Prisma.SortOrder
  encryptionKeyId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.TenantCountOrderByAggregateInput
  _avg?: Prisma.TenantAvgOrderByAggregateInput
  _max?: Prisma.TenantMaxOrderByAggregateInput
  _min?: Prisma.TenantMinOrderByAggregateInput
  _sum?: Prisma.TenantSumOrderByAggregateInput
}

export type TenantScalarWhereWithAggregatesInput = {
  AND?: Prisma.TenantScalarWhereWithAggregatesInput | Prisma.TenantScalarWhereWithAggregatesInput[]
  OR?: Prisma.TenantScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TenantScalarWhereWithAggregatesInput | Prisma.TenantScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Tenant"> | string
  name?: Prisma.StringWithAggregatesFilter<"Tenant"> | string
  enabled?: Prisma.BoolWithAggregatesFilter<"Tenant"> | boolean
  language?: Prisma.StringWithAggregatesFilter<"Tenant"> | string
  storeMessageForDays?: Prisma.IntWithAggregatesFilter<"Tenant"> | number
  archive?: Prisma.BoolWithAggregatesFilter<"Tenant"> | boolean
  encryptionKeyId?: Prisma.StringWithAggregatesFilter<"Tenant"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Tenant"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Tenant"> | Date | string
}

export type TenantCreateInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateManyInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TenantUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TenantUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TenantScalarRelationFilter = {
  is?: Prisma.TenantWhereInput
  isNot?: Prisma.TenantWhereInput
}

export type TenantCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  language?: Prisma.SortOrder
  storeMessageForDays?: Prisma.SortOrder
  archive?: Prisma.SortOrder
  encryptionKeyId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TenantAvgOrderByAggregateInput = {
  storeMessageForDays?: Prisma.SortOrder
}

export type TenantMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  language?: Prisma.SortOrder
  storeMessageForDays?: Prisma.SortOrder
  archive?: Prisma.SortOrder
  encryptionKeyId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TenantMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  language?: Prisma.SortOrder
  storeMessageForDays?: Prisma.SortOrder
  archive?: Prisma.SortOrder
  encryptionKeyId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TenantSumOrderByAggregateInput = {
  storeMessageForDays?: Prisma.SortOrder
}

export type TenantCreateNestedOneWithoutChannelConfigurationsInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutChannelConfigurationsInput, Prisma.TenantUncheckedCreateWithoutChannelConfigurationsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutChannelConfigurationsInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutChannelConfigurationsNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutChannelConfigurationsInput, Prisma.TenantUncheckedCreateWithoutChannelConfigurationsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutChannelConfigurationsInput
  upsert?: Prisma.TenantUpsertWithoutChannelConfigurationsInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutChannelConfigurationsInput, Prisma.TenantUpdateWithoutChannelConfigurationsInput>, Prisma.TenantUncheckedUpdateWithoutChannelConfigurationsInput>
}

export type TenantCreateNestedOneWithoutMessagesInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutMessagesInput, Prisma.TenantUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutMessagesInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutMessagesInput, Prisma.TenantUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutMessagesInput
  upsert?: Prisma.TenantUpsertWithoutMessagesInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutMessagesInput, Prisma.TenantUpdateWithoutMessagesInput>, Prisma.TenantUncheckedUpdateWithoutMessagesInput>
}

export type TenantCreateNestedOneWithoutRecipientsInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutRecipientsInput, Prisma.TenantUncheckedCreateWithoutRecipientsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutRecipientsInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutRecipientsNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutRecipientsInput, Prisma.TenantUncheckedCreateWithoutRecipientsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutRecipientsInput
  upsert?: Prisma.TenantUpsertWithoutRecipientsInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutRecipientsInput, Prisma.TenantUpdateWithoutRecipientsInput>, Prisma.TenantUncheckedUpdateWithoutRecipientsInput>
}

export type TenantCreateNestedOneWithoutTagsInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTagsInput, Prisma.TenantUncheckedCreateWithoutTagsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTagsInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutTagsNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTagsInput, Prisma.TenantUncheckedCreateWithoutTagsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTagsInput
  upsert?: Prisma.TenantUpsertWithoutTagsInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutTagsInput, Prisma.TenantUpdateWithoutTagsInput>, Prisma.TenantUncheckedUpdateWithoutTagsInput>
}

export type TenantCreateNestedOneWithoutTeamsUserInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTeamsUserInput, Prisma.TenantUncheckedCreateWithoutTeamsUserInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTeamsUserInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutTeamsUserNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTeamsUserInput, Prisma.TenantUncheckedCreateWithoutTeamsUserInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTeamsUserInput
  upsert?: Prisma.TenantUpsertWithoutTeamsUserInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutTeamsUserInput, Prisma.TenantUpdateWithoutTeamsUserInput>, Prisma.TenantUncheckedUpdateWithoutTeamsUserInput>
}

export type TenantCreateNestedOneWithoutTemplatesInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTemplatesInput, Prisma.TenantUncheckedCreateWithoutTemplatesInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTemplatesInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutTemplatesNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTemplatesInput, Prisma.TenantUncheckedCreateWithoutTemplatesInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTemplatesInput
  upsert?: Prisma.TenantUpsertWithoutTemplatesInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutTemplatesInput, Prisma.TenantUpdateWithoutTemplatesInput>, Prisma.TenantUncheckedUpdateWithoutTemplatesInput>
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type TenantCreateNestedOneWithoutTopicsInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTopicsInput, Prisma.TenantUncheckedCreateWithoutTopicsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTopicsInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutTopicsNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTopicsInput, Prisma.TenantUncheckedCreateWithoutTopicsInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTopicsInput
  upsert?: Prisma.TenantUpsertWithoutTopicsInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutTopicsInput, Prisma.TenantUpdateWithoutTopicsInput>, Prisma.TenantUncheckedUpdateWithoutTopicsInput>
}

export type TenantCreateNestedOneWithoutTrackedLinksInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTrackedLinksInput, Prisma.TenantUncheckedCreateWithoutTrackedLinksInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTrackedLinksInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutTrackedLinksNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTrackedLinksInput, Prisma.TenantUncheckedCreateWithoutTrackedLinksInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTrackedLinksInput
  upsert?: Prisma.TenantUpsertWithoutTrackedLinksInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutTrackedLinksInput, Prisma.TenantUpdateWithoutTrackedLinksInput>, Prisma.TenantUncheckedUpdateWithoutTrackedLinksInput>
}

export type TenantCreateNestedOneWithoutTwoWayMessagesInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTwoWayMessagesInput, Prisma.TenantUncheckedCreateWithoutTwoWayMessagesInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTwoWayMessagesInput
  connect?: Prisma.TenantWhereUniqueInput
}

export type TenantUpdateOneRequiredWithoutTwoWayMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.TenantCreateWithoutTwoWayMessagesInput, Prisma.TenantUncheckedCreateWithoutTwoWayMessagesInput>
  connectOrCreate?: Prisma.TenantCreateOrConnectWithoutTwoWayMessagesInput
  upsert?: Prisma.TenantUpsertWithoutTwoWayMessagesInput
  connect?: Prisma.TenantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TenantUpdateToOneWithWhereWithoutTwoWayMessagesInput, Prisma.TenantUpdateWithoutTwoWayMessagesInput>, Prisma.TenantUncheckedUpdateWithoutTwoWayMessagesInput>
}

export type TenantCreateWithoutChannelConfigurationsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutChannelConfigurationsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutChannelConfigurationsInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutChannelConfigurationsInput, Prisma.TenantUncheckedCreateWithoutChannelConfigurationsInput>
}

export type TenantUpsertWithoutChannelConfigurationsInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutChannelConfigurationsInput, Prisma.TenantUncheckedUpdateWithoutChannelConfigurationsInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutChannelConfigurationsInput, Prisma.TenantUncheckedCreateWithoutChannelConfigurationsInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutChannelConfigurationsInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutChannelConfigurationsInput, Prisma.TenantUncheckedUpdateWithoutChannelConfigurationsInput>
}

export type TenantUpdateWithoutChannelConfigurationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutChannelConfigurationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutMessagesInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutMessagesInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutMessagesInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutMessagesInput, Prisma.TenantUncheckedCreateWithoutMessagesInput>
}

export type TenantUpsertWithoutMessagesInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutMessagesInput, Prisma.TenantUncheckedUpdateWithoutMessagesInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutMessagesInput, Prisma.TenantUncheckedCreateWithoutMessagesInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutMessagesInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutMessagesInput, Prisma.TenantUncheckedUpdateWithoutMessagesInput>
}

export type TenantUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutRecipientsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutRecipientsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutRecipientsInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutRecipientsInput, Prisma.TenantUncheckedCreateWithoutRecipientsInput>
}

export type TenantUpsertWithoutRecipientsInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutRecipientsInput, Prisma.TenantUncheckedUpdateWithoutRecipientsInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutRecipientsInput, Prisma.TenantUncheckedCreateWithoutRecipientsInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutRecipientsInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutRecipientsInput, Prisma.TenantUncheckedUpdateWithoutRecipientsInput>
}

export type TenantUpdateWithoutRecipientsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutRecipientsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutTagsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutTagsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutTagsInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutTagsInput, Prisma.TenantUncheckedCreateWithoutTagsInput>
}

export type TenantUpsertWithoutTagsInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutTagsInput, Prisma.TenantUncheckedUpdateWithoutTagsInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutTagsInput, Prisma.TenantUncheckedCreateWithoutTagsInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutTagsInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutTagsInput, Prisma.TenantUncheckedUpdateWithoutTagsInput>
}

export type TenantUpdateWithoutTagsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutTagsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutTeamsUserInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutTeamsUserInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutTeamsUserInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutTeamsUserInput, Prisma.TenantUncheckedCreateWithoutTeamsUserInput>
}

export type TenantUpsertWithoutTeamsUserInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutTeamsUserInput, Prisma.TenantUncheckedUpdateWithoutTeamsUserInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutTeamsUserInput, Prisma.TenantUncheckedCreateWithoutTeamsUserInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutTeamsUserInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutTeamsUserInput, Prisma.TenantUncheckedUpdateWithoutTeamsUserInput>
}

export type TenantUpdateWithoutTeamsUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutTeamsUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutTemplatesInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutTemplatesInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutTemplatesInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutTemplatesInput, Prisma.TenantUncheckedCreateWithoutTemplatesInput>
}

export type TenantUpsertWithoutTemplatesInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutTemplatesInput, Prisma.TenantUncheckedUpdateWithoutTemplatesInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutTemplatesInput, Prisma.TenantUncheckedCreateWithoutTemplatesInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutTemplatesInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutTemplatesInput, Prisma.TenantUncheckedUpdateWithoutTemplatesInput>
}

export type TenantUpdateWithoutTemplatesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutTemplatesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutTopicsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutTopicsInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutTopicsInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutTopicsInput, Prisma.TenantUncheckedCreateWithoutTopicsInput>
}

export type TenantUpsertWithoutTopicsInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutTopicsInput, Prisma.TenantUncheckedUpdateWithoutTopicsInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutTopicsInput, Prisma.TenantUncheckedCreateWithoutTopicsInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutTopicsInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutTopicsInput, Prisma.TenantUncheckedUpdateWithoutTopicsInput>
}

export type TenantUpdateWithoutTopicsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutTopicsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutTrackedLinksInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutTrackedLinksInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutTrackedLinksInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutTrackedLinksInput, Prisma.TenantUncheckedCreateWithoutTrackedLinksInput>
}

export type TenantUpsertWithoutTrackedLinksInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutTrackedLinksInput, Prisma.TenantUncheckedUpdateWithoutTrackedLinksInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutTrackedLinksInput, Prisma.TenantUncheckedCreateWithoutTrackedLinksInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutTrackedLinksInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutTrackedLinksInput, Prisma.TenantUncheckedUpdateWithoutTrackedLinksInput>
}

export type TenantUpdateWithoutTrackedLinksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutTrackedLinksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  twoWayMessages?: Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
}

export type TenantCreateWithoutTwoWayMessagesInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  channelConfigurations?: Prisma.ChannelCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutTenantInput
}

export type TenantUncheckedCreateWithoutTwoWayMessagesInput = {
  id?: string
  name: string
  enabled?: boolean
  language?: string
  storeMessageForDays?: number
  archive?: boolean
  encryptionKeyId?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  channelConfigurations?: Prisma.ChannelUncheckedCreateNestedManyWithoutTenantInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutTenantInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutTenantInput
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTenantInput
  topics?: Prisma.TopicUncheckedCreateNestedManyWithoutTenantInput
  templates?: Prisma.TemplateUncheckedCreateNestedManyWithoutTenantInput
  TeamsUser?: Prisma.TeamsUserUncheckedCreateNestedManyWithoutTenantInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutTenantInput
}

export type TenantCreateOrConnectWithoutTwoWayMessagesInput = {
  where: Prisma.TenantWhereUniqueInput
  create: Prisma.XOR<Prisma.TenantCreateWithoutTwoWayMessagesInput, Prisma.TenantUncheckedCreateWithoutTwoWayMessagesInput>
}

export type TenantUpsertWithoutTwoWayMessagesInput = {
  update: Prisma.XOR<Prisma.TenantUpdateWithoutTwoWayMessagesInput, Prisma.TenantUncheckedUpdateWithoutTwoWayMessagesInput>
  create: Prisma.XOR<Prisma.TenantCreateWithoutTwoWayMessagesInput, Prisma.TenantUncheckedCreateWithoutTwoWayMessagesInput>
  where?: Prisma.TenantWhereInput
}

export type TenantUpdateToOneWithWhereWithoutTwoWayMessagesInput = {
  where?: Prisma.TenantWhereInput
  data: Prisma.XOR<Prisma.TenantUpdateWithoutTwoWayMessagesInput, Prisma.TenantUncheckedUpdateWithoutTwoWayMessagesInput>
}

export type TenantUpdateWithoutTwoWayMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelConfigurations?: Prisma.ChannelUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUpdateManyWithoutTenantNestedInput
}

export type TenantUncheckedUpdateWithoutTwoWayMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  language?: Prisma.StringFieldUpdateOperationsInput | string
  storeMessageForDays?: Prisma.IntFieldUpdateOperationsInput | number
  archive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  encryptionKeyId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channelConfigurations?: Prisma.ChannelUncheckedUpdateManyWithoutTenantNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutTenantNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutTenantNestedInput
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTenantNestedInput
  topics?: Prisma.TopicUncheckedUpdateManyWithoutTenantNestedInput
  templates?: Prisma.TemplateUncheckedUpdateManyWithoutTenantNestedInput
  TeamsUser?: Prisma.TeamsUserUncheckedUpdateManyWithoutTenantNestedInput
  TrackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutTenantNestedInput
}


/**
 * Count Type TenantCountOutputType
 */

export type TenantCountOutputType = {
  twoWayMessages: number
  channelConfigurations: number
  messages: number
  tags: number
  recipients: number
  topics: number
  templates: number
  TeamsUser: number
  TrackedLinks: number
}

export type TenantCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  twoWayMessages?: boolean | TenantCountOutputTypeCountTwoWayMessagesArgs
  channelConfigurations?: boolean | TenantCountOutputTypeCountChannelConfigurationsArgs
  messages?: boolean | TenantCountOutputTypeCountMessagesArgs
  tags?: boolean | TenantCountOutputTypeCountTagsArgs
  recipients?: boolean | TenantCountOutputTypeCountRecipientsArgs
  topics?: boolean | TenantCountOutputTypeCountTopicsArgs
  templates?: boolean | TenantCountOutputTypeCountTemplatesArgs
  TeamsUser?: boolean | TenantCountOutputTypeCountTeamsUserArgs
  TrackedLinks?: boolean | TenantCountOutputTypeCountTrackedLinksArgs
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TenantCountOutputType
   */
  select?: Prisma.TenantCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountTwoWayMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TwoWayMessageWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountChannelConfigurationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountTagsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TagWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountRecipientsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountTopicsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TopicWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountTemplatesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TemplateWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountTeamsUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TeamsUserWhereInput
}

/**
 * TenantCountOutputType without action
 */
export type TenantCountOutputTypeCountTrackedLinksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackedLinkWhereInput
}


export type TenantSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  enabled?: boolean
  language?: boolean
  storeMessageForDays?: boolean
  archive?: boolean
  encryptionKeyId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  twoWayMessages?: boolean | Prisma.Tenant$twoWayMessagesArgs<ExtArgs>
  channelConfigurations?: boolean | Prisma.Tenant$channelConfigurationsArgs<ExtArgs>
  messages?: boolean | Prisma.Tenant$messagesArgs<ExtArgs>
  tags?: boolean | Prisma.Tenant$tagsArgs<ExtArgs>
  recipients?: boolean | Prisma.Tenant$recipientsArgs<ExtArgs>
  topics?: boolean | Prisma.Tenant$topicsArgs<ExtArgs>
  templates?: boolean | Prisma.Tenant$templatesArgs<ExtArgs>
  TeamsUser?: boolean | Prisma.Tenant$TeamsUserArgs<ExtArgs>
  TrackedLinks?: boolean | Prisma.Tenant$TrackedLinksArgs<ExtArgs>
  _count?: boolean | Prisma.TenantCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["tenant"]>

export type TenantSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  enabled?: boolean
  language?: boolean
  storeMessageForDays?: boolean
  archive?: boolean
  encryptionKeyId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["tenant"]>

export type TenantSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  enabled?: boolean
  language?: boolean
  storeMessageForDays?: boolean
  archive?: boolean
  encryptionKeyId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["tenant"]>

export type TenantSelectScalar = {
  id?: boolean
  name?: boolean
  enabled?: boolean
  language?: boolean
  storeMessageForDays?: boolean
  archive?: boolean
  encryptionKeyId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type TenantOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "enabled" | "language" | "storeMessageForDays" | "archive" | "encryptionKeyId" | "createdAt" | "updatedAt", ExtArgs["result"]["tenant"]>
export type TenantInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  twoWayMessages?: boolean | Prisma.Tenant$twoWayMessagesArgs<ExtArgs>
  channelConfigurations?: boolean | Prisma.Tenant$channelConfigurationsArgs<ExtArgs>
  messages?: boolean | Prisma.Tenant$messagesArgs<ExtArgs>
  tags?: boolean | Prisma.Tenant$tagsArgs<ExtArgs>
  recipients?: boolean | Prisma.Tenant$recipientsArgs<ExtArgs>
  topics?: boolean | Prisma.Tenant$topicsArgs<ExtArgs>
  templates?: boolean | Prisma.Tenant$templatesArgs<ExtArgs>
  TeamsUser?: boolean | Prisma.Tenant$TeamsUserArgs<ExtArgs>
  TrackedLinks?: boolean | Prisma.Tenant$TrackedLinksArgs<ExtArgs>
  _count?: boolean | Prisma.TenantCountOutputTypeDefaultArgs<ExtArgs>
}
export type TenantIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type TenantIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $TenantPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Tenant"
  objects: {
    twoWayMessages: Prisma.$TwoWayMessagePayload<ExtArgs>[]
    channelConfigurations: Prisma.$ChannelPayload<ExtArgs>[]
    messages: Prisma.$MessagePayload<ExtArgs>[]
    tags: Prisma.$TagPayload<ExtArgs>[]
    recipients: Prisma.$RecipientPayload<ExtArgs>[]
    topics: Prisma.$TopicPayload<ExtArgs>[]
    templates: Prisma.$TemplatePayload<ExtArgs>[]
    TeamsUser: Prisma.$TeamsUserPayload<ExtArgs>[]
    TrackedLinks: Prisma.$TrackedLinkPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    enabled: boolean
    language: string
    storeMessageForDays: number
    archive: boolean
    encryptionKeyId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["tenant"]>
  composites: {}
}

export type TenantGetPayload<S extends boolean | null | undefined | TenantDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TenantPayload, S>

export type TenantCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TenantFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TenantCountAggregateInputType | true
  }

export interface TenantDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Tenant'], meta: { name: 'Tenant' } }
  /**
   * Find zero or one Tenant that matches the filter.
   * @param {TenantFindUniqueArgs} args - Arguments to find a Tenant
   * @example
   * // Get one Tenant
   * const tenant = await prisma.tenant.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TenantFindUniqueArgs>(args: Prisma.SelectSubset<T, TenantFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Tenant that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TenantFindUniqueOrThrowArgs} args - Arguments to find a Tenant
   * @example
   * // Get one Tenant
   * const tenant = await prisma.tenant.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TenantFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TenantFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Tenant that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantFindFirstArgs} args - Arguments to find a Tenant
   * @example
   * // Get one Tenant
   * const tenant = await prisma.tenant.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TenantFindFirstArgs>(args?: Prisma.SelectSubset<T, TenantFindFirstArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Tenant that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantFindFirstOrThrowArgs} args - Arguments to find a Tenant
   * @example
   * // Get one Tenant
   * const tenant = await prisma.tenant.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TenantFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TenantFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Tenants that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Tenants
   * const tenants = await prisma.tenant.findMany()
   * 
   * // Get first 10 Tenants
   * const tenants = await prisma.tenant.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const tenantWithIdOnly = await prisma.tenant.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TenantFindManyArgs>(args?: Prisma.SelectSubset<T, TenantFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Tenant.
   * @param {TenantCreateArgs} args - Arguments to create a Tenant.
   * @example
   * // Create one Tenant
   * const Tenant = await prisma.tenant.create({
   *   data: {
   *     // ... data to create a Tenant
   *   }
   * })
   * 
   */
  create<T extends TenantCreateArgs>(args: Prisma.SelectSubset<T, TenantCreateArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Tenants.
   * @param {TenantCreateManyArgs} args - Arguments to create many Tenants.
   * @example
   * // Create many Tenants
   * const tenant = await prisma.tenant.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TenantCreateManyArgs>(args?: Prisma.SelectSubset<T, TenantCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Tenants and returns the data saved in the database.
   * @param {TenantCreateManyAndReturnArgs} args - Arguments to create many Tenants.
   * @example
   * // Create many Tenants
   * const tenant = await prisma.tenant.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Tenants and only return the `id`
   * const tenantWithIdOnly = await prisma.tenant.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TenantCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TenantCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Tenant.
   * @param {TenantDeleteArgs} args - Arguments to delete one Tenant.
   * @example
   * // Delete one Tenant
   * const Tenant = await prisma.tenant.delete({
   *   where: {
   *     // ... filter to delete one Tenant
   *   }
   * })
   * 
   */
  delete<T extends TenantDeleteArgs>(args: Prisma.SelectSubset<T, TenantDeleteArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Tenant.
   * @param {TenantUpdateArgs} args - Arguments to update one Tenant.
   * @example
   * // Update one Tenant
   * const tenant = await prisma.tenant.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TenantUpdateArgs>(args: Prisma.SelectSubset<T, TenantUpdateArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Tenants.
   * @param {TenantDeleteManyArgs} args - Arguments to filter Tenants to delete.
   * @example
   * // Delete a few Tenants
   * const { count } = await prisma.tenant.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TenantDeleteManyArgs>(args?: Prisma.SelectSubset<T, TenantDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tenants.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Tenants
   * const tenant = await prisma.tenant.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TenantUpdateManyArgs>(args: Prisma.SelectSubset<T, TenantUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tenants and returns the data updated in the database.
   * @param {TenantUpdateManyAndReturnArgs} args - Arguments to update many Tenants.
   * @example
   * // Update many Tenants
   * const tenant = await prisma.tenant.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Tenants and only return the `id`
   * const tenantWithIdOnly = await prisma.tenant.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TenantUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TenantUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Tenant.
   * @param {TenantUpsertArgs} args - Arguments to update or create a Tenant.
   * @example
   * // Update or create a Tenant
   * const tenant = await prisma.tenant.upsert({
   *   create: {
   *     // ... data to create a Tenant
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Tenant we want to update
   *   }
   * })
   */
  upsert<T extends TenantUpsertArgs>(args: Prisma.SelectSubset<T, TenantUpsertArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Tenants.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantCountArgs} args - Arguments to filter Tenants to count.
   * @example
   * // Count the number of Tenants
   * const count = await prisma.tenant.count({
   *   where: {
   *     // ... the filter for the Tenants we want to count
   *   }
   * })
  **/
  count<T extends TenantCountArgs>(
    args?: Prisma.Subset<T, TenantCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TenantCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Tenant.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TenantAggregateArgs>(args: Prisma.Subset<T, TenantAggregateArgs>): Prisma.PrismaPromise<GetTenantAggregateType<T>>

  /**
   * Group by Tenant.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TenantGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TenantGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TenantGroupByArgs['orderBy'] }
      : { orderBy?: TenantGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TenantGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTenantGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Tenant model
 */
readonly fields: TenantFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Tenant.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TenantClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  twoWayMessages<T extends Prisma.Tenant$twoWayMessagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$twoWayMessagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  channelConfigurations<T extends Prisma.Tenant$channelConfigurationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$channelConfigurationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  messages<T extends Prisma.Tenant$messagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$messagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  tags<T extends Prisma.Tenant$tagsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$tagsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  recipients<T extends Prisma.Tenant$recipientsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$recipientsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  topics<T extends Prisma.Tenant$topicsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$topicsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  templates<T extends Prisma.Tenant$templatesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$templatesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TemplatePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  TeamsUser<T extends Prisma.Tenant$TeamsUserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$TeamsUserArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TeamsUserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  TrackedLinks<T extends Prisma.Tenant$TrackedLinksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tenant$TrackedLinksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Tenant model
 */
export interface TenantFieldRefs {
  readonly id: Prisma.FieldRef<"Tenant", 'String'>
  readonly name: Prisma.FieldRef<"Tenant", 'String'>
  readonly enabled: Prisma.FieldRef<"Tenant", 'Boolean'>
  readonly language: Prisma.FieldRef<"Tenant", 'String'>
  readonly storeMessageForDays: Prisma.FieldRef<"Tenant", 'Int'>
  readonly archive: Prisma.FieldRef<"Tenant", 'Boolean'>
  readonly encryptionKeyId: Prisma.FieldRef<"Tenant", 'String'>
  readonly createdAt: Prisma.FieldRef<"Tenant", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Tenant", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Tenant findUnique
 */
export type TenantFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * Filter, which Tenant to fetch.
   */
  where: Prisma.TenantWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant findUniqueOrThrow
 */
export type TenantFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * Filter, which Tenant to fetch.
   */
  where: Prisma.TenantWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant findFirst
 */
export type TenantFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * Filter, which Tenant to fetch.
   */
  where?: Prisma.TenantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tenants to fetch.
   */
  orderBy?: Prisma.TenantOrderByWithRelationInput | Prisma.TenantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Tenants.
   */
  cursor?: Prisma.TenantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tenants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tenants.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Tenants.
   */
  distinct?: Prisma.TenantScalarFieldEnum | Prisma.TenantScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant findFirstOrThrow
 */
export type TenantFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * Filter, which Tenant to fetch.
   */
  where?: Prisma.TenantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tenants to fetch.
   */
  orderBy?: Prisma.TenantOrderByWithRelationInput | Prisma.TenantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Tenants.
   */
  cursor?: Prisma.TenantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tenants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tenants.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Tenants.
   */
  distinct?: Prisma.TenantScalarFieldEnum | Prisma.TenantScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant findMany
 */
export type TenantFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * Filter, which Tenants to fetch.
   */
  where?: Prisma.TenantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tenants to fetch.
   */
  orderBy?: Prisma.TenantOrderByWithRelationInput | Prisma.TenantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Tenants.
   */
  cursor?: Prisma.TenantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tenants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tenants.
   */
  skip?: number
  distinct?: Prisma.TenantScalarFieldEnum | Prisma.TenantScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant create
 */
export type TenantCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * The data needed to create a Tenant.
   */
  data: Prisma.XOR<Prisma.TenantCreateInput, Prisma.TenantUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant createMany
 */
export type TenantCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Tenants.
   */
  data: Prisma.TenantCreateManyInput | Prisma.TenantCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Tenant createManyAndReturn
 */
export type TenantCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * The data used to create many Tenants.
   */
  data: Prisma.TenantCreateManyInput | Prisma.TenantCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Tenant update
 */
export type TenantUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * The data needed to update a Tenant.
   */
  data: Prisma.XOR<Prisma.TenantUpdateInput, Prisma.TenantUncheckedUpdateInput>
  /**
   * Choose, which Tenant to update.
   */
  where: Prisma.TenantWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant updateMany
 */
export type TenantUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Tenants.
   */
  data: Prisma.XOR<Prisma.TenantUpdateManyMutationInput, Prisma.TenantUncheckedUpdateManyInput>
  /**
   * Filter which Tenants to update
   */
  where?: Prisma.TenantWhereInput
  /**
   * Limit how many Tenants to update.
   */
  limit?: number
}

/**
 * Tenant updateManyAndReturn
 */
export type TenantUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * The data used to update Tenants.
   */
  data: Prisma.XOR<Prisma.TenantUpdateManyMutationInput, Prisma.TenantUncheckedUpdateManyInput>
  /**
   * Filter which Tenants to update
   */
  where?: Prisma.TenantWhereInput
  /**
   * Limit how many Tenants to update.
   */
  limit?: number
}

/**
 * Tenant upsert
 */
export type TenantUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * The filter to search for the Tenant to update in case it exists.
   */
  where: Prisma.TenantWhereUniqueInput
  /**
   * In case the Tenant found by the `where` argument doesn't exist, create a new Tenant with this data.
   */
  create: Prisma.XOR<Prisma.TenantCreateInput, Prisma.TenantUncheckedCreateInput>
  /**
   * In case the Tenant was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TenantUpdateInput, Prisma.TenantUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant delete
 */
export type TenantDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
  /**
   * Filter which Tenant to delete.
   */
  where: Prisma.TenantWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tenant deleteMany
 */
export type TenantDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Tenants to delete
   */
  where?: Prisma.TenantWhereInput
  /**
   * Limit how many Tenants to delete.
   */
  limit?: number
}

/**
 * Tenant.twoWayMessages
 */
export type Tenant$twoWayMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  where?: Prisma.TwoWayMessageWhereInput
  orderBy?: Prisma.TwoWayMessageOrderByWithRelationInput | Prisma.TwoWayMessageOrderByWithRelationInput[]
  cursor?: Prisma.TwoWayMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TwoWayMessageScalarFieldEnum | Prisma.TwoWayMessageScalarFieldEnum[]
}

/**
 * Tenant.channelConfigurations
 */
export type Tenant$channelConfigurationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  where?: Prisma.ChannelWhereInput
  orderBy?: Prisma.ChannelOrderByWithRelationInput | Prisma.ChannelOrderByWithRelationInput[]
  cursor?: Prisma.ChannelWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ChannelScalarFieldEnum | Prisma.ChannelScalarFieldEnum[]
}

/**
 * Tenant.messages
 */
export type Tenant$messagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  where?: Prisma.MessageWhereInput
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  cursor?: Prisma.MessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
}

/**
 * Tenant.tags
 */
export type Tenant$tagsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  where?: Prisma.TagWhereInput
  orderBy?: Prisma.TagOrderByWithRelationInput | Prisma.TagOrderByWithRelationInput[]
  cursor?: Prisma.TagWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TagScalarFieldEnum | Prisma.TagScalarFieldEnum[]
}

/**
 * Tenant.recipients
 */
export type Tenant$recipientsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  where?: Prisma.RecipientWhereInput
  orderBy?: Prisma.RecipientOrderByWithRelationInput | Prisma.RecipientOrderByWithRelationInput[]
  cursor?: Prisma.RecipientWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RecipientScalarFieldEnum | Prisma.RecipientScalarFieldEnum[]
}

/**
 * Tenant.topics
 */
export type Tenant$topicsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Topic
   */
  select?: Prisma.TopicSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Topic
   */
  omit?: Prisma.TopicOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicInclude<ExtArgs> | null
  where?: Prisma.TopicWhereInput
  orderBy?: Prisma.TopicOrderByWithRelationInput | Prisma.TopicOrderByWithRelationInput[]
  cursor?: Prisma.TopicWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TopicScalarFieldEnum | Prisma.TopicScalarFieldEnum[]
}

/**
 * Tenant.templates
 */
export type Tenant$templatesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Template
   */
  select?: Prisma.TemplateSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Template
   */
  omit?: Prisma.TemplateOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TemplateInclude<ExtArgs> | null
  where?: Prisma.TemplateWhereInput
  orderBy?: Prisma.TemplateOrderByWithRelationInput | Prisma.TemplateOrderByWithRelationInput[]
  cursor?: Prisma.TemplateWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TemplateScalarFieldEnum | Prisma.TemplateScalarFieldEnum[]
}

/**
 * Tenant.TeamsUser
 */
export type Tenant$TeamsUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TeamsUser
   */
  select?: Prisma.TeamsUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TeamsUser
   */
  omit?: Prisma.TeamsUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeamsUserInclude<ExtArgs> | null
  where?: Prisma.TeamsUserWhereInput
  orderBy?: Prisma.TeamsUserOrderByWithRelationInput | Prisma.TeamsUserOrderByWithRelationInput[]
  cursor?: Prisma.TeamsUserWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TeamsUserScalarFieldEnum | Prisma.TeamsUserScalarFieldEnum[]
}

/**
 * Tenant.TrackedLinks
 */
export type Tenant$TrackedLinksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  where?: Prisma.TrackedLinkWhereInput
  orderBy?: Prisma.TrackedLinkOrderByWithRelationInput | Prisma.TrackedLinkOrderByWithRelationInput[]
  cursor?: Prisma.TrackedLinkWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackedLinkScalarFieldEnum | Prisma.TrackedLinkScalarFieldEnum[]
}

/**
 * Tenant without action
 */
export type TenantDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tenant
   */
  select?: Prisma.TenantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tenant
   */
  omit?: Prisma.TenantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TenantInclude<ExtArgs> | null
}
