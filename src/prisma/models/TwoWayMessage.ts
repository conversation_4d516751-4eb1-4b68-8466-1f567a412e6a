
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TwoWayMessage` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TwoWayMessage
 * 
 */
export type TwoWayMessageModel = runtime.Types.Result.DefaultSelection<Prisma.$TwoWayMessagePayload>

export type AggregateTwoWayMessage = {
  _count: TwoWayMessageCountAggregateOutputType | null
  _min: TwoWayMessageMinAggregateOutputType | null
  _max: TwoWayMessageMaxAggregateOutputType | null
}

export type TwoWayMessageMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  recipientServiceId: string | null
  tenantServiceId: string | null
  conversationId: string | null
  channel: string | null
  direction: string | null
  subject: string | null
  message: string | null
  reaction: string | null
  category: string | null
  recipientId: string | null
  tenantMessageId: string | null
  transactionId: string | null
  error: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TwoWayMessageMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  recipientServiceId: string | null
  tenantServiceId: string | null
  conversationId: string | null
  channel: string | null
  direction: string | null
  subject: string | null
  message: string | null
  reaction: string | null
  category: string | null
  recipientId: string | null
  tenantMessageId: string | null
  transactionId: string | null
  error: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TwoWayMessageCountAggregateOutputType = {
  id: number
  tenantId: number
  recipientServiceId: number
  tenantServiceId: number
  conversationId: number
  channel: number
  direction: number
  subject: number
  message: number
  reaction: number
  category: number
  recipientId: number
  tenantMessageId: number
  transactionId: number
  error: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type TwoWayMessageMinAggregateInputType = {
  id?: true
  tenantId?: true
  recipientServiceId?: true
  tenantServiceId?: true
  conversationId?: true
  channel?: true
  direction?: true
  subject?: true
  message?: true
  reaction?: true
  category?: true
  recipientId?: true
  tenantMessageId?: true
  transactionId?: true
  error?: true
  createdAt?: true
  updatedAt?: true
}

export type TwoWayMessageMaxAggregateInputType = {
  id?: true
  tenantId?: true
  recipientServiceId?: true
  tenantServiceId?: true
  conversationId?: true
  channel?: true
  direction?: true
  subject?: true
  message?: true
  reaction?: true
  category?: true
  recipientId?: true
  tenantMessageId?: true
  transactionId?: true
  error?: true
  createdAt?: true
  updatedAt?: true
}

export type TwoWayMessageCountAggregateInputType = {
  id?: true
  tenantId?: true
  recipientServiceId?: true
  tenantServiceId?: true
  conversationId?: true
  channel?: true
  direction?: true
  subject?: true
  message?: true
  reaction?: true
  category?: true
  recipientId?: true
  tenantMessageId?: true
  transactionId?: true
  error?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type TwoWayMessageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TwoWayMessage to aggregate.
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoWayMessages to fetch.
   */
  orderBy?: Prisma.TwoWayMessageOrderByWithRelationInput | Prisma.TwoWayMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TwoWayMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoWayMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoWayMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TwoWayMessages
  **/
  _count?: true | TwoWayMessageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TwoWayMessageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TwoWayMessageMaxAggregateInputType
}

export type GetTwoWayMessageAggregateType<T extends TwoWayMessageAggregateArgs> = {
      [P in keyof T & keyof AggregateTwoWayMessage]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTwoWayMessage[P]>
    : Prisma.GetScalarType<T[P], AggregateTwoWayMessage[P]>
}




export type TwoWayMessageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TwoWayMessageWhereInput
  orderBy?: Prisma.TwoWayMessageOrderByWithAggregationInput | Prisma.TwoWayMessageOrderByWithAggregationInput[]
  by: Prisma.TwoWayMessageScalarFieldEnum[] | Prisma.TwoWayMessageScalarFieldEnum
  having?: Prisma.TwoWayMessageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TwoWayMessageCountAggregateInputType | true
  _min?: TwoWayMessageMinAggregateInputType
  _max?: TwoWayMessageMaxAggregateInputType
}

export type TwoWayMessageGroupByOutputType = {
  id: string
  tenantId: string
  recipientServiceId: string | null
  tenantServiceId: string | null
  conversationId: string | null
  channel: string
  direction: string
  subject: string | null
  message: string
  reaction: string | null
  category: string | null
  recipientId: string | null
  tenantMessageId: string | null
  transactionId: string | null
  error: string | null
  createdAt: Date
  updatedAt: Date
  _count: TwoWayMessageCountAggregateOutputType | null
  _min: TwoWayMessageMinAggregateOutputType | null
  _max: TwoWayMessageMaxAggregateOutputType | null
}

type GetTwoWayMessageGroupByPayload<T extends TwoWayMessageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TwoWayMessageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TwoWayMessageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TwoWayMessageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TwoWayMessageGroupByOutputType[P]>
      }
    >
  > 



export type TwoWayMessageWhereInput = {
  AND?: Prisma.TwoWayMessageWhereInput | Prisma.TwoWayMessageWhereInput[]
  OR?: Prisma.TwoWayMessageWhereInput[]
  NOT?: Prisma.TwoWayMessageWhereInput | Prisma.TwoWayMessageWhereInput[]
  id?: Prisma.StringFilter<"TwoWayMessage"> | string
  tenantId?: Prisma.StringFilter<"TwoWayMessage"> | string
  recipientServiceId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  tenantServiceId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  conversationId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  channel?: Prisma.StringFilter<"TwoWayMessage"> | string
  direction?: Prisma.StringFilter<"TwoWayMessage"> | string
  subject?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  message?: Prisma.StringFilter<"TwoWayMessage"> | string
  reaction?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  category?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  recipientId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  tenantMessageId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  transactionId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  error?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  createdAt?: Prisma.DateTimeFilter<"TwoWayMessage"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"TwoWayMessage"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
}

export type TwoWayMessageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipientServiceId?: Prisma.SortOrderInput | Prisma.SortOrder
  tenantServiceId?: Prisma.SortOrderInput | Prisma.SortOrder
  conversationId?: Prisma.SortOrderInput | Prisma.SortOrder
  channel?: Prisma.SortOrder
  direction?: Prisma.SortOrder
  subject?: Prisma.SortOrderInput | Prisma.SortOrder
  message?: Prisma.SortOrder
  reaction?: Prisma.SortOrderInput | Prisma.SortOrder
  category?: Prisma.SortOrderInput | Prisma.SortOrder
  recipientId?: Prisma.SortOrderInput | Prisma.SortOrder
  tenantMessageId?: Prisma.SortOrderInput | Prisma.SortOrder
  transactionId?: Prisma.SortOrderInput | Prisma.SortOrder
  error?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
}

export type TwoWayMessageWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TwoWayMessageWhereInput | Prisma.TwoWayMessageWhereInput[]
  OR?: Prisma.TwoWayMessageWhereInput[]
  NOT?: Prisma.TwoWayMessageWhereInput | Prisma.TwoWayMessageWhereInput[]
  tenantId?: Prisma.StringFilter<"TwoWayMessage"> | string
  recipientServiceId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  tenantServiceId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  conversationId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  channel?: Prisma.StringFilter<"TwoWayMessage"> | string
  direction?: Prisma.StringFilter<"TwoWayMessage"> | string
  subject?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  message?: Prisma.StringFilter<"TwoWayMessage"> | string
  reaction?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  category?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  recipientId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  tenantMessageId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  transactionId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  error?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  createdAt?: Prisma.DateTimeFilter<"TwoWayMessage"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"TwoWayMessage"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
}, "id">

export type TwoWayMessageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipientServiceId?: Prisma.SortOrderInput | Prisma.SortOrder
  tenantServiceId?: Prisma.SortOrderInput | Prisma.SortOrder
  conversationId?: Prisma.SortOrderInput | Prisma.SortOrder
  channel?: Prisma.SortOrder
  direction?: Prisma.SortOrder
  subject?: Prisma.SortOrderInput | Prisma.SortOrder
  message?: Prisma.SortOrder
  reaction?: Prisma.SortOrderInput | Prisma.SortOrder
  category?: Prisma.SortOrderInput | Prisma.SortOrder
  recipientId?: Prisma.SortOrderInput | Prisma.SortOrder
  tenantMessageId?: Prisma.SortOrderInput | Prisma.SortOrder
  transactionId?: Prisma.SortOrderInput | Prisma.SortOrder
  error?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.TwoWayMessageCountOrderByAggregateInput
  _max?: Prisma.TwoWayMessageMaxOrderByAggregateInput
  _min?: Prisma.TwoWayMessageMinOrderByAggregateInput
}

export type TwoWayMessageScalarWhereWithAggregatesInput = {
  AND?: Prisma.TwoWayMessageScalarWhereWithAggregatesInput | Prisma.TwoWayMessageScalarWhereWithAggregatesInput[]
  OR?: Prisma.TwoWayMessageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TwoWayMessageScalarWhereWithAggregatesInput | Prisma.TwoWayMessageScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"TwoWayMessage"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"TwoWayMessage"> | string
  recipientServiceId?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  tenantServiceId?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  conversationId?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  channel?: Prisma.StringWithAggregatesFilter<"TwoWayMessage"> | string
  direction?: Prisma.StringWithAggregatesFilter<"TwoWayMessage"> | string
  subject?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  message?: Prisma.StringWithAggregatesFilter<"TwoWayMessage"> | string
  reaction?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  category?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  recipientId?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  tenantMessageId?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  transactionId?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  error?: Prisma.StringNullableWithAggregatesFilter<"TwoWayMessage"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"TwoWayMessage"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"TwoWayMessage"> | Date | string
}

export type TwoWayMessageCreateInput = {
  id?: string
  recipientServiceId?: string | null
  tenantServiceId?: string | null
  conversationId?: string | null
  channel: string
  direction?: string
  subject?: string | null
  message: string
  reaction?: string | null
  category?: string | null
  recipientId?: string | null
  tenantMessageId?: string | null
  transactionId?: string | null
  error?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTwoWayMessagesInput
}

export type TwoWayMessageUncheckedCreateInput = {
  id?: string
  tenantId: string
  recipientServiceId?: string | null
  tenantServiceId?: string | null
  conversationId?: string | null
  channel: string
  direction?: string
  subject?: string | null
  message: string
  reaction?: string | null
  category?: string | null
  recipientId?: string | null
  tenantMessageId?: string | null
  transactionId?: string | null
  error?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TwoWayMessageUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTwoWayMessagesNestedInput
}

export type TwoWayMessageUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TwoWayMessageCreateManyInput = {
  id?: string
  tenantId: string
  recipientServiceId?: string | null
  tenantServiceId?: string | null
  conversationId?: string | null
  channel: string
  direction?: string
  subject?: string | null
  message: string
  reaction?: string | null
  category?: string | null
  recipientId?: string | null
  tenantMessageId?: string | null
  transactionId?: string | null
  error?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TwoWayMessageUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TwoWayMessageUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TwoWayMessageListRelationFilter = {
  every?: Prisma.TwoWayMessageWhereInput
  some?: Prisma.TwoWayMessageWhereInput
  none?: Prisma.TwoWayMessageWhereInput
}

export type TwoWayMessageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TwoWayMessageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipientServiceId?: Prisma.SortOrder
  tenantServiceId?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  channel?: Prisma.SortOrder
  direction?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  message?: Prisma.SortOrder
  reaction?: Prisma.SortOrder
  category?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  tenantMessageId?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  error?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TwoWayMessageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipientServiceId?: Prisma.SortOrder
  tenantServiceId?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  channel?: Prisma.SortOrder
  direction?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  message?: Prisma.SortOrder
  reaction?: Prisma.SortOrder
  category?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  tenantMessageId?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  error?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TwoWayMessageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipientServiceId?: Prisma.SortOrder
  tenantServiceId?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  channel?: Prisma.SortOrder
  direction?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  message?: Prisma.SortOrder
  reaction?: Prisma.SortOrder
  category?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  tenantMessageId?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  error?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TwoWayMessageCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TwoWayMessageCreateWithoutTenantInput, Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput> | Prisma.TwoWayMessageCreateWithoutTenantInput[] | Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput | Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TwoWayMessageCreateManyTenantInputEnvelope
  connect?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
}

export type TwoWayMessageUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TwoWayMessageCreateWithoutTenantInput, Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput> | Prisma.TwoWayMessageCreateWithoutTenantInput[] | Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput | Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TwoWayMessageCreateManyTenantInputEnvelope
  connect?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
}

export type TwoWayMessageUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TwoWayMessageCreateWithoutTenantInput, Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput> | Prisma.TwoWayMessageCreateWithoutTenantInput[] | Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput | Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TwoWayMessageUpsertWithWhereUniqueWithoutTenantInput | Prisma.TwoWayMessageUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TwoWayMessageCreateManyTenantInputEnvelope
  set?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  disconnect?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  delete?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  connect?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  update?: Prisma.TwoWayMessageUpdateWithWhereUniqueWithoutTenantInput | Prisma.TwoWayMessageUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TwoWayMessageUpdateManyWithWhereWithoutTenantInput | Prisma.TwoWayMessageUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TwoWayMessageScalarWhereInput | Prisma.TwoWayMessageScalarWhereInput[]
}

export type TwoWayMessageUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TwoWayMessageCreateWithoutTenantInput, Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput> | Prisma.TwoWayMessageCreateWithoutTenantInput[] | Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput | Prisma.TwoWayMessageCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TwoWayMessageUpsertWithWhereUniqueWithoutTenantInput | Prisma.TwoWayMessageUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TwoWayMessageCreateManyTenantInputEnvelope
  set?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  disconnect?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  delete?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  connect?: Prisma.TwoWayMessageWhereUniqueInput | Prisma.TwoWayMessageWhereUniqueInput[]
  update?: Prisma.TwoWayMessageUpdateWithWhereUniqueWithoutTenantInput | Prisma.TwoWayMessageUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TwoWayMessageUpdateManyWithWhereWithoutTenantInput | Prisma.TwoWayMessageUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TwoWayMessageScalarWhereInput | Prisma.TwoWayMessageScalarWhereInput[]
}

export type TwoWayMessageCreateWithoutTenantInput = {
  id?: string
  recipientServiceId?: string | null
  tenantServiceId?: string | null
  conversationId?: string | null
  channel: string
  direction?: string
  subject?: string | null
  message: string
  reaction?: string | null
  category?: string | null
  recipientId?: string | null
  tenantMessageId?: string | null
  transactionId?: string | null
  error?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TwoWayMessageUncheckedCreateWithoutTenantInput = {
  id?: string
  recipientServiceId?: string | null
  tenantServiceId?: string | null
  conversationId?: string | null
  channel: string
  direction?: string
  subject?: string | null
  message: string
  reaction?: string | null
  category?: string | null
  recipientId?: string | null
  tenantMessageId?: string | null
  transactionId?: string | null
  error?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TwoWayMessageCreateOrConnectWithoutTenantInput = {
  where: Prisma.TwoWayMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.TwoWayMessageCreateWithoutTenantInput, Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput>
}

export type TwoWayMessageCreateManyTenantInputEnvelope = {
  data: Prisma.TwoWayMessageCreateManyTenantInput | Prisma.TwoWayMessageCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type TwoWayMessageUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TwoWayMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.TwoWayMessageUpdateWithoutTenantInput, Prisma.TwoWayMessageUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.TwoWayMessageCreateWithoutTenantInput, Prisma.TwoWayMessageUncheckedCreateWithoutTenantInput>
}

export type TwoWayMessageUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TwoWayMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.TwoWayMessageUpdateWithoutTenantInput, Prisma.TwoWayMessageUncheckedUpdateWithoutTenantInput>
}

export type TwoWayMessageUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.TwoWayMessageScalarWhereInput
  data: Prisma.XOR<Prisma.TwoWayMessageUpdateManyMutationInput, Prisma.TwoWayMessageUncheckedUpdateManyWithoutTenantInput>
}

export type TwoWayMessageScalarWhereInput = {
  AND?: Prisma.TwoWayMessageScalarWhereInput | Prisma.TwoWayMessageScalarWhereInput[]
  OR?: Prisma.TwoWayMessageScalarWhereInput[]
  NOT?: Prisma.TwoWayMessageScalarWhereInput | Prisma.TwoWayMessageScalarWhereInput[]
  id?: Prisma.StringFilter<"TwoWayMessage"> | string
  tenantId?: Prisma.StringFilter<"TwoWayMessage"> | string
  recipientServiceId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  tenantServiceId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  conversationId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  channel?: Prisma.StringFilter<"TwoWayMessage"> | string
  direction?: Prisma.StringFilter<"TwoWayMessage"> | string
  subject?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  message?: Prisma.StringFilter<"TwoWayMessage"> | string
  reaction?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  category?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  recipientId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  tenantMessageId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  transactionId?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  error?: Prisma.StringNullableFilter<"TwoWayMessage"> | string | null
  createdAt?: Prisma.DateTimeFilter<"TwoWayMessage"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"TwoWayMessage"> | Date | string
}

export type TwoWayMessageCreateManyTenantInput = {
  id?: string
  recipientServiceId?: string | null
  tenantServiceId?: string | null
  conversationId?: string | null
  channel: string
  direction?: string
  subject?: string | null
  message: string
  reaction?: string | null
  category?: string | null
  recipientId?: string | null
  tenantMessageId?: string | null
  transactionId?: string | null
  error?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TwoWayMessageUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TwoWayMessageUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TwoWayMessageUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantServiceId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conversationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  channel?: Prisma.StringFieldUpdateOperationsInput | string
  direction?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  message?: Prisma.StringFieldUpdateOperationsInput | string
  reaction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tenantMessageId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  error?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type TwoWayMessageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  recipientServiceId?: boolean
  tenantServiceId?: boolean
  conversationId?: boolean
  channel?: boolean
  direction?: boolean
  subject?: boolean
  message?: boolean
  reaction?: boolean
  category?: boolean
  recipientId?: boolean
  tenantMessageId?: boolean
  transactionId?: boolean
  error?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["twoWayMessage"]>

export type TwoWayMessageSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  recipientServiceId?: boolean
  tenantServiceId?: boolean
  conversationId?: boolean
  channel?: boolean
  direction?: boolean
  subject?: boolean
  message?: boolean
  reaction?: boolean
  category?: boolean
  recipientId?: boolean
  tenantMessageId?: boolean
  transactionId?: boolean
  error?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["twoWayMessage"]>

export type TwoWayMessageSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  recipientServiceId?: boolean
  tenantServiceId?: boolean
  conversationId?: boolean
  channel?: boolean
  direction?: boolean
  subject?: boolean
  message?: boolean
  reaction?: boolean
  category?: boolean
  recipientId?: boolean
  tenantMessageId?: boolean
  transactionId?: boolean
  error?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["twoWayMessage"]>

export type TwoWayMessageSelectScalar = {
  id?: boolean
  tenantId?: boolean
  recipientServiceId?: boolean
  tenantServiceId?: boolean
  conversationId?: boolean
  channel?: boolean
  direction?: boolean
  subject?: boolean
  message?: boolean
  reaction?: boolean
  category?: boolean
  recipientId?: boolean
  tenantMessageId?: boolean
  transactionId?: boolean
  error?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type TwoWayMessageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "recipientServiceId" | "tenantServiceId" | "conversationId" | "channel" | "direction" | "subject" | "message" | "reaction" | "category" | "recipientId" | "tenantMessageId" | "transactionId" | "error" | "createdAt" | "updatedAt", ExtArgs["result"]["twoWayMessage"]>
export type TwoWayMessageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TwoWayMessageIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TwoWayMessageIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $TwoWayMessagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TwoWayMessage"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    recipientServiceId: string | null
    tenantServiceId: string | null
    conversationId: string | null
    channel: string
    direction: string
    subject: string | null
    message: string
    reaction: string | null
    category: string | null
    recipientId: string | null
    tenantMessageId: string | null
    transactionId: string | null
    error: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["twoWayMessage"]>
  composites: {}
}

export type TwoWayMessageGetPayload<S extends boolean | null | undefined | TwoWayMessageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload, S>

export type TwoWayMessageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TwoWayMessageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TwoWayMessageCountAggregateInputType | true
  }

export interface TwoWayMessageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TwoWayMessage'], meta: { name: 'TwoWayMessage' } }
  /**
   * Find zero or one TwoWayMessage that matches the filter.
   * @param {TwoWayMessageFindUniqueArgs} args - Arguments to find a TwoWayMessage
   * @example
   * // Get one TwoWayMessage
   * const twoWayMessage = await prisma.twoWayMessage.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TwoWayMessageFindUniqueArgs>(args: Prisma.SelectSubset<T, TwoWayMessageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TwoWayMessage that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TwoWayMessageFindUniqueOrThrowArgs} args - Arguments to find a TwoWayMessage
   * @example
   * // Get one TwoWayMessage
   * const twoWayMessage = await prisma.twoWayMessage.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TwoWayMessageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TwoWayMessageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TwoWayMessage that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageFindFirstArgs} args - Arguments to find a TwoWayMessage
   * @example
   * // Get one TwoWayMessage
   * const twoWayMessage = await prisma.twoWayMessage.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TwoWayMessageFindFirstArgs>(args?: Prisma.SelectSubset<T, TwoWayMessageFindFirstArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TwoWayMessage that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageFindFirstOrThrowArgs} args - Arguments to find a TwoWayMessage
   * @example
   * // Get one TwoWayMessage
   * const twoWayMessage = await prisma.twoWayMessage.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TwoWayMessageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TwoWayMessageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TwoWayMessages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TwoWayMessages
   * const twoWayMessages = await prisma.twoWayMessage.findMany()
   * 
   * // Get first 10 TwoWayMessages
   * const twoWayMessages = await prisma.twoWayMessage.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const twoWayMessageWithIdOnly = await prisma.twoWayMessage.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TwoWayMessageFindManyArgs>(args?: Prisma.SelectSubset<T, TwoWayMessageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TwoWayMessage.
   * @param {TwoWayMessageCreateArgs} args - Arguments to create a TwoWayMessage.
   * @example
   * // Create one TwoWayMessage
   * const TwoWayMessage = await prisma.twoWayMessage.create({
   *   data: {
   *     // ... data to create a TwoWayMessage
   *   }
   * })
   * 
   */
  create<T extends TwoWayMessageCreateArgs>(args: Prisma.SelectSubset<T, TwoWayMessageCreateArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TwoWayMessages.
   * @param {TwoWayMessageCreateManyArgs} args - Arguments to create many TwoWayMessages.
   * @example
   * // Create many TwoWayMessages
   * const twoWayMessage = await prisma.twoWayMessage.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TwoWayMessageCreateManyArgs>(args?: Prisma.SelectSubset<T, TwoWayMessageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TwoWayMessages and returns the data saved in the database.
   * @param {TwoWayMessageCreateManyAndReturnArgs} args - Arguments to create many TwoWayMessages.
   * @example
   * // Create many TwoWayMessages
   * const twoWayMessage = await prisma.twoWayMessage.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TwoWayMessages and only return the `id`
   * const twoWayMessageWithIdOnly = await prisma.twoWayMessage.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TwoWayMessageCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TwoWayMessageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TwoWayMessage.
   * @param {TwoWayMessageDeleteArgs} args - Arguments to delete one TwoWayMessage.
   * @example
   * // Delete one TwoWayMessage
   * const TwoWayMessage = await prisma.twoWayMessage.delete({
   *   where: {
   *     // ... filter to delete one TwoWayMessage
   *   }
   * })
   * 
   */
  delete<T extends TwoWayMessageDeleteArgs>(args: Prisma.SelectSubset<T, TwoWayMessageDeleteArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TwoWayMessage.
   * @param {TwoWayMessageUpdateArgs} args - Arguments to update one TwoWayMessage.
   * @example
   * // Update one TwoWayMessage
   * const twoWayMessage = await prisma.twoWayMessage.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TwoWayMessageUpdateArgs>(args: Prisma.SelectSubset<T, TwoWayMessageUpdateArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TwoWayMessages.
   * @param {TwoWayMessageDeleteManyArgs} args - Arguments to filter TwoWayMessages to delete.
   * @example
   * // Delete a few TwoWayMessages
   * const { count } = await prisma.twoWayMessage.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TwoWayMessageDeleteManyArgs>(args?: Prisma.SelectSubset<T, TwoWayMessageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TwoWayMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TwoWayMessages
   * const twoWayMessage = await prisma.twoWayMessage.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TwoWayMessageUpdateManyArgs>(args: Prisma.SelectSubset<T, TwoWayMessageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TwoWayMessages and returns the data updated in the database.
   * @param {TwoWayMessageUpdateManyAndReturnArgs} args - Arguments to update many TwoWayMessages.
   * @example
   * // Update many TwoWayMessages
   * const twoWayMessage = await prisma.twoWayMessage.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TwoWayMessages and only return the `id`
   * const twoWayMessageWithIdOnly = await prisma.twoWayMessage.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TwoWayMessageUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TwoWayMessageUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TwoWayMessage.
   * @param {TwoWayMessageUpsertArgs} args - Arguments to update or create a TwoWayMessage.
   * @example
   * // Update or create a TwoWayMessage
   * const twoWayMessage = await prisma.twoWayMessage.upsert({
   *   create: {
   *     // ... data to create a TwoWayMessage
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TwoWayMessage we want to update
   *   }
   * })
   */
  upsert<T extends TwoWayMessageUpsertArgs>(args: Prisma.SelectSubset<T, TwoWayMessageUpsertArgs<ExtArgs>>): Prisma.Prisma__TwoWayMessageClient<runtime.Types.Result.GetResult<Prisma.$TwoWayMessagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TwoWayMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageCountArgs} args - Arguments to filter TwoWayMessages to count.
   * @example
   * // Count the number of TwoWayMessages
   * const count = await prisma.twoWayMessage.count({
   *   where: {
   *     // ... the filter for the TwoWayMessages we want to count
   *   }
   * })
  **/
  count<T extends TwoWayMessageCountArgs>(
    args?: Prisma.Subset<T, TwoWayMessageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TwoWayMessageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TwoWayMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TwoWayMessageAggregateArgs>(args: Prisma.Subset<T, TwoWayMessageAggregateArgs>): Prisma.PrismaPromise<GetTwoWayMessageAggregateType<T>>

  /**
   * Group by TwoWayMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoWayMessageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TwoWayMessageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TwoWayMessageGroupByArgs['orderBy'] }
      : { orderBy?: TwoWayMessageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TwoWayMessageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTwoWayMessageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TwoWayMessage model
 */
readonly fields: TwoWayMessageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TwoWayMessage.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TwoWayMessageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TwoWayMessage model
 */
export interface TwoWayMessageFieldRefs {
  readonly id: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly tenantId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly recipientServiceId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly tenantServiceId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly conversationId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly channel: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly direction: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly subject: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly message: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly reaction: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly category: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly recipientId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly tenantMessageId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly transactionId: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly error: Prisma.FieldRef<"TwoWayMessage", 'String'>
  readonly createdAt: Prisma.FieldRef<"TwoWayMessage", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"TwoWayMessage", 'DateTime'>
}
    

// Custom InputTypes
/**
 * TwoWayMessage findUnique
 */
export type TwoWayMessageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * Filter, which TwoWayMessage to fetch.
   */
  where: Prisma.TwoWayMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage findUniqueOrThrow
 */
export type TwoWayMessageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * Filter, which TwoWayMessage to fetch.
   */
  where: Prisma.TwoWayMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage findFirst
 */
export type TwoWayMessageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * Filter, which TwoWayMessage to fetch.
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoWayMessages to fetch.
   */
  orderBy?: Prisma.TwoWayMessageOrderByWithRelationInput | Prisma.TwoWayMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TwoWayMessages.
   */
  cursor?: Prisma.TwoWayMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoWayMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoWayMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TwoWayMessages.
   */
  distinct?: Prisma.TwoWayMessageScalarFieldEnum | Prisma.TwoWayMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage findFirstOrThrow
 */
export type TwoWayMessageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * Filter, which TwoWayMessage to fetch.
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoWayMessages to fetch.
   */
  orderBy?: Prisma.TwoWayMessageOrderByWithRelationInput | Prisma.TwoWayMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TwoWayMessages.
   */
  cursor?: Prisma.TwoWayMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoWayMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoWayMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TwoWayMessages.
   */
  distinct?: Prisma.TwoWayMessageScalarFieldEnum | Prisma.TwoWayMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage findMany
 */
export type TwoWayMessageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * Filter, which TwoWayMessages to fetch.
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoWayMessages to fetch.
   */
  orderBy?: Prisma.TwoWayMessageOrderByWithRelationInput | Prisma.TwoWayMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TwoWayMessages.
   */
  cursor?: Prisma.TwoWayMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoWayMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoWayMessages.
   */
  skip?: number
  distinct?: Prisma.TwoWayMessageScalarFieldEnum | Prisma.TwoWayMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage create
 */
export type TwoWayMessageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * The data needed to create a TwoWayMessage.
   */
  data: Prisma.XOR<Prisma.TwoWayMessageCreateInput, Prisma.TwoWayMessageUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage createMany
 */
export type TwoWayMessageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TwoWayMessages.
   */
  data: Prisma.TwoWayMessageCreateManyInput | Prisma.TwoWayMessageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TwoWayMessage createManyAndReturn
 */
export type TwoWayMessageCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * The data used to create many TwoWayMessages.
   */
  data: Prisma.TwoWayMessageCreateManyInput | Prisma.TwoWayMessageCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TwoWayMessage update
 */
export type TwoWayMessageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * The data needed to update a TwoWayMessage.
   */
  data: Prisma.XOR<Prisma.TwoWayMessageUpdateInput, Prisma.TwoWayMessageUncheckedUpdateInput>
  /**
   * Choose, which TwoWayMessage to update.
   */
  where: Prisma.TwoWayMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage updateMany
 */
export type TwoWayMessageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TwoWayMessages.
   */
  data: Prisma.XOR<Prisma.TwoWayMessageUpdateManyMutationInput, Prisma.TwoWayMessageUncheckedUpdateManyInput>
  /**
   * Filter which TwoWayMessages to update
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * Limit how many TwoWayMessages to update.
   */
  limit?: number
}

/**
 * TwoWayMessage updateManyAndReturn
 */
export type TwoWayMessageUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * The data used to update TwoWayMessages.
   */
  data: Prisma.XOR<Prisma.TwoWayMessageUpdateManyMutationInput, Prisma.TwoWayMessageUncheckedUpdateManyInput>
  /**
   * Filter which TwoWayMessages to update
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * Limit how many TwoWayMessages to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TwoWayMessage upsert
 */
export type TwoWayMessageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * The filter to search for the TwoWayMessage to update in case it exists.
   */
  where: Prisma.TwoWayMessageWhereUniqueInput
  /**
   * In case the TwoWayMessage found by the `where` argument doesn't exist, create a new TwoWayMessage with this data.
   */
  create: Prisma.XOR<Prisma.TwoWayMessageCreateInput, Prisma.TwoWayMessageUncheckedCreateInput>
  /**
   * In case the TwoWayMessage was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TwoWayMessageUpdateInput, Prisma.TwoWayMessageUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage delete
 */
export type TwoWayMessageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
  /**
   * Filter which TwoWayMessage to delete.
   */
  where: Prisma.TwoWayMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * TwoWayMessage deleteMany
 */
export type TwoWayMessageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TwoWayMessages to delete
   */
  where?: Prisma.TwoWayMessageWhereInput
  /**
   * Limit how many TwoWayMessages to delete.
   */
  limit?: number
}

/**
 * TwoWayMessage without action
 */
export type TwoWayMessageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoWayMessage
   */
  select?: Prisma.TwoWayMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoWayMessage
   */
  omit?: Prisma.TwoWayMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoWayMessageInclude<ExtArgs> | null
}
