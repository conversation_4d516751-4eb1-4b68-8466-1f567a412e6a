
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `MessageAttribute` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model MessageAttribute
 * 
 */
export type MessageAttributeModel = runtime.Types.Result.DefaultSelection<Prisma.$MessageAttributePayload>

export type AggregateMessageAttribute = {
  _count: MessageAttributeCountAggregateOutputType | null
  _min: MessageAttributeMinAggregateOutputType | null
  _max: MessageAttributeMaxAggregateOutputType | null
}

export type MessageAttributeMinAggregateOutputType = {
  id: string | null
  messageId: string | null
  name: string | null
  value: string | null
}

export type MessageAttributeMaxAggregateOutputType = {
  id: string | null
  messageId: string | null
  name: string | null
  value: string | null
}

export type MessageAttributeCountAggregateOutputType = {
  id: number
  messageId: number
  name: number
  value: number
  _all: number
}


export type MessageAttributeMinAggregateInputType = {
  id?: true
  messageId?: true
  name?: true
  value?: true
}

export type MessageAttributeMaxAggregateInputType = {
  id?: true
  messageId?: true
  name?: true
  value?: true
}

export type MessageAttributeCountAggregateInputType = {
  id?: true
  messageId?: true
  name?: true
  value?: true
  _all?: true
}

export type MessageAttributeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which MessageAttribute to aggregate.
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageAttributes to fetch.
   */
  orderBy?: Prisma.MessageAttributeOrderByWithRelationInput | Prisma.MessageAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.MessageAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned MessageAttributes
  **/
  _count?: true | MessageAttributeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: MessageAttributeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: MessageAttributeMaxAggregateInputType
}

export type GetMessageAttributeAggregateType<T extends MessageAttributeAggregateArgs> = {
      [P in keyof T & keyof AggregateMessageAttribute]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateMessageAttribute[P]>
    : Prisma.GetScalarType<T[P], AggregateMessageAttribute[P]>
}




export type MessageAttributeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageAttributeWhereInput
  orderBy?: Prisma.MessageAttributeOrderByWithAggregationInput | Prisma.MessageAttributeOrderByWithAggregationInput[]
  by: Prisma.MessageAttributeScalarFieldEnum[] | Prisma.MessageAttributeScalarFieldEnum
  having?: Prisma.MessageAttributeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: MessageAttributeCountAggregateInputType | true
  _min?: MessageAttributeMinAggregateInputType
  _max?: MessageAttributeMaxAggregateInputType
}

export type MessageAttributeGroupByOutputType = {
  id: string
  messageId: string
  name: string
  value: string
  _count: MessageAttributeCountAggregateOutputType | null
  _min: MessageAttributeMinAggregateOutputType | null
  _max: MessageAttributeMaxAggregateOutputType | null
}

type GetMessageAttributeGroupByPayload<T extends MessageAttributeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<MessageAttributeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof MessageAttributeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], MessageAttributeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], MessageAttributeGroupByOutputType[P]>
      }
    >
  > 



export type MessageAttributeWhereInput = {
  AND?: Prisma.MessageAttributeWhereInput | Prisma.MessageAttributeWhereInput[]
  OR?: Prisma.MessageAttributeWhereInput[]
  NOT?: Prisma.MessageAttributeWhereInput | Prisma.MessageAttributeWhereInput[]
  id?: Prisma.StringFilter<"MessageAttribute"> | string
  messageId?: Prisma.StringFilter<"MessageAttribute"> | string
  name?: Prisma.StringFilter<"MessageAttribute"> | string
  value?: Prisma.StringFilter<"MessageAttribute"> | string
  message?: Prisma.XOR<Prisma.MessageScalarRelationFilter, Prisma.MessageWhereInput>
}

export type MessageAttributeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  message?: Prisma.MessageOrderByWithRelationInput
}

export type MessageAttributeWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.MessageAttributeWhereInput | Prisma.MessageAttributeWhereInput[]
  OR?: Prisma.MessageAttributeWhereInput[]
  NOT?: Prisma.MessageAttributeWhereInput | Prisma.MessageAttributeWhereInput[]
  messageId?: Prisma.StringFilter<"MessageAttribute"> | string
  name?: Prisma.StringFilter<"MessageAttribute"> | string
  value?: Prisma.StringFilter<"MessageAttribute"> | string
  message?: Prisma.XOR<Prisma.MessageScalarRelationFilter, Prisma.MessageWhereInput>
}, "id">

export type MessageAttributeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  _count?: Prisma.MessageAttributeCountOrderByAggregateInput
  _max?: Prisma.MessageAttributeMaxOrderByAggregateInput
  _min?: Prisma.MessageAttributeMinOrderByAggregateInput
}

export type MessageAttributeScalarWhereWithAggregatesInput = {
  AND?: Prisma.MessageAttributeScalarWhereWithAggregatesInput | Prisma.MessageAttributeScalarWhereWithAggregatesInput[]
  OR?: Prisma.MessageAttributeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.MessageAttributeScalarWhereWithAggregatesInput | Prisma.MessageAttributeScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"MessageAttribute"> | string
  messageId?: Prisma.StringWithAggregatesFilter<"MessageAttribute"> | string
  name?: Prisma.StringWithAggregatesFilter<"MessageAttribute"> | string
  value?: Prisma.StringWithAggregatesFilter<"MessageAttribute"> | string
}

export type MessageAttributeCreateInput = {
  id?: string
  name: string
  value?: string
  message: Prisma.MessageCreateNestedOneWithoutAttributesInput
}

export type MessageAttributeUncheckedCreateInput = {
  id?: string
  messageId: string
  name: string
  value?: string
}

export type MessageAttributeUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.MessageUpdateOneRequiredWithoutAttributesNestedInput
}

export type MessageAttributeUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageAttributeCreateManyInput = {
  id?: string
  messageId: string
  name: string
  value?: string
}

export type MessageAttributeUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageAttributeUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageAttributeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
}

export type MessageAttributeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
}

export type MessageAttributeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
}

export type MessageAttributeListRelationFilter = {
  every?: Prisma.MessageAttributeWhereInput
  some?: Prisma.MessageAttributeWhereInput
  none?: Prisma.MessageAttributeWhereInput
}

export type MessageAttributeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type MessageAttributeCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.MessageAttributeCreateWithoutMessageInput, Prisma.MessageAttributeUncheckedCreateWithoutMessageInput> | Prisma.MessageAttributeCreateWithoutMessageInput[] | Prisma.MessageAttributeUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageAttributeCreateOrConnectWithoutMessageInput | Prisma.MessageAttributeCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.MessageAttributeCreateManyMessageInputEnvelope
  connect?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
}

export type MessageAttributeUncheckedCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.MessageAttributeCreateWithoutMessageInput, Prisma.MessageAttributeUncheckedCreateWithoutMessageInput> | Prisma.MessageAttributeCreateWithoutMessageInput[] | Prisma.MessageAttributeUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageAttributeCreateOrConnectWithoutMessageInput | Prisma.MessageAttributeCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.MessageAttributeCreateManyMessageInputEnvelope
  connect?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
}

export type MessageAttributeUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.MessageAttributeCreateWithoutMessageInput, Prisma.MessageAttributeUncheckedCreateWithoutMessageInput> | Prisma.MessageAttributeCreateWithoutMessageInput[] | Prisma.MessageAttributeUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageAttributeCreateOrConnectWithoutMessageInput | Prisma.MessageAttributeCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.MessageAttributeUpsertWithWhereUniqueWithoutMessageInput | Prisma.MessageAttributeUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.MessageAttributeCreateManyMessageInputEnvelope
  set?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  disconnect?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  delete?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  connect?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  update?: Prisma.MessageAttributeUpdateWithWhereUniqueWithoutMessageInput | Prisma.MessageAttributeUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.MessageAttributeUpdateManyWithWhereWithoutMessageInput | Prisma.MessageAttributeUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.MessageAttributeScalarWhereInput | Prisma.MessageAttributeScalarWhereInput[]
}

export type MessageAttributeUncheckedUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.MessageAttributeCreateWithoutMessageInput, Prisma.MessageAttributeUncheckedCreateWithoutMessageInput> | Prisma.MessageAttributeCreateWithoutMessageInput[] | Prisma.MessageAttributeUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.MessageAttributeCreateOrConnectWithoutMessageInput | Prisma.MessageAttributeCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.MessageAttributeUpsertWithWhereUniqueWithoutMessageInput | Prisma.MessageAttributeUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.MessageAttributeCreateManyMessageInputEnvelope
  set?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  disconnect?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  delete?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  connect?: Prisma.MessageAttributeWhereUniqueInput | Prisma.MessageAttributeWhereUniqueInput[]
  update?: Prisma.MessageAttributeUpdateWithWhereUniqueWithoutMessageInput | Prisma.MessageAttributeUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.MessageAttributeUpdateManyWithWhereWithoutMessageInput | Prisma.MessageAttributeUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.MessageAttributeScalarWhereInput | Prisma.MessageAttributeScalarWhereInput[]
}

export type MessageAttributeCreateWithoutMessageInput = {
  id?: string
  name: string
  value?: string
}

export type MessageAttributeUncheckedCreateWithoutMessageInput = {
  id?: string
  name: string
  value?: string
}

export type MessageAttributeCreateOrConnectWithoutMessageInput = {
  where: Prisma.MessageAttributeWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageAttributeCreateWithoutMessageInput, Prisma.MessageAttributeUncheckedCreateWithoutMessageInput>
}

export type MessageAttributeCreateManyMessageInputEnvelope = {
  data: Prisma.MessageAttributeCreateManyMessageInput | Prisma.MessageAttributeCreateManyMessageInput[]
  skipDuplicates?: boolean
}

export type MessageAttributeUpsertWithWhereUniqueWithoutMessageInput = {
  where: Prisma.MessageAttributeWhereUniqueInput
  update: Prisma.XOR<Prisma.MessageAttributeUpdateWithoutMessageInput, Prisma.MessageAttributeUncheckedUpdateWithoutMessageInput>
  create: Prisma.XOR<Prisma.MessageAttributeCreateWithoutMessageInput, Prisma.MessageAttributeUncheckedCreateWithoutMessageInput>
}

export type MessageAttributeUpdateWithWhereUniqueWithoutMessageInput = {
  where: Prisma.MessageAttributeWhereUniqueInput
  data: Prisma.XOR<Prisma.MessageAttributeUpdateWithoutMessageInput, Prisma.MessageAttributeUncheckedUpdateWithoutMessageInput>
}

export type MessageAttributeUpdateManyWithWhereWithoutMessageInput = {
  where: Prisma.MessageAttributeScalarWhereInput
  data: Prisma.XOR<Prisma.MessageAttributeUpdateManyMutationInput, Prisma.MessageAttributeUncheckedUpdateManyWithoutMessageInput>
}

export type MessageAttributeScalarWhereInput = {
  AND?: Prisma.MessageAttributeScalarWhereInput | Prisma.MessageAttributeScalarWhereInput[]
  OR?: Prisma.MessageAttributeScalarWhereInput[]
  NOT?: Prisma.MessageAttributeScalarWhereInput | Prisma.MessageAttributeScalarWhereInput[]
  id?: Prisma.StringFilter<"MessageAttribute"> | string
  messageId?: Prisma.StringFilter<"MessageAttribute"> | string
  name?: Prisma.StringFilter<"MessageAttribute"> | string
  value?: Prisma.StringFilter<"MessageAttribute"> | string
}

export type MessageAttributeCreateManyMessageInput = {
  id?: string
  name: string
  value?: string
}

export type MessageAttributeUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageAttributeUncheckedUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}

export type MessageAttributeUncheckedUpdateManyWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
}



export type MessageAttributeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}, ExtArgs["result"]["messageAttribute"]>

export type MessageAttributeSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}, ExtArgs["result"]["messageAttribute"]>

export type MessageAttributeSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}, ExtArgs["result"]["messageAttribute"]>

export type MessageAttributeSelectScalar = {
  id?: boolean
  messageId?: boolean
  name?: boolean
  value?: boolean
}

export type MessageAttributeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "messageId" | "name" | "value", ExtArgs["result"]["messageAttribute"]>
export type MessageAttributeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}
export type MessageAttributeIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}
export type MessageAttributeIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
}

export type $MessageAttributePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "MessageAttribute"
  objects: {
    message: Prisma.$MessagePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    messageId: string
    name: string
    value: string
  }, ExtArgs["result"]["messageAttribute"]>
  composites: {}
}

export type MessageAttributeGetPayload<S extends boolean | null | undefined | MessageAttributeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload, S>

export type MessageAttributeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<MessageAttributeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: MessageAttributeCountAggregateInputType | true
  }

export interface MessageAttributeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['MessageAttribute'], meta: { name: 'MessageAttribute' } }
  /**
   * Find zero or one MessageAttribute that matches the filter.
   * @param {MessageAttributeFindUniqueArgs} args - Arguments to find a MessageAttribute
   * @example
   * // Get one MessageAttribute
   * const messageAttribute = await prisma.messageAttribute.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends MessageAttributeFindUniqueArgs>(args: Prisma.SelectSubset<T, MessageAttributeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one MessageAttribute that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {MessageAttributeFindUniqueOrThrowArgs} args - Arguments to find a MessageAttribute
   * @example
   * // Get one MessageAttribute
   * const messageAttribute = await prisma.messageAttribute.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends MessageAttributeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, MessageAttributeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first MessageAttribute that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeFindFirstArgs} args - Arguments to find a MessageAttribute
   * @example
   * // Get one MessageAttribute
   * const messageAttribute = await prisma.messageAttribute.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends MessageAttributeFindFirstArgs>(args?: Prisma.SelectSubset<T, MessageAttributeFindFirstArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first MessageAttribute that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeFindFirstOrThrowArgs} args - Arguments to find a MessageAttribute
   * @example
   * // Get one MessageAttribute
   * const messageAttribute = await prisma.messageAttribute.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends MessageAttributeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, MessageAttributeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more MessageAttributes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all MessageAttributes
   * const messageAttributes = await prisma.messageAttribute.findMany()
   * 
   * // Get first 10 MessageAttributes
   * const messageAttributes = await prisma.messageAttribute.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const messageAttributeWithIdOnly = await prisma.messageAttribute.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends MessageAttributeFindManyArgs>(args?: Prisma.SelectSubset<T, MessageAttributeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a MessageAttribute.
   * @param {MessageAttributeCreateArgs} args - Arguments to create a MessageAttribute.
   * @example
   * // Create one MessageAttribute
   * const MessageAttribute = await prisma.messageAttribute.create({
   *   data: {
   *     // ... data to create a MessageAttribute
   *   }
   * })
   * 
   */
  create<T extends MessageAttributeCreateArgs>(args: Prisma.SelectSubset<T, MessageAttributeCreateArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many MessageAttributes.
   * @param {MessageAttributeCreateManyArgs} args - Arguments to create many MessageAttributes.
   * @example
   * // Create many MessageAttributes
   * const messageAttribute = await prisma.messageAttribute.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends MessageAttributeCreateManyArgs>(args?: Prisma.SelectSubset<T, MessageAttributeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many MessageAttributes and returns the data saved in the database.
   * @param {MessageAttributeCreateManyAndReturnArgs} args - Arguments to create many MessageAttributes.
   * @example
   * // Create many MessageAttributes
   * const messageAttribute = await prisma.messageAttribute.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many MessageAttributes and only return the `id`
   * const messageAttributeWithIdOnly = await prisma.messageAttribute.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends MessageAttributeCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, MessageAttributeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a MessageAttribute.
   * @param {MessageAttributeDeleteArgs} args - Arguments to delete one MessageAttribute.
   * @example
   * // Delete one MessageAttribute
   * const MessageAttribute = await prisma.messageAttribute.delete({
   *   where: {
   *     // ... filter to delete one MessageAttribute
   *   }
   * })
   * 
   */
  delete<T extends MessageAttributeDeleteArgs>(args: Prisma.SelectSubset<T, MessageAttributeDeleteArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one MessageAttribute.
   * @param {MessageAttributeUpdateArgs} args - Arguments to update one MessageAttribute.
   * @example
   * // Update one MessageAttribute
   * const messageAttribute = await prisma.messageAttribute.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends MessageAttributeUpdateArgs>(args: Prisma.SelectSubset<T, MessageAttributeUpdateArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more MessageAttributes.
   * @param {MessageAttributeDeleteManyArgs} args - Arguments to filter MessageAttributes to delete.
   * @example
   * // Delete a few MessageAttributes
   * const { count } = await prisma.messageAttribute.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends MessageAttributeDeleteManyArgs>(args?: Prisma.SelectSubset<T, MessageAttributeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more MessageAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many MessageAttributes
   * const messageAttribute = await prisma.messageAttribute.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends MessageAttributeUpdateManyArgs>(args: Prisma.SelectSubset<T, MessageAttributeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more MessageAttributes and returns the data updated in the database.
   * @param {MessageAttributeUpdateManyAndReturnArgs} args - Arguments to update many MessageAttributes.
   * @example
   * // Update many MessageAttributes
   * const messageAttribute = await prisma.messageAttribute.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more MessageAttributes and only return the `id`
   * const messageAttributeWithIdOnly = await prisma.messageAttribute.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends MessageAttributeUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, MessageAttributeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one MessageAttribute.
   * @param {MessageAttributeUpsertArgs} args - Arguments to update or create a MessageAttribute.
   * @example
   * // Update or create a MessageAttribute
   * const messageAttribute = await prisma.messageAttribute.upsert({
   *   create: {
   *     // ... data to create a MessageAttribute
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the MessageAttribute we want to update
   *   }
   * })
   */
  upsert<T extends MessageAttributeUpsertArgs>(args: Prisma.SelectSubset<T, MessageAttributeUpsertArgs<ExtArgs>>): Prisma.Prisma__MessageAttributeClient<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of MessageAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeCountArgs} args - Arguments to filter MessageAttributes to count.
   * @example
   * // Count the number of MessageAttributes
   * const count = await prisma.messageAttribute.count({
   *   where: {
   *     // ... the filter for the MessageAttributes we want to count
   *   }
   * })
  **/
  count<T extends MessageAttributeCountArgs>(
    args?: Prisma.Subset<T, MessageAttributeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], MessageAttributeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a MessageAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends MessageAttributeAggregateArgs>(args: Prisma.Subset<T, MessageAttributeAggregateArgs>): Prisma.PrismaPromise<GetMessageAttributeAggregateType<T>>

  /**
   * Group by MessageAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAttributeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends MessageAttributeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: MessageAttributeGroupByArgs['orderBy'] }
      : { orderBy?: MessageAttributeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, MessageAttributeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMessageAttributeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the MessageAttribute model
 */
readonly fields: MessageAttributeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for MessageAttribute.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__MessageAttributeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  message<T extends Prisma.MessageDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.MessageDefaultArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the MessageAttribute model
 */
export interface MessageAttributeFieldRefs {
  readonly id: Prisma.FieldRef<"MessageAttribute", 'String'>
  readonly messageId: Prisma.FieldRef<"MessageAttribute", 'String'>
  readonly name: Prisma.FieldRef<"MessageAttribute", 'String'>
  readonly value: Prisma.FieldRef<"MessageAttribute", 'String'>
}
    

// Custom InputTypes
/**
 * MessageAttribute findUnique
 */
export type MessageAttributeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * Filter, which MessageAttribute to fetch.
   */
  where: Prisma.MessageAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute findUniqueOrThrow
 */
export type MessageAttributeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * Filter, which MessageAttribute to fetch.
   */
  where: Prisma.MessageAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute findFirst
 */
export type MessageAttributeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * Filter, which MessageAttribute to fetch.
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageAttributes to fetch.
   */
  orderBy?: Prisma.MessageAttributeOrderByWithRelationInput | Prisma.MessageAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for MessageAttributes.
   */
  cursor?: Prisma.MessageAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of MessageAttributes.
   */
  distinct?: Prisma.MessageAttributeScalarFieldEnum | Prisma.MessageAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute findFirstOrThrow
 */
export type MessageAttributeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * Filter, which MessageAttribute to fetch.
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageAttributes to fetch.
   */
  orderBy?: Prisma.MessageAttributeOrderByWithRelationInput | Prisma.MessageAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for MessageAttributes.
   */
  cursor?: Prisma.MessageAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of MessageAttributes.
   */
  distinct?: Prisma.MessageAttributeScalarFieldEnum | Prisma.MessageAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute findMany
 */
export type MessageAttributeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * Filter, which MessageAttributes to fetch.
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of MessageAttributes to fetch.
   */
  orderBy?: Prisma.MessageAttributeOrderByWithRelationInput | Prisma.MessageAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing MessageAttributes.
   */
  cursor?: Prisma.MessageAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` MessageAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` MessageAttributes.
   */
  skip?: number
  distinct?: Prisma.MessageAttributeScalarFieldEnum | Prisma.MessageAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute create
 */
export type MessageAttributeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * The data needed to create a MessageAttribute.
   */
  data: Prisma.XOR<Prisma.MessageAttributeCreateInput, Prisma.MessageAttributeUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute createMany
 */
export type MessageAttributeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many MessageAttributes.
   */
  data: Prisma.MessageAttributeCreateManyInput | Prisma.MessageAttributeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * MessageAttribute createManyAndReturn
 */
export type MessageAttributeCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * The data used to create many MessageAttributes.
   */
  data: Prisma.MessageAttributeCreateManyInput | Prisma.MessageAttributeCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * MessageAttribute update
 */
export type MessageAttributeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * The data needed to update a MessageAttribute.
   */
  data: Prisma.XOR<Prisma.MessageAttributeUpdateInput, Prisma.MessageAttributeUncheckedUpdateInput>
  /**
   * Choose, which MessageAttribute to update.
   */
  where: Prisma.MessageAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute updateMany
 */
export type MessageAttributeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update MessageAttributes.
   */
  data: Prisma.XOR<Prisma.MessageAttributeUpdateManyMutationInput, Prisma.MessageAttributeUncheckedUpdateManyInput>
  /**
   * Filter which MessageAttributes to update
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * Limit how many MessageAttributes to update.
   */
  limit?: number
}

/**
 * MessageAttribute updateManyAndReturn
 */
export type MessageAttributeUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * The data used to update MessageAttributes.
   */
  data: Prisma.XOR<Prisma.MessageAttributeUpdateManyMutationInput, Prisma.MessageAttributeUncheckedUpdateManyInput>
  /**
   * Filter which MessageAttributes to update
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * Limit how many MessageAttributes to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * MessageAttribute upsert
 */
export type MessageAttributeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * The filter to search for the MessageAttribute to update in case it exists.
   */
  where: Prisma.MessageAttributeWhereUniqueInput
  /**
   * In case the MessageAttribute found by the `where` argument doesn't exist, create a new MessageAttribute with this data.
   */
  create: Prisma.XOR<Prisma.MessageAttributeCreateInput, Prisma.MessageAttributeUncheckedCreateInput>
  /**
   * In case the MessageAttribute was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.MessageAttributeUpdateInput, Prisma.MessageAttributeUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute delete
 */
export type MessageAttributeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  /**
   * Filter which MessageAttribute to delete.
   */
  where: Prisma.MessageAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * MessageAttribute deleteMany
 */
export type MessageAttributeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which MessageAttributes to delete
   */
  where?: Prisma.MessageAttributeWhereInput
  /**
   * Limit how many MessageAttributes to delete.
   */
  limit?: number
}

/**
 * MessageAttribute without action
 */
export type MessageAttributeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
}
