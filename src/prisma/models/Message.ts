
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Message` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Message
 * 
 */
export type MessageModel = runtime.Types.Result.DefaultSelection<Prisma.$MessagePayload>

export type AggregateMessage = {
  _count: MessageCountAggregateOutputType | null
  _min: MessageMinAggregateOutputType | null
  _max: MessageMaxAggregateOutputType | null
}

export type MessageMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  topic: string | null
  importance: string | null
  isNotificationMessage: boolean | null
  conversationId: string | null
  category: string | null
  sendAt: Date | null
  status: $Enums.Status | null
  error: string | null
  title: string | null
  message: string | null
  shortMessage: string | null
  plainText: string | null
  apiUser: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type MessageMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  topic: string | null
  importance: string | null
  isNotificationMessage: boolean | null
  conversationId: string | null
  category: string | null
  sendAt: Date | null
  status: $Enums.Status | null
  error: string | null
  title: string | null
  message: string | null
  shortMessage: string | null
  plainText: string | null
  apiUser: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type MessageCountAggregateOutputType = {
  id: number
  tenantId: number
  recipients: number
  tags: number
  topic: number
  importance: number
  isNotificationMessage: number
  conversationId: number
  category: number
  sendAt: number
  status: number
  error: number
  title: number
  message: number
  shortMessage: number
  plainText: number
  apiUser: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type MessageMinAggregateInputType = {
  id?: true
  tenantId?: true
  topic?: true
  importance?: true
  isNotificationMessage?: true
  conversationId?: true
  category?: true
  sendAt?: true
  status?: true
  error?: true
  title?: true
  message?: true
  shortMessage?: true
  plainText?: true
  apiUser?: true
  createdAt?: true
  updatedAt?: true
}

export type MessageMaxAggregateInputType = {
  id?: true
  tenantId?: true
  topic?: true
  importance?: true
  isNotificationMessage?: true
  conversationId?: true
  category?: true
  sendAt?: true
  status?: true
  error?: true
  title?: true
  message?: true
  shortMessage?: true
  plainText?: true
  apiUser?: true
  createdAt?: true
  updatedAt?: true
}

export type MessageCountAggregateInputType = {
  id?: true
  tenantId?: true
  recipients?: true
  tags?: true
  topic?: true
  importance?: true
  isNotificationMessage?: true
  conversationId?: true
  category?: true
  sendAt?: true
  status?: true
  error?: true
  title?: true
  message?: true
  shortMessage?: true
  plainText?: true
  apiUser?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type MessageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Message to aggregate.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Messages
  **/
  _count?: true | MessageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: MessageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: MessageMaxAggregateInputType
}

export type GetMessageAggregateType<T extends MessageAggregateArgs> = {
      [P in keyof T & keyof AggregateMessage]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateMessage[P]>
    : Prisma.GetScalarType<T[P], AggregateMessage[P]>
}




export type MessageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageWhereInput
  orderBy?: Prisma.MessageOrderByWithAggregationInput | Prisma.MessageOrderByWithAggregationInput[]
  by: Prisma.MessageScalarFieldEnum[] | Prisma.MessageScalarFieldEnum
  having?: Prisma.MessageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: MessageCountAggregateInputType | true
  _min?: MessageMinAggregateInputType
  _max?: MessageMaxAggregateInputType
}

export type MessageGroupByOutputType = {
  id: string
  tenantId: string
  recipients: string[]
  tags: string[]
  topic: string
  importance: string
  isNotificationMessage: boolean
  conversationId: string
  category: string
  sendAt: Date | null
  status: $Enums.Status
  error: string
  title: string
  message: string
  shortMessage: string
  plainText: string
  apiUser: string
  createdAt: Date
  updatedAt: Date
  _count: MessageCountAggregateOutputType | null
  _min: MessageMinAggregateOutputType | null
  _max: MessageMaxAggregateOutputType | null
}

type GetMessageGroupByPayload<T extends MessageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<MessageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof MessageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], MessageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], MessageGroupByOutputType[P]>
      }
    >
  > 



export type MessageWhereInput = {
  AND?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  OR?: Prisma.MessageWhereInput[]
  NOT?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  id?: Prisma.StringFilter<"Message"> | string
  tenantId?: Prisma.StringFilter<"Message"> | string
  recipients?: Prisma.StringNullableListFilter<"Message">
  tags?: Prisma.StringNullableListFilter<"Message">
  topic?: Prisma.StringFilter<"Message"> | string
  importance?: Prisma.StringFilter<"Message"> | string
  isNotificationMessage?: Prisma.BoolFilter<"Message"> | boolean
  conversationId?: Prisma.StringFilter<"Message"> | string
  category?: Prisma.StringFilter<"Message"> | string
  sendAt?: Prisma.DateTimeNullableFilter<"Message"> | Date | string | null
  status?: Prisma.EnumStatusFilter<"Message"> | $Enums.Status
  error?: Prisma.StringFilter<"Message"> | string
  title?: Prisma.StringFilter<"Message"> | string
  message?: Prisma.StringFilter<"Message"> | string
  shortMessage?: Prisma.StringFilter<"Message"> | string
  plainText?: Prisma.StringFilter<"Message"> | string
  apiUser?: Prisma.StringFilter<"Message"> | string
  createdAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  trackedLinks?: Prisma.TrackedLinkListRelationFilter
  recipientMessages?: Prisma.RecipientMessageListRelationFilter
  attributes?: Prisma.MessageAttributeListRelationFilter
  context?: Prisma.MessageContextListRelationFilter
}

export type MessageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipients?: Prisma.SortOrder
  tags?: Prisma.SortOrder
  topic?: Prisma.SortOrder
  importance?: Prisma.SortOrder
  isNotificationMessage?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  category?: Prisma.SortOrder
  sendAt?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  apiUser?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  trackedLinks?: Prisma.TrackedLinkOrderByRelationAggregateInput
  recipientMessages?: Prisma.RecipientMessageOrderByRelationAggregateInput
  attributes?: Prisma.MessageAttributeOrderByRelationAggregateInput
  context?: Prisma.MessageContextOrderByRelationAggregateInput
}

export type MessageWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  OR?: Prisma.MessageWhereInput[]
  NOT?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  tenantId?: Prisma.StringFilter<"Message"> | string
  recipients?: Prisma.StringNullableListFilter<"Message">
  tags?: Prisma.StringNullableListFilter<"Message">
  topic?: Prisma.StringFilter<"Message"> | string
  importance?: Prisma.StringFilter<"Message"> | string
  isNotificationMessage?: Prisma.BoolFilter<"Message"> | boolean
  conversationId?: Prisma.StringFilter<"Message"> | string
  category?: Prisma.StringFilter<"Message"> | string
  sendAt?: Prisma.DateTimeNullableFilter<"Message"> | Date | string | null
  status?: Prisma.EnumStatusFilter<"Message"> | $Enums.Status
  error?: Prisma.StringFilter<"Message"> | string
  title?: Prisma.StringFilter<"Message"> | string
  message?: Prisma.StringFilter<"Message"> | string
  shortMessage?: Prisma.StringFilter<"Message"> | string
  plainText?: Prisma.StringFilter<"Message"> | string
  apiUser?: Prisma.StringFilter<"Message"> | string
  createdAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  trackedLinks?: Prisma.TrackedLinkListRelationFilter
  recipientMessages?: Prisma.RecipientMessageListRelationFilter
  attributes?: Prisma.MessageAttributeListRelationFilter
  context?: Prisma.MessageContextListRelationFilter
}, "id">

export type MessageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipients?: Prisma.SortOrder
  tags?: Prisma.SortOrder
  topic?: Prisma.SortOrder
  importance?: Prisma.SortOrder
  isNotificationMessage?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  category?: Prisma.SortOrder
  sendAt?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  apiUser?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.MessageCountOrderByAggregateInput
  _max?: Prisma.MessageMaxOrderByAggregateInput
  _min?: Prisma.MessageMinOrderByAggregateInput
}

export type MessageScalarWhereWithAggregatesInput = {
  AND?: Prisma.MessageScalarWhereWithAggregatesInput | Prisma.MessageScalarWhereWithAggregatesInput[]
  OR?: Prisma.MessageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.MessageScalarWhereWithAggregatesInput | Prisma.MessageScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Message"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"Message"> | string
  recipients?: Prisma.StringNullableListFilter<"Message">
  tags?: Prisma.StringNullableListFilter<"Message">
  topic?: Prisma.StringWithAggregatesFilter<"Message"> | string
  importance?: Prisma.StringWithAggregatesFilter<"Message"> | string
  isNotificationMessage?: Prisma.BoolWithAggregatesFilter<"Message"> | boolean
  conversationId?: Prisma.StringWithAggregatesFilter<"Message"> | string
  category?: Prisma.StringWithAggregatesFilter<"Message"> | string
  sendAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Message"> | Date | string | null
  status?: Prisma.EnumStatusWithAggregatesFilter<"Message"> | $Enums.Status
  error?: Prisma.StringWithAggregatesFilter<"Message"> | string
  title?: Prisma.StringWithAggregatesFilter<"Message"> | string
  message?: Prisma.StringWithAggregatesFilter<"Message"> | string
  shortMessage?: Prisma.StringWithAggregatesFilter<"Message"> | string
  plainText?: Prisma.StringWithAggregatesFilter<"Message"> | string
  apiUser?: Prisma.StringWithAggregatesFilter<"Message"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Message"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Message"> | Date | string
}

export type MessageCreateInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutMessagesInput
  trackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextCreateNestedManyWithoutMessageInput
}

export type MessageUncheckedCreateInput = {
  id?: string
  tenantId: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeUncheckedCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextUncheckedCreateNestedManyWithoutMessageInput
}

export type MessageUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutMessagesNestedInput
  trackedLinks?: Prisma.TrackedLinkUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUncheckedUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUncheckedUpdateManyWithoutMessageNestedInput
}

export type MessageCreateManyInput = {
  id?: string
  tenantId: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type MessageUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageScalarRelationFilter = {
  is?: Prisma.MessageWhereInput
  isNot?: Prisma.MessageWhereInput
}

export type StringNullableListFilter<$PrismaModel = never> = {
  equals?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  has?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  hasEvery?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  hasSome?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  isEmpty?: boolean
}

export type MessageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  recipients?: Prisma.SortOrder
  tags?: Prisma.SortOrder
  topic?: Prisma.SortOrder
  importance?: Prisma.SortOrder
  isNotificationMessage?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  category?: Prisma.SortOrder
  sendAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  apiUser?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type MessageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  topic?: Prisma.SortOrder
  importance?: Prisma.SortOrder
  isNotificationMessage?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  category?: Prisma.SortOrder
  sendAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  apiUser?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type MessageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  topic?: Prisma.SortOrder
  importance?: Prisma.SortOrder
  isNotificationMessage?: Prisma.SortOrder
  conversationId?: Prisma.SortOrder
  category?: Prisma.SortOrder
  sendAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  shortMessage?: Prisma.SortOrder
  plainText?: Prisma.SortOrder
  apiUser?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type MessageListRelationFilter = {
  every?: Prisma.MessageWhereInput
  some?: Prisma.MessageWhereInput
  none?: Prisma.MessageWhereInput
}

export type MessageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type MessageNullableScalarRelationFilter = {
  is?: Prisma.MessageWhereInput | null
  isNot?: Prisma.MessageWhereInput | null
}

export type MessageCreateNestedOneWithoutAttributesInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutAttributesInput, Prisma.MessageUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutAttributesInput
  connect?: Prisma.MessageWhereUniqueInput
}

export type MessageUpdateOneRequiredWithoutAttributesNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutAttributesInput, Prisma.MessageUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutAttributesInput
  upsert?: Prisma.MessageUpsertWithoutAttributesInput
  connect?: Prisma.MessageWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.MessageUpdateToOneWithWhereWithoutAttributesInput, Prisma.MessageUpdateWithoutAttributesInput>, Prisma.MessageUncheckedUpdateWithoutAttributesInput>
}

export type MessageCreateNestedOneWithoutContextInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutContextInput, Prisma.MessageUncheckedCreateWithoutContextInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutContextInput
  connect?: Prisma.MessageWhereUniqueInput
}

export type MessageUpdateOneRequiredWithoutContextNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutContextInput, Prisma.MessageUncheckedCreateWithoutContextInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutContextInput
  upsert?: Prisma.MessageUpsertWithoutContextInput
  connect?: Prisma.MessageWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.MessageUpdateToOneWithWhereWithoutContextInput, Prisma.MessageUpdateWithoutContextInput>, Prisma.MessageUncheckedUpdateWithoutContextInput>
}

export type MessageCreaterecipientsInput = {
  set: string[]
}

export type MessageCreatetagsInput = {
  set: string[]
}

export type MessageUpdaterecipientsInput = {
  set?: string[]
  push?: string | string[]
}

export type MessageUpdatetagsInput = {
  set?: string[]
  push?: string | string[]
}

export type MessageCreateNestedOneWithoutRecipientMessagesInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutRecipientMessagesInput, Prisma.MessageUncheckedCreateWithoutRecipientMessagesInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutRecipientMessagesInput
  connect?: Prisma.MessageWhereUniqueInput
}

export type MessageUpdateOneRequiredWithoutRecipientMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutRecipientMessagesInput, Prisma.MessageUncheckedCreateWithoutRecipientMessagesInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutRecipientMessagesInput
  upsert?: Prisma.MessageUpsertWithoutRecipientMessagesInput
  connect?: Prisma.MessageWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.MessageUpdateToOneWithWhereWithoutRecipientMessagesInput, Prisma.MessageUpdateWithoutRecipientMessagesInput>, Prisma.MessageUncheckedUpdateWithoutRecipientMessagesInput>
}

export type MessageCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutTenantInput, Prisma.MessageUncheckedCreateWithoutTenantInput> | Prisma.MessageCreateWithoutTenantInput[] | Prisma.MessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutTenantInput | Prisma.MessageCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.MessageCreateManyTenantInputEnvelope
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
}

export type MessageUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutTenantInput, Prisma.MessageUncheckedCreateWithoutTenantInput> | Prisma.MessageCreateWithoutTenantInput[] | Prisma.MessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutTenantInput | Prisma.MessageCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.MessageCreateManyTenantInputEnvelope
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
}

export type MessageUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutTenantInput, Prisma.MessageUncheckedCreateWithoutTenantInput> | Prisma.MessageCreateWithoutTenantInput[] | Prisma.MessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutTenantInput | Prisma.MessageCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.MessageUpsertWithWhereUniqueWithoutTenantInput | Prisma.MessageUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.MessageCreateManyTenantInputEnvelope
  set?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  disconnect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  delete?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  update?: Prisma.MessageUpdateWithWhereUniqueWithoutTenantInput | Prisma.MessageUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.MessageUpdateManyWithWhereWithoutTenantInput | Prisma.MessageUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
}

export type MessageUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutTenantInput, Prisma.MessageUncheckedCreateWithoutTenantInput> | Prisma.MessageCreateWithoutTenantInput[] | Prisma.MessageUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutTenantInput | Prisma.MessageCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.MessageUpsertWithWhereUniqueWithoutTenantInput | Prisma.MessageUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.MessageCreateManyTenantInputEnvelope
  set?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  disconnect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  delete?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  update?: Prisma.MessageUpdateWithWhereUniqueWithoutTenantInput | Prisma.MessageUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.MessageUpdateManyWithWhereWithoutTenantInput | Prisma.MessageUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
}

export type MessageCreateNestedOneWithoutTrackedLinksInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutTrackedLinksInput, Prisma.MessageUncheckedCreateWithoutTrackedLinksInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutTrackedLinksInput
  connect?: Prisma.MessageWhereUniqueInput
}

export type MessageUpdateOneWithoutTrackedLinksNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutTrackedLinksInput, Prisma.MessageUncheckedCreateWithoutTrackedLinksInput>
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutTrackedLinksInput
  upsert?: Prisma.MessageUpsertWithoutTrackedLinksInput
  disconnect?: Prisma.MessageWhereInput | boolean
  delete?: Prisma.MessageWhereInput | boolean
  connect?: Prisma.MessageWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.MessageUpdateToOneWithWhereWithoutTrackedLinksInput, Prisma.MessageUpdateWithoutTrackedLinksInput>, Prisma.MessageUncheckedUpdateWithoutTrackedLinksInput>
}

export type MessageCreateWithoutAttributesInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutMessagesInput
  trackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextCreateNestedManyWithoutMessageInput
}

export type MessageUncheckedCreateWithoutAttributesInput = {
  id?: string
  tenantId: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextUncheckedCreateNestedManyWithoutMessageInput
}

export type MessageCreateOrConnectWithoutAttributesInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutAttributesInput, Prisma.MessageUncheckedCreateWithoutAttributesInput>
}

export type MessageUpsertWithoutAttributesInput = {
  update: Prisma.XOR<Prisma.MessageUpdateWithoutAttributesInput, Prisma.MessageUncheckedUpdateWithoutAttributesInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutAttributesInput, Prisma.MessageUncheckedCreateWithoutAttributesInput>
  where?: Prisma.MessageWhereInput
}

export type MessageUpdateToOneWithWhereWithoutAttributesInput = {
  where?: Prisma.MessageWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutAttributesInput, Prisma.MessageUncheckedUpdateWithoutAttributesInput>
}

export type MessageUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutMessagesNestedInput
  trackedLinks?: Prisma.TrackedLinkUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUncheckedUpdateManyWithoutMessageNestedInput
}

export type MessageCreateWithoutContextInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutMessagesInput
  trackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeCreateNestedManyWithoutMessageInput
}

export type MessageUncheckedCreateWithoutContextInput = {
  id?: string
  tenantId: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeUncheckedCreateNestedManyWithoutMessageInput
}

export type MessageCreateOrConnectWithoutContextInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutContextInput, Prisma.MessageUncheckedCreateWithoutContextInput>
}

export type MessageUpsertWithoutContextInput = {
  update: Prisma.XOR<Prisma.MessageUpdateWithoutContextInput, Prisma.MessageUncheckedUpdateWithoutContextInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutContextInput, Prisma.MessageUncheckedCreateWithoutContextInput>
  where?: Prisma.MessageWhereInput
}

export type MessageUpdateToOneWithWhereWithoutContextInput = {
  where?: Prisma.MessageWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutContextInput, Prisma.MessageUncheckedUpdateWithoutContextInput>
}

export type MessageUpdateWithoutContextInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutMessagesNestedInput
  trackedLinks?: Prisma.TrackedLinkUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateWithoutContextInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUncheckedUpdateManyWithoutMessageNestedInput
}

export type MessageCreateWithoutRecipientMessagesInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutMessagesInput
  trackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextCreateNestedManyWithoutMessageInput
}

export type MessageUncheckedCreateWithoutRecipientMessagesInput = {
  id?: string
  tenantId: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeUncheckedCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextUncheckedCreateNestedManyWithoutMessageInput
}

export type MessageCreateOrConnectWithoutRecipientMessagesInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutRecipientMessagesInput, Prisma.MessageUncheckedCreateWithoutRecipientMessagesInput>
}

export type MessageUpsertWithoutRecipientMessagesInput = {
  update: Prisma.XOR<Prisma.MessageUpdateWithoutRecipientMessagesInput, Prisma.MessageUncheckedUpdateWithoutRecipientMessagesInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutRecipientMessagesInput, Prisma.MessageUncheckedCreateWithoutRecipientMessagesInput>
  where?: Prisma.MessageWhereInput
}

export type MessageUpdateToOneWithWhereWithoutRecipientMessagesInput = {
  where?: Prisma.MessageWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutRecipientMessagesInput, Prisma.MessageUncheckedUpdateWithoutRecipientMessagesInput>
}

export type MessageUpdateWithoutRecipientMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutMessagesNestedInput
  trackedLinks?: Prisma.TrackedLinkUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateWithoutRecipientMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUncheckedUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUncheckedUpdateManyWithoutMessageNestedInput
}

export type MessageCreateWithoutTenantInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackedLinks?: Prisma.TrackedLinkCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextCreateNestedManyWithoutMessageInput
}

export type MessageUncheckedCreateWithoutTenantInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedCreateNestedManyWithoutMessageInput
  recipientMessages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeUncheckedCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextUncheckedCreateNestedManyWithoutMessageInput
}

export type MessageCreateOrConnectWithoutTenantInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutTenantInput, Prisma.MessageUncheckedCreateWithoutTenantInput>
}

export type MessageCreateManyTenantInputEnvelope = {
  data: Prisma.MessageCreateManyTenantInput | Prisma.MessageCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type MessageUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.MessageWhereUniqueInput
  update: Prisma.XOR<Prisma.MessageUpdateWithoutTenantInput, Prisma.MessageUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutTenantInput, Prisma.MessageUncheckedCreateWithoutTenantInput>
}

export type MessageUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.MessageWhereUniqueInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutTenantInput, Prisma.MessageUncheckedUpdateWithoutTenantInput>
}

export type MessageUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.MessageScalarWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateManyMutationInput, Prisma.MessageUncheckedUpdateManyWithoutTenantInput>
}

export type MessageScalarWhereInput = {
  AND?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
  OR?: Prisma.MessageScalarWhereInput[]
  NOT?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
  id?: Prisma.StringFilter<"Message"> | string
  tenantId?: Prisma.StringFilter<"Message"> | string
  recipients?: Prisma.StringNullableListFilter<"Message">
  tags?: Prisma.StringNullableListFilter<"Message">
  topic?: Prisma.StringFilter<"Message"> | string
  importance?: Prisma.StringFilter<"Message"> | string
  isNotificationMessage?: Prisma.BoolFilter<"Message"> | boolean
  conversationId?: Prisma.StringFilter<"Message"> | string
  category?: Prisma.StringFilter<"Message"> | string
  sendAt?: Prisma.DateTimeNullableFilter<"Message"> | Date | string | null
  status?: Prisma.EnumStatusFilter<"Message"> | $Enums.Status
  error?: Prisma.StringFilter<"Message"> | string
  title?: Prisma.StringFilter<"Message"> | string
  message?: Prisma.StringFilter<"Message"> | string
  shortMessage?: Prisma.StringFilter<"Message"> | string
  plainText?: Prisma.StringFilter<"Message"> | string
  apiUser?: Prisma.StringFilter<"Message"> | string
  createdAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Message"> | Date | string
}

export type MessageCreateWithoutTrackedLinksInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutMessagesInput
  recipientMessages?: Prisma.RecipientMessageCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextCreateNestedManyWithoutMessageInput
}

export type MessageUncheckedCreateWithoutTrackedLinksInput = {
  id?: string
  tenantId: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  recipientMessages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutMessageInput
  attributes?: Prisma.MessageAttributeUncheckedCreateNestedManyWithoutMessageInput
  context?: Prisma.MessageContextUncheckedCreateNestedManyWithoutMessageInput
}

export type MessageCreateOrConnectWithoutTrackedLinksInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutTrackedLinksInput, Prisma.MessageUncheckedCreateWithoutTrackedLinksInput>
}

export type MessageUpsertWithoutTrackedLinksInput = {
  update: Prisma.XOR<Prisma.MessageUpdateWithoutTrackedLinksInput, Prisma.MessageUncheckedUpdateWithoutTrackedLinksInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutTrackedLinksInput, Prisma.MessageUncheckedCreateWithoutTrackedLinksInput>
  where?: Prisma.MessageWhereInput
}

export type MessageUpdateToOneWithWhereWithoutTrackedLinksInput = {
  where?: Prisma.MessageWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutTrackedLinksInput, Prisma.MessageUncheckedUpdateWithoutTrackedLinksInput>
}

export type MessageUpdateWithoutTrackedLinksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutMessagesNestedInput
  recipientMessages?: Prisma.RecipientMessageUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateWithoutTrackedLinksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipientMessages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUncheckedUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUncheckedUpdateManyWithoutMessageNestedInput
}

export type MessageCreateManyTenantInput = {
  id?: string
  recipients?: Prisma.MessageCreaterecipientsInput | string[]
  tags?: Prisma.MessageCreatetagsInput | string[]
  topic: string
  importance?: string
  isNotificationMessage?: boolean
  conversationId?: string
  category?: string
  sendAt?: Date | string | null
  status?: $Enums.Status
  error?: string
  title?: string
  message?: string
  shortMessage?: string
  plainText?: string
  apiUser?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type MessageUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackedLinks?: Prisma.TrackedLinkUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackedLinks?: Prisma.TrackedLinkUncheckedUpdateManyWithoutMessageNestedInput
  recipientMessages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutMessageNestedInput
  attributes?: Prisma.MessageAttributeUncheckedUpdateManyWithoutMessageNestedInput
  context?: Prisma.MessageContextUncheckedUpdateManyWithoutMessageNestedInput
}

export type MessageUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipients?: Prisma.MessageUpdaterecipientsInput | string[]
  tags?: Prisma.MessageUpdatetagsInput | string[]
  topic?: Prisma.StringFieldUpdateOperationsInput | string
  importance?: Prisma.StringFieldUpdateOperationsInput | string
  isNotificationMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  conversationId?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.StringFieldUpdateOperationsInput | string
  sendAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  shortMessage?: Prisma.StringFieldUpdateOperationsInput | string
  plainText?: Prisma.StringFieldUpdateOperationsInput | string
  apiUser?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type MessageCountOutputType
 */

export type MessageCountOutputType = {
  trackedLinks: number
  recipientMessages: number
  attributes: number
  context: number
}

export type MessageCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  trackedLinks?: boolean | MessageCountOutputTypeCountTrackedLinksArgs
  recipientMessages?: boolean | MessageCountOutputTypeCountRecipientMessagesArgs
  attributes?: boolean | MessageCountOutputTypeCountAttributesArgs
  context?: boolean | MessageCountOutputTypeCountContextArgs
}

/**
 * MessageCountOutputType without action
 */
export type MessageCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageCountOutputType
   */
  select?: Prisma.MessageCountOutputTypeSelect<ExtArgs> | null
}

/**
 * MessageCountOutputType without action
 */
export type MessageCountOutputTypeCountTrackedLinksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackedLinkWhereInput
}

/**
 * MessageCountOutputType without action
 */
export type MessageCountOutputTypeCountRecipientMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientMessageWhereInput
}

/**
 * MessageCountOutputType without action
 */
export type MessageCountOutputTypeCountAttributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageAttributeWhereInput
}

/**
 * MessageCountOutputType without action
 */
export type MessageCountOutputTypeCountContextArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageContextWhereInput
}


export type MessageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  recipients?: boolean
  tags?: boolean
  topic?: boolean
  importance?: boolean
  isNotificationMessage?: boolean
  conversationId?: boolean
  category?: boolean
  sendAt?: boolean
  status?: boolean
  error?: boolean
  title?: boolean
  message?: boolean
  shortMessage?: boolean
  plainText?: boolean
  apiUser?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  trackedLinks?: boolean | Prisma.Message$trackedLinksArgs<ExtArgs>
  recipientMessages?: boolean | Prisma.Message$recipientMessagesArgs<ExtArgs>
  attributes?: boolean | Prisma.Message$attributesArgs<ExtArgs>
  context?: boolean | Prisma.Message$contextArgs<ExtArgs>
  _count?: boolean | Prisma.MessageCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["message"]>

export type MessageSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  recipients?: boolean
  tags?: boolean
  topic?: boolean
  importance?: boolean
  isNotificationMessage?: boolean
  conversationId?: boolean
  category?: boolean
  sendAt?: boolean
  status?: boolean
  error?: boolean
  title?: boolean
  message?: boolean
  shortMessage?: boolean
  plainText?: boolean
  apiUser?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["message"]>

export type MessageSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  recipients?: boolean
  tags?: boolean
  topic?: boolean
  importance?: boolean
  isNotificationMessage?: boolean
  conversationId?: boolean
  category?: boolean
  sendAt?: boolean
  status?: boolean
  error?: boolean
  title?: boolean
  message?: boolean
  shortMessage?: boolean
  plainText?: boolean
  apiUser?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["message"]>

export type MessageSelectScalar = {
  id?: boolean
  tenantId?: boolean
  recipients?: boolean
  tags?: boolean
  topic?: boolean
  importance?: boolean
  isNotificationMessage?: boolean
  conversationId?: boolean
  category?: boolean
  sendAt?: boolean
  status?: boolean
  error?: boolean
  title?: boolean
  message?: boolean
  shortMessage?: boolean
  plainText?: boolean
  apiUser?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type MessageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "recipients" | "tags" | "topic" | "importance" | "isNotificationMessage" | "conversationId" | "category" | "sendAt" | "status" | "error" | "title" | "message" | "shortMessage" | "plainText" | "apiUser" | "createdAt" | "updatedAt", ExtArgs["result"]["message"]>
export type MessageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  trackedLinks?: boolean | Prisma.Message$trackedLinksArgs<ExtArgs>
  recipientMessages?: boolean | Prisma.Message$recipientMessagesArgs<ExtArgs>
  attributes?: boolean | Prisma.Message$attributesArgs<ExtArgs>
  context?: boolean | Prisma.Message$contextArgs<ExtArgs>
  _count?: boolean | Prisma.MessageCountOutputTypeDefaultArgs<ExtArgs>
}
export type MessageIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type MessageIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $MessagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Message"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    trackedLinks: Prisma.$TrackedLinkPayload<ExtArgs>[]
    recipientMessages: Prisma.$RecipientMessagePayload<ExtArgs>[]
    attributes: Prisma.$MessageAttributePayload<ExtArgs>[]
    context: Prisma.$MessageContextPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    recipients: string[]
    tags: string[]
    topic: string
    importance: string
    isNotificationMessage: boolean
    conversationId: string
    category: string
    sendAt: Date | null
    status: $Enums.Status
    error: string
    title: string
    message: string
    shortMessage: string
    plainText: string
    apiUser: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["message"]>
  composites: {}
}

export type MessageGetPayload<S extends boolean | null | undefined | MessageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$MessagePayload, S>

export type MessageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<MessageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: MessageCountAggregateInputType | true
  }

export interface MessageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Message'], meta: { name: 'Message' } }
  /**
   * Find zero or one Message that matches the filter.
   * @param {MessageFindUniqueArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends MessageFindUniqueArgs>(args: Prisma.SelectSubset<T, MessageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Message that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {MessageFindUniqueOrThrowArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends MessageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, MessageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Message that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageFindFirstArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends MessageFindFirstArgs>(args?: Prisma.SelectSubset<T, MessageFindFirstArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Message that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageFindFirstOrThrowArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends MessageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, MessageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Messages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Messages
   * const messages = await prisma.message.findMany()
   * 
   * // Get first 10 Messages
   * const messages = await prisma.message.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const messageWithIdOnly = await prisma.message.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends MessageFindManyArgs>(args?: Prisma.SelectSubset<T, MessageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Message.
   * @param {MessageCreateArgs} args - Arguments to create a Message.
   * @example
   * // Create one Message
   * const Message = await prisma.message.create({
   *   data: {
   *     // ... data to create a Message
   *   }
   * })
   * 
   */
  create<T extends MessageCreateArgs>(args: Prisma.SelectSubset<T, MessageCreateArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Messages.
   * @param {MessageCreateManyArgs} args - Arguments to create many Messages.
   * @example
   * // Create many Messages
   * const message = await prisma.message.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends MessageCreateManyArgs>(args?: Prisma.SelectSubset<T, MessageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Messages and returns the data saved in the database.
   * @param {MessageCreateManyAndReturnArgs} args - Arguments to create many Messages.
   * @example
   * // Create many Messages
   * const message = await prisma.message.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Messages and only return the `id`
   * const messageWithIdOnly = await prisma.message.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends MessageCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, MessageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Message.
   * @param {MessageDeleteArgs} args - Arguments to delete one Message.
   * @example
   * // Delete one Message
   * const Message = await prisma.message.delete({
   *   where: {
   *     // ... filter to delete one Message
   *   }
   * })
   * 
   */
  delete<T extends MessageDeleteArgs>(args: Prisma.SelectSubset<T, MessageDeleteArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Message.
   * @param {MessageUpdateArgs} args - Arguments to update one Message.
   * @example
   * // Update one Message
   * const message = await prisma.message.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends MessageUpdateArgs>(args: Prisma.SelectSubset<T, MessageUpdateArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Messages.
   * @param {MessageDeleteManyArgs} args - Arguments to filter Messages to delete.
   * @example
   * // Delete a few Messages
   * const { count } = await prisma.message.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends MessageDeleteManyArgs>(args?: Prisma.SelectSubset<T, MessageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Messages
   * const message = await prisma.message.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends MessageUpdateManyArgs>(args: Prisma.SelectSubset<T, MessageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Messages and returns the data updated in the database.
   * @param {MessageUpdateManyAndReturnArgs} args - Arguments to update many Messages.
   * @example
   * // Update many Messages
   * const message = await prisma.message.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Messages and only return the `id`
   * const messageWithIdOnly = await prisma.message.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends MessageUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, MessageUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Message.
   * @param {MessageUpsertArgs} args - Arguments to update or create a Message.
   * @example
   * // Update or create a Message
   * const message = await prisma.message.upsert({
   *   create: {
   *     // ... data to create a Message
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Message we want to update
   *   }
   * })
   */
  upsert<T extends MessageUpsertArgs>(args: Prisma.SelectSubset<T, MessageUpsertArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageCountArgs} args - Arguments to filter Messages to count.
   * @example
   * // Count the number of Messages
   * const count = await prisma.message.count({
   *   where: {
   *     // ... the filter for the Messages we want to count
   *   }
   * })
  **/
  count<T extends MessageCountArgs>(
    args?: Prisma.Subset<T, MessageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], MessageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends MessageAggregateArgs>(args: Prisma.Subset<T, MessageAggregateArgs>): Prisma.PrismaPromise<GetMessageAggregateType<T>>

  /**
   * Group by Message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends MessageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: MessageGroupByArgs['orderBy'] }
      : { orderBy?: MessageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, MessageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMessageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Message model
 */
readonly fields: MessageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Message.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__MessageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  trackedLinks<T extends Prisma.Message$trackedLinksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Message$trackedLinksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackedLinkPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  recipientMessages<T extends Prisma.Message$recipientMessagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Message$recipientMessagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  attributes<T extends Prisma.Message$attributesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Message$attributesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  context<T extends Prisma.Message$contextArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Message$contextArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessageContextPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Message model
 */
export interface MessageFieldRefs {
  readonly id: Prisma.FieldRef<"Message", 'String'>
  readonly tenantId: Prisma.FieldRef<"Message", 'String'>
  readonly recipients: Prisma.FieldRef<"Message", 'String[]'>
  readonly tags: Prisma.FieldRef<"Message", 'String[]'>
  readonly topic: Prisma.FieldRef<"Message", 'String'>
  readonly importance: Prisma.FieldRef<"Message", 'String'>
  readonly isNotificationMessage: Prisma.FieldRef<"Message", 'Boolean'>
  readonly conversationId: Prisma.FieldRef<"Message", 'String'>
  readonly category: Prisma.FieldRef<"Message", 'String'>
  readonly sendAt: Prisma.FieldRef<"Message", 'DateTime'>
  readonly status: Prisma.FieldRef<"Message", 'Status'>
  readonly error: Prisma.FieldRef<"Message", 'String'>
  readonly title: Prisma.FieldRef<"Message", 'String'>
  readonly message: Prisma.FieldRef<"Message", 'String'>
  readonly shortMessage: Prisma.FieldRef<"Message", 'String'>
  readonly plainText: Prisma.FieldRef<"Message", 'String'>
  readonly apiUser: Prisma.FieldRef<"Message", 'String'>
  readonly createdAt: Prisma.FieldRef<"Message", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Message", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Message findUnique
 */
export type MessageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where: Prisma.MessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message findUniqueOrThrow
 */
export type MessageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where: Prisma.MessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message findFirst
 */
export type MessageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Messages.
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Messages.
   */
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message findFirstOrThrow
 */
export type MessageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Messages.
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Messages.
   */
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message findMany
 */
export type MessageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Messages to fetch.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Messages.
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message create
 */
export type MessageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * The data needed to create a Message.
   */
  data: Prisma.XOR<Prisma.MessageCreateInput, Prisma.MessageUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message createMany
 */
export type MessageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Messages.
   */
  data: Prisma.MessageCreateManyInput | Prisma.MessageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Message createManyAndReturn
 */
export type MessageCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * The data used to create many Messages.
   */
  data: Prisma.MessageCreateManyInput | Prisma.MessageCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Message update
 */
export type MessageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * The data needed to update a Message.
   */
  data: Prisma.XOR<Prisma.MessageUpdateInput, Prisma.MessageUncheckedUpdateInput>
  /**
   * Choose, which Message to update.
   */
  where: Prisma.MessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message updateMany
 */
export type MessageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Messages.
   */
  data: Prisma.XOR<Prisma.MessageUpdateManyMutationInput, Prisma.MessageUncheckedUpdateManyInput>
  /**
   * Filter which Messages to update
   */
  where?: Prisma.MessageWhereInput
  /**
   * Limit how many Messages to update.
   */
  limit?: number
}

/**
 * Message updateManyAndReturn
 */
export type MessageUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * The data used to update Messages.
   */
  data: Prisma.XOR<Prisma.MessageUpdateManyMutationInput, Prisma.MessageUncheckedUpdateManyInput>
  /**
   * Filter which Messages to update
   */
  where?: Prisma.MessageWhereInput
  /**
   * Limit how many Messages to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Message upsert
 */
export type MessageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * The filter to search for the Message to update in case it exists.
   */
  where: Prisma.MessageWhereUniqueInput
  /**
   * In case the Message found by the `where` argument doesn't exist, create a new Message with this data.
   */
  create: Prisma.XOR<Prisma.MessageCreateInput, Prisma.MessageUncheckedCreateInput>
  /**
   * In case the Message was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.MessageUpdateInput, Prisma.MessageUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message delete
 */
export type MessageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter which Message to delete.
   */
  where: Prisma.MessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Message deleteMany
 */
export type MessageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Messages to delete
   */
  where?: Prisma.MessageWhereInput
  /**
   * Limit how many Messages to delete.
   */
  limit?: number
}

/**
 * Message.trackedLinks
 */
export type Message$trackedLinksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackedLink
   */
  select?: Prisma.TrackedLinkSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackedLink
   */
  omit?: Prisma.TrackedLinkOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackedLinkInclude<ExtArgs> | null
  where?: Prisma.TrackedLinkWhereInput
  orderBy?: Prisma.TrackedLinkOrderByWithRelationInput | Prisma.TrackedLinkOrderByWithRelationInput[]
  cursor?: Prisma.TrackedLinkWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackedLinkScalarFieldEnum | Prisma.TrackedLinkScalarFieldEnum[]
}

/**
 * Message.recipientMessages
 */
export type Message$recipientMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  where?: Prisma.RecipientMessageWhereInput
  orderBy?: Prisma.RecipientMessageOrderByWithRelationInput | Prisma.RecipientMessageOrderByWithRelationInput[]
  cursor?: Prisma.RecipientMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RecipientMessageScalarFieldEnum | Prisma.RecipientMessageScalarFieldEnum[]
}

/**
 * Message.attributes
 */
export type Message$attributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageAttribute
   */
  select?: Prisma.MessageAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageAttribute
   */
  omit?: Prisma.MessageAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageAttributeInclude<ExtArgs> | null
  where?: Prisma.MessageAttributeWhereInput
  orderBy?: Prisma.MessageAttributeOrderByWithRelationInput | Prisma.MessageAttributeOrderByWithRelationInput[]
  cursor?: Prisma.MessageAttributeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MessageAttributeScalarFieldEnum | Prisma.MessageAttributeScalarFieldEnum[]
}

/**
 * Message.context
 */
export type Message$contextArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MessageContext
   */
  select?: Prisma.MessageContextSelect<ExtArgs> | null
  /**
   * Omit specific fields from the MessageContext
   */
  omit?: Prisma.MessageContextOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageContextInclude<ExtArgs> | null
  where?: Prisma.MessageContextWhereInput
  orderBy?: Prisma.MessageContextOrderByWithRelationInput | Prisma.MessageContextOrderByWithRelationInput[]
  cursor?: Prisma.MessageContextWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MessageContextScalarFieldEnum | Prisma.MessageContextScalarFieldEnum[]
}

/**
 * Message without action
 */
export type MessageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
}
