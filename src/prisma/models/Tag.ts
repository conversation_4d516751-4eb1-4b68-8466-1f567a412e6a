
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Tag` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Tag
 * 
 */
export type TagModel = runtime.Types.Result.DefaultSelection<Prisma.$TagPayload>

export type AggregateTag = {
  _count: TagCountAggregateOutputType | null
  _min: TagMinAggregateOutputType | null
  _max: TagMaxAggregateOutputType | null
}

export type TagMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  displayName: string | null
  description: string | null
  enabled: boolean | null
  selectable: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TagMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  externalId: string | null
  displayName: string | null
  description: string | null
  enabled: boolean | null
  selectable: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TagCountAggregateOutputType = {
  id: number
  tenantId: number
  externalId: number
  displayName: number
  description: number
  enabled: number
  selectable: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type TagMinAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  enabled?: true
  selectable?: true
  createdAt?: true
  updatedAt?: true
}

export type TagMaxAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  enabled?: true
  selectable?: true
  createdAt?: true
  updatedAt?: true
}

export type TagCountAggregateInputType = {
  id?: true
  tenantId?: true
  externalId?: true
  displayName?: true
  description?: true
  enabled?: true
  selectable?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type TagAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Tag to aggregate.
   */
  where?: Prisma.TagWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tags to fetch.
   */
  orderBy?: Prisma.TagOrderByWithRelationInput | Prisma.TagOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TagWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tags from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tags.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Tags
  **/
  _count?: true | TagCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TagMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TagMaxAggregateInputType
}

export type GetTagAggregateType<T extends TagAggregateArgs> = {
      [P in keyof T & keyof AggregateTag]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTag[P]>
    : Prisma.GetScalarType<T[P], AggregateTag[P]>
}




export type TagGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TagWhereInput
  orderBy?: Prisma.TagOrderByWithAggregationInput | Prisma.TagOrderByWithAggregationInput[]
  by: Prisma.TagScalarFieldEnum[] | Prisma.TagScalarFieldEnum
  having?: Prisma.TagScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TagCountAggregateInputType | true
  _min?: TagMinAggregateInputType
  _max?: TagMaxAggregateInputType
}

export type TagGroupByOutputType = {
  id: string
  tenantId: string
  externalId: string | null
  displayName: string
  description: string | null
  enabled: boolean
  selectable: boolean
  createdAt: Date
  updatedAt: Date
  _count: TagCountAggregateOutputType | null
  _min: TagMinAggregateOutputType | null
  _max: TagMaxAggregateOutputType | null
}

type GetTagGroupByPayload<T extends TagGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TagGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TagGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TagGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TagGroupByOutputType[P]>
      }
    >
  > 



export type TagWhereInput = {
  AND?: Prisma.TagWhereInput | Prisma.TagWhereInput[]
  OR?: Prisma.TagWhereInput[]
  NOT?: Prisma.TagWhereInput | Prisma.TagWhereInput[]
  id?: Prisma.StringFilter<"Tag"> | string
  tenantId?: Prisma.StringFilter<"Tag"> | string
  externalId?: Prisma.StringNullableFilter<"Tag"> | string | null
  displayName?: Prisma.StringFilter<"Tag"> | string
  description?: Prisma.StringNullableFilter<"Tag"> | string | null
  enabled?: Prisma.BoolFilter<"Tag"> | boolean
  selectable?: Prisma.BoolFilter<"Tag"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Tag"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Tag"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  recipients?: Prisma.RecipientListRelationFilter
}

export type TagOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrderInput | Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  enabled?: Prisma.SortOrder
  selectable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  recipients?: Prisma.RecipientOrderByRelationAggregateInput
}

export type TagWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TagWhereInput | Prisma.TagWhereInput[]
  OR?: Prisma.TagWhereInput[]
  NOT?: Prisma.TagWhereInput | Prisma.TagWhereInput[]
  tenantId?: Prisma.StringFilter<"Tag"> | string
  externalId?: Prisma.StringNullableFilter<"Tag"> | string | null
  displayName?: Prisma.StringFilter<"Tag"> | string
  description?: Prisma.StringNullableFilter<"Tag"> | string | null
  enabled?: Prisma.BoolFilter<"Tag"> | boolean
  selectable?: Prisma.BoolFilter<"Tag"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Tag"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Tag"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  recipients?: Prisma.RecipientListRelationFilter
}, "id">

export type TagOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrderInput | Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  enabled?: Prisma.SortOrder
  selectable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.TagCountOrderByAggregateInput
  _max?: Prisma.TagMaxOrderByAggregateInput
  _min?: Prisma.TagMinOrderByAggregateInput
}

export type TagScalarWhereWithAggregatesInput = {
  AND?: Prisma.TagScalarWhereWithAggregatesInput | Prisma.TagScalarWhereWithAggregatesInput[]
  OR?: Prisma.TagScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TagScalarWhereWithAggregatesInput | Prisma.TagScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Tag"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"Tag"> | string
  externalId?: Prisma.StringNullableWithAggregatesFilter<"Tag"> | string | null
  displayName?: Prisma.StringWithAggregatesFilter<"Tag"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"Tag"> | string | null
  enabled?: Prisma.BoolWithAggregatesFilter<"Tag"> | boolean
  selectable?: Prisma.BoolWithAggregatesFilter<"Tag"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Tag"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Tag"> | Date | string
}

export type TagCreateInput = {
  id?: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTagsInput
  recipients?: Prisma.RecipientCreateNestedManyWithoutTagsInput
}

export type TagUncheckedCreateInput = {
  id?: string
  tenantId: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTagsInput
}

export type TagUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTagsNestedInput
  recipients?: Prisma.RecipientUpdateManyWithoutTagsNestedInput
}

export type TagUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTagsNestedInput
}

export type TagCreateManyInput = {
  id?: string
  tenantId: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TagUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TagUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TagListRelationFilter = {
  every?: Prisma.TagWhereInput
  some?: Prisma.TagWhereInput
  none?: Prisma.TagWhereInput
}

export type TagOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TagCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  selectable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TagMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  selectable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TagMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  externalId?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  selectable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TagCreateNestedManyWithoutRecipientsInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutRecipientsInput, Prisma.TagUncheckedCreateWithoutRecipientsInput> | Prisma.TagCreateWithoutRecipientsInput[] | Prisma.TagUncheckedCreateWithoutRecipientsInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutRecipientsInput | Prisma.TagCreateOrConnectWithoutRecipientsInput[]
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
}

export type TagUncheckedCreateNestedManyWithoutRecipientsInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutRecipientsInput, Prisma.TagUncheckedCreateWithoutRecipientsInput> | Prisma.TagCreateWithoutRecipientsInput[] | Prisma.TagUncheckedCreateWithoutRecipientsInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutRecipientsInput | Prisma.TagCreateOrConnectWithoutRecipientsInput[]
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
}

export type TagUpdateManyWithoutRecipientsNestedInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutRecipientsInput, Prisma.TagUncheckedCreateWithoutRecipientsInput> | Prisma.TagCreateWithoutRecipientsInput[] | Prisma.TagUncheckedCreateWithoutRecipientsInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutRecipientsInput | Prisma.TagCreateOrConnectWithoutRecipientsInput[]
  upsert?: Prisma.TagUpsertWithWhereUniqueWithoutRecipientsInput | Prisma.TagUpsertWithWhereUniqueWithoutRecipientsInput[]
  set?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  disconnect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  delete?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  update?: Prisma.TagUpdateWithWhereUniqueWithoutRecipientsInput | Prisma.TagUpdateWithWhereUniqueWithoutRecipientsInput[]
  updateMany?: Prisma.TagUpdateManyWithWhereWithoutRecipientsInput | Prisma.TagUpdateManyWithWhereWithoutRecipientsInput[]
  deleteMany?: Prisma.TagScalarWhereInput | Prisma.TagScalarWhereInput[]
}

export type TagUncheckedUpdateManyWithoutRecipientsNestedInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutRecipientsInput, Prisma.TagUncheckedCreateWithoutRecipientsInput> | Prisma.TagCreateWithoutRecipientsInput[] | Prisma.TagUncheckedCreateWithoutRecipientsInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutRecipientsInput | Prisma.TagCreateOrConnectWithoutRecipientsInput[]
  upsert?: Prisma.TagUpsertWithWhereUniqueWithoutRecipientsInput | Prisma.TagUpsertWithWhereUniqueWithoutRecipientsInput[]
  set?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  disconnect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  delete?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  update?: Prisma.TagUpdateWithWhereUniqueWithoutRecipientsInput | Prisma.TagUpdateWithWhereUniqueWithoutRecipientsInput[]
  updateMany?: Prisma.TagUpdateManyWithWhereWithoutRecipientsInput | Prisma.TagUpdateManyWithWhereWithoutRecipientsInput[]
  deleteMany?: Prisma.TagScalarWhereInput | Prisma.TagScalarWhereInput[]
}

export type TagCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutTenantInput, Prisma.TagUncheckedCreateWithoutTenantInput> | Prisma.TagCreateWithoutTenantInput[] | Prisma.TagUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutTenantInput | Prisma.TagCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TagCreateManyTenantInputEnvelope
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
}

export type TagUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutTenantInput, Prisma.TagUncheckedCreateWithoutTenantInput> | Prisma.TagCreateWithoutTenantInput[] | Prisma.TagUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutTenantInput | Prisma.TagCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.TagCreateManyTenantInputEnvelope
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
}

export type TagUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutTenantInput, Prisma.TagUncheckedCreateWithoutTenantInput> | Prisma.TagCreateWithoutTenantInput[] | Prisma.TagUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutTenantInput | Prisma.TagCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TagUpsertWithWhereUniqueWithoutTenantInput | Prisma.TagUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TagCreateManyTenantInputEnvelope
  set?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  disconnect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  delete?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  update?: Prisma.TagUpdateWithWhereUniqueWithoutTenantInput | Prisma.TagUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TagUpdateManyWithWhereWithoutTenantInput | Prisma.TagUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TagScalarWhereInput | Prisma.TagScalarWhereInput[]
}

export type TagUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.TagCreateWithoutTenantInput, Prisma.TagUncheckedCreateWithoutTenantInput> | Prisma.TagCreateWithoutTenantInput[] | Prisma.TagUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.TagCreateOrConnectWithoutTenantInput | Prisma.TagCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.TagUpsertWithWhereUniqueWithoutTenantInput | Prisma.TagUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.TagCreateManyTenantInputEnvelope
  set?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  disconnect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  delete?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  connect?: Prisma.TagWhereUniqueInput | Prisma.TagWhereUniqueInput[]
  update?: Prisma.TagUpdateWithWhereUniqueWithoutTenantInput | Prisma.TagUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.TagUpdateManyWithWhereWithoutTenantInput | Prisma.TagUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.TagScalarWhereInput | Prisma.TagScalarWhereInput[]
}

export type TagCreateWithoutRecipientsInput = {
  id?: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutTagsInput
}

export type TagUncheckedCreateWithoutRecipientsInput = {
  id?: string
  tenantId: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TagCreateOrConnectWithoutRecipientsInput = {
  where: Prisma.TagWhereUniqueInput
  create: Prisma.XOR<Prisma.TagCreateWithoutRecipientsInput, Prisma.TagUncheckedCreateWithoutRecipientsInput>
}

export type TagUpsertWithWhereUniqueWithoutRecipientsInput = {
  where: Prisma.TagWhereUniqueInput
  update: Prisma.XOR<Prisma.TagUpdateWithoutRecipientsInput, Prisma.TagUncheckedUpdateWithoutRecipientsInput>
  create: Prisma.XOR<Prisma.TagCreateWithoutRecipientsInput, Prisma.TagUncheckedCreateWithoutRecipientsInput>
}

export type TagUpdateWithWhereUniqueWithoutRecipientsInput = {
  where: Prisma.TagWhereUniqueInput
  data: Prisma.XOR<Prisma.TagUpdateWithoutRecipientsInput, Prisma.TagUncheckedUpdateWithoutRecipientsInput>
}

export type TagUpdateManyWithWhereWithoutRecipientsInput = {
  where: Prisma.TagScalarWhereInput
  data: Prisma.XOR<Prisma.TagUpdateManyMutationInput, Prisma.TagUncheckedUpdateManyWithoutRecipientsInput>
}

export type TagScalarWhereInput = {
  AND?: Prisma.TagScalarWhereInput | Prisma.TagScalarWhereInput[]
  OR?: Prisma.TagScalarWhereInput[]
  NOT?: Prisma.TagScalarWhereInput | Prisma.TagScalarWhereInput[]
  id?: Prisma.StringFilter<"Tag"> | string
  tenantId?: Prisma.StringFilter<"Tag"> | string
  externalId?: Prisma.StringNullableFilter<"Tag"> | string | null
  displayName?: Prisma.StringFilter<"Tag"> | string
  description?: Prisma.StringNullableFilter<"Tag"> | string | null
  enabled?: Prisma.BoolFilter<"Tag"> | boolean
  selectable?: Prisma.BoolFilter<"Tag"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Tag"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Tag"> | Date | string
}

export type TagCreateWithoutTenantInput = {
  id?: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipients?: Prisma.RecipientCreateNestedManyWithoutTagsInput
}

export type TagUncheckedCreateWithoutTenantInput = {
  id?: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  recipients?: Prisma.RecipientUncheckedCreateNestedManyWithoutTagsInput
}

export type TagCreateOrConnectWithoutTenantInput = {
  where: Prisma.TagWhereUniqueInput
  create: Prisma.XOR<Prisma.TagCreateWithoutTenantInput, Prisma.TagUncheckedCreateWithoutTenantInput>
}

export type TagCreateManyTenantInputEnvelope = {
  data: Prisma.TagCreateManyTenantInput | Prisma.TagCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type TagUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TagWhereUniqueInput
  update: Prisma.XOR<Prisma.TagUpdateWithoutTenantInput, Prisma.TagUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.TagCreateWithoutTenantInput, Prisma.TagUncheckedCreateWithoutTenantInput>
}

export type TagUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.TagWhereUniqueInput
  data: Prisma.XOR<Prisma.TagUpdateWithoutTenantInput, Prisma.TagUncheckedUpdateWithoutTenantInput>
}

export type TagUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.TagScalarWhereInput
  data: Prisma.XOR<Prisma.TagUpdateManyMutationInput, Prisma.TagUncheckedUpdateManyWithoutTenantInput>
}

export type TagUpdateWithoutRecipientsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutTagsNestedInput
}

export type TagUncheckedUpdateWithoutRecipientsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TagUncheckedUpdateManyWithoutRecipientsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TagCreateManyTenantInput = {
  id?: string
  externalId?: string | null
  displayName: string
  description?: string | null
  enabled?: boolean
  selectable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TagUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipients?: Prisma.RecipientUpdateManyWithoutTagsNestedInput
}

export type TagUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipients?: Prisma.RecipientUncheckedUpdateManyWithoutTagsNestedInput
}

export type TagUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  externalId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  selectable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type TagCountOutputType
 */

export type TagCountOutputType = {
  recipients: number
}

export type TagCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipients?: boolean | TagCountOutputTypeCountRecipientsArgs
}

/**
 * TagCountOutputType without action
 */
export type TagCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TagCountOutputType
   */
  select?: Prisma.TagCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TagCountOutputType without action
 */
export type TagCountOutputTypeCountRecipientsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientWhereInput
}


export type TagSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  selectable?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  recipients?: boolean | Prisma.Tag$recipientsArgs<ExtArgs>
  _count?: boolean | Prisma.TagCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["tag"]>

export type TagSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  selectable?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["tag"]>

export type TagSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  selectable?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["tag"]>

export type TagSelectScalar = {
  id?: boolean
  tenantId?: boolean
  externalId?: boolean
  displayName?: boolean
  description?: boolean
  enabled?: boolean
  selectable?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type TagOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "externalId" | "displayName" | "description" | "enabled" | "selectable" | "createdAt" | "updatedAt", ExtArgs["result"]["tag"]>
export type TagInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  recipients?: boolean | Prisma.Tag$recipientsArgs<ExtArgs>
  _count?: boolean | Prisma.TagCountOutputTypeDefaultArgs<ExtArgs>
}
export type TagIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type TagIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $TagPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Tag"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    recipients: Prisma.$RecipientPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    externalId: string | null
    displayName: string
    description: string | null
    enabled: boolean
    selectable: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["tag"]>
  composites: {}
}

export type TagGetPayload<S extends boolean | null | undefined | TagDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TagPayload, S>

export type TagCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TagFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: TagCountAggregateInputType | true
  }

export interface TagDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Tag'], meta: { name: 'Tag' } }
  /**
   * Find zero or one Tag that matches the filter.
   * @param {TagFindUniqueArgs} args - Arguments to find a Tag
   * @example
   * // Get one Tag
   * const tag = await prisma.tag.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TagFindUniqueArgs>(args: Prisma.SelectSubset<T, TagFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Tag that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TagFindUniqueOrThrowArgs} args - Arguments to find a Tag
   * @example
   * // Get one Tag
   * const tag = await prisma.tag.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TagFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TagFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Tag that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagFindFirstArgs} args - Arguments to find a Tag
   * @example
   * // Get one Tag
   * const tag = await prisma.tag.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TagFindFirstArgs>(args?: Prisma.SelectSubset<T, TagFindFirstArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Tag that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagFindFirstOrThrowArgs} args - Arguments to find a Tag
   * @example
   * // Get one Tag
   * const tag = await prisma.tag.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TagFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TagFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Tags that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Tags
   * const tags = await prisma.tag.findMany()
   * 
   * // Get first 10 Tags
   * const tags = await prisma.tag.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const tagWithIdOnly = await prisma.tag.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TagFindManyArgs>(args?: Prisma.SelectSubset<T, TagFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Tag.
   * @param {TagCreateArgs} args - Arguments to create a Tag.
   * @example
   * // Create one Tag
   * const Tag = await prisma.tag.create({
   *   data: {
   *     // ... data to create a Tag
   *   }
   * })
   * 
   */
  create<T extends TagCreateArgs>(args: Prisma.SelectSubset<T, TagCreateArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Tags.
   * @param {TagCreateManyArgs} args - Arguments to create many Tags.
   * @example
   * // Create many Tags
   * const tag = await prisma.tag.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TagCreateManyArgs>(args?: Prisma.SelectSubset<T, TagCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Tags and returns the data saved in the database.
   * @param {TagCreateManyAndReturnArgs} args - Arguments to create many Tags.
   * @example
   * // Create many Tags
   * const tag = await prisma.tag.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Tags and only return the `id`
   * const tagWithIdOnly = await prisma.tag.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TagCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TagCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Tag.
   * @param {TagDeleteArgs} args - Arguments to delete one Tag.
   * @example
   * // Delete one Tag
   * const Tag = await prisma.tag.delete({
   *   where: {
   *     // ... filter to delete one Tag
   *   }
   * })
   * 
   */
  delete<T extends TagDeleteArgs>(args: Prisma.SelectSubset<T, TagDeleteArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Tag.
   * @param {TagUpdateArgs} args - Arguments to update one Tag.
   * @example
   * // Update one Tag
   * const tag = await prisma.tag.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TagUpdateArgs>(args: Prisma.SelectSubset<T, TagUpdateArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Tags.
   * @param {TagDeleteManyArgs} args - Arguments to filter Tags to delete.
   * @example
   * // Delete a few Tags
   * const { count } = await prisma.tag.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TagDeleteManyArgs>(args?: Prisma.SelectSubset<T, TagDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tags.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Tags
   * const tag = await prisma.tag.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TagUpdateManyArgs>(args: Prisma.SelectSubset<T, TagUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tags and returns the data updated in the database.
   * @param {TagUpdateManyAndReturnArgs} args - Arguments to update many Tags.
   * @example
   * // Update many Tags
   * const tag = await prisma.tag.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Tags and only return the `id`
   * const tagWithIdOnly = await prisma.tag.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TagUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TagUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Tag.
   * @param {TagUpsertArgs} args - Arguments to update or create a Tag.
   * @example
   * // Update or create a Tag
   * const tag = await prisma.tag.upsert({
   *   create: {
   *     // ... data to create a Tag
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Tag we want to update
   *   }
   * })
   */
  upsert<T extends TagUpsertArgs>(args: Prisma.SelectSubset<T, TagUpsertArgs<ExtArgs>>): Prisma.Prisma__TagClient<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Tags.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagCountArgs} args - Arguments to filter Tags to count.
   * @example
   * // Count the number of Tags
   * const count = await prisma.tag.count({
   *   where: {
   *     // ... the filter for the Tags we want to count
   *   }
   * })
  **/
  count<T extends TagCountArgs>(
    args?: Prisma.Subset<T, TagCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TagCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Tag.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TagAggregateArgs>(args: Prisma.Subset<T, TagAggregateArgs>): Prisma.PrismaPromise<GetTagAggregateType<T>>

  /**
   * Group by Tag.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TagGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TagGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TagGroupByArgs['orderBy'] }
      : { orderBy?: TagGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TagGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTagGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Tag model
 */
readonly fields: TagFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Tag.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TagClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  recipients<T extends Prisma.Tag$recipientsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Tag$recipientsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Tag model
 */
export interface TagFieldRefs {
  readonly id: Prisma.FieldRef<"Tag", 'String'>
  readonly tenantId: Prisma.FieldRef<"Tag", 'String'>
  readonly externalId: Prisma.FieldRef<"Tag", 'String'>
  readonly displayName: Prisma.FieldRef<"Tag", 'String'>
  readonly description: Prisma.FieldRef<"Tag", 'String'>
  readonly enabled: Prisma.FieldRef<"Tag", 'Boolean'>
  readonly selectable: Prisma.FieldRef<"Tag", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Tag", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Tag", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Tag findUnique
 */
export type TagFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * Filter, which Tag to fetch.
   */
  where: Prisma.TagWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag findUniqueOrThrow
 */
export type TagFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * Filter, which Tag to fetch.
   */
  where: Prisma.TagWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag findFirst
 */
export type TagFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * Filter, which Tag to fetch.
   */
  where?: Prisma.TagWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tags to fetch.
   */
  orderBy?: Prisma.TagOrderByWithRelationInput | Prisma.TagOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Tags.
   */
  cursor?: Prisma.TagWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tags from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tags.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Tags.
   */
  distinct?: Prisma.TagScalarFieldEnum | Prisma.TagScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag findFirstOrThrow
 */
export type TagFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * Filter, which Tag to fetch.
   */
  where?: Prisma.TagWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tags to fetch.
   */
  orderBy?: Prisma.TagOrderByWithRelationInput | Prisma.TagOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Tags.
   */
  cursor?: Prisma.TagWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tags from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tags.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Tags.
   */
  distinct?: Prisma.TagScalarFieldEnum | Prisma.TagScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag findMany
 */
export type TagFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * Filter, which Tags to fetch.
   */
  where?: Prisma.TagWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tags to fetch.
   */
  orderBy?: Prisma.TagOrderByWithRelationInput | Prisma.TagOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Tags.
   */
  cursor?: Prisma.TagWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tags from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tags.
   */
  skip?: number
  distinct?: Prisma.TagScalarFieldEnum | Prisma.TagScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag create
 */
export type TagCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * The data needed to create a Tag.
   */
  data: Prisma.XOR<Prisma.TagCreateInput, Prisma.TagUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag createMany
 */
export type TagCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Tags.
   */
  data: Prisma.TagCreateManyInput | Prisma.TagCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Tag createManyAndReturn
 */
export type TagCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * The data used to create many Tags.
   */
  data: Prisma.TagCreateManyInput | Prisma.TagCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Tag update
 */
export type TagUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * The data needed to update a Tag.
   */
  data: Prisma.XOR<Prisma.TagUpdateInput, Prisma.TagUncheckedUpdateInput>
  /**
   * Choose, which Tag to update.
   */
  where: Prisma.TagWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag updateMany
 */
export type TagUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Tags.
   */
  data: Prisma.XOR<Prisma.TagUpdateManyMutationInput, Prisma.TagUncheckedUpdateManyInput>
  /**
   * Filter which Tags to update
   */
  where?: Prisma.TagWhereInput
  /**
   * Limit how many Tags to update.
   */
  limit?: number
}

/**
 * Tag updateManyAndReturn
 */
export type TagUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * The data used to update Tags.
   */
  data: Prisma.XOR<Prisma.TagUpdateManyMutationInput, Prisma.TagUncheckedUpdateManyInput>
  /**
   * Filter which Tags to update
   */
  where?: Prisma.TagWhereInput
  /**
   * Limit how many Tags to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Tag upsert
 */
export type TagUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * The filter to search for the Tag to update in case it exists.
   */
  where: Prisma.TagWhereUniqueInput
  /**
   * In case the Tag found by the `where` argument doesn't exist, create a new Tag with this data.
   */
  create: Prisma.XOR<Prisma.TagCreateInput, Prisma.TagUncheckedCreateInput>
  /**
   * In case the Tag was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TagUpdateInput, Prisma.TagUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag delete
 */
export type TagDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  /**
   * Filter which Tag to delete.
   */
  where: Prisma.TagWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Tag deleteMany
 */
export type TagDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Tags to delete
   */
  where?: Prisma.TagWhereInput
  /**
   * Limit how many Tags to delete.
   */
  limit?: number
}

/**
 * Tag.recipients
 */
export type Tag$recipientsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  where?: Prisma.RecipientWhereInput
  orderBy?: Prisma.RecipientOrderByWithRelationInput | Prisma.RecipientOrderByWithRelationInput[]
  cursor?: Prisma.RecipientWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RecipientScalarFieldEnum | Prisma.RecipientScalarFieldEnum[]
}

/**
 * Tag without action
 */
export type TagDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
}
