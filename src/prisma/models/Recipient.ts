
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Recipient` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Recipient
 * 
 */
export type RecipientModel = runtime.Types.Result.DefaultSelection<Prisma.$RecipientPayload>

export type AggregateRecipient = {
  _count: RecipientCountAggregateOutputType | null
  _min: RecipientMinAggregateOutputType | null
  _max: RecipientMaxAggregateOutputType | null
}

export type RecipientMinAggregateOutputType = {
  id: string | null
  tenantId: string | null
  enabled: boolean | null
  timezone: string | null
  locale: string | null
  digest: boolean | null
  defaultService: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RecipientMaxAggregateOutputType = {
  id: string | null
  tenantId: string | null
  enabled: boolean | null
  timezone: string | null
  locale: string | null
  digest: boolean | null
  defaultService: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RecipientCountAggregateOutputType = {
  id: number
  tenantId: number
  enabled: number
  timezone: number
  locale: number
  digest: number
  defaultService: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type RecipientMinAggregateInputType = {
  id?: true
  tenantId?: true
  enabled?: true
  timezone?: true
  locale?: true
  digest?: true
  defaultService?: true
  createdAt?: true
  updatedAt?: true
}

export type RecipientMaxAggregateInputType = {
  id?: true
  tenantId?: true
  enabled?: true
  timezone?: true
  locale?: true
  digest?: true
  defaultService?: true
  createdAt?: true
  updatedAt?: true
}

export type RecipientCountAggregateInputType = {
  id?: true
  tenantId?: true
  enabled?: true
  timezone?: true
  locale?: true
  digest?: true
  defaultService?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type RecipientAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Recipient to aggregate.
   */
  where?: Prisma.RecipientWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Recipients to fetch.
   */
  orderBy?: Prisma.RecipientOrderByWithRelationInput | Prisma.RecipientOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.RecipientWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Recipients from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Recipients.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Recipients
  **/
  _count?: true | RecipientCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RecipientMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RecipientMaxAggregateInputType
}

export type GetRecipientAggregateType<T extends RecipientAggregateArgs> = {
      [P in keyof T & keyof AggregateRecipient]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRecipient[P]>
    : Prisma.GetScalarType<T[P], AggregateRecipient[P]>
}




export type RecipientGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientWhereInput
  orderBy?: Prisma.RecipientOrderByWithAggregationInput | Prisma.RecipientOrderByWithAggregationInput[]
  by: Prisma.RecipientScalarFieldEnum[] | Prisma.RecipientScalarFieldEnum
  having?: Prisma.RecipientScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RecipientCountAggregateInputType | true
  _min?: RecipientMinAggregateInputType
  _max?: RecipientMaxAggregateInputType
}

export type RecipientGroupByOutputType = {
  id: string
  tenantId: string
  enabled: boolean
  timezone: string
  locale: string
  digest: boolean
  defaultService: string | null
  createdAt: Date
  updatedAt: Date
  _count: RecipientCountAggregateOutputType | null
  _min: RecipientMinAggregateOutputType | null
  _max: RecipientMaxAggregateOutputType | null
}

type GetRecipientGroupByPayload<T extends RecipientGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RecipientGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RecipientGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RecipientGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RecipientGroupByOutputType[P]>
      }
    >
  > 



export type RecipientWhereInput = {
  AND?: Prisma.RecipientWhereInput | Prisma.RecipientWhereInput[]
  OR?: Prisma.RecipientWhereInput[]
  NOT?: Prisma.RecipientWhereInput | Prisma.RecipientWhereInput[]
  id?: Prisma.StringFilter<"Recipient"> | string
  tenantId?: Prisma.StringFilter<"Recipient"> | string
  enabled?: Prisma.BoolFilter<"Recipient"> | boolean
  timezone?: Prisma.StringFilter<"Recipient"> | string
  locale?: Prisma.StringFilter<"Recipient"> | string
  digest?: Prisma.BoolFilter<"Recipient"> | boolean
  defaultService?: Prisma.StringNullableFilter<"Recipient"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Recipient"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Recipient"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  identifiers?: Prisma.IdentifierListRelationFilter
  attributes?: Prisma.RecipientAttributeListRelationFilter
  connections?: Prisma.ConnectionListRelationFilter
  messages?: Prisma.RecipientMessageListRelationFilter
  tags?: Prisma.TagListRelationFilter
  preferences?: Prisma.TopicPreferenceListRelationFilter
}

export type RecipientOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  digest?: Prisma.SortOrder
  defaultService?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  tenant?: Prisma.TenantOrderByWithRelationInput
  identifiers?: Prisma.IdentifierOrderByRelationAggregateInput
  attributes?: Prisma.RecipientAttributeOrderByRelationAggregateInput
  connections?: Prisma.ConnectionOrderByRelationAggregateInput
  messages?: Prisma.RecipientMessageOrderByRelationAggregateInput
  tags?: Prisma.TagOrderByRelationAggregateInput
  preferences?: Prisma.TopicPreferenceOrderByRelationAggregateInput
}

export type RecipientWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.RecipientWhereInput | Prisma.RecipientWhereInput[]
  OR?: Prisma.RecipientWhereInput[]
  NOT?: Prisma.RecipientWhereInput | Prisma.RecipientWhereInput[]
  tenantId?: Prisma.StringFilter<"Recipient"> | string
  enabled?: Prisma.BoolFilter<"Recipient"> | boolean
  timezone?: Prisma.StringFilter<"Recipient"> | string
  locale?: Prisma.StringFilter<"Recipient"> | string
  digest?: Prisma.BoolFilter<"Recipient"> | boolean
  defaultService?: Prisma.StringNullableFilter<"Recipient"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Recipient"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Recipient"> | Date | string
  tenant?: Prisma.XOR<Prisma.TenantScalarRelationFilter, Prisma.TenantWhereInput>
  identifiers?: Prisma.IdentifierListRelationFilter
  attributes?: Prisma.RecipientAttributeListRelationFilter
  connections?: Prisma.ConnectionListRelationFilter
  messages?: Prisma.RecipientMessageListRelationFilter
  tags?: Prisma.TagListRelationFilter
  preferences?: Prisma.TopicPreferenceListRelationFilter
}, "id">

export type RecipientOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  digest?: Prisma.SortOrder
  defaultService?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.RecipientCountOrderByAggregateInput
  _max?: Prisma.RecipientMaxOrderByAggregateInput
  _min?: Prisma.RecipientMinOrderByAggregateInput
}

export type RecipientScalarWhereWithAggregatesInput = {
  AND?: Prisma.RecipientScalarWhereWithAggregatesInput | Prisma.RecipientScalarWhereWithAggregatesInput[]
  OR?: Prisma.RecipientScalarWhereWithAggregatesInput[]
  NOT?: Prisma.RecipientScalarWhereWithAggregatesInput | Prisma.RecipientScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Recipient"> | string
  tenantId?: Prisma.StringWithAggregatesFilter<"Recipient"> | string
  enabled?: Prisma.BoolWithAggregatesFilter<"Recipient"> | boolean
  timezone?: Prisma.StringWithAggregatesFilter<"Recipient"> | string
  locale?: Prisma.StringWithAggregatesFilter<"Recipient"> | string
  digest?: Prisma.BoolWithAggregatesFilter<"Recipient"> | boolean
  defaultService?: Prisma.StringNullableWithAggregatesFilter<"Recipient"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Recipient"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Recipient"> | Date | string
}

export type RecipientCreateInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientCreateManyInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientScalarRelationFilter = {
  is?: Prisma.RecipientWhereInput
  isNot?: Prisma.RecipientWhereInput
}

export type RecipientNullableScalarRelationFilter = {
  is?: Prisma.RecipientWhereInput | null
  isNot?: Prisma.RecipientWhereInput | null
}

export type RecipientCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  digest?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RecipientMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  digest?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RecipientMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tenantId?: Prisma.SortOrder
  enabled?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  locale?: Prisma.SortOrder
  digest?: Prisma.SortOrder
  defaultService?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RecipientListRelationFilter = {
  every?: Prisma.RecipientWhereInput
  some?: Prisma.RecipientWhereInput
  none?: Prisma.RecipientWhereInput
}

export type RecipientOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type RecipientCreateNestedOneWithoutConnectionsInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutConnectionsInput, Prisma.RecipientUncheckedCreateWithoutConnectionsInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutConnectionsInput
  connect?: Prisma.RecipientWhereUniqueInput
}

export type RecipientUpdateOneRequiredWithoutConnectionsNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutConnectionsInput, Prisma.RecipientUncheckedCreateWithoutConnectionsInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutConnectionsInput
  upsert?: Prisma.RecipientUpsertWithoutConnectionsInput
  connect?: Prisma.RecipientWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RecipientUpdateToOneWithWhereWithoutConnectionsInput, Prisma.RecipientUpdateWithoutConnectionsInput>, Prisma.RecipientUncheckedUpdateWithoutConnectionsInput>
}

export type RecipientCreateNestedOneWithoutIdentifiersInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutIdentifiersInput, Prisma.RecipientUncheckedCreateWithoutIdentifiersInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutIdentifiersInput
  connect?: Prisma.RecipientWhereUniqueInput
}

export type RecipientUpdateOneRequiredWithoutIdentifiersNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutIdentifiersInput, Prisma.RecipientUncheckedCreateWithoutIdentifiersInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutIdentifiersInput
  upsert?: Prisma.RecipientUpsertWithoutIdentifiersInput
  connect?: Prisma.RecipientWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RecipientUpdateToOneWithWhereWithoutIdentifiersInput, Prisma.RecipientUpdateWithoutIdentifiersInput>, Prisma.RecipientUncheckedUpdateWithoutIdentifiersInput>
}

export type RecipientCreateNestedOneWithoutAttributesInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutAttributesInput, Prisma.RecipientUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutAttributesInput
  connect?: Prisma.RecipientWhereUniqueInput
}

export type RecipientUpdateOneRequiredWithoutAttributesNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutAttributesInput, Prisma.RecipientUncheckedCreateWithoutAttributesInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutAttributesInput
  upsert?: Prisma.RecipientUpsertWithoutAttributesInput
  connect?: Prisma.RecipientWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RecipientUpdateToOneWithWhereWithoutAttributesInput, Prisma.RecipientUpdateWithoutAttributesInput>, Prisma.RecipientUncheckedUpdateWithoutAttributesInput>
}

export type RecipientCreateNestedOneWithoutMessagesInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutMessagesInput, Prisma.RecipientUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutMessagesInput
  connect?: Prisma.RecipientWhereUniqueInput
}

export type RecipientUpdateOneWithoutMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutMessagesInput, Prisma.RecipientUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutMessagesInput
  upsert?: Prisma.RecipientUpsertWithoutMessagesInput
  disconnect?: Prisma.RecipientWhereInput | boolean
  delete?: Prisma.RecipientWhereInput | boolean
  connect?: Prisma.RecipientWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RecipientUpdateToOneWithWhereWithoutMessagesInput, Prisma.RecipientUpdateWithoutMessagesInput>, Prisma.RecipientUncheckedUpdateWithoutMessagesInput>
}

export type RecipientCreateNestedManyWithoutTagsInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTagsInput, Prisma.RecipientUncheckedCreateWithoutTagsInput> | Prisma.RecipientCreateWithoutTagsInput[] | Prisma.RecipientUncheckedCreateWithoutTagsInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTagsInput | Prisma.RecipientCreateOrConnectWithoutTagsInput[]
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
}

export type RecipientUncheckedCreateNestedManyWithoutTagsInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTagsInput, Prisma.RecipientUncheckedCreateWithoutTagsInput> | Prisma.RecipientCreateWithoutTagsInput[] | Prisma.RecipientUncheckedCreateWithoutTagsInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTagsInput | Prisma.RecipientCreateOrConnectWithoutTagsInput[]
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
}

export type RecipientUpdateManyWithoutTagsNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTagsInput, Prisma.RecipientUncheckedCreateWithoutTagsInput> | Prisma.RecipientCreateWithoutTagsInput[] | Prisma.RecipientUncheckedCreateWithoutTagsInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTagsInput | Prisma.RecipientCreateOrConnectWithoutTagsInput[]
  upsert?: Prisma.RecipientUpsertWithWhereUniqueWithoutTagsInput | Prisma.RecipientUpsertWithWhereUniqueWithoutTagsInput[]
  set?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  disconnect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  delete?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  update?: Prisma.RecipientUpdateWithWhereUniqueWithoutTagsInput | Prisma.RecipientUpdateWithWhereUniqueWithoutTagsInput[]
  updateMany?: Prisma.RecipientUpdateManyWithWhereWithoutTagsInput | Prisma.RecipientUpdateManyWithWhereWithoutTagsInput[]
  deleteMany?: Prisma.RecipientScalarWhereInput | Prisma.RecipientScalarWhereInput[]
}

export type RecipientUncheckedUpdateManyWithoutTagsNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTagsInput, Prisma.RecipientUncheckedCreateWithoutTagsInput> | Prisma.RecipientCreateWithoutTagsInput[] | Prisma.RecipientUncheckedCreateWithoutTagsInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTagsInput | Prisma.RecipientCreateOrConnectWithoutTagsInput[]
  upsert?: Prisma.RecipientUpsertWithWhereUniqueWithoutTagsInput | Prisma.RecipientUpsertWithWhereUniqueWithoutTagsInput[]
  set?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  disconnect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  delete?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  update?: Prisma.RecipientUpdateWithWhereUniqueWithoutTagsInput | Prisma.RecipientUpdateWithWhereUniqueWithoutTagsInput[]
  updateMany?: Prisma.RecipientUpdateManyWithWhereWithoutTagsInput | Prisma.RecipientUpdateManyWithWhereWithoutTagsInput[]
  deleteMany?: Prisma.RecipientScalarWhereInput | Prisma.RecipientScalarWhereInput[]
}

export type RecipientCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTenantInput, Prisma.RecipientUncheckedCreateWithoutTenantInput> | Prisma.RecipientCreateWithoutTenantInput[] | Prisma.RecipientUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTenantInput | Prisma.RecipientCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.RecipientCreateManyTenantInputEnvelope
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
}

export type RecipientUncheckedCreateNestedManyWithoutTenantInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTenantInput, Prisma.RecipientUncheckedCreateWithoutTenantInput> | Prisma.RecipientCreateWithoutTenantInput[] | Prisma.RecipientUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTenantInput | Prisma.RecipientCreateOrConnectWithoutTenantInput[]
  createMany?: Prisma.RecipientCreateManyTenantInputEnvelope
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
}

export type RecipientUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTenantInput, Prisma.RecipientUncheckedCreateWithoutTenantInput> | Prisma.RecipientCreateWithoutTenantInput[] | Prisma.RecipientUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTenantInput | Prisma.RecipientCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.RecipientUpsertWithWhereUniqueWithoutTenantInput | Prisma.RecipientUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.RecipientCreateManyTenantInputEnvelope
  set?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  disconnect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  delete?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  update?: Prisma.RecipientUpdateWithWhereUniqueWithoutTenantInput | Prisma.RecipientUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.RecipientUpdateManyWithWhereWithoutTenantInput | Prisma.RecipientUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.RecipientScalarWhereInput | Prisma.RecipientScalarWhereInput[]
}

export type RecipientUncheckedUpdateManyWithoutTenantNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutTenantInput, Prisma.RecipientUncheckedCreateWithoutTenantInput> | Prisma.RecipientCreateWithoutTenantInput[] | Prisma.RecipientUncheckedCreateWithoutTenantInput[]
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutTenantInput | Prisma.RecipientCreateOrConnectWithoutTenantInput[]
  upsert?: Prisma.RecipientUpsertWithWhereUniqueWithoutTenantInput | Prisma.RecipientUpsertWithWhereUniqueWithoutTenantInput[]
  createMany?: Prisma.RecipientCreateManyTenantInputEnvelope
  set?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  disconnect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  delete?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  connect?: Prisma.RecipientWhereUniqueInput | Prisma.RecipientWhereUniqueInput[]
  update?: Prisma.RecipientUpdateWithWhereUniqueWithoutTenantInput | Prisma.RecipientUpdateWithWhereUniqueWithoutTenantInput[]
  updateMany?: Prisma.RecipientUpdateManyWithWhereWithoutTenantInput | Prisma.RecipientUpdateManyWithWhereWithoutTenantInput[]
  deleteMany?: Prisma.RecipientScalarWhereInput | Prisma.RecipientScalarWhereInput[]
}

export type RecipientCreateNestedOneWithoutPreferencesInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutPreferencesInput, Prisma.RecipientUncheckedCreateWithoutPreferencesInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutPreferencesInput
  connect?: Prisma.RecipientWhereUniqueInput
}

export type RecipientUpdateOneRequiredWithoutPreferencesNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientCreateWithoutPreferencesInput, Prisma.RecipientUncheckedCreateWithoutPreferencesInput>
  connectOrCreate?: Prisma.RecipientCreateOrConnectWithoutPreferencesInput
  upsert?: Prisma.RecipientUpsertWithoutPreferencesInput
  connect?: Prisma.RecipientWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RecipientUpdateToOneWithWhereWithoutPreferencesInput, Prisma.RecipientUpdateWithoutPreferencesInput>, Prisma.RecipientUncheckedUpdateWithoutPreferencesInput>
}

export type RecipientCreateWithoutConnectionsInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateWithoutConnectionsInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientCreateOrConnectWithoutConnectionsInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutConnectionsInput, Prisma.RecipientUncheckedCreateWithoutConnectionsInput>
}

export type RecipientUpsertWithoutConnectionsInput = {
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutConnectionsInput, Prisma.RecipientUncheckedUpdateWithoutConnectionsInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutConnectionsInput, Prisma.RecipientUncheckedCreateWithoutConnectionsInput>
  where?: Prisma.RecipientWhereInput
}

export type RecipientUpdateToOneWithWhereWithoutConnectionsInput = {
  where?: Prisma.RecipientWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutConnectionsInput, Prisma.RecipientUncheckedUpdateWithoutConnectionsInput>
}

export type RecipientUpdateWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientCreateWithoutIdentifiersInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateWithoutIdentifiersInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientCreateOrConnectWithoutIdentifiersInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutIdentifiersInput, Prisma.RecipientUncheckedCreateWithoutIdentifiersInput>
}

export type RecipientUpsertWithoutIdentifiersInput = {
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutIdentifiersInput, Prisma.RecipientUncheckedUpdateWithoutIdentifiersInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutIdentifiersInput, Prisma.RecipientUncheckedCreateWithoutIdentifiersInput>
  where?: Prisma.RecipientWhereInput
}

export type RecipientUpdateToOneWithWhereWithoutIdentifiersInput = {
  where?: Prisma.RecipientWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutIdentifiersInput, Prisma.RecipientUncheckedUpdateWithoutIdentifiersInput>
}

export type RecipientUpdateWithoutIdentifiersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateWithoutIdentifiersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientCreateWithoutAttributesInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateWithoutAttributesInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientCreateOrConnectWithoutAttributesInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutAttributesInput, Prisma.RecipientUncheckedCreateWithoutAttributesInput>
}

export type RecipientUpsertWithoutAttributesInput = {
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutAttributesInput, Prisma.RecipientUncheckedUpdateWithoutAttributesInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutAttributesInput, Prisma.RecipientUncheckedCreateWithoutAttributesInput>
  where?: Prisma.RecipientWhereInput
}

export type RecipientUpdateToOneWithWhereWithoutAttributesInput = {
  where?: Prisma.RecipientWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutAttributesInput, Prisma.RecipientUncheckedUpdateWithoutAttributesInput>
}

export type RecipientUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateWithoutAttributesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientCreateWithoutMessagesInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateWithoutMessagesInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientCreateOrConnectWithoutMessagesInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutMessagesInput, Prisma.RecipientUncheckedCreateWithoutMessagesInput>
}

export type RecipientUpsertWithoutMessagesInput = {
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutMessagesInput, Prisma.RecipientUncheckedUpdateWithoutMessagesInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutMessagesInput, Prisma.RecipientUncheckedCreateWithoutMessagesInput>
  where?: Prisma.RecipientWhereInput
}

export type RecipientUpdateToOneWithWhereWithoutMessagesInput = {
  where?: Prisma.RecipientWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutMessagesInput, Prisma.RecipientUncheckedUpdateWithoutMessagesInput>
}

export type RecipientUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientCreateWithoutTagsInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateWithoutTagsInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientCreateOrConnectWithoutTagsInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutTagsInput, Prisma.RecipientUncheckedCreateWithoutTagsInput>
}

export type RecipientUpsertWithWhereUniqueWithoutTagsInput = {
  where: Prisma.RecipientWhereUniqueInput
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutTagsInput, Prisma.RecipientUncheckedUpdateWithoutTagsInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutTagsInput, Prisma.RecipientUncheckedCreateWithoutTagsInput>
}

export type RecipientUpdateWithWhereUniqueWithoutTagsInput = {
  where: Prisma.RecipientWhereUniqueInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutTagsInput, Prisma.RecipientUncheckedUpdateWithoutTagsInput>
}

export type RecipientUpdateManyWithWhereWithoutTagsInput = {
  where: Prisma.RecipientScalarWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateManyMutationInput, Prisma.RecipientUncheckedUpdateManyWithoutTagsInput>
}

export type RecipientScalarWhereInput = {
  AND?: Prisma.RecipientScalarWhereInput | Prisma.RecipientScalarWhereInput[]
  OR?: Prisma.RecipientScalarWhereInput[]
  NOT?: Prisma.RecipientScalarWhereInput | Prisma.RecipientScalarWhereInput[]
  id?: Prisma.StringFilter<"Recipient"> | string
  tenantId?: Prisma.StringFilter<"Recipient"> | string
  enabled?: Prisma.BoolFilter<"Recipient"> | boolean
  timezone?: Prisma.StringFilter<"Recipient"> | string
  locale?: Prisma.StringFilter<"Recipient"> | string
  digest?: Prisma.BoolFilter<"Recipient"> | boolean
  defaultService?: Prisma.StringNullableFilter<"Recipient"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Recipient"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Recipient"> | Date | string
}

export type RecipientCreateWithoutTenantInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceCreateNestedManyWithoutRecipientInput
}

export type RecipientUncheckedCreateWithoutTenantInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
  preferences?: Prisma.TopicPreferenceUncheckedCreateNestedManyWithoutRecipientInput
}

export type RecipientCreateOrConnectWithoutTenantInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutTenantInput, Prisma.RecipientUncheckedCreateWithoutTenantInput>
}

export type RecipientCreateManyTenantInputEnvelope = {
  data: Prisma.RecipientCreateManyTenantInput | Prisma.RecipientCreateManyTenantInput[]
  skipDuplicates?: boolean
}

export type RecipientUpsertWithWhereUniqueWithoutTenantInput = {
  where: Prisma.RecipientWhereUniqueInput
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutTenantInput, Prisma.RecipientUncheckedUpdateWithoutTenantInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutTenantInput, Prisma.RecipientUncheckedCreateWithoutTenantInput>
}

export type RecipientUpdateWithWhereUniqueWithoutTenantInput = {
  where: Prisma.RecipientWhereUniqueInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutTenantInput, Prisma.RecipientUncheckedUpdateWithoutTenantInput>
}

export type RecipientUpdateManyWithWhereWithoutTenantInput = {
  where: Prisma.RecipientScalarWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateManyMutationInput, Prisma.RecipientUncheckedUpdateManyWithoutTenantInput>
}

export type RecipientCreateWithoutPreferencesInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  tenant: Prisma.TenantCreateNestedOneWithoutRecipientsInput
  identifiers?: Prisma.IdentifierCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagCreateNestedManyWithoutRecipientsInput
}

export type RecipientUncheckedCreateWithoutPreferencesInput = {
  id?: string
  tenantId: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.IdentifierUncheckedCreateNestedManyWithoutRecipientInput
  attributes?: Prisma.RecipientAttributeUncheckedCreateNestedManyWithoutRecipientInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRecipientInput
  messages?: Prisma.RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput
  tags?: Prisma.TagUncheckedCreateNestedManyWithoutRecipientsInput
}

export type RecipientCreateOrConnectWithoutPreferencesInput = {
  where: Prisma.RecipientWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientCreateWithoutPreferencesInput, Prisma.RecipientUncheckedCreateWithoutPreferencesInput>
}

export type RecipientUpsertWithoutPreferencesInput = {
  update: Prisma.XOR<Prisma.RecipientUpdateWithoutPreferencesInput, Prisma.RecipientUncheckedUpdateWithoutPreferencesInput>
  create: Prisma.XOR<Prisma.RecipientCreateWithoutPreferencesInput, Prisma.RecipientUncheckedCreateWithoutPreferencesInput>
  where?: Prisma.RecipientWhereInput
}

export type RecipientUpdateToOneWithWhereWithoutPreferencesInput = {
  where?: Prisma.RecipientWhereInput
  data: Prisma.XOR<Prisma.RecipientUpdateWithoutPreferencesInput, Prisma.RecipientUncheckedUpdateWithoutPreferencesInput>
}

export type RecipientUpdateWithoutPreferencesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
}

export type RecipientUncheckedUpdateWithoutPreferencesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
}

export type RecipientUpdateWithoutTagsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tenant?: Prisma.TenantUpdateOneRequiredWithoutRecipientsNestedInput
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateWithoutTagsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateManyWithoutTagsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  tenantId?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RecipientCreateManyTenantInput = {
  id?: string
  enabled?: boolean
  timezone?: string
  locale?: string
  digest?: boolean
  defaultService?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RecipientUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.IdentifierUncheckedUpdateManyWithoutRecipientNestedInput
  attributes?: Prisma.RecipientAttributeUncheckedUpdateManyWithoutRecipientNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRecipientNestedInput
  messages?: Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput
  tags?: Prisma.TagUncheckedUpdateManyWithoutRecipientsNestedInput
  preferences?: Prisma.TopicPreferenceUncheckedUpdateManyWithoutRecipientNestedInput
}

export type RecipientUncheckedUpdateManyWithoutTenantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  enabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  timezone?: Prisma.StringFieldUpdateOperationsInput | string
  locale?: Prisma.StringFieldUpdateOperationsInput | string
  digest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  defaultService?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type RecipientCountOutputType
 */

export type RecipientCountOutputType = {
  identifiers: number
  attributes: number
  connections: number
  messages: number
  tags: number
  preferences: number
}

export type RecipientCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  identifiers?: boolean | RecipientCountOutputTypeCountIdentifiersArgs
  attributes?: boolean | RecipientCountOutputTypeCountAttributesArgs
  connections?: boolean | RecipientCountOutputTypeCountConnectionsArgs
  messages?: boolean | RecipientCountOutputTypeCountMessagesArgs
  tags?: boolean | RecipientCountOutputTypeCountTagsArgs
  preferences?: boolean | RecipientCountOutputTypeCountPreferencesArgs
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientCountOutputType
   */
  select?: Prisma.RecipientCountOutputTypeSelect<ExtArgs> | null
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeCountIdentifiersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.IdentifierWhereInput
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeCountAttributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientAttributeWhereInput
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeCountConnectionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeCountMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientMessageWhereInput
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeCountTagsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TagWhereInput
}

/**
 * RecipientCountOutputType without action
 */
export type RecipientCountOutputTypeCountPreferencesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TopicPreferenceWhereInput
}


export type RecipientSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  enabled?: boolean
  timezone?: boolean
  locale?: boolean
  digest?: boolean
  defaultService?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  identifiers?: boolean | Prisma.Recipient$identifiersArgs<ExtArgs>
  attributes?: boolean | Prisma.Recipient$attributesArgs<ExtArgs>
  connections?: boolean | Prisma.Recipient$connectionsArgs<ExtArgs>
  messages?: boolean | Prisma.Recipient$messagesArgs<ExtArgs>
  tags?: boolean | Prisma.Recipient$tagsArgs<ExtArgs>
  preferences?: boolean | Prisma.Recipient$preferencesArgs<ExtArgs>
  _count?: boolean | Prisma.RecipientCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipient"]>

export type RecipientSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  enabled?: boolean
  timezone?: boolean
  locale?: boolean
  digest?: boolean
  defaultService?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipient"]>

export type RecipientSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tenantId?: boolean
  enabled?: boolean
  timezone?: boolean
  locale?: boolean
  digest?: boolean
  defaultService?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipient"]>

export type RecipientSelectScalar = {
  id?: boolean
  tenantId?: boolean
  enabled?: boolean
  timezone?: boolean
  locale?: boolean
  digest?: boolean
  defaultService?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type RecipientOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tenantId" | "enabled" | "timezone" | "locale" | "digest" | "defaultService" | "createdAt" | "updatedAt", ExtArgs["result"]["recipient"]>
export type RecipientInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
  identifiers?: boolean | Prisma.Recipient$identifiersArgs<ExtArgs>
  attributes?: boolean | Prisma.Recipient$attributesArgs<ExtArgs>
  connections?: boolean | Prisma.Recipient$connectionsArgs<ExtArgs>
  messages?: boolean | Prisma.Recipient$messagesArgs<ExtArgs>
  tags?: boolean | Prisma.Recipient$tagsArgs<ExtArgs>
  preferences?: boolean | Prisma.Recipient$preferencesArgs<ExtArgs>
  _count?: boolean | Prisma.RecipientCountOutputTypeDefaultArgs<ExtArgs>
}
export type RecipientIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}
export type RecipientIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tenant?: boolean | Prisma.TenantDefaultArgs<ExtArgs>
}

export type $RecipientPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Recipient"
  objects: {
    tenant: Prisma.$TenantPayload<ExtArgs>
    identifiers: Prisma.$IdentifierPayload<ExtArgs>[]
    attributes: Prisma.$RecipientAttributePayload<ExtArgs>[]
    connections: Prisma.$ConnectionPayload<ExtArgs>[]
    messages: Prisma.$RecipientMessagePayload<ExtArgs>[]
    tags: Prisma.$TagPayload<ExtArgs>[]
    preferences: Prisma.$TopicPreferencePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    tenantId: string
    enabled: boolean
    timezone: string
    locale: string
    digest: boolean
    defaultService: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["recipient"]>
  composites: {}
}

export type RecipientGetPayload<S extends boolean | null | undefined | RecipientDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$RecipientPayload, S>

export type RecipientCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<RecipientFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: RecipientCountAggregateInputType | true
  }

export interface RecipientDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Recipient'], meta: { name: 'Recipient' } }
  /**
   * Find zero or one Recipient that matches the filter.
   * @param {RecipientFindUniqueArgs} args - Arguments to find a Recipient
   * @example
   * // Get one Recipient
   * const recipient = await prisma.recipient.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RecipientFindUniqueArgs>(args: Prisma.SelectSubset<T, RecipientFindUniqueArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Recipient that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RecipientFindUniqueOrThrowArgs} args - Arguments to find a Recipient
   * @example
   * // Get one Recipient
   * const recipient = await prisma.recipient.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RecipientFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, RecipientFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Recipient that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientFindFirstArgs} args - Arguments to find a Recipient
   * @example
   * // Get one Recipient
   * const recipient = await prisma.recipient.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RecipientFindFirstArgs>(args?: Prisma.SelectSubset<T, RecipientFindFirstArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Recipient that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientFindFirstOrThrowArgs} args - Arguments to find a Recipient
   * @example
   * // Get one Recipient
   * const recipient = await prisma.recipient.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RecipientFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, RecipientFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Recipients that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Recipients
   * const recipients = await prisma.recipient.findMany()
   * 
   * // Get first 10 Recipients
   * const recipients = await prisma.recipient.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const recipientWithIdOnly = await prisma.recipient.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends RecipientFindManyArgs>(args?: Prisma.SelectSubset<T, RecipientFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Recipient.
   * @param {RecipientCreateArgs} args - Arguments to create a Recipient.
   * @example
   * // Create one Recipient
   * const Recipient = await prisma.recipient.create({
   *   data: {
   *     // ... data to create a Recipient
   *   }
   * })
   * 
   */
  create<T extends RecipientCreateArgs>(args: Prisma.SelectSubset<T, RecipientCreateArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Recipients.
   * @param {RecipientCreateManyArgs} args - Arguments to create many Recipients.
   * @example
   * // Create many Recipients
   * const recipient = await prisma.recipient.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends RecipientCreateManyArgs>(args?: Prisma.SelectSubset<T, RecipientCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Recipients and returns the data saved in the database.
   * @param {RecipientCreateManyAndReturnArgs} args - Arguments to create many Recipients.
   * @example
   * // Create many Recipients
   * const recipient = await prisma.recipient.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Recipients and only return the `id`
   * const recipientWithIdOnly = await prisma.recipient.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends RecipientCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, RecipientCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Recipient.
   * @param {RecipientDeleteArgs} args - Arguments to delete one Recipient.
   * @example
   * // Delete one Recipient
   * const Recipient = await prisma.recipient.delete({
   *   where: {
   *     // ... filter to delete one Recipient
   *   }
   * })
   * 
   */
  delete<T extends RecipientDeleteArgs>(args: Prisma.SelectSubset<T, RecipientDeleteArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Recipient.
   * @param {RecipientUpdateArgs} args - Arguments to update one Recipient.
   * @example
   * // Update one Recipient
   * const recipient = await prisma.recipient.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends RecipientUpdateArgs>(args: Prisma.SelectSubset<T, RecipientUpdateArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Recipients.
   * @param {RecipientDeleteManyArgs} args - Arguments to filter Recipients to delete.
   * @example
   * // Delete a few Recipients
   * const { count } = await prisma.recipient.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends RecipientDeleteManyArgs>(args?: Prisma.SelectSubset<T, RecipientDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Recipients.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Recipients
   * const recipient = await prisma.recipient.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends RecipientUpdateManyArgs>(args: Prisma.SelectSubset<T, RecipientUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Recipients and returns the data updated in the database.
   * @param {RecipientUpdateManyAndReturnArgs} args - Arguments to update many Recipients.
   * @example
   * // Update many Recipients
   * const recipient = await prisma.recipient.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Recipients and only return the `id`
   * const recipientWithIdOnly = await prisma.recipient.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends RecipientUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, RecipientUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Recipient.
   * @param {RecipientUpsertArgs} args - Arguments to update or create a Recipient.
   * @example
   * // Update or create a Recipient
   * const recipient = await prisma.recipient.upsert({
   *   create: {
   *     // ... data to create a Recipient
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Recipient we want to update
   *   }
   * })
   */
  upsert<T extends RecipientUpsertArgs>(args: Prisma.SelectSubset<T, RecipientUpsertArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Recipients.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientCountArgs} args - Arguments to filter Recipients to count.
   * @example
   * // Count the number of Recipients
   * const count = await prisma.recipient.count({
   *   where: {
   *     // ... the filter for the Recipients we want to count
   *   }
   * })
  **/
  count<T extends RecipientCountArgs>(
    args?: Prisma.Subset<T, RecipientCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RecipientCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Recipient.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RecipientAggregateArgs>(args: Prisma.Subset<T, RecipientAggregateArgs>): Prisma.PrismaPromise<GetRecipientAggregateType<T>>

  /**
   * Group by Recipient.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends RecipientGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RecipientGroupByArgs['orderBy'] }
      : { orderBy?: RecipientGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, RecipientGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecipientGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Recipient model
 */
readonly fields: RecipientFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Recipient.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RecipientClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  tenant<T extends Prisma.TenantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TenantDefaultArgs<ExtArgs>>): Prisma.Prisma__TenantClient<runtime.Types.Result.GetResult<Prisma.$TenantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  identifiers<T extends Prisma.Recipient$identifiersArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Recipient$identifiersArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  attributes<T extends Prisma.Recipient$attributesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Recipient$attributesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  connections<T extends Prisma.Recipient$connectionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Recipient$connectionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  messages<T extends Prisma.Recipient$messagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Recipient$messagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  tags<T extends Prisma.Recipient$tagsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Recipient$tagsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TagPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  preferences<T extends Prisma.Recipient$preferencesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Recipient$preferencesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TopicPreferencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Recipient model
 */
export interface RecipientFieldRefs {
  readonly id: Prisma.FieldRef<"Recipient", 'String'>
  readonly tenantId: Prisma.FieldRef<"Recipient", 'String'>
  readonly enabled: Prisma.FieldRef<"Recipient", 'Boolean'>
  readonly timezone: Prisma.FieldRef<"Recipient", 'String'>
  readonly locale: Prisma.FieldRef<"Recipient", 'String'>
  readonly digest: Prisma.FieldRef<"Recipient", 'Boolean'>
  readonly defaultService: Prisma.FieldRef<"Recipient", 'String'>
  readonly createdAt: Prisma.FieldRef<"Recipient", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Recipient", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Recipient findUnique
 */
export type RecipientFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * Filter, which Recipient to fetch.
   */
  where: Prisma.RecipientWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient findUniqueOrThrow
 */
export type RecipientFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * Filter, which Recipient to fetch.
   */
  where: Prisma.RecipientWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient findFirst
 */
export type RecipientFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * Filter, which Recipient to fetch.
   */
  where?: Prisma.RecipientWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Recipients to fetch.
   */
  orderBy?: Prisma.RecipientOrderByWithRelationInput | Prisma.RecipientOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Recipients.
   */
  cursor?: Prisma.RecipientWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Recipients from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Recipients.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Recipients.
   */
  distinct?: Prisma.RecipientScalarFieldEnum | Prisma.RecipientScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient findFirstOrThrow
 */
export type RecipientFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * Filter, which Recipient to fetch.
   */
  where?: Prisma.RecipientWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Recipients to fetch.
   */
  orderBy?: Prisma.RecipientOrderByWithRelationInput | Prisma.RecipientOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Recipients.
   */
  cursor?: Prisma.RecipientWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Recipients from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Recipients.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Recipients.
   */
  distinct?: Prisma.RecipientScalarFieldEnum | Prisma.RecipientScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient findMany
 */
export type RecipientFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * Filter, which Recipients to fetch.
   */
  where?: Prisma.RecipientWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Recipients to fetch.
   */
  orderBy?: Prisma.RecipientOrderByWithRelationInput | Prisma.RecipientOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Recipients.
   */
  cursor?: Prisma.RecipientWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Recipients from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Recipients.
   */
  skip?: number
  distinct?: Prisma.RecipientScalarFieldEnum | Prisma.RecipientScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient create
 */
export type RecipientCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * The data needed to create a Recipient.
   */
  data: Prisma.XOR<Prisma.RecipientCreateInput, Prisma.RecipientUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient createMany
 */
export type RecipientCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Recipients.
   */
  data: Prisma.RecipientCreateManyInput | Prisma.RecipientCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Recipient createManyAndReturn
 */
export type RecipientCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * The data used to create many Recipients.
   */
  data: Prisma.RecipientCreateManyInput | Prisma.RecipientCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Recipient update
 */
export type RecipientUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * The data needed to update a Recipient.
   */
  data: Prisma.XOR<Prisma.RecipientUpdateInput, Prisma.RecipientUncheckedUpdateInput>
  /**
   * Choose, which Recipient to update.
   */
  where: Prisma.RecipientWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient updateMany
 */
export type RecipientUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Recipients.
   */
  data: Prisma.XOR<Prisma.RecipientUpdateManyMutationInput, Prisma.RecipientUncheckedUpdateManyInput>
  /**
   * Filter which Recipients to update
   */
  where?: Prisma.RecipientWhereInput
  /**
   * Limit how many Recipients to update.
   */
  limit?: number
}

/**
 * Recipient updateManyAndReturn
 */
export type RecipientUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * The data used to update Recipients.
   */
  data: Prisma.XOR<Prisma.RecipientUpdateManyMutationInput, Prisma.RecipientUncheckedUpdateManyInput>
  /**
   * Filter which Recipients to update
   */
  where?: Prisma.RecipientWhereInput
  /**
   * Limit how many Recipients to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Recipient upsert
 */
export type RecipientUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * The filter to search for the Recipient to update in case it exists.
   */
  where: Prisma.RecipientWhereUniqueInput
  /**
   * In case the Recipient found by the `where` argument doesn't exist, create a new Recipient with this data.
   */
  create: Prisma.XOR<Prisma.RecipientCreateInput, Prisma.RecipientUncheckedCreateInput>
  /**
   * In case the Recipient was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RecipientUpdateInput, Prisma.RecipientUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient delete
 */
export type RecipientDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  /**
   * Filter which Recipient to delete.
   */
  where: Prisma.RecipientWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Recipient deleteMany
 */
export type RecipientDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Recipients to delete
   */
  where?: Prisma.RecipientWhereInput
  /**
   * Limit how many Recipients to delete.
   */
  limit?: number
}

/**
 * Recipient.identifiers
 */
export type Recipient$identifiersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  where?: Prisma.IdentifierWhereInput
  orderBy?: Prisma.IdentifierOrderByWithRelationInput | Prisma.IdentifierOrderByWithRelationInput[]
  cursor?: Prisma.IdentifierWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.IdentifierScalarFieldEnum | Prisma.IdentifierScalarFieldEnum[]
}

/**
 * Recipient.attributes
 */
export type Recipient$attributesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientAttribute
   */
  select?: Prisma.RecipientAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientAttribute
   */
  omit?: Prisma.RecipientAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientAttributeInclude<ExtArgs> | null
  where?: Prisma.RecipientAttributeWhereInput
  orderBy?: Prisma.RecipientAttributeOrderByWithRelationInput | Prisma.RecipientAttributeOrderByWithRelationInput[]
  cursor?: Prisma.RecipientAttributeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RecipientAttributeScalarFieldEnum | Prisma.RecipientAttributeScalarFieldEnum[]
}

/**
 * Recipient.connections
 */
export type Recipient$connectionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  cursor?: Prisma.ConnectionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * Recipient.messages
 */
export type Recipient$messagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  where?: Prisma.RecipientMessageWhereInput
  orderBy?: Prisma.RecipientMessageOrderByWithRelationInput | Prisma.RecipientMessageOrderByWithRelationInput[]
  cursor?: Prisma.RecipientMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RecipientMessageScalarFieldEnum | Prisma.RecipientMessageScalarFieldEnum[]
}

/**
 * Recipient.tags
 */
export type Recipient$tagsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Tag
   */
  select?: Prisma.TagSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Tag
   */
  omit?: Prisma.TagOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TagInclude<ExtArgs> | null
  where?: Prisma.TagWhereInput
  orderBy?: Prisma.TagOrderByWithRelationInput | Prisma.TagOrderByWithRelationInput[]
  cursor?: Prisma.TagWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TagScalarFieldEnum | Prisma.TagScalarFieldEnum[]
}

/**
 * Recipient.preferences
 */
export type Recipient$preferencesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TopicPreference
   */
  select?: Prisma.TopicPreferenceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TopicPreference
   */
  omit?: Prisma.TopicPreferenceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TopicPreferenceInclude<ExtArgs> | null
  where?: Prisma.TopicPreferenceWhereInput
  orderBy?: Prisma.TopicPreferenceOrderByWithRelationInput | Prisma.TopicPreferenceOrderByWithRelationInput[]
  cursor?: Prisma.TopicPreferenceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TopicPreferenceScalarFieldEnum | Prisma.TopicPreferenceScalarFieldEnum[]
}

/**
 * Recipient without action
 */
export type RecipientDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
}
