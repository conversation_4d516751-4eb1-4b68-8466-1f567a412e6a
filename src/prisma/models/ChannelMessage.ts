
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ChannelMessage` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ChannelMessage
 * 
 */
export type ChannelMessageModel = runtime.Types.Result.DefaultSelection<Prisma.$ChannelMessagePayload>

export type AggregateChannelMessage = {
  _count: ChannelMessageCountAggregateOutputType | null
  _min: ChannelMessageMinAggregateOutputType | null
  _max: ChannelMessageMaxAggregateOutputType | null
}

export type ChannelMessageMinAggregateOutputType = {
  id: string | null
  messageId: string | null
  status: $Enums.Status | null
  error: string | null
  channelId: string | null
  endpointIdentifier: string | null
  title: string | null
  text: string | null
  transactionId: string | null
  isRecipientMessage: boolean | null
  sentAt: Date | null
  aborted: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ChannelMessageMaxAggregateOutputType = {
  id: string | null
  messageId: string | null
  status: $Enums.Status | null
  error: string | null
  channelId: string | null
  endpointIdentifier: string | null
  title: string | null
  text: string | null
  transactionId: string | null
  isRecipientMessage: boolean | null
  sentAt: Date | null
  aborted: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ChannelMessageCountAggregateOutputType = {
  id: number
  messageId: number
  status: number
  error: number
  channelId: number
  endpointIdentifier: number
  title: number
  text: number
  transactionId: number
  isRecipientMessage: number
  sentAt: number
  aborted: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ChannelMessageMinAggregateInputType = {
  id?: true
  messageId?: true
  status?: true
  error?: true
  channelId?: true
  endpointIdentifier?: true
  title?: true
  text?: true
  transactionId?: true
  isRecipientMessage?: true
  sentAt?: true
  aborted?: true
  createdAt?: true
  updatedAt?: true
}

export type ChannelMessageMaxAggregateInputType = {
  id?: true
  messageId?: true
  status?: true
  error?: true
  channelId?: true
  endpointIdentifier?: true
  title?: true
  text?: true
  transactionId?: true
  isRecipientMessage?: true
  sentAt?: true
  aborted?: true
  createdAt?: true
  updatedAt?: true
}

export type ChannelMessageCountAggregateInputType = {
  id?: true
  messageId?: true
  status?: true
  error?: true
  channelId?: true
  endpointIdentifier?: true
  title?: true
  text?: true
  transactionId?: true
  isRecipientMessage?: true
  sentAt?: true
  aborted?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ChannelMessageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ChannelMessage to aggregate.
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelMessages to fetch.
   */
  orderBy?: Prisma.ChannelMessageOrderByWithRelationInput | Prisma.ChannelMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ChannelMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ChannelMessages
  **/
  _count?: true | ChannelMessageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ChannelMessageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ChannelMessageMaxAggregateInputType
}

export type GetChannelMessageAggregateType<T extends ChannelMessageAggregateArgs> = {
      [P in keyof T & keyof AggregateChannelMessage]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateChannelMessage[P]>
    : Prisma.GetScalarType<T[P], AggregateChannelMessage[P]>
}




export type ChannelMessageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelMessageWhereInput
  orderBy?: Prisma.ChannelMessageOrderByWithAggregationInput | Prisma.ChannelMessageOrderByWithAggregationInput[]
  by: Prisma.ChannelMessageScalarFieldEnum[] | Prisma.ChannelMessageScalarFieldEnum
  having?: Prisma.ChannelMessageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ChannelMessageCountAggregateInputType | true
  _min?: ChannelMessageMinAggregateInputType
  _max?: ChannelMessageMaxAggregateInputType
}

export type ChannelMessageGroupByOutputType = {
  id: string
  messageId: string
  status: $Enums.Status
  error: string
  channelId: string | null
  endpointIdentifier: string
  title: string
  text: string
  transactionId: string
  isRecipientMessage: boolean
  sentAt: Date | null
  aborted: boolean
  createdAt: Date
  updatedAt: Date
  _count: ChannelMessageCountAggregateOutputType | null
  _min: ChannelMessageMinAggregateOutputType | null
  _max: ChannelMessageMaxAggregateOutputType | null
}

type GetChannelMessageGroupByPayload<T extends ChannelMessageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ChannelMessageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ChannelMessageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ChannelMessageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ChannelMessageGroupByOutputType[P]>
      }
    >
  > 



export type ChannelMessageWhereInput = {
  AND?: Prisma.ChannelMessageWhereInput | Prisma.ChannelMessageWhereInput[]
  OR?: Prisma.ChannelMessageWhereInput[]
  NOT?: Prisma.ChannelMessageWhereInput | Prisma.ChannelMessageWhereInput[]
  id?: Prisma.StringFilter<"ChannelMessage"> | string
  messageId?: Prisma.StringFilter<"ChannelMessage"> | string
  status?: Prisma.EnumStatusFilter<"ChannelMessage"> | $Enums.Status
  error?: Prisma.StringFilter<"ChannelMessage"> | string
  channelId?: Prisma.StringNullableFilter<"ChannelMessage"> | string | null
  endpointIdentifier?: Prisma.StringFilter<"ChannelMessage"> | string
  title?: Prisma.StringFilter<"ChannelMessage"> | string
  text?: Prisma.StringFilter<"ChannelMessage"> | string
  transactionId?: Prisma.StringFilter<"ChannelMessage"> | string
  isRecipientMessage?: Prisma.BoolFilter<"ChannelMessage"> | boolean
  sentAt?: Prisma.DateTimeNullableFilter<"ChannelMessage"> | Date | string | null
  aborted?: Prisma.BoolFilter<"ChannelMessage"> | boolean
  createdAt?: Prisma.DateTimeFilter<"ChannelMessage"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ChannelMessage"> | Date | string
  message?: Prisma.XOR<Prisma.RecipientMessageScalarRelationFilter, Prisma.RecipientMessageWhereInput>
  channel?: Prisma.XOR<Prisma.ChannelNullableScalarRelationFilter, Prisma.ChannelWhereInput> | null
}

export type ChannelMessageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  channelId?: Prisma.SortOrderInput | Prisma.SortOrder
  endpointIdentifier?: Prisma.SortOrder
  title?: Prisma.SortOrder
  text?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isRecipientMessage?: Prisma.SortOrder
  sentAt?: Prisma.SortOrderInput | Prisma.SortOrder
  aborted?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  message?: Prisma.RecipientMessageOrderByWithRelationInput
  channel?: Prisma.ChannelOrderByWithRelationInput
}

export type ChannelMessageWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.ChannelMessageWhereInput | Prisma.ChannelMessageWhereInput[]
  OR?: Prisma.ChannelMessageWhereInput[]
  NOT?: Prisma.ChannelMessageWhereInput | Prisma.ChannelMessageWhereInput[]
  messageId?: Prisma.StringFilter<"ChannelMessage"> | string
  status?: Prisma.EnumStatusFilter<"ChannelMessage"> | $Enums.Status
  error?: Prisma.StringFilter<"ChannelMessage"> | string
  channelId?: Prisma.StringNullableFilter<"ChannelMessage"> | string | null
  endpointIdentifier?: Prisma.StringFilter<"ChannelMessage"> | string
  title?: Prisma.StringFilter<"ChannelMessage"> | string
  text?: Prisma.StringFilter<"ChannelMessage"> | string
  transactionId?: Prisma.StringFilter<"ChannelMessage"> | string
  isRecipientMessage?: Prisma.BoolFilter<"ChannelMessage"> | boolean
  sentAt?: Prisma.DateTimeNullableFilter<"ChannelMessage"> | Date | string | null
  aborted?: Prisma.BoolFilter<"ChannelMessage"> | boolean
  createdAt?: Prisma.DateTimeFilter<"ChannelMessage"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ChannelMessage"> | Date | string
  message?: Prisma.XOR<Prisma.RecipientMessageScalarRelationFilter, Prisma.RecipientMessageWhereInput>
  channel?: Prisma.XOR<Prisma.ChannelNullableScalarRelationFilter, Prisma.ChannelWhereInput> | null
}, "id">

export type ChannelMessageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  channelId?: Prisma.SortOrderInput | Prisma.SortOrder
  endpointIdentifier?: Prisma.SortOrder
  title?: Prisma.SortOrder
  text?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isRecipientMessage?: Prisma.SortOrder
  sentAt?: Prisma.SortOrderInput | Prisma.SortOrder
  aborted?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ChannelMessageCountOrderByAggregateInput
  _max?: Prisma.ChannelMessageMaxOrderByAggregateInput
  _min?: Prisma.ChannelMessageMinOrderByAggregateInput
}

export type ChannelMessageScalarWhereWithAggregatesInput = {
  AND?: Prisma.ChannelMessageScalarWhereWithAggregatesInput | Prisma.ChannelMessageScalarWhereWithAggregatesInput[]
  OR?: Prisma.ChannelMessageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ChannelMessageScalarWhereWithAggregatesInput | Prisma.ChannelMessageScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  messageId?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  status?: Prisma.EnumStatusWithAggregatesFilter<"ChannelMessage"> | $Enums.Status
  error?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  channelId?: Prisma.StringNullableWithAggregatesFilter<"ChannelMessage"> | string | null
  endpointIdentifier?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  title?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  text?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  transactionId?: Prisma.StringWithAggregatesFilter<"ChannelMessage"> | string
  isRecipientMessage?: Prisma.BoolWithAggregatesFilter<"ChannelMessage"> | boolean
  sentAt?: Prisma.DateTimeNullableWithAggregatesFilter<"ChannelMessage"> | Date | string | null
  aborted?: Prisma.BoolWithAggregatesFilter<"ChannelMessage"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"ChannelMessage"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"ChannelMessage"> | Date | string
}

export type ChannelMessageCreateInput = {
  id?: string
  status?: $Enums.Status
  error?: string
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  message: Prisma.RecipientMessageCreateNestedOneWithoutChannelMessagesInput
  channel?: Prisma.ChannelCreateNestedOneWithoutChannelMessagesInput
}

export type ChannelMessageUncheckedCreateInput = {
  id?: string
  messageId: string
  status?: $Enums.Status
  error?: string
  channelId?: string | null
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelMessageUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  message?: Prisma.RecipientMessageUpdateOneRequiredWithoutChannelMessagesNestedInput
  channel?: Prisma.ChannelUpdateOneWithoutChannelMessagesNestedInput
}

export type ChannelMessageUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelMessageCreateManyInput = {
  id?: string
  messageId: string
  status?: $Enums.Status
  error?: string
  channelId?: string | null
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelMessageUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelMessageUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelMessageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  endpointIdentifier?: Prisma.SortOrder
  title?: Prisma.SortOrder
  text?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isRecipientMessage?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  aborted?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ChannelMessageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  endpointIdentifier?: Prisma.SortOrder
  title?: Prisma.SortOrder
  text?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isRecipientMessage?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  aborted?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ChannelMessageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  channelId?: Prisma.SortOrder
  endpointIdentifier?: Prisma.SortOrder
  title?: Prisma.SortOrder
  text?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isRecipientMessage?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  aborted?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ChannelMessageListRelationFilter = {
  every?: Prisma.ChannelMessageWhereInput
  some?: Prisma.ChannelMessageWhereInput
  none?: Prisma.ChannelMessageWhereInput
}

export type ChannelMessageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type EnumStatusFieldUpdateOperationsInput = {
  set?: $Enums.Status
}

export type NullableDateTimeFieldUpdateOperationsInput = {
  set?: Date | string | null
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type ChannelMessageCreateNestedManyWithoutChannelInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutChannelInput, Prisma.ChannelMessageUncheckedCreateWithoutChannelInput> | Prisma.ChannelMessageCreateWithoutChannelInput[] | Prisma.ChannelMessageUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutChannelInput | Prisma.ChannelMessageCreateOrConnectWithoutChannelInput[]
  createMany?: Prisma.ChannelMessageCreateManyChannelInputEnvelope
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
}

export type ChannelMessageUncheckedCreateNestedManyWithoutChannelInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutChannelInput, Prisma.ChannelMessageUncheckedCreateWithoutChannelInput> | Prisma.ChannelMessageCreateWithoutChannelInput[] | Prisma.ChannelMessageUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutChannelInput | Prisma.ChannelMessageCreateOrConnectWithoutChannelInput[]
  createMany?: Prisma.ChannelMessageCreateManyChannelInputEnvelope
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
}

export type ChannelMessageUpdateManyWithoutChannelNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutChannelInput, Prisma.ChannelMessageUncheckedCreateWithoutChannelInput> | Prisma.ChannelMessageCreateWithoutChannelInput[] | Prisma.ChannelMessageUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutChannelInput | Prisma.ChannelMessageCreateOrConnectWithoutChannelInput[]
  upsert?: Prisma.ChannelMessageUpsertWithWhereUniqueWithoutChannelInput | Prisma.ChannelMessageUpsertWithWhereUniqueWithoutChannelInput[]
  createMany?: Prisma.ChannelMessageCreateManyChannelInputEnvelope
  set?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  disconnect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  delete?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  update?: Prisma.ChannelMessageUpdateWithWhereUniqueWithoutChannelInput | Prisma.ChannelMessageUpdateWithWhereUniqueWithoutChannelInput[]
  updateMany?: Prisma.ChannelMessageUpdateManyWithWhereWithoutChannelInput | Prisma.ChannelMessageUpdateManyWithWhereWithoutChannelInput[]
  deleteMany?: Prisma.ChannelMessageScalarWhereInput | Prisma.ChannelMessageScalarWhereInput[]
}

export type ChannelMessageUncheckedUpdateManyWithoutChannelNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutChannelInput, Prisma.ChannelMessageUncheckedCreateWithoutChannelInput> | Prisma.ChannelMessageCreateWithoutChannelInput[] | Prisma.ChannelMessageUncheckedCreateWithoutChannelInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutChannelInput | Prisma.ChannelMessageCreateOrConnectWithoutChannelInput[]
  upsert?: Prisma.ChannelMessageUpsertWithWhereUniqueWithoutChannelInput | Prisma.ChannelMessageUpsertWithWhereUniqueWithoutChannelInput[]
  createMany?: Prisma.ChannelMessageCreateManyChannelInputEnvelope
  set?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  disconnect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  delete?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  update?: Prisma.ChannelMessageUpdateWithWhereUniqueWithoutChannelInput | Prisma.ChannelMessageUpdateWithWhereUniqueWithoutChannelInput[]
  updateMany?: Prisma.ChannelMessageUpdateManyWithWhereWithoutChannelInput | Prisma.ChannelMessageUpdateManyWithWhereWithoutChannelInput[]
  deleteMany?: Prisma.ChannelMessageScalarWhereInput | Prisma.ChannelMessageScalarWhereInput[]
}

export type ChannelMessageCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutMessageInput, Prisma.ChannelMessageUncheckedCreateWithoutMessageInput> | Prisma.ChannelMessageCreateWithoutMessageInput[] | Prisma.ChannelMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutMessageInput | Prisma.ChannelMessageCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.ChannelMessageCreateManyMessageInputEnvelope
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
}

export type ChannelMessageUncheckedCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutMessageInput, Prisma.ChannelMessageUncheckedCreateWithoutMessageInput> | Prisma.ChannelMessageCreateWithoutMessageInput[] | Prisma.ChannelMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutMessageInput | Prisma.ChannelMessageCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.ChannelMessageCreateManyMessageInputEnvelope
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
}

export type ChannelMessageUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutMessageInput, Prisma.ChannelMessageUncheckedCreateWithoutMessageInput> | Prisma.ChannelMessageCreateWithoutMessageInput[] | Prisma.ChannelMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutMessageInput | Prisma.ChannelMessageCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.ChannelMessageUpsertWithWhereUniqueWithoutMessageInput | Prisma.ChannelMessageUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.ChannelMessageCreateManyMessageInputEnvelope
  set?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  disconnect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  delete?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  update?: Prisma.ChannelMessageUpdateWithWhereUniqueWithoutMessageInput | Prisma.ChannelMessageUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.ChannelMessageUpdateManyWithWhereWithoutMessageInput | Prisma.ChannelMessageUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.ChannelMessageScalarWhereInput | Prisma.ChannelMessageScalarWhereInput[]
}

export type ChannelMessageUncheckedUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.ChannelMessageCreateWithoutMessageInput, Prisma.ChannelMessageUncheckedCreateWithoutMessageInput> | Prisma.ChannelMessageCreateWithoutMessageInput[] | Prisma.ChannelMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.ChannelMessageCreateOrConnectWithoutMessageInput | Prisma.ChannelMessageCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.ChannelMessageUpsertWithWhereUniqueWithoutMessageInput | Prisma.ChannelMessageUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.ChannelMessageCreateManyMessageInputEnvelope
  set?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  disconnect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  delete?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  connect?: Prisma.ChannelMessageWhereUniqueInput | Prisma.ChannelMessageWhereUniqueInput[]
  update?: Prisma.ChannelMessageUpdateWithWhereUniqueWithoutMessageInput | Prisma.ChannelMessageUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.ChannelMessageUpdateManyWithWhereWithoutMessageInput | Prisma.ChannelMessageUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.ChannelMessageScalarWhereInput | Prisma.ChannelMessageScalarWhereInput[]
}

export type ChannelMessageCreateWithoutChannelInput = {
  id?: string
  status?: $Enums.Status
  error?: string
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  message: Prisma.RecipientMessageCreateNestedOneWithoutChannelMessagesInput
}

export type ChannelMessageUncheckedCreateWithoutChannelInput = {
  id?: string
  messageId: string
  status?: $Enums.Status
  error?: string
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelMessageCreateOrConnectWithoutChannelInput = {
  where: Prisma.ChannelMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelMessageCreateWithoutChannelInput, Prisma.ChannelMessageUncheckedCreateWithoutChannelInput>
}

export type ChannelMessageCreateManyChannelInputEnvelope = {
  data: Prisma.ChannelMessageCreateManyChannelInput | Prisma.ChannelMessageCreateManyChannelInput[]
  skipDuplicates?: boolean
}

export type ChannelMessageUpsertWithWhereUniqueWithoutChannelInput = {
  where: Prisma.ChannelMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.ChannelMessageUpdateWithoutChannelInput, Prisma.ChannelMessageUncheckedUpdateWithoutChannelInput>
  create: Prisma.XOR<Prisma.ChannelMessageCreateWithoutChannelInput, Prisma.ChannelMessageUncheckedCreateWithoutChannelInput>
}

export type ChannelMessageUpdateWithWhereUniqueWithoutChannelInput = {
  where: Prisma.ChannelMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.ChannelMessageUpdateWithoutChannelInput, Prisma.ChannelMessageUncheckedUpdateWithoutChannelInput>
}

export type ChannelMessageUpdateManyWithWhereWithoutChannelInput = {
  where: Prisma.ChannelMessageScalarWhereInput
  data: Prisma.XOR<Prisma.ChannelMessageUpdateManyMutationInput, Prisma.ChannelMessageUncheckedUpdateManyWithoutChannelInput>
}

export type ChannelMessageScalarWhereInput = {
  AND?: Prisma.ChannelMessageScalarWhereInput | Prisma.ChannelMessageScalarWhereInput[]
  OR?: Prisma.ChannelMessageScalarWhereInput[]
  NOT?: Prisma.ChannelMessageScalarWhereInput | Prisma.ChannelMessageScalarWhereInput[]
  id?: Prisma.StringFilter<"ChannelMessage"> | string
  messageId?: Prisma.StringFilter<"ChannelMessage"> | string
  status?: Prisma.EnumStatusFilter<"ChannelMessage"> | $Enums.Status
  error?: Prisma.StringFilter<"ChannelMessage"> | string
  channelId?: Prisma.StringNullableFilter<"ChannelMessage"> | string | null
  endpointIdentifier?: Prisma.StringFilter<"ChannelMessage"> | string
  title?: Prisma.StringFilter<"ChannelMessage"> | string
  text?: Prisma.StringFilter<"ChannelMessage"> | string
  transactionId?: Prisma.StringFilter<"ChannelMessage"> | string
  isRecipientMessage?: Prisma.BoolFilter<"ChannelMessage"> | boolean
  sentAt?: Prisma.DateTimeNullableFilter<"ChannelMessage"> | Date | string | null
  aborted?: Prisma.BoolFilter<"ChannelMessage"> | boolean
  createdAt?: Prisma.DateTimeFilter<"ChannelMessage"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ChannelMessage"> | Date | string
}

export type ChannelMessageCreateWithoutMessageInput = {
  id?: string
  status?: $Enums.Status
  error?: string
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  channel?: Prisma.ChannelCreateNestedOneWithoutChannelMessagesInput
}

export type ChannelMessageUncheckedCreateWithoutMessageInput = {
  id?: string
  status?: $Enums.Status
  error?: string
  channelId?: string | null
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelMessageCreateOrConnectWithoutMessageInput = {
  where: Prisma.ChannelMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.ChannelMessageCreateWithoutMessageInput, Prisma.ChannelMessageUncheckedCreateWithoutMessageInput>
}

export type ChannelMessageCreateManyMessageInputEnvelope = {
  data: Prisma.ChannelMessageCreateManyMessageInput | Prisma.ChannelMessageCreateManyMessageInput[]
  skipDuplicates?: boolean
}

export type ChannelMessageUpsertWithWhereUniqueWithoutMessageInput = {
  where: Prisma.ChannelMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.ChannelMessageUpdateWithoutMessageInput, Prisma.ChannelMessageUncheckedUpdateWithoutMessageInput>
  create: Prisma.XOR<Prisma.ChannelMessageCreateWithoutMessageInput, Prisma.ChannelMessageUncheckedCreateWithoutMessageInput>
}

export type ChannelMessageUpdateWithWhereUniqueWithoutMessageInput = {
  where: Prisma.ChannelMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.ChannelMessageUpdateWithoutMessageInput, Prisma.ChannelMessageUncheckedUpdateWithoutMessageInput>
}

export type ChannelMessageUpdateManyWithWhereWithoutMessageInput = {
  where: Prisma.ChannelMessageScalarWhereInput
  data: Prisma.XOR<Prisma.ChannelMessageUpdateManyMutationInput, Prisma.ChannelMessageUncheckedUpdateManyWithoutMessageInput>
}

export type ChannelMessageCreateManyChannelInput = {
  id?: string
  messageId: string
  status?: $Enums.Status
  error?: string
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelMessageUpdateWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  message?: Prisma.RecipientMessageUpdateOneRequiredWithoutChannelMessagesNestedInput
}

export type ChannelMessageUncheckedUpdateWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelMessageUncheckedUpdateManyWithoutChannelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelMessageCreateManyMessageInput = {
  id?: string
  status?: $Enums.Status
  error?: string
  channelId?: string | null
  endpointIdentifier: string
  title?: string
  text: string
  transactionId?: string
  isRecipientMessage?: boolean
  sentAt?: Date | string | null
  aborted?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ChannelMessageUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  channel?: Prisma.ChannelUpdateOneWithoutChannelMessagesNestedInput
}

export type ChannelMessageUncheckedUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ChannelMessageUncheckedUpdateManyWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  channelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  endpointIdentifier?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  text?: Prisma.StringFieldUpdateOperationsInput | string
  transactionId?: Prisma.StringFieldUpdateOperationsInput | string
  isRecipientMessage?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aborted?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type ChannelMessageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  status?: boolean
  error?: boolean
  channelId?: boolean
  endpointIdentifier?: boolean
  title?: boolean
  text?: boolean
  transactionId?: boolean
  isRecipientMessage?: boolean
  sentAt?: boolean
  aborted?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  message?: boolean | Prisma.RecipientMessageDefaultArgs<ExtArgs>
  channel?: boolean | Prisma.ChannelMessage$channelArgs<ExtArgs>
}, ExtArgs["result"]["channelMessage"]>

export type ChannelMessageSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  status?: boolean
  error?: boolean
  channelId?: boolean
  endpointIdentifier?: boolean
  title?: boolean
  text?: boolean
  transactionId?: boolean
  isRecipientMessage?: boolean
  sentAt?: boolean
  aborted?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  message?: boolean | Prisma.RecipientMessageDefaultArgs<ExtArgs>
  channel?: boolean | Prisma.ChannelMessage$channelArgs<ExtArgs>
}, ExtArgs["result"]["channelMessage"]>

export type ChannelMessageSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  status?: boolean
  error?: boolean
  channelId?: boolean
  endpointIdentifier?: boolean
  title?: boolean
  text?: boolean
  transactionId?: boolean
  isRecipientMessage?: boolean
  sentAt?: boolean
  aborted?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  message?: boolean | Prisma.RecipientMessageDefaultArgs<ExtArgs>
  channel?: boolean | Prisma.ChannelMessage$channelArgs<ExtArgs>
}, ExtArgs["result"]["channelMessage"]>

export type ChannelMessageSelectScalar = {
  id?: boolean
  messageId?: boolean
  status?: boolean
  error?: boolean
  channelId?: boolean
  endpointIdentifier?: boolean
  title?: boolean
  text?: boolean
  transactionId?: boolean
  isRecipientMessage?: boolean
  sentAt?: boolean
  aborted?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ChannelMessageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "messageId" | "status" | "error" | "channelId" | "endpointIdentifier" | "title" | "text" | "transactionId" | "isRecipientMessage" | "sentAt" | "aborted" | "createdAt" | "updatedAt", ExtArgs["result"]["channelMessage"]>
export type ChannelMessageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.RecipientMessageDefaultArgs<ExtArgs>
  channel?: boolean | Prisma.ChannelMessage$channelArgs<ExtArgs>
}
export type ChannelMessageIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.RecipientMessageDefaultArgs<ExtArgs>
  channel?: boolean | Prisma.ChannelMessage$channelArgs<ExtArgs>
}
export type ChannelMessageIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.RecipientMessageDefaultArgs<ExtArgs>
  channel?: boolean | Prisma.ChannelMessage$channelArgs<ExtArgs>
}

export type $ChannelMessagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ChannelMessage"
  objects: {
    message: Prisma.$RecipientMessagePayload<ExtArgs>
    channel: Prisma.$ChannelPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    messageId: string
    status: $Enums.Status
    error: string
    channelId: string | null
    endpointIdentifier: string
    title: string
    text: string
    transactionId: string
    isRecipientMessage: boolean
    sentAt: Date | null
    aborted: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["channelMessage"]>
  composites: {}
}

export type ChannelMessageGetPayload<S extends boolean | null | undefined | ChannelMessageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload, S>

export type ChannelMessageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ChannelMessageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: ChannelMessageCountAggregateInputType | true
  }

export interface ChannelMessageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ChannelMessage'], meta: { name: 'ChannelMessage' } }
  /**
   * Find zero or one ChannelMessage that matches the filter.
   * @param {ChannelMessageFindUniqueArgs} args - Arguments to find a ChannelMessage
   * @example
   * // Get one ChannelMessage
   * const channelMessage = await prisma.channelMessage.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ChannelMessageFindUniqueArgs>(args: Prisma.SelectSubset<T, ChannelMessageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ChannelMessage that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ChannelMessageFindUniqueOrThrowArgs} args - Arguments to find a ChannelMessage
   * @example
   * // Get one ChannelMessage
   * const channelMessage = await prisma.channelMessage.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ChannelMessageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ChannelMessageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ChannelMessage that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageFindFirstArgs} args - Arguments to find a ChannelMessage
   * @example
   * // Get one ChannelMessage
   * const channelMessage = await prisma.channelMessage.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ChannelMessageFindFirstArgs>(args?: Prisma.SelectSubset<T, ChannelMessageFindFirstArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ChannelMessage that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageFindFirstOrThrowArgs} args - Arguments to find a ChannelMessage
   * @example
   * // Get one ChannelMessage
   * const channelMessage = await prisma.channelMessage.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ChannelMessageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ChannelMessageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ChannelMessages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ChannelMessages
   * const channelMessages = await prisma.channelMessage.findMany()
   * 
   * // Get first 10 ChannelMessages
   * const channelMessages = await prisma.channelMessage.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const channelMessageWithIdOnly = await prisma.channelMessage.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ChannelMessageFindManyArgs>(args?: Prisma.SelectSubset<T, ChannelMessageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ChannelMessage.
   * @param {ChannelMessageCreateArgs} args - Arguments to create a ChannelMessage.
   * @example
   * // Create one ChannelMessage
   * const ChannelMessage = await prisma.channelMessage.create({
   *   data: {
   *     // ... data to create a ChannelMessage
   *   }
   * })
   * 
   */
  create<T extends ChannelMessageCreateArgs>(args: Prisma.SelectSubset<T, ChannelMessageCreateArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ChannelMessages.
   * @param {ChannelMessageCreateManyArgs} args - Arguments to create many ChannelMessages.
   * @example
   * // Create many ChannelMessages
   * const channelMessage = await prisma.channelMessage.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ChannelMessageCreateManyArgs>(args?: Prisma.SelectSubset<T, ChannelMessageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ChannelMessages and returns the data saved in the database.
   * @param {ChannelMessageCreateManyAndReturnArgs} args - Arguments to create many ChannelMessages.
   * @example
   * // Create many ChannelMessages
   * const channelMessage = await prisma.channelMessage.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ChannelMessages and only return the `id`
   * const channelMessageWithIdOnly = await prisma.channelMessage.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ChannelMessageCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ChannelMessageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ChannelMessage.
   * @param {ChannelMessageDeleteArgs} args - Arguments to delete one ChannelMessage.
   * @example
   * // Delete one ChannelMessage
   * const ChannelMessage = await prisma.channelMessage.delete({
   *   where: {
   *     // ... filter to delete one ChannelMessage
   *   }
   * })
   * 
   */
  delete<T extends ChannelMessageDeleteArgs>(args: Prisma.SelectSubset<T, ChannelMessageDeleteArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ChannelMessage.
   * @param {ChannelMessageUpdateArgs} args - Arguments to update one ChannelMessage.
   * @example
   * // Update one ChannelMessage
   * const channelMessage = await prisma.channelMessage.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ChannelMessageUpdateArgs>(args: Prisma.SelectSubset<T, ChannelMessageUpdateArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ChannelMessages.
   * @param {ChannelMessageDeleteManyArgs} args - Arguments to filter ChannelMessages to delete.
   * @example
   * // Delete a few ChannelMessages
   * const { count } = await prisma.channelMessage.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ChannelMessageDeleteManyArgs>(args?: Prisma.SelectSubset<T, ChannelMessageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ChannelMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ChannelMessages
   * const channelMessage = await prisma.channelMessage.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ChannelMessageUpdateManyArgs>(args: Prisma.SelectSubset<T, ChannelMessageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ChannelMessages and returns the data updated in the database.
   * @param {ChannelMessageUpdateManyAndReturnArgs} args - Arguments to update many ChannelMessages.
   * @example
   * // Update many ChannelMessages
   * const channelMessage = await prisma.channelMessage.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ChannelMessages and only return the `id`
   * const channelMessageWithIdOnly = await prisma.channelMessage.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ChannelMessageUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ChannelMessageUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ChannelMessage.
   * @param {ChannelMessageUpsertArgs} args - Arguments to update or create a ChannelMessage.
   * @example
   * // Update or create a ChannelMessage
   * const channelMessage = await prisma.channelMessage.upsert({
   *   create: {
   *     // ... data to create a ChannelMessage
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ChannelMessage we want to update
   *   }
   * })
   */
  upsert<T extends ChannelMessageUpsertArgs>(args: Prisma.SelectSubset<T, ChannelMessageUpsertArgs<ExtArgs>>): Prisma.Prisma__ChannelMessageClient<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ChannelMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageCountArgs} args - Arguments to filter ChannelMessages to count.
   * @example
   * // Count the number of ChannelMessages
   * const count = await prisma.channelMessage.count({
   *   where: {
   *     // ... the filter for the ChannelMessages we want to count
   *   }
   * })
  **/
  count<T extends ChannelMessageCountArgs>(
    args?: Prisma.Subset<T, ChannelMessageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ChannelMessageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ChannelMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ChannelMessageAggregateArgs>(args: Prisma.Subset<T, ChannelMessageAggregateArgs>): Prisma.PrismaPromise<GetChannelMessageAggregateType<T>>

  /**
   * Group by ChannelMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ChannelMessageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ChannelMessageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ChannelMessageGroupByArgs['orderBy'] }
      : { orderBy?: ChannelMessageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ChannelMessageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetChannelMessageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ChannelMessage model
 */
readonly fields: ChannelMessageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ChannelMessage.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ChannelMessageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  message<T extends Prisma.RecipientMessageDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientMessageDefaultArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  channel<T extends Prisma.ChannelMessage$channelArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ChannelMessage$channelArgs<ExtArgs>>): Prisma.Prisma__ChannelClient<runtime.Types.Result.GetResult<Prisma.$ChannelPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ChannelMessage model
 */
export interface ChannelMessageFieldRefs {
  readonly id: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly messageId: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly status: Prisma.FieldRef<"ChannelMessage", 'Status'>
  readonly error: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly channelId: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly endpointIdentifier: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly title: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly text: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly transactionId: Prisma.FieldRef<"ChannelMessage", 'String'>
  readonly isRecipientMessage: Prisma.FieldRef<"ChannelMessage", 'Boolean'>
  readonly sentAt: Prisma.FieldRef<"ChannelMessage", 'DateTime'>
  readonly aborted: Prisma.FieldRef<"ChannelMessage", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"ChannelMessage", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"ChannelMessage", 'DateTime'>
}
    

// Custom InputTypes
/**
 * ChannelMessage findUnique
 */
export type ChannelMessageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * Filter, which ChannelMessage to fetch.
   */
  where: Prisma.ChannelMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage findUniqueOrThrow
 */
export type ChannelMessageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * Filter, which ChannelMessage to fetch.
   */
  where: Prisma.ChannelMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage findFirst
 */
export type ChannelMessageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * Filter, which ChannelMessage to fetch.
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelMessages to fetch.
   */
  orderBy?: Prisma.ChannelMessageOrderByWithRelationInput | Prisma.ChannelMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ChannelMessages.
   */
  cursor?: Prisma.ChannelMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ChannelMessages.
   */
  distinct?: Prisma.ChannelMessageScalarFieldEnum | Prisma.ChannelMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage findFirstOrThrow
 */
export type ChannelMessageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * Filter, which ChannelMessage to fetch.
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelMessages to fetch.
   */
  orderBy?: Prisma.ChannelMessageOrderByWithRelationInput | Prisma.ChannelMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ChannelMessages.
   */
  cursor?: Prisma.ChannelMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ChannelMessages.
   */
  distinct?: Prisma.ChannelMessageScalarFieldEnum | Prisma.ChannelMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage findMany
 */
export type ChannelMessageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * Filter, which ChannelMessages to fetch.
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ChannelMessages to fetch.
   */
  orderBy?: Prisma.ChannelMessageOrderByWithRelationInput | Prisma.ChannelMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ChannelMessages.
   */
  cursor?: Prisma.ChannelMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ChannelMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ChannelMessages.
   */
  skip?: number
  distinct?: Prisma.ChannelMessageScalarFieldEnum | Prisma.ChannelMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage create
 */
export type ChannelMessageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * The data needed to create a ChannelMessage.
   */
  data: Prisma.XOR<Prisma.ChannelMessageCreateInput, Prisma.ChannelMessageUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage createMany
 */
export type ChannelMessageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ChannelMessages.
   */
  data: Prisma.ChannelMessageCreateManyInput | Prisma.ChannelMessageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ChannelMessage createManyAndReturn
 */
export type ChannelMessageCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * The data used to create many ChannelMessages.
   */
  data: Prisma.ChannelMessageCreateManyInput | Prisma.ChannelMessageCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ChannelMessage update
 */
export type ChannelMessageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * The data needed to update a ChannelMessage.
   */
  data: Prisma.XOR<Prisma.ChannelMessageUpdateInput, Prisma.ChannelMessageUncheckedUpdateInput>
  /**
   * Choose, which ChannelMessage to update.
   */
  where: Prisma.ChannelMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage updateMany
 */
export type ChannelMessageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ChannelMessages.
   */
  data: Prisma.XOR<Prisma.ChannelMessageUpdateManyMutationInput, Prisma.ChannelMessageUncheckedUpdateManyInput>
  /**
   * Filter which ChannelMessages to update
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * Limit how many ChannelMessages to update.
   */
  limit?: number
}

/**
 * ChannelMessage updateManyAndReturn
 */
export type ChannelMessageUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * The data used to update ChannelMessages.
   */
  data: Prisma.XOR<Prisma.ChannelMessageUpdateManyMutationInput, Prisma.ChannelMessageUncheckedUpdateManyInput>
  /**
   * Filter which ChannelMessages to update
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * Limit how many ChannelMessages to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ChannelMessage upsert
 */
export type ChannelMessageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * The filter to search for the ChannelMessage to update in case it exists.
   */
  where: Prisma.ChannelMessageWhereUniqueInput
  /**
   * In case the ChannelMessage found by the `where` argument doesn't exist, create a new ChannelMessage with this data.
   */
  create: Prisma.XOR<Prisma.ChannelMessageCreateInput, Prisma.ChannelMessageUncheckedCreateInput>
  /**
   * In case the ChannelMessage was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ChannelMessageUpdateInput, Prisma.ChannelMessageUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage delete
 */
export type ChannelMessageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  /**
   * Filter which ChannelMessage to delete.
   */
  where: Prisma.ChannelMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ChannelMessage deleteMany
 */
export type ChannelMessageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ChannelMessages to delete
   */
  where?: Prisma.ChannelMessageWhereInput
  /**
   * Limit how many ChannelMessages to delete.
   */
  limit?: number
}

/**
 * ChannelMessage.channel
 */
export type ChannelMessage$channelArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Channel
   */
  select?: Prisma.ChannelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Channel
   */
  omit?: Prisma.ChannelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelInclude<ExtArgs> | null
  where?: Prisma.ChannelWhereInput
}

/**
 * ChannelMessage without action
 */
export type ChannelMessageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
}
