
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `RecipientMessage` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model RecipientMessage
 * 
 */
export type RecipientMessageModel = runtime.Types.Result.DefaultSelection<Prisma.$RecipientMessagePayload>

export type AggregateRecipientMessage = {
  _count: RecipientMessageCountAggregateOutputType | null
  _min: RecipientMessageMinAggregateOutputType | null
  _max: RecipientMessageMaxAggregateOutputType | null
}

export type RecipientMessageMinAggregateOutputType = {
  id: string | null
  messageId: string | null
  recipientId: string | null
  acknowledged: boolean | null
  acknowledgedAt: Date | null
  actionTaken: boolean | null
  actionTakenAt: Date | null
  idUsed: string | null
  status: $Enums.Status | null
  error: string | null
  relevant: boolean | null
}

export type RecipientMessageMaxAggregateOutputType = {
  id: string | null
  messageId: string | null
  recipientId: string | null
  acknowledged: boolean | null
  acknowledgedAt: Date | null
  actionTaken: boolean | null
  actionTakenAt: Date | null
  idUsed: string | null
  status: $Enums.Status | null
  error: string | null
  relevant: boolean | null
}

export type RecipientMessageCountAggregateOutputType = {
  id: number
  messageId: number
  recipientId: number
  acknowledged: number
  acknowledgedAt: number
  actionTaken: number
  actionTakenAt: number
  idUsed: number
  status: number
  error: number
  relevant: number
  _all: number
}


export type RecipientMessageMinAggregateInputType = {
  id?: true
  messageId?: true
  recipientId?: true
  acknowledged?: true
  acknowledgedAt?: true
  actionTaken?: true
  actionTakenAt?: true
  idUsed?: true
  status?: true
  error?: true
  relevant?: true
}

export type RecipientMessageMaxAggregateInputType = {
  id?: true
  messageId?: true
  recipientId?: true
  acknowledged?: true
  acknowledgedAt?: true
  actionTaken?: true
  actionTakenAt?: true
  idUsed?: true
  status?: true
  error?: true
  relevant?: true
}

export type RecipientMessageCountAggregateInputType = {
  id?: true
  messageId?: true
  recipientId?: true
  acknowledged?: true
  acknowledgedAt?: true
  actionTaken?: true
  actionTakenAt?: true
  idUsed?: true
  status?: true
  error?: true
  relevant?: true
  _all?: true
}

export type RecipientMessageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RecipientMessage to aggregate.
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientMessages to fetch.
   */
  orderBy?: Prisma.RecipientMessageOrderByWithRelationInput | Prisma.RecipientMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.RecipientMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned RecipientMessages
  **/
  _count?: true | RecipientMessageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RecipientMessageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RecipientMessageMaxAggregateInputType
}

export type GetRecipientMessageAggregateType<T extends RecipientMessageAggregateArgs> = {
      [P in keyof T & keyof AggregateRecipientMessage]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRecipientMessage[P]>
    : Prisma.GetScalarType<T[P], AggregateRecipientMessage[P]>
}




export type RecipientMessageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RecipientMessageWhereInput
  orderBy?: Prisma.RecipientMessageOrderByWithAggregationInput | Prisma.RecipientMessageOrderByWithAggregationInput[]
  by: Prisma.RecipientMessageScalarFieldEnum[] | Prisma.RecipientMessageScalarFieldEnum
  having?: Prisma.RecipientMessageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RecipientMessageCountAggregateInputType | true
  _min?: RecipientMessageMinAggregateInputType
  _max?: RecipientMessageMaxAggregateInputType
}

export type RecipientMessageGroupByOutputType = {
  id: string
  messageId: string
  recipientId: string | null
  acknowledged: boolean
  acknowledgedAt: Date | null
  actionTaken: boolean
  actionTakenAt: Date | null
  idUsed: string
  status: $Enums.Status
  error: string
  relevant: boolean
  _count: RecipientMessageCountAggregateOutputType | null
  _min: RecipientMessageMinAggregateOutputType | null
  _max: RecipientMessageMaxAggregateOutputType | null
}

type GetRecipientMessageGroupByPayload<T extends RecipientMessageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RecipientMessageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RecipientMessageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RecipientMessageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RecipientMessageGroupByOutputType[P]>
      }
    >
  > 



export type RecipientMessageWhereInput = {
  AND?: Prisma.RecipientMessageWhereInput | Prisma.RecipientMessageWhereInput[]
  OR?: Prisma.RecipientMessageWhereInput[]
  NOT?: Prisma.RecipientMessageWhereInput | Prisma.RecipientMessageWhereInput[]
  id?: Prisma.StringFilter<"RecipientMessage"> | string
  messageId?: Prisma.StringFilter<"RecipientMessage"> | string
  recipientId?: Prisma.StringNullableFilter<"RecipientMessage"> | string | null
  acknowledged?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  acknowledgedAt?: Prisma.DateTimeNullableFilter<"RecipientMessage"> | Date | string | null
  actionTaken?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  actionTakenAt?: Prisma.DateTimeNullableFilter<"RecipientMessage"> | Date | string | null
  idUsed?: Prisma.StringFilter<"RecipientMessage"> | string
  status?: Prisma.EnumStatusFilter<"RecipientMessage"> | $Enums.Status
  error?: Prisma.StringFilter<"RecipientMessage"> | string
  relevant?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  message?: Prisma.XOR<Prisma.MessageScalarRelationFilter, Prisma.MessageWhereInput>
  recipient?: Prisma.XOR<Prisma.RecipientNullableScalarRelationFilter, Prisma.RecipientWhereInput> | null
  channelMessages?: Prisma.ChannelMessageListRelationFilter
}

export type RecipientMessageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrderInput | Prisma.SortOrder
  acknowledged?: Prisma.SortOrder
  acknowledgedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  actionTaken?: Prisma.SortOrder
  actionTakenAt?: Prisma.SortOrderInput | Prisma.SortOrder
  idUsed?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  relevant?: Prisma.SortOrder
  message?: Prisma.MessageOrderByWithRelationInput
  recipient?: Prisma.RecipientOrderByWithRelationInput
  channelMessages?: Prisma.ChannelMessageOrderByRelationAggregateInput
}

export type RecipientMessageWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.RecipientMessageWhereInput | Prisma.RecipientMessageWhereInput[]
  OR?: Prisma.RecipientMessageWhereInput[]
  NOT?: Prisma.RecipientMessageWhereInput | Prisma.RecipientMessageWhereInput[]
  messageId?: Prisma.StringFilter<"RecipientMessage"> | string
  recipientId?: Prisma.StringNullableFilter<"RecipientMessage"> | string | null
  acknowledged?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  acknowledgedAt?: Prisma.DateTimeNullableFilter<"RecipientMessage"> | Date | string | null
  actionTaken?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  actionTakenAt?: Prisma.DateTimeNullableFilter<"RecipientMessage"> | Date | string | null
  idUsed?: Prisma.StringFilter<"RecipientMessage"> | string
  status?: Prisma.EnumStatusFilter<"RecipientMessage"> | $Enums.Status
  error?: Prisma.StringFilter<"RecipientMessage"> | string
  relevant?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  message?: Prisma.XOR<Prisma.MessageScalarRelationFilter, Prisma.MessageWhereInput>
  recipient?: Prisma.XOR<Prisma.RecipientNullableScalarRelationFilter, Prisma.RecipientWhereInput> | null
  channelMessages?: Prisma.ChannelMessageListRelationFilter
}, "id">

export type RecipientMessageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrderInput | Prisma.SortOrder
  acknowledged?: Prisma.SortOrder
  acknowledgedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  actionTaken?: Prisma.SortOrder
  actionTakenAt?: Prisma.SortOrderInput | Prisma.SortOrder
  idUsed?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  relevant?: Prisma.SortOrder
  _count?: Prisma.RecipientMessageCountOrderByAggregateInput
  _max?: Prisma.RecipientMessageMaxOrderByAggregateInput
  _min?: Prisma.RecipientMessageMinOrderByAggregateInput
}

export type RecipientMessageScalarWhereWithAggregatesInput = {
  AND?: Prisma.RecipientMessageScalarWhereWithAggregatesInput | Prisma.RecipientMessageScalarWhereWithAggregatesInput[]
  OR?: Prisma.RecipientMessageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.RecipientMessageScalarWhereWithAggregatesInput | Prisma.RecipientMessageScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"RecipientMessage"> | string
  messageId?: Prisma.StringWithAggregatesFilter<"RecipientMessage"> | string
  recipientId?: Prisma.StringNullableWithAggregatesFilter<"RecipientMessage"> | string | null
  acknowledged?: Prisma.BoolWithAggregatesFilter<"RecipientMessage"> | boolean
  acknowledgedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"RecipientMessage"> | Date | string | null
  actionTaken?: Prisma.BoolWithAggregatesFilter<"RecipientMessage"> | boolean
  actionTakenAt?: Prisma.DateTimeNullableWithAggregatesFilter<"RecipientMessage"> | Date | string | null
  idUsed?: Prisma.StringWithAggregatesFilter<"RecipientMessage"> | string
  status?: Prisma.EnumStatusWithAggregatesFilter<"RecipientMessage"> | $Enums.Status
  error?: Prisma.StringWithAggregatesFilter<"RecipientMessage"> | string
  relevant?: Prisma.BoolWithAggregatesFilter<"RecipientMessage"> | boolean
}

export type RecipientMessageCreateInput = {
  id?: string
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  message: Prisma.MessageCreateNestedOneWithoutRecipientMessagesInput
  recipient?: Prisma.RecipientCreateNestedOneWithoutMessagesInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutMessageInput
}

export type RecipientMessageUncheckedCreateInput = {
  id?: string
  messageId: string
  recipientId?: string | null
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutMessageInput
}

export type RecipientMessageUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  message?: Prisma.MessageUpdateOneRequiredWithoutRecipientMessagesNestedInput
  recipient?: Prisma.RecipientUpdateOneWithoutMessagesNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutMessageNestedInput
}

export type RecipientMessageUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutMessageNestedInput
}

export type RecipientMessageCreateManyInput = {
  id?: string
  messageId: string
  recipientId?: string | null
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
}

export type RecipientMessageUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type RecipientMessageUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type RecipientMessageScalarRelationFilter = {
  is?: Prisma.RecipientMessageWhereInput
  isNot?: Prisma.RecipientMessageWhereInput
}

export type RecipientMessageListRelationFilter = {
  every?: Prisma.RecipientMessageWhereInput
  some?: Prisma.RecipientMessageWhereInput
  none?: Prisma.RecipientMessageWhereInput
}

export type RecipientMessageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type RecipientMessageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  acknowledged?: Prisma.SortOrder
  acknowledgedAt?: Prisma.SortOrder
  actionTaken?: Prisma.SortOrder
  actionTakenAt?: Prisma.SortOrder
  idUsed?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  relevant?: Prisma.SortOrder
}

export type RecipientMessageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  acknowledged?: Prisma.SortOrder
  acknowledgedAt?: Prisma.SortOrder
  actionTaken?: Prisma.SortOrder
  actionTakenAt?: Prisma.SortOrder
  idUsed?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  relevant?: Prisma.SortOrder
}

export type RecipientMessageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  messageId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  acknowledged?: Prisma.SortOrder
  acknowledgedAt?: Prisma.SortOrder
  actionTaken?: Prisma.SortOrder
  actionTakenAt?: Prisma.SortOrder
  idUsed?: Prisma.SortOrder
  status?: Prisma.SortOrder
  error?: Prisma.SortOrder
  relevant?: Prisma.SortOrder
}

export type RecipientMessageCreateNestedOneWithoutChannelMessagesInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutChannelMessagesInput, Prisma.RecipientMessageUncheckedCreateWithoutChannelMessagesInput>
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutChannelMessagesInput
  connect?: Prisma.RecipientMessageWhereUniqueInput
}

export type RecipientMessageUpdateOneRequiredWithoutChannelMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutChannelMessagesInput, Prisma.RecipientMessageUncheckedCreateWithoutChannelMessagesInput>
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutChannelMessagesInput
  upsert?: Prisma.RecipientMessageUpsertWithoutChannelMessagesInput
  connect?: Prisma.RecipientMessageWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RecipientMessageUpdateToOneWithWhereWithoutChannelMessagesInput, Prisma.RecipientMessageUpdateWithoutChannelMessagesInput>, Prisma.RecipientMessageUncheckedUpdateWithoutChannelMessagesInput>
}

export type RecipientMessageCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutMessageInput, Prisma.RecipientMessageUncheckedCreateWithoutMessageInput> | Prisma.RecipientMessageCreateWithoutMessageInput[] | Prisma.RecipientMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutMessageInput | Prisma.RecipientMessageCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.RecipientMessageCreateManyMessageInputEnvelope
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
}

export type RecipientMessageUncheckedCreateNestedManyWithoutMessageInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutMessageInput, Prisma.RecipientMessageUncheckedCreateWithoutMessageInput> | Prisma.RecipientMessageCreateWithoutMessageInput[] | Prisma.RecipientMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutMessageInput | Prisma.RecipientMessageCreateOrConnectWithoutMessageInput[]
  createMany?: Prisma.RecipientMessageCreateManyMessageInputEnvelope
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
}

export type RecipientMessageUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutMessageInput, Prisma.RecipientMessageUncheckedCreateWithoutMessageInput> | Prisma.RecipientMessageCreateWithoutMessageInput[] | Prisma.RecipientMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutMessageInput | Prisma.RecipientMessageCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.RecipientMessageUpsertWithWhereUniqueWithoutMessageInput | Prisma.RecipientMessageUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.RecipientMessageCreateManyMessageInputEnvelope
  set?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  disconnect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  delete?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  update?: Prisma.RecipientMessageUpdateWithWhereUniqueWithoutMessageInput | Prisma.RecipientMessageUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.RecipientMessageUpdateManyWithWhereWithoutMessageInput | Prisma.RecipientMessageUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.RecipientMessageScalarWhereInput | Prisma.RecipientMessageScalarWhereInput[]
}

export type RecipientMessageUncheckedUpdateManyWithoutMessageNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutMessageInput, Prisma.RecipientMessageUncheckedCreateWithoutMessageInput> | Prisma.RecipientMessageCreateWithoutMessageInput[] | Prisma.RecipientMessageUncheckedCreateWithoutMessageInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutMessageInput | Prisma.RecipientMessageCreateOrConnectWithoutMessageInput[]
  upsert?: Prisma.RecipientMessageUpsertWithWhereUniqueWithoutMessageInput | Prisma.RecipientMessageUpsertWithWhereUniqueWithoutMessageInput[]
  createMany?: Prisma.RecipientMessageCreateManyMessageInputEnvelope
  set?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  disconnect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  delete?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  update?: Prisma.RecipientMessageUpdateWithWhereUniqueWithoutMessageInput | Prisma.RecipientMessageUpdateWithWhereUniqueWithoutMessageInput[]
  updateMany?: Prisma.RecipientMessageUpdateManyWithWhereWithoutMessageInput | Prisma.RecipientMessageUpdateManyWithWhereWithoutMessageInput[]
  deleteMany?: Prisma.RecipientMessageScalarWhereInput | Prisma.RecipientMessageScalarWhereInput[]
}

export type RecipientMessageCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutRecipientInput, Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput> | Prisma.RecipientMessageCreateWithoutRecipientInput[] | Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput | Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.RecipientMessageCreateManyRecipientInputEnvelope
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
}

export type RecipientMessageUncheckedCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutRecipientInput, Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput> | Prisma.RecipientMessageCreateWithoutRecipientInput[] | Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput | Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.RecipientMessageCreateManyRecipientInputEnvelope
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
}

export type RecipientMessageUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutRecipientInput, Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput> | Prisma.RecipientMessageCreateWithoutRecipientInput[] | Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput | Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.RecipientMessageUpsertWithWhereUniqueWithoutRecipientInput | Prisma.RecipientMessageUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.RecipientMessageCreateManyRecipientInputEnvelope
  set?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  disconnect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  delete?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  update?: Prisma.RecipientMessageUpdateWithWhereUniqueWithoutRecipientInput | Prisma.RecipientMessageUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.RecipientMessageUpdateManyWithWhereWithoutRecipientInput | Prisma.RecipientMessageUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.RecipientMessageScalarWhereInput | Prisma.RecipientMessageScalarWhereInput[]
}

export type RecipientMessageUncheckedUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.RecipientMessageCreateWithoutRecipientInput, Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput> | Prisma.RecipientMessageCreateWithoutRecipientInput[] | Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput | Prisma.RecipientMessageCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.RecipientMessageUpsertWithWhereUniqueWithoutRecipientInput | Prisma.RecipientMessageUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.RecipientMessageCreateManyRecipientInputEnvelope
  set?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  disconnect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  delete?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  connect?: Prisma.RecipientMessageWhereUniqueInput | Prisma.RecipientMessageWhereUniqueInput[]
  update?: Prisma.RecipientMessageUpdateWithWhereUniqueWithoutRecipientInput | Prisma.RecipientMessageUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.RecipientMessageUpdateManyWithWhereWithoutRecipientInput | Prisma.RecipientMessageUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.RecipientMessageScalarWhereInput | Prisma.RecipientMessageScalarWhereInput[]
}

export type RecipientMessageCreateWithoutChannelMessagesInput = {
  id?: string
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  message: Prisma.MessageCreateNestedOneWithoutRecipientMessagesInput
  recipient?: Prisma.RecipientCreateNestedOneWithoutMessagesInput
}

export type RecipientMessageUncheckedCreateWithoutChannelMessagesInput = {
  id?: string
  messageId: string
  recipientId?: string | null
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
}

export type RecipientMessageCreateOrConnectWithoutChannelMessagesInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientMessageCreateWithoutChannelMessagesInput, Prisma.RecipientMessageUncheckedCreateWithoutChannelMessagesInput>
}

export type RecipientMessageUpsertWithoutChannelMessagesInput = {
  update: Prisma.XOR<Prisma.RecipientMessageUpdateWithoutChannelMessagesInput, Prisma.RecipientMessageUncheckedUpdateWithoutChannelMessagesInput>
  create: Prisma.XOR<Prisma.RecipientMessageCreateWithoutChannelMessagesInput, Prisma.RecipientMessageUncheckedCreateWithoutChannelMessagesInput>
  where?: Prisma.RecipientMessageWhereInput
}

export type RecipientMessageUpdateToOneWithWhereWithoutChannelMessagesInput = {
  where?: Prisma.RecipientMessageWhereInput
  data: Prisma.XOR<Prisma.RecipientMessageUpdateWithoutChannelMessagesInput, Prisma.RecipientMessageUncheckedUpdateWithoutChannelMessagesInput>
}

export type RecipientMessageUpdateWithoutChannelMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  message?: Prisma.MessageUpdateOneRequiredWithoutRecipientMessagesNestedInput
  recipient?: Prisma.RecipientUpdateOneWithoutMessagesNestedInput
}

export type RecipientMessageUncheckedUpdateWithoutChannelMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type RecipientMessageCreateWithoutMessageInput = {
  id?: string
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  recipient?: Prisma.RecipientCreateNestedOneWithoutMessagesInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutMessageInput
}

export type RecipientMessageUncheckedCreateWithoutMessageInput = {
  id?: string
  recipientId?: string | null
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutMessageInput
}

export type RecipientMessageCreateOrConnectWithoutMessageInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientMessageCreateWithoutMessageInput, Prisma.RecipientMessageUncheckedCreateWithoutMessageInput>
}

export type RecipientMessageCreateManyMessageInputEnvelope = {
  data: Prisma.RecipientMessageCreateManyMessageInput | Prisma.RecipientMessageCreateManyMessageInput[]
  skipDuplicates?: boolean
}

export type RecipientMessageUpsertWithWhereUniqueWithoutMessageInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.RecipientMessageUpdateWithoutMessageInput, Prisma.RecipientMessageUncheckedUpdateWithoutMessageInput>
  create: Prisma.XOR<Prisma.RecipientMessageCreateWithoutMessageInput, Prisma.RecipientMessageUncheckedCreateWithoutMessageInput>
}

export type RecipientMessageUpdateWithWhereUniqueWithoutMessageInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.RecipientMessageUpdateWithoutMessageInput, Prisma.RecipientMessageUncheckedUpdateWithoutMessageInput>
}

export type RecipientMessageUpdateManyWithWhereWithoutMessageInput = {
  where: Prisma.RecipientMessageScalarWhereInput
  data: Prisma.XOR<Prisma.RecipientMessageUpdateManyMutationInput, Prisma.RecipientMessageUncheckedUpdateManyWithoutMessageInput>
}

export type RecipientMessageScalarWhereInput = {
  AND?: Prisma.RecipientMessageScalarWhereInput | Prisma.RecipientMessageScalarWhereInput[]
  OR?: Prisma.RecipientMessageScalarWhereInput[]
  NOT?: Prisma.RecipientMessageScalarWhereInput | Prisma.RecipientMessageScalarWhereInput[]
  id?: Prisma.StringFilter<"RecipientMessage"> | string
  messageId?: Prisma.StringFilter<"RecipientMessage"> | string
  recipientId?: Prisma.StringNullableFilter<"RecipientMessage"> | string | null
  acknowledged?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  acknowledgedAt?: Prisma.DateTimeNullableFilter<"RecipientMessage"> | Date | string | null
  actionTaken?: Prisma.BoolFilter<"RecipientMessage"> | boolean
  actionTakenAt?: Prisma.DateTimeNullableFilter<"RecipientMessage"> | Date | string | null
  idUsed?: Prisma.StringFilter<"RecipientMessage"> | string
  status?: Prisma.EnumStatusFilter<"RecipientMessage"> | $Enums.Status
  error?: Prisma.StringFilter<"RecipientMessage"> | string
  relevant?: Prisma.BoolFilter<"RecipientMessage"> | boolean
}

export type RecipientMessageCreateWithoutRecipientInput = {
  id?: string
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  message: Prisma.MessageCreateNestedOneWithoutRecipientMessagesInput
  channelMessages?: Prisma.ChannelMessageCreateNestedManyWithoutMessageInput
}

export type RecipientMessageUncheckedCreateWithoutRecipientInput = {
  id?: string
  messageId: string
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
  channelMessages?: Prisma.ChannelMessageUncheckedCreateNestedManyWithoutMessageInput
}

export type RecipientMessageCreateOrConnectWithoutRecipientInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.RecipientMessageCreateWithoutRecipientInput, Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput>
}

export type RecipientMessageCreateManyRecipientInputEnvelope = {
  data: Prisma.RecipientMessageCreateManyRecipientInput | Prisma.RecipientMessageCreateManyRecipientInput[]
  skipDuplicates?: boolean
}

export type RecipientMessageUpsertWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.RecipientMessageUpdateWithoutRecipientInput, Prisma.RecipientMessageUncheckedUpdateWithoutRecipientInput>
  create: Prisma.XOR<Prisma.RecipientMessageCreateWithoutRecipientInput, Prisma.RecipientMessageUncheckedCreateWithoutRecipientInput>
}

export type RecipientMessageUpdateWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.RecipientMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.RecipientMessageUpdateWithoutRecipientInput, Prisma.RecipientMessageUncheckedUpdateWithoutRecipientInput>
}

export type RecipientMessageUpdateManyWithWhereWithoutRecipientInput = {
  where: Prisma.RecipientMessageScalarWhereInput
  data: Prisma.XOR<Prisma.RecipientMessageUpdateManyMutationInput, Prisma.RecipientMessageUncheckedUpdateManyWithoutRecipientInput>
}

export type RecipientMessageCreateManyMessageInput = {
  id?: string
  recipientId?: string | null
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
}

export type RecipientMessageUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recipient?: Prisma.RecipientUpdateOneWithoutMessagesNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutMessageNestedInput
}

export type RecipientMessageUncheckedUpdateWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutMessageNestedInput
}

export type RecipientMessageUncheckedUpdateManyWithoutMessageInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type RecipientMessageCreateManyRecipientInput = {
  id?: string
  messageId: string
  acknowledged?: boolean
  acknowledgedAt?: Date | string | null
  actionTaken?: boolean
  actionTakenAt?: Date | string | null
  idUsed: string
  status?: $Enums.Status
  error?: string
  relevant?: boolean
}

export type RecipientMessageUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  message?: Prisma.MessageUpdateOneRequiredWithoutRecipientMessagesNestedInput
  channelMessages?: Prisma.ChannelMessageUpdateManyWithoutMessageNestedInput
}

export type RecipientMessageUncheckedUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  channelMessages?: Prisma.ChannelMessageUncheckedUpdateManyWithoutMessageNestedInput
}

export type RecipientMessageUncheckedUpdateManyWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  messageId?: Prisma.StringFieldUpdateOperationsInput | string
  acknowledged?: Prisma.BoolFieldUpdateOperationsInput | boolean
  acknowledgedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  actionTaken?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionTakenAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  idUsed?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStatusFieldUpdateOperationsInput | $Enums.Status
  error?: Prisma.StringFieldUpdateOperationsInput | string
  relevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
}


/**
 * Count Type RecipientMessageCountOutputType
 */

export type RecipientMessageCountOutputType = {
  channelMessages: number
}

export type RecipientMessageCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  channelMessages?: boolean | RecipientMessageCountOutputTypeCountChannelMessagesArgs
}

/**
 * RecipientMessageCountOutputType without action
 */
export type RecipientMessageCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessageCountOutputType
   */
  select?: Prisma.RecipientMessageCountOutputTypeSelect<ExtArgs> | null
}

/**
 * RecipientMessageCountOutputType without action
 */
export type RecipientMessageCountOutputTypeCountChannelMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ChannelMessageWhereInput
}


export type RecipientMessageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  recipientId?: boolean
  acknowledged?: boolean
  acknowledgedAt?: boolean
  actionTaken?: boolean
  actionTakenAt?: boolean
  idUsed?: boolean
  status?: boolean
  error?: boolean
  relevant?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
  recipient?: boolean | Prisma.RecipientMessage$recipientArgs<ExtArgs>
  channelMessages?: boolean | Prisma.RecipientMessage$channelMessagesArgs<ExtArgs>
  _count?: boolean | Prisma.RecipientMessageCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipientMessage"]>

export type RecipientMessageSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  recipientId?: boolean
  acknowledged?: boolean
  acknowledgedAt?: boolean
  actionTaken?: boolean
  actionTakenAt?: boolean
  idUsed?: boolean
  status?: boolean
  error?: boolean
  relevant?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
  recipient?: boolean | Prisma.RecipientMessage$recipientArgs<ExtArgs>
}, ExtArgs["result"]["recipientMessage"]>

export type RecipientMessageSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  messageId?: boolean
  recipientId?: boolean
  acknowledged?: boolean
  acknowledgedAt?: boolean
  actionTaken?: boolean
  actionTakenAt?: boolean
  idUsed?: boolean
  status?: boolean
  error?: boolean
  relevant?: boolean
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
  recipient?: boolean | Prisma.RecipientMessage$recipientArgs<ExtArgs>
}, ExtArgs["result"]["recipientMessage"]>

export type RecipientMessageSelectScalar = {
  id?: boolean
  messageId?: boolean
  recipientId?: boolean
  acknowledged?: boolean
  acknowledgedAt?: boolean
  actionTaken?: boolean
  actionTakenAt?: boolean
  idUsed?: boolean
  status?: boolean
  error?: boolean
  relevant?: boolean
}

export type RecipientMessageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "messageId" | "recipientId" | "acknowledged" | "acknowledgedAt" | "actionTaken" | "actionTakenAt" | "idUsed" | "status" | "error" | "relevant", ExtArgs["result"]["recipientMessage"]>
export type RecipientMessageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
  recipient?: boolean | Prisma.RecipientMessage$recipientArgs<ExtArgs>
  channelMessages?: boolean | Prisma.RecipientMessage$channelMessagesArgs<ExtArgs>
  _count?: boolean | Prisma.RecipientMessageCountOutputTypeDefaultArgs<ExtArgs>
}
export type RecipientMessageIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
  recipient?: boolean | Prisma.RecipientMessage$recipientArgs<ExtArgs>
}
export type RecipientMessageIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  message?: boolean | Prisma.MessageDefaultArgs<ExtArgs>
  recipient?: boolean | Prisma.RecipientMessage$recipientArgs<ExtArgs>
}

export type $RecipientMessagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "RecipientMessage"
  objects: {
    message: Prisma.$MessagePayload<ExtArgs>
    recipient: Prisma.$RecipientPayload<ExtArgs> | null
    channelMessages: Prisma.$ChannelMessagePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    messageId: string
    recipientId: string | null
    acknowledged: boolean
    acknowledgedAt: Date | null
    actionTaken: boolean
    actionTakenAt: Date | null
    idUsed: string
    status: $Enums.Status
    error: string
    relevant: boolean
  }, ExtArgs["result"]["recipientMessage"]>
  composites: {}
}

export type RecipientMessageGetPayload<S extends boolean | null | undefined | RecipientMessageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload, S>

export type RecipientMessageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<RecipientMessageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: RecipientMessageCountAggregateInputType | true
  }

export interface RecipientMessageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RecipientMessage'], meta: { name: 'RecipientMessage' } }
  /**
   * Find zero or one RecipientMessage that matches the filter.
   * @param {RecipientMessageFindUniqueArgs} args - Arguments to find a RecipientMessage
   * @example
   * // Get one RecipientMessage
   * const recipientMessage = await prisma.recipientMessage.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RecipientMessageFindUniqueArgs>(args: Prisma.SelectSubset<T, RecipientMessageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one RecipientMessage that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RecipientMessageFindUniqueOrThrowArgs} args - Arguments to find a RecipientMessage
   * @example
   * // Get one RecipientMessage
   * const recipientMessage = await prisma.recipientMessage.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RecipientMessageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, RecipientMessageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RecipientMessage that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageFindFirstArgs} args - Arguments to find a RecipientMessage
   * @example
   * // Get one RecipientMessage
   * const recipientMessage = await prisma.recipientMessage.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RecipientMessageFindFirstArgs>(args?: Prisma.SelectSubset<T, RecipientMessageFindFirstArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RecipientMessage that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageFindFirstOrThrowArgs} args - Arguments to find a RecipientMessage
   * @example
   * // Get one RecipientMessage
   * const recipientMessage = await prisma.recipientMessage.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RecipientMessageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, RecipientMessageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more RecipientMessages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all RecipientMessages
   * const recipientMessages = await prisma.recipientMessage.findMany()
   * 
   * // Get first 10 RecipientMessages
   * const recipientMessages = await prisma.recipientMessage.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const recipientMessageWithIdOnly = await prisma.recipientMessage.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends RecipientMessageFindManyArgs>(args?: Prisma.SelectSubset<T, RecipientMessageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a RecipientMessage.
   * @param {RecipientMessageCreateArgs} args - Arguments to create a RecipientMessage.
   * @example
   * // Create one RecipientMessage
   * const RecipientMessage = await prisma.recipientMessage.create({
   *   data: {
   *     // ... data to create a RecipientMessage
   *   }
   * })
   * 
   */
  create<T extends RecipientMessageCreateArgs>(args: Prisma.SelectSubset<T, RecipientMessageCreateArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many RecipientMessages.
   * @param {RecipientMessageCreateManyArgs} args - Arguments to create many RecipientMessages.
   * @example
   * // Create many RecipientMessages
   * const recipientMessage = await prisma.recipientMessage.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends RecipientMessageCreateManyArgs>(args?: Prisma.SelectSubset<T, RecipientMessageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many RecipientMessages and returns the data saved in the database.
   * @param {RecipientMessageCreateManyAndReturnArgs} args - Arguments to create many RecipientMessages.
   * @example
   * // Create many RecipientMessages
   * const recipientMessage = await prisma.recipientMessage.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many RecipientMessages and only return the `id`
   * const recipientMessageWithIdOnly = await prisma.recipientMessage.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends RecipientMessageCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, RecipientMessageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a RecipientMessage.
   * @param {RecipientMessageDeleteArgs} args - Arguments to delete one RecipientMessage.
   * @example
   * // Delete one RecipientMessage
   * const RecipientMessage = await prisma.recipientMessage.delete({
   *   where: {
   *     // ... filter to delete one RecipientMessage
   *   }
   * })
   * 
   */
  delete<T extends RecipientMessageDeleteArgs>(args: Prisma.SelectSubset<T, RecipientMessageDeleteArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one RecipientMessage.
   * @param {RecipientMessageUpdateArgs} args - Arguments to update one RecipientMessage.
   * @example
   * // Update one RecipientMessage
   * const recipientMessage = await prisma.recipientMessage.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends RecipientMessageUpdateArgs>(args: Prisma.SelectSubset<T, RecipientMessageUpdateArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more RecipientMessages.
   * @param {RecipientMessageDeleteManyArgs} args - Arguments to filter RecipientMessages to delete.
   * @example
   * // Delete a few RecipientMessages
   * const { count } = await prisma.recipientMessage.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends RecipientMessageDeleteManyArgs>(args?: Prisma.SelectSubset<T, RecipientMessageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RecipientMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many RecipientMessages
   * const recipientMessage = await prisma.recipientMessage.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends RecipientMessageUpdateManyArgs>(args: Prisma.SelectSubset<T, RecipientMessageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RecipientMessages and returns the data updated in the database.
   * @param {RecipientMessageUpdateManyAndReturnArgs} args - Arguments to update many RecipientMessages.
   * @example
   * // Update many RecipientMessages
   * const recipientMessage = await prisma.recipientMessage.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more RecipientMessages and only return the `id`
   * const recipientMessageWithIdOnly = await prisma.recipientMessage.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends RecipientMessageUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, RecipientMessageUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one RecipientMessage.
   * @param {RecipientMessageUpsertArgs} args - Arguments to update or create a RecipientMessage.
   * @example
   * // Update or create a RecipientMessage
   * const recipientMessage = await prisma.recipientMessage.upsert({
   *   create: {
   *     // ... data to create a RecipientMessage
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the RecipientMessage we want to update
   *   }
   * })
   */
  upsert<T extends RecipientMessageUpsertArgs>(args: Prisma.SelectSubset<T, RecipientMessageUpsertArgs<ExtArgs>>): Prisma.Prisma__RecipientMessageClient<runtime.Types.Result.GetResult<Prisma.$RecipientMessagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of RecipientMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageCountArgs} args - Arguments to filter RecipientMessages to count.
   * @example
   * // Count the number of RecipientMessages
   * const count = await prisma.recipientMessage.count({
   *   where: {
   *     // ... the filter for the RecipientMessages we want to count
   *   }
   * })
  **/
  count<T extends RecipientMessageCountArgs>(
    args?: Prisma.Subset<T, RecipientMessageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RecipientMessageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a RecipientMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RecipientMessageAggregateArgs>(args: Prisma.Subset<T, RecipientMessageAggregateArgs>): Prisma.PrismaPromise<GetRecipientMessageAggregateType<T>>

  /**
   * Group by RecipientMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RecipientMessageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends RecipientMessageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RecipientMessageGroupByArgs['orderBy'] }
      : { orderBy?: RecipientMessageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, RecipientMessageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecipientMessageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the RecipientMessage model
 */
readonly fields: RecipientMessageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for RecipientMessage.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RecipientMessageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  message<T extends Prisma.MessageDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.MessageDefaultArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  recipient<T extends Prisma.RecipientMessage$recipientArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientMessage$recipientArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  channelMessages<T extends Prisma.RecipientMessage$channelMessagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientMessage$channelMessagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ChannelMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the RecipientMessage model
 */
export interface RecipientMessageFieldRefs {
  readonly id: Prisma.FieldRef<"RecipientMessage", 'String'>
  readonly messageId: Prisma.FieldRef<"RecipientMessage", 'String'>
  readonly recipientId: Prisma.FieldRef<"RecipientMessage", 'String'>
  readonly acknowledged: Prisma.FieldRef<"RecipientMessage", 'Boolean'>
  readonly acknowledgedAt: Prisma.FieldRef<"RecipientMessage", 'DateTime'>
  readonly actionTaken: Prisma.FieldRef<"RecipientMessage", 'Boolean'>
  readonly actionTakenAt: Prisma.FieldRef<"RecipientMessage", 'DateTime'>
  readonly idUsed: Prisma.FieldRef<"RecipientMessage", 'String'>
  readonly status: Prisma.FieldRef<"RecipientMessage", 'Status'>
  readonly error: Prisma.FieldRef<"RecipientMessage", 'String'>
  readonly relevant: Prisma.FieldRef<"RecipientMessage", 'Boolean'>
}
    

// Custom InputTypes
/**
 * RecipientMessage findUnique
 */
export type RecipientMessageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * Filter, which RecipientMessage to fetch.
   */
  where: Prisma.RecipientMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage findUniqueOrThrow
 */
export type RecipientMessageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * Filter, which RecipientMessage to fetch.
   */
  where: Prisma.RecipientMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage findFirst
 */
export type RecipientMessageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * Filter, which RecipientMessage to fetch.
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientMessages to fetch.
   */
  orderBy?: Prisma.RecipientMessageOrderByWithRelationInput | Prisma.RecipientMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RecipientMessages.
   */
  cursor?: Prisma.RecipientMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RecipientMessages.
   */
  distinct?: Prisma.RecipientMessageScalarFieldEnum | Prisma.RecipientMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage findFirstOrThrow
 */
export type RecipientMessageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * Filter, which RecipientMessage to fetch.
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientMessages to fetch.
   */
  orderBy?: Prisma.RecipientMessageOrderByWithRelationInput | Prisma.RecipientMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RecipientMessages.
   */
  cursor?: Prisma.RecipientMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RecipientMessages.
   */
  distinct?: Prisma.RecipientMessageScalarFieldEnum | Prisma.RecipientMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage findMany
 */
export type RecipientMessageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * Filter, which RecipientMessages to fetch.
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RecipientMessages to fetch.
   */
  orderBy?: Prisma.RecipientMessageOrderByWithRelationInput | Prisma.RecipientMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing RecipientMessages.
   */
  cursor?: Prisma.RecipientMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RecipientMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RecipientMessages.
   */
  skip?: number
  distinct?: Prisma.RecipientMessageScalarFieldEnum | Prisma.RecipientMessageScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage create
 */
export type RecipientMessageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * The data needed to create a RecipientMessage.
   */
  data: Prisma.XOR<Prisma.RecipientMessageCreateInput, Prisma.RecipientMessageUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage createMany
 */
export type RecipientMessageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many RecipientMessages.
   */
  data: Prisma.RecipientMessageCreateManyInput | Prisma.RecipientMessageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * RecipientMessage createManyAndReturn
 */
export type RecipientMessageCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * The data used to create many RecipientMessages.
   */
  data: Prisma.RecipientMessageCreateManyInput | Prisma.RecipientMessageCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * RecipientMessage update
 */
export type RecipientMessageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * The data needed to update a RecipientMessage.
   */
  data: Prisma.XOR<Prisma.RecipientMessageUpdateInput, Prisma.RecipientMessageUncheckedUpdateInput>
  /**
   * Choose, which RecipientMessage to update.
   */
  where: Prisma.RecipientMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage updateMany
 */
export type RecipientMessageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update RecipientMessages.
   */
  data: Prisma.XOR<Prisma.RecipientMessageUpdateManyMutationInput, Prisma.RecipientMessageUncheckedUpdateManyInput>
  /**
   * Filter which RecipientMessages to update
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * Limit how many RecipientMessages to update.
   */
  limit?: number
}

/**
 * RecipientMessage updateManyAndReturn
 */
export type RecipientMessageUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * The data used to update RecipientMessages.
   */
  data: Prisma.XOR<Prisma.RecipientMessageUpdateManyMutationInput, Prisma.RecipientMessageUncheckedUpdateManyInput>
  /**
   * Filter which RecipientMessages to update
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * Limit how many RecipientMessages to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * RecipientMessage upsert
 */
export type RecipientMessageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * The filter to search for the RecipientMessage to update in case it exists.
   */
  where: Prisma.RecipientMessageWhereUniqueInput
  /**
   * In case the RecipientMessage found by the `where` argument doesn't exist, create a new RecipientMessage with this data.
   */
  create: Prisma.XOR<Prisma.RecipientMessageCreateInput, Prisma.RecipientMessageUncheckedCreateInput>
  /**
   * In case the RecipientMessage was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RecipientMessageUpdateInput, Prisma.RecipientMessageUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage delete
 */
export type RecipientMessageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
  /**
   * Filter which RecipientMessage to delete.
   */
  where: Prisma.RecipientMessageWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * RecipientMessage deleteMany
 */
export type RecipientMessageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RecipientMessages to delete
   */
  where?: Prisma.RecipientMessageWhereInput
  /**
   * Limit how many RecipientMessages to delete.
   */
  limit?: number
}

/**
 * RecipientMessage.recipient
 */
export type RecipientMessage$recipientArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Recipient
   */
  select?: Prisma.RecipientSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Recipient
   */
  omit?: Prisma.RecipientOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientInclude<ExtArgs> | null
  where?: Prisma.RecipientWhereInput
}

/**
 * RecipientMessage.channelMessages
 */
export type RecipientMessage$channelMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ChannelMessage
   */
  select?: Prisma.ChannelMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ChannelMessage
   */
  omit?: Prisma.ChannelMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ChannelMessageInclude<ExtArgs> | null
  where?: Prisma.ChannelMessageWhereInput
  orderBy?: Prisma.ChannelMessageOrderByWithRelationInput | Prisma.ChannelMessageOrderByWithRelationInput[]
  cursor?: Prisma.ChannelMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ChannelMessageScalarFieldEnum | Prisma.ChannelMessageScalarFieldEnum[]
}

/**
 * RecipientMessage without action
 */
export type RecipientMessageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RecipientMessage
   */
  select?: Prisma.RecipientMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RecipientMessage
   */
  omit?: Prisma.RecipientMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RecipientMessageInclude<ExtArgs> | null
}
