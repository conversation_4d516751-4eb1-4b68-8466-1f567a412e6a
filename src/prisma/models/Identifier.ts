
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Identifier` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Identifier
 * 
 */
export type IdentifierModel = runtime.Types.Result.DefaultSelection<Prisma.$IdentifierPayload>

export type AggregateIdentifier = {
  _count: IdentifierCountAggregateOutputType | null
  _min: IdentifierMinAggregateOutputType | null
  _max: IdentifierMaxAggregateOutputType | null
}

export type IdentifierMinAggregateOutputType = {
  id: string | null
  recipientId: string | null
  type: string | null
  value: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type IdentifierMaxAggregateOutputType = {
  id: string | null
  recipientId: string | null
  type: string | null
  value: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type IdentifierCountAggregateOutputType = {
  id: number
  recipientId: number
  type: number
  value: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type IdentifierMinAggregateInputType = {
  id?: true
  recipientId?: true
  type?: true
  value?: true
  createdAt?: true
  updatedAt?: true
}

export type IdentifierMaxAggregateInputType = {
  id?: true
  recipientId?: true
  type?: true
  value?: true
  createdAt?: true
  updatedAt?: true
}

export type IdentifierCountAggregateInputType = {
  id?: true
  recipientId?: true
  type?: true
  value?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type IdentifierAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Identifier to aggregate.
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Identifiers to fetch.
   */
  orderBy?: Prisma.IdentifierOrderByWithRelationInput | Prisma.IdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.IdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Identifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Identifiers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Identifiers
  **/
  _count?: true | IdentifierCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: IdentifierMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: IdentifierMaxAggregateInputType
}

export type GetIdentifierAggregateType<T extends IdentifierAggregateArgs> = {
      [P in keyof T & keyof AggregateIdentifier]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateIdentifier[P]>
    : Prisma.GetScalarType<T[P], AggregateIdentifier[P]>
}




export type IdentifierGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.IdentifierWhereInput
  orderBy?: Prisma.IdentifierOrderByWithAggregationInput | Prisma.IdentifierOrderByWithAggregationInput[]
  by: Prisma.IdentifierScalarFieldEnum[] | Prisma.IdentifierScalarFieldEnum
  having?: Prisma.IdentifierScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: IdentifierCountAggregateInputType | true
  _min?: IdentifierMinAggregateInputType
  _max?: IdentifierMaxAggregateInputType
}

export type IdentifierGroupByOutputType = {
  id: string
  recipientId: string
  type: string
  value: string
  createdAt: Date
  updatedAt: Date
  _count: IdentifierCountAggregateOutputType | null
  _min: IdentifierMinAggregateOutputType | null
  _max: IdentifierMaxAggregateOutputType | null
}

type GetIdentifierGroupByPayload<T extends IdentifierGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<IdentifierGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof IdentifierGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], IdentifierGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], IdentifierGroupByOutputType[P]>
      }
    >
  > 



export type IdentifierWhereInput = {
  AND?: Prisma.IdentifierWhereInput | Prisma.IdentifierWhereInput[]
  OR?: Prisma.IdentifierWhereInput[]
  NOT?: Prisma.IdentifierWhereInput | Prisma.IdentifierWhereInput[]
  id?: Prisma.StringFilter<"Identifier"> | string
  recipientId?: Prisma.StringFilter<"Identifier"> | string
  type?: Prisma.StringFilter<"Identifier"> | string
  value?: Prisma.StringFilter<"Identifier"> | string
  createdAt?: Prisma.DateTimeFilter<"Identifier"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Identifier"> | Date | string
  recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
}

export type IdentifierOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipient?: Prisma.RecipientOrderByWithRelationInput
}

export type IdentifierWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.IdentifierWhereInput | Prisma.IdentifierWhereInput[]
  OR?: Prisma.IdentifierWhereInput[]
  NOT?: Prisma.IdentifierWhereInput | Prisma.IdentifierWhereInput[]
  recipientId?: Prisma.StringFilter<"Identifier"> | string
  type?: Prisma.StringFilter<"Identifier"> | string
  value?: Prisma.StringFilter<"Identifier"> | string
  createdAt?: Prisma.DateTimeFilter<"Identifier"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Identifier"> | Date | string
  recipient?: Prisma.XOR<Prisma.RecipientScalarRelationFilter, Prisma.RecipientWhereInput>
}, "id">

export type IdentifierOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.IdentifierCountOrderByAggregateInput
  _max?: Prisma.IdentifierMaxOrderByAggregateInput
  _min?: Prisma.IdentifierMinOrderByAggregateInput
}

export type IdentifierScalarWhereWithAggregatesInput = {
  AND?: Prisma.IdentifierScalarWhereWithAggregatesInput | Prisma.IdentifierScalarWhereWithAggregatesInput[]
  OR?: Prisma.IdentifierScalarWhereWithAggregatesInput[]
  NOT?: Prisma.IdentifierScalarWhereWithAggregatesInput | Prisma.IdentifierScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Identifier"> | string
  recipientId?: Prisma.StringWithAggregatesFilter<"Identifier"> | string
  type?: Prisma.StringWithAggregatesFilter<"Identifier"> | string
  value?: Prisma.StringWithAggregatesFilter<"Identifier"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Identifier"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Identifier"> | Date | string
}

export type IdentifierCreateInput = {
  id?: string
  type: string
  value: string
  createdAt?: Date | string
  updatedAt?: Date | string
  recipient: Prisma.RecipientCreateNestedOneWithoutIdentifiersInput
}

export type IdentifierUncheckedCreateInput = {
  id?: string
  recipientId: string
  type: string
  value: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type IdentifierUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.RecipientUpdateOneRequiredWithoutIdentifiersNestedInput
}

export type IdentifierUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type IdentifierCreateManyInput = {
  id?: string
  recipientId: string
  type: string
  value: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type IdentifierUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type IdentifierUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type IdentifierCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type IdentifierMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type IdentifierMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type IdentifierListRelationFilter = {
  every?: Prisma.IdentifierWhereInput
  some?: Prisma.IdentifierWhereInput
  none?: Prisma.IdentifierWhereInput
}

export type IdentifierOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type IdentifierCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.IdentifierCreateWithoutRecipientInput, Prisma.IdentifierUncheckedCreateWithoutRecipientInput> | Prisma.IdentifierCreateWithoutRecipientInput[] | Prisma.IdentifierUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.IdentifierCreateOrConnectWithoutRecipientInput | Prisma.IdentifierCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.IdentifierCreateManyRecipientInputEnvelope
  connect?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
}

export type IdentifierUncheckedCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.IdentifierCreateWithoutRecipientInput, Prisma.IdentifierUncheckedCreateWithoutRecipientInput> | Prisma.IdentifierCreateWithoutRecipientInput[] | Prisma.IdentifierUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.IdentifierCreateOrConnectWithoutRecipientInput | Prisma.IdentifierCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.IdentifierCreateManyRecipientInputEnvelope
  connect?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
}

export type IdentifierUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.IdentifierCreateWithoutRecipientInput, Prisma.IdentifierUncheckedCreateWithoutRecipientInput> | Prisma.IdentifierCreateWithoutRecipientInput[] | Prisma.IdentifierUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.IdentifierCreateOrConnectWithoutRecipientInput | Prisma.IdentifierCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.IdentifierUpsertWithWhereUniqueWithoutRecipientInput | Prisma.IdentifierUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.IdentifierCreateManyRecipientInputEnvelope
  set?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  disconnect?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  delete?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  connect?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  update?: Prisma.IdentifierUpdateWithWhereUniqueWithoutRecipientInput | Prisma.IdentifierUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.IdentifierUpdateManyWithWhereWithoutRecipientInput | Prisma.IdentifierUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.IdentifierScalarWhereInput | Prisma.IdentifierScalarWhereInput[]
}

export type IdentifierUncheckedUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.IdentifierCreateWithoutRecipientInput, Prisma.IdentifierUncheckedCreateWithoutRecipientInput> | Prisma.IdentifierCreateWithoutRecipientInput[] | Prisma.IdentifierUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.IdentifierCreateOrConnectWithoutRecipientInput | Prisma.IdentifierCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.IdentifierUpsertWithWhereUniqueWithoutRecipientInput | Prisma.IdentifierUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.IdentifierCreateManyRecipientInputEnvelope
  set?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  disconnect?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  delete?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  connect?: Prisma.IdentifierWhereUniqueInput | Prisma.IdentifierWhereUniqueInput[]
  update?: Prisma.IdentifierUpdateWithWhereUniqueWithoutRecipientInput | Prisma.IdentifierUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.IdentifierUpdateManyWithWhereWithoutRecipientInput | Prisma.IdentifierUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.IdentifierScalarWhereInput | Prisma.IdentifierScalarWhereInput[]
}

export type IdentifierCreateWithoutRecipientInput = {
  id?: string
  type: string
  value: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type IdentifierUncheckedCreateWithoutRecipientInput = {
  id?: string
  type: string
  value: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type IdentifierCreateOrConnectWithoutRecipientInput = {
  where: Prisma.IdentifierWhereUniqueInput
  create: Prisma.XOR<Prisma.IdentifierCreateWithoutRecipientInput, Prisma.IdentifierUncheckedCreateWithoutRecipientInput>
}

export type IdentifierCreateManyRecipientInputEnvelope = {
  data: Prisma.IdentifierCreateManyRecipientInput | Prisma.IdentifierCreateManyRecipientInput[]
  skipDuplicates?: boolean
}

export type IdentifierUpsertWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.IdentifierWhereUniqueInput
  update: Prisma.XOR<Prisma.IdentifierUpdateWithoutRecipientInput, Prisma.IdentifierUncheckedUpdateWithoutRecipientInput>
  create: Prisma.XOR<Prisma.IdentifierCreateWithoutRecipientInput, Prisma.IdentifierUncheckedCreateWithoutRecipientInput>
}

export type IdentifierUpdateWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.IdentifierWhereUniqueInput
  data: Prisma.XOR<Prisma.IdentifierUpdateWithoutRecipientInput, Prisma.IdentifierUncheckedUpdateWithoutRecipientInput>
}

export type IdentifierUpdateManyWithWhereWithoutRecipientInput = {
  where: Prisma.IdentifierScalarWhereInput
  data: Prisma.XOR<Prisma.IdentifierUpdateManyMutationInput, Prisma.IdentifierUncheckedUpdateManyWithoutRecipientInput>
}

export type IdentifierScalarWhereInput = {
  AND?: Prisma.IdentifierScalarWhereInput | Prisma.IdentifierScalarWhereInput[]
  OR?: Prisma.IdentifierScalarWhereInput[]
  NOT?: Prisma.IdentifierScalarWhereInput | Prisma.IdentifierScalarWhereInput[]
  id?: Prisma.StringFilter<"Identifier"> | string
  recipientId?: Prisma.StringFilter<"Identifier"> | string
  type?: Prisma.StringFilter<"Identifier"> | string
  value?: Prisma.StringFilter<"Identifier"> | string
  createdAt?: Prisma.DateTimeFilter<"Identifier"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Identifier"> | Date | string
}

export type IdentifierCreateManyRecipientInput = {
  id?: string
  type: string
  value: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type IdentifierUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type IdentifierUncheckedUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type IdentifierUncheckedUpdateManyWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type IdentifierSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  type?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}, ExtArgs["result"]["identifier"]>

export type IdentifierSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  type?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}, ExtArgs["result"]["identifier"]>

export type IdentifierSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  type?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}, ExtArgs["result"]["identifier"]>

export type IdentifierSelectScalar = {
  id?: boolean
  recipientId?: boolean
  type?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type IdentifierOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "recipientId" | "type" | "value" | "createdAt" | "updatedAt", ExtArgs["result"]["identifier"]>
export type IdentifierInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}
export type IdentifierIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}
export type IdentifierIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.RecipientDefaultArgs<ExtArgs>
}

export type $IdentifierPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Identifier"
  objects: {
    recipient: Prisma.$RecipientPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    recipientId: string
    type: string
    value: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["identifier"]>
  composites: {}
}

export type IdentifierGetPayload<S extends boolean | null | undefined | IdentifierDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$IdentifierPayload, S>

export type IdentifierCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<IdentifierFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: IdentifierCountAggregateInputType | true
  }

export interface IdentifierDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Identifier'], meta: { name: 'Identifier' } }
  /**
   * Find zero or one Identifier that matches the filter.
   * @param {IdentifierFindUniqueArgs} args - Arguments to find a Identifier
   * @example
   * // Get one Identifier
   * const identifier = await prisma.identifier.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends IdentifierFindUniqueArgs>(args: Prisma.SelectSubset<T, IdentifierFindUniqueArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Identifier that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {IdentifierFindUniqueOrThrowArgs} args - Arguments to find a Identifier
   * @example
   * // Get one Identifier
   * const identifier = await prisma.identifier.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends IdentifierFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, IdentifierFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Identifier that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierFindFirstArgs} args - Arguments to find a Identifier
   * @example
   * // Get one Identifier
   * const identifier = await prisma.identifier.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends IdentifierFindFirstArgs>(args?: Prisma.SelectSubset<T, IdentifierFindFirstArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Identifier that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierFindFirstOrThrowArgs} args - Arguments to find a Identifier
   * @example
   * // Get one Identifier
   * const identifier = await prisma.identifier.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends IdentifierFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, IdentifierFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Identifiers that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Identifiers
   * const identifiers = await prisma.identifier.findMany()
   * 
   * // Get first 10 Identifiers
   * const identifiers = await prisma.identifier.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const identifierWithIdOnly = await prisma.identifier.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends IdentifierFindManyArgs>(args?: Prisma.SelectSubset<T, IdentifierFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Identifier.
   * @param {IdentifierCreateArgs} args - Arguments to create a Identifier.
   * @example
   * // Create one Identifier
   * const Identifier = await prisma.identifier.create({
   *   data: {
   *     // ... data to create a Identifier
   *   }
   * })
   * 
   */
  create<T extends IdentifierCreateArgs>(args: Prisma.SelectSubset<T, IdentifierCreateArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Identifiers.
   * @param {IdentifierCreateManyArgs} args - Arguments to create many Identifiers.
   * @example
   * // Create many Identifiers
   * const identifier = await prisma.identifier.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends IdentifierCreateManyArgs>(args?: Prisma.SelectSubset<T, IdentifierCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Identifiers and returns the data saved in the database.
   * @param {IdentifierCreateManyAndReturnArgs} args - Arguments to create many Identifiers.
   * @example
   * // Create many Identifiers
   * const identifier = await prisma.identifier.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Identifiers and only return the `id`
   * const identifierWithIdOnly = await prisma.identifier.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends IdentifierCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, IdentifierCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Identifier.
   * @param {IdentifierDeleteArgs} args - Arguments to delete one Identifier.
   * @example
   * // Delete one Identifier
   * const Identifier = await prisma.identifier.delete({
   *   where: {
   *     // ... filter to delete one Identifier
   *   }
   * })
   * 
   */
  delete<T extends IdentifierDeleteArgs>(args: Prisma.SelectSubset<T, IdentifierDeleteArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Identifier.
   * @param {IdentifierUpdateArgs} args - Arguments to update one Identifier.
   * @example
   * // Update one Identifier
   * const identifier = await prisma.identifier.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends IdentifierUpdateArgs>(args: Prisma.SelectSubset<T, IdentifierUpdateArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Identifiers.
   * @param {IdentifierDeleteManyArgs} args - Arguments to filter Identifiers to delete.
   * @example
   * // Delete a few Identifiers
   * const { count } = await prisma.identifier.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends IdentifierDeleteManyArgs>(args?: Prisma.SelectSubset<T, IdentifierDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Identifiers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Identifiers
   * const identifier = await prisma.identifier.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends IdentifierUpdateManyArgs>(args: Prisma.SelectSubset<T, IdentifierUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Identifiers and returns the data updated in the database.
   * @param {IdentifierUpdateManyAndReturnArgs} args - Arguments to update many Identifiers.
   * @example
   * // Update many Identifiers
   * const identifier = await prisma.identifier.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Identifiers and only return the `id`
   * const identifierWithIdOnly = await prisma.identifier.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends IdentifierUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, IdentifierUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Identifier.
   * @param {IdentifierUpsertArgs} args - Arguments to update or create a Identifier.
   * @example
   * // Update or create a Identifier
   * const identifier = await prisma.identifier.upsert({
   *   create: {
   *     // ... data to create a Identifier
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Identifier we want to update
   *   }
   * })
   */
  upsert<T extends IdentifierUpsertArgs>(args: Prisma.SelectSubset<T, IdentifierUpsertArgs<ExtArgs>>): Prisma.Prisma__IdentifierClient<runtime.Types.Result.GetResult<Prisma.$IdentifierPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Identifiers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierCountArgs} args - Arguments to filter Identifiers to count.
   * @example
   * // Count the number of Identifiers
   * const count = await prisma.identifier.count({
   *   where: {
   *     // ... the filter for the Identifiers we want to count
   *   }
   * })
  **/
  count<T extends IdentifierCountArgs>(
    args?: Prisma.Subset<T, IdentifierCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], IdentifierCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Identifier.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends IdentifierAggregateArgs>(args: Prisma.Subset<T, IdentifierAggregateArgs>): Prisma.PrismaPromise<GetIdentifierAggregateType<T>>

  /**
   * Group by Identifier.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {IdentifierGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends IdentifierGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: IdentifierGroupByArgs['orderBy'] }
      : { orderBy?: IdentifierGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, IdentifierGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetIdentifierGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Identifier model
 */
readonly fields: IdentifierFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Identifier.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__IdentifierClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  recipient<T extends Prisma.RecipientDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RecipientDefaultArgs<ExtArgs>>): Prisma.Prisma__RecipientClient<runtime.Types.Result.GetResult<Prisma.$RecipientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Identifier model
 */
export interface IdentifierFieldRefs {
  readonly id: Prisma.FieldRef<"Identifier", 'String'>
  readonly recipientId: Prisma.FieldRef<"Identifier", 'String'>
  readonly type: Prisma.FieldRef<"Identifier", 'String'>
  readonly value: Prisma.FieldRef<"Identifier", 'String'>
  readonly createdAt: Prisma.FieldRef<"Identifier", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Identifier", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Identifier findUnique
 */
export type IdentifierFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * Filter, which Identifier to fetch.
   */
  where: Prisma.IdentifierWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier findUniqueOrThrow
 */
export type IdentifierFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * Filter, which Identifier to fetch.
   */
  where: Prisma.IdentifierWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier findFirst
 */
export type IdentifierFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * Filter, which Identifier to fetch.
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Identifiers to fetch.
   */
  orderBy?: Prisma.IdentifierOrderByWithRelationInput | Prisma.IdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Identifiers.
   */
  cursor?: Prisma.IdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Identifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Identifiers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Identifiers.
   */
  distinct?: Prisma.IdentifierScalarFieldEnum | Prisma.IdentifierScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier findFirstOrThrow
 */
export type IdentifierFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * Filter, which Identifier to fetch.
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Identifiers to fetch.
   */
  orderBy?: Prisma.IdentifierOrderByWithRelationInput | Prisma.IdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Identifiers.
   */
  cursor?: Prisma.IdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Identifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Identifiers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Identifiers.
   */
  distinct?: Prisma.IdentifierScalarFieldEnum | Prisma.IdentifierScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier findMany
 */
export type IdentifierFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * Filter, which Identifiers to fetch.
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Identifiers to fetch.
   */
  orderBy?: Prisma.IdentifierOrderByWithRelationInput | Prisma.IdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Identifiers.
   */
  cursor?: Prisma.IdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Identifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Identifiers.
   */
  skip?: number
  distinct?: Prisma.IdentifierScalarFieldEnum | Prisma.IdentifierScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier create
 */
export type IdentifierCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * The data needed to create a Identifier.
   */
  data: Prisma.XOR<Prisma.IdentifierCreateInput, Prisma.IdentifierUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier createMany
 */
export type IdentifierCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Identifiers.
   */
  data: Prisma.IdentifierCreateManyInput | Prisma.IdentifierCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Identifier createManyAndReturn
 */
export type IdentifierCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * The data used to create many Identifiers.
   */
  data: Prisma.IdentifierCreateManyInput | Prisma.IdentifierCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Identifier update
 */
export type IdentifierUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * The data needed to update a Identifier.
   */
  data: Prisma.XOR<Prisma.IdentifierUpdateInput, Prisma.IdentifierUncheckedUpdateInput>
  /**
   * Choose, which Identifier to update.
   */
  where: Prisma.IdentifierWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier updateMany
 */
export type IdentifierUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Identifiers.
   */
  data: Prisma.XOR<Prisma.IdentifierUpdateManyMutationInput, Prisma.IdentifierUncheckedUpdateManyInput>
  /**
   * Filter which Identifiers to update
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * Limit how many Identifiers to update.
   */
  limit?: number
}

/**
 * Identifier updateManyAndReturn
 */
export type IdentifierUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * The data used to update Identifiers.
   */
  data: Prisma.XOR<Prisma.IdentifierUpdateManyMutationInput, Prisma.IdentifierUncheckedUpdateManyInput>
  /**
   * Filter which Identifiers to update
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * Limit how many Identifiers to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Identifier upsert
 */
export type IdentifierUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * The filter to search for the Identifier to update in case it exists.
   */
  where: Prisma.IdentifierWhereUniqueInput
  /**
   * In case the Identifier found by the `where` argument doesn't exist, create a new Identifier with this data.
   */
  create: Prisma.XOR<Prisma.IdentifierCreateInput, Prisma.IdentifierUncheckedCreateInput>
  /**
   * In case the Identifier was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.IdentifierUpdateInput, Prisma.IdentifierUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier delete
 */
export type IdentifierDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
  /**
   * Filter which Identifier to delete.
   */
  where: Prisma.IdentifierWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * Identifier deleteMany
 */
export type IdentifierDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Identifiers to delete
   */
  where?: Prisma.IdentifierWhereInput
  /**
   * Limit how many Identifiers to delete.
   */
  limit?: number
}

/**
 * Identifier without action
 */
export type IdentifierDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Identifier
   */
  select?: Prisma.IdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Identifier
   */
  omit?: Prisma.IdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.IdentifierInclude<ExtArgs> | null
}
