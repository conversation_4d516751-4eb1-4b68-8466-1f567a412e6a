
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ConnectionAttribute` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ConnectionAttribute
 * 
 */
export type ConnectionAttributeModel = runtime.Types.Result.DefaultSelection<Prisma.$ConnectionAttributePayload>

export type AggregateConnectionAttribute = {
  _count: ConnectionAttributeCountAggregateOutputType | null
  _min: ConnectionAttributeMinAggregateOutputType | null
  _max: ConnectionAttributeMaxAggregateOutputType | null
}

export type ConnectionAttributeMinAggregateOutputType = {
  id: string | null
  connectionId: string | null
  name: string | null
  value: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ConnectionAttributeMaxAggregateOutputType = {
  id: string | null
  connectionId: string | null
  name: string | null
  value: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ConnectionAttributeCountAggregateOutputType = {
  id: number
  connectionId: number
  name: number
  value: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ConnectionAttributeMinAggregateInputType = {
  id?: true
  connectionId?: true
  name?: true
  value?: true
  createdAt?: true
  updatedAt?: true
}

export type ConnectionAttributeMaxAggregateInputType = {
  id?: true
  connectionId?: true
  name?: true
  value?: true
  createdAt?: true
  updatedAt?: true
}

export type ConnectionAttributeCountAggregateInputType = {
  id?: true
  connectionId?: true
  name?: true
  value?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ConnectionAttributeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ConnectionAttribute to aggregate.
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ConnectionAttributes to fetch.
   */
  orderBy?: Prisma.ConnectionAttributeOrderByWithRelationInput | Prisma.ConnectionAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ConnectionAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ConnectionAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ConnectionAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ConnectionAttributes
  **/
  _count?: true | ConnectionAttributeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ConnectionAttributeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ConnectionAttributeMaxAggregateInputType
}

export type GetConnectionAttributeAggregateType<T extends ConnectionAttributeAggregateArgs> = {
      [P in keyof T & keyof AggregateConnectionAttribute]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateConnectionAttribute[P]>
    : Prisma.GetScalarType<T[P], AggregateConnectionAttribute[P]>
}




export type ConnectionAttributeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionAttributeWhereInput
  orderBy?: Prisma.ConnectionAttributeOrderByWithAggregationInput | Prisma.ConnectionAttributeOrderByWithAggregationInput[]
  by: Prisma.ConnectionAttributeScalarFieldEnum[] | Prisma.ConnectionAttributeScalarFieldEnum
  having?: Prisma.ConnectionAttributeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ConnectionAttributeCountAggregateInputType | true
  _min?: ConnectionAttributeMinAggregateInputType
  _max?: ConnectionAttributeMaxAggregateInputType
}

export type ConnectionAttributeGroupByOutputType = {
  id: string
  connectionId: string
  name: string
  value: string
  createdAt: Date
  updatedAt: Date
  _count: ConnectionAttributeCountAggregateOutputType | null
  _min: ConnectionAttributeMinAggregateOutputType | null
  _max: ConnectionAttributeMaxAggregateOutputType | null
}

type GetConnectionAttributeGroupByPayload<T extends ConnectionAttributeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ConnectionAttributeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ConnectionAttributeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ConnectionAttributeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ConnectionAttributeGroupByOutputType[P]>
      }
    >
  > 



export type ConnectionAttributeWhereInput = {
  AND?: Prisma.ConnectionAttributeWhereInput | Prisma.ConnectionAttributeWhereInput[]
  OR?: Prisma.ConnectionAttributeWhereInput[]
  NOT?: Prisma.ConnectionAttributeWhereInput | Prisma.ConnectionAttributeWhereInput[]
  id?: Prisma.StringFilter<"ConnectionAttribute"> | string
  connectionId?: Prisma.StringFilter<"ConnectionAttribute"> | string
  name?: Prisma.StringFilter<"ConnectionAttribute"> | string
  value?: Prisma.StringFilter<"ConnectionAttribute"> | string
  createdAt?: Prisma.DateTimeFilter<"ConnectionAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ConnectionAttribute"> | Date | string
  connection?: Prisma.XOR<Prisma.ConnectionScalarRelationFilter, Prisma.ConnectionWhereInput>
}

export type ConnectionAttributeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  connectionId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  connection?: Prisma.ConnectionOrderByWithRelationInput
}

export type ConnectionAttributeWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  unique_connection_attribute_connection_id_name?: Prisma.ConnectionAttributeUnique_connection_attribute_connection_id_nameCompoundUniqueInput
  AND?: Prisma.ConnectionAttributeWhereInput | Prisma.ConnectionAttributeWhereInput[]
  OR?: Prisma.ConnectionAttributeWhereInput[]
  NOT?: Prisma.ConnectionAttributeWhereInput | Prisma.ConnectionAttributeWhereInput[]
  connectionId?: Prisma.StringFilter<"ConnectionAttribute"> | string
  name?: Prisma.StringFilter<"ConnectionAttribute"> | string
  value?: Prisma.StringFilter<"ConnectionAttribute"> | string
  createdAt?: Prisma.DateTimeFilter<"ConnectionAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ConnectionAttribute"> | Date | string
  connection?: Prisma.XOR<Prisma.ConnectionScalarRelationFilter, Prisma.ConnectionWhereInput>
}, "id" | "unique_connection_attribute_connection_id_name">

export type ConnectionAttributeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  connectionId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ConnectionAttributeCountOrderByAggregateInput
  _max?: Prisma.ConnectionAttributeMaxOrderByAggregateInput
  _min?: Prisma.ConnectionAttributeMinOrderByAggregateInput
}

export type ConnectionAttributeScalarWhereWithAggregatesInput = {
  AND?: Prisma.ConnectionAttributeScalarWhereWithAggregatesInput | Prisma.ConnectionAttributeScalarWhereWithAggregatesInput[]
  OR?: Prisma.ConnectionAttributeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ConnectionAttributeScalarWhereWithAggregatesInput | Prisma.ConnectionAttributeScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"ConnectionAttribute"> | string
  connectionId?: Prisma.StringWithAggregatesFilter<"ConnectionAttribute"> | string
  name?: Prisma.StringWithAggregatesFilter<"ConnectionAttribute"> | string
  value?: Prisma.StringWithAggregatesFilter<"ConnectionAttribute"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"ConnectionAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"ConnectionAttribute"> | Date | string
}

export type ConnectionAttributeCreateInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  connection: Prisma.ConnectionCreateNestedOneWithoutAttributesInput
}

export type ConnectionAttributeUncheckedCreateInput = {
  id?: string
  connectionId: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ConnectionAttributeUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  connection?: Prisma.ConnectionUpdateOneRequiredWithoutAttributesNestedInput
}

export type ConnectionAttributeUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  connectionId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionAttributeCreateManyInput = {
  id?: string
  connectionId: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ConnectionAttributeUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionAttributeUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  connectionId?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionAttributeUnique_connection_attribute_connection_id_nameCompoundUniqueInput = {
  connectionId: string
  name: string
}

export type ConnectionAttributeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  connectionId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ConnectionAttributeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  connectionId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ConnectionAttributeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  connectionId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  value?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ConnectionAttributeListRelationFilter = {
  every?: Prisma.ConnectionAttributeWhereInput
  some?: Prisma.ConnectionAttributeWhereInput
  none?: Prisma.ConnectionAttributeWhereInput
}

export type ConnectionAttributeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ConnectionAttributeCreateNestedManyWithoutConnectionInput = {
  create?: Prisma.XOR<Prisma.ConnectionAttributeCreateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput> | Prisma.ConnectionAttributeCreateWithoutConnectionInput[] | Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput[]
  connectOrCreate?: Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput | Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput[]
  createMany?: Prisma.ConnectionAttributeCreateManyConnectionInputEnvelope
  connect?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
}

export type ConnectionAttributeUncheckedCreateNestedManyWithoutConnectionInput = {
  create?: Prisma.XOR<Prisma.ConnectionAttributeCreateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput> | Prisma.ConnectionAttributeCreateWithoutConnectionInput[] | Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput[]
  connectOrCreate?: Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput | Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput[]
  createMany?: Prisma.ConnectionAttributeCreateManyConnectionInputEnvelope
  connect?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
}

export type ConnectionAttributeUpdateManyWithoutConnectionNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionAttributeCreateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput> | Prisma.ConnectionAttributeCreateWithoutConnectionInput[] | Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput[]
  connectOrCreate?: Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput | Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput[]
  upsert?: Prisma.ConnectionAttributeUpsertWithWhereUniqueWithoutConnectionInput | Prisma.ConnectionAttributeUpsertWithWhereUniqueWithoutConnectionInput[]
  createMany?: Prisma.ConnectionAttributeCreateManyConnectionInputEnvelope
  set?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  disconnect?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  delete?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  connect?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  update?: Prisma.ConnectionAttributeUpdateWithWhereUniqueWithoutConnectionInput | Prisma.ConnectionAttributeUpdateWithWhereUniqueWithoutConnectionInput[]
  updateMany?: Prisma.ConnectionAttributeUpdateManyWithWhereWithoutConnectionInput | Prisma.ConnectionAttributeUpdateManyWithWhereWithoutConnectionInput[]
  deleteMany?: Prisma.ConnectionAttributeScalarWhereInput | Prisma.ConnectionAttributeScalarWhereInput[]
}

export type ConnectionAttributeUncheckedUpdateManyWithoutConnectionNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionAttributeCreateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput> | Prisma.ConnectionAttributeCreateWithoutConnectionInput[] | Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput[]
  connectOrCreate?: Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput | Prisma.ConnectionAttributeCreateOrConnectWithoutConnectionInput[]
  upsert?: Prisma.ConnectionAttributeUpsertWithWhereUniqueWithoutConnectionInput | Prisma.ConnectionAttributeUpsertWithWhereUniqueWithoutConnectionInput[]
  createMany?: Prisma.ConnectionAttributeCreateManyConnectionInputEnvelope
  set?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  disconnect?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  delete?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  connect?: Prisma.ConnectionAttributeWhereUniqueInput | Prisma.ConnectionAttributeWhereUniqueInput[]
  update?: Prisma.ConnectionAttributeUpdateWithWhereUniqueWithoutConnectionInput | Prisma.ConnectionAttributeUpdateWithWhereUniqueWithoutConnectionInput[]
  updateMany?: Prisma.ConnectionAttributeUpdateManyWithWhereWithoutConnectionInput | Prisma.ConnectionAttributeUpdateManyWithWhereWithoutConnectionInput[]
  deleteMany?: Prisma.ConnectionAttributeScalarWhereInput | Prisma.ConnectionAttributeScalarWhereInput[]
}

export type ConnectionAttributeCreateWithoutConnectionInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ConnectionAttributeUncheckedCreateWithoutConnectionInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ConnectionAttributeCreateOrConnectWithoutConnectionInput = {
  where: Prisma.ConnectionAttributeWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionAttributeCreateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput>
}

export type ConnectionAttributeCreateManyConnectionInputEnvelope = {
  data: Prisma.ConnectionAttributeCreateManyConnectionInput | Prisma.ConnectionAttributeCreateManyConnectionInput[]
  skipDuplicates?: boolean
}

export type ConnectionAttributeUpsertWithWhereUniqueWithoutConnectionInput = {
  where: Prisma.ConnectionAttributeWhereUniqueInput
  update: Prisma.XOR<Prisma.ConnectionAttributeUpdateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedUpdateWithoutConnectionInput>
  create: Prisma.XOR<Prisma.ConnectionAttributeCreateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedCreateWithoutConnectionInput>
}

export type ConnectionAttributeUpdateWithWhereUniqueWithoutConnectionInput = {
  where: Prisma.ConnectionAttributeWhereUniqueInput
  data: Prisma.XOR<Prisma.ConnectionAttributeUpdateWithoutConnectionInput, Prisma.ConnectionAttributeUncheckedUpdateWithoutConnectionInput>
}

export type ConnectionAttributeUpdateManyWithWhereWithoutConnectionInput = {
  where: Prisma.ConnectionAttributeScalarWhereInput
  data: Prisma.XOR<Prisma.ConnectionAttributeUpdateManyMutationInput, Prisma.ConnectionAttributeUncheckedUpdateManyWithoutConnectionInput>
}

export type ConnectionAttributeScalarWhereInput = {
  AND?: Prisma.ConnectionAttributeScalarWhereInput | Prisma.ConnectionAttributeScalarWhereInput[]
  OR?: Prisma.ConnectionAttributeScalarWhereInput[]
  NOT?: Prisma.ConnectionAttributeScalarWhereInput | Prisma.ConnectionAttributeScalarWhereInput[]
  id?: Prisma.StringFilter<"ConnectionAttribute"> | string
  connectionId?: Prisma.StringFilter<"ConnectionAttribute"> | string
  name?: Prisma.StringFilter<"ConnectionAttribute"> | string
  value?: Prisma.StringFilter<"ConnectionAttribute"> | string
  createdAt?: Prisma.DateTimeFilter<"ConnectionAttribute"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ConnectionAttribute"> | Date | string
}

export type ConnectionAttributeCreateManyConnectionInput = {
  id?: string
  name: string
  value?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ConnectionAttributeUpdateWithoutConnectionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionAttributeUncheckedUpdateWithoutConnectionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ConnectionAttributeUncheckedUpdateManyWithoutConnectionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type ConnectionAttributeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  connectionId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  connection?: boolean | Prisma.ConnectionDefaultArgs<ExtArgs>
}, ExtArgs["result"]["connectionAttribute"]>

export type ConnectionAttributeSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  connectionId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  connection?: boolean | Prisma.ConnectionDefaultArgs<ExtArgs>
}, ExtArgs["result"]["connectionAttribute"]>

export type ConnectionAttributeSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  connectionId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  connection?: boolean | Prisma.ConnectionDefaultArgs<ExtArgs>
}, ExtArgs["result"]["connectionAttribute"]>

export type ConnectionAttributeSelectScalar = {
  id?: boolean
  connectionId?: boolean
  name?: boolean
  value?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ConnectionAttributeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "connectionId" | "name" | "value" | "createdAt" | "updatedAt", ExtArgs["result"]["connectionAttribute"]>
export type ConnectionAttributeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  connection?: boolean | Prisma.ConnectionDefaultArgs<ExtArgs>
}
export type ConnectionAttributeIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  connection?: boolean | Prisma.ConnectionDefaultArgs<ExtArgs>
}
export type ConnectionAttributeIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  connection?: boolean | Prisma.ConnectionDefaultArgs<ExtArgs>
}

export type $ConnectionAttributePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ConnectionAttribute"
  objects: {
    connection: Prisma.$ConnectionPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    connectionId: string
    name: string
    value: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["connectionAttribute"]>
  composites: {}
}

export type ConnectionAttributeGetPayload<S extends boolean | null | undefined | ConnectionAttributeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload, S>

export type ConnectionAttributeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ConnectionAttributeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit' | 'relationLoadStrategy'> & {
    select?: ConnectionAttributeCountAggregateInputType | true
  }

export interface ConnectionAttributeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ConnectionAttribute'], meta: { name: 'ConnectionAttribute' } }
  /**
   * Find zero or one ConnectionAttribute that matches the filter.
   * @param {ConnectionAttributeFindUniqueArgs} args - Arguments to find a ConnectionAttribute
   * @example
   * // Get one ConnectionAttribute
   * const connectionAttribute = await prisma.connectionAttribute.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ConnectionAttributeFindUniqueArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ConnectionAttribute that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ConnectionAttributeFindUniqueOrThrowArgs} args - Arguments to find a ConnectionAttribute
   * @example
   * // Get one ConnectionAttribute
   * const connectionAttribute = await prisma.connectionAttribute.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ConnectionAttributeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ConnectionAttribute that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeFindFirstArgs} args - Arguments to find a ConnectionAttribute
   * @example
   * // Get one ConnectionAttribute
   * const connectionAttribute = await prisma.connectionAttribute.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ConnectionAttributeFindFirstArgs>(args?: Prisma.SelectSubset<T, ConnectionAttributeFindFirstArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ConnectionAttribute that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeFindFirstOrThrowArgs} args - Arguments to find a ConnectionAttribute
   * @example
   * // Get one ConnectionAttribute
   * const connectionAttribute = await prisma.connectionAttribute.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ConnectionAttributeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ConnectionAttributeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ConnectionAttributes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ConnectionAttributes
   * const connectionAttributes = await prisma.connectionAttribute.findMany()
   * 
   * // Get first 10 ConnectionAttributes
   * const connectionAttributes = await prisma.connectionAttribute.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const connectionAttributeWithIdOnly = await prisma.connectionAttribute.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ConnectionAttributeFindManyArgs>(args?: Prisma.SelectSubset<T, ConnectionAttributeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ConnectionAttribute.
   * @param {ConnectionAttributeCreateArgs} args - Arguments to create a ConnectionAttribute.
   * @example
   * // Create one ConnectionAttribute
   * const ConnectionAttribute = await prisma.connectionAttribute.create({
   *   data: {
   *     // ... data to create a ConnectionAttribute
   *   }
   * })
   * 
   */
  create<T extends ConnectionAttributeCreateArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeCreateArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ConnectionAttributes.
   * @param {ConnectionAttributeCreateManyArgs} args - Arguments to create many ConnectionAttributes.
   * @example
   * // Create many ConnectionAttributes
   * const connectionAttribute = await prisma.connectionAttribute.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ConnectionAttributeCreateManyArgs>(args?: Prisma.SelectSubset<T, ConnectionAttributeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ConnectionAttributes and returns the data saved in the database.
   * @param {ConnectionAttributeCreateManyAndReturnArgs} args - Arguments to create many ConnectionAttributes.
   * @example
   * // Create many ConnectionAttributes
   * const connectionAttribute = await prisma.connectionAttribute.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ConnectionAttributes and only return the `id`
   * const connectionAttributeWithIdOnly = await prisma.connectionAttribute.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ConnectionAttributeCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ConnectionAttributeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ConnectionAttribute.
   * @param {ConnectionAttributeDeleteArgs} args - Arguments to delete one ConnectionAttribute.
   * @example
   * // Delete one ConnectionAttribute
   * const ConnectionAttribute = await prisma.connectionAttribute.delete({
   *   where: {
   *     // ... filter to delete one ConnectionAttribute
   *   }
   * })
   * 
   */
  delete<T extends ConnectionAttributeDeleteArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeDeleteArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ConnectionAttribute.
   * @param {ConnectionAttributeUpdateArgs} args - Arguments to update one ConnectionAttribute.
   * @example
   * // Update one ConnectionAttribute
   * const connectionAttribute = await prisma.connectionAttribute.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ConnectionAttributeUpdateArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeUpdateArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ConnectionAttributes.
   * @param {ConnectionAttributeDeleteManyArgs} args - Arguments to filter ConnectionAttributes to delete.
   * @example
   * // Delete a few ConnectionAttributes
   * const { count } = await prisma.connectionAttribute.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ConnectionAttributeDeleteManyArgs>(args?: Prisma.SelectSubset<T, ConnectionAttributeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ConnectionAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ConnectionAttributes
   * const connectionAttribute = await prisma.connectionAttribute.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ConnectionAttributeUpdateManyArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ConnectionAttributes and returns the data updated in the database.
   * @param {ConnectionAttributeUpdateManyAndReturnArgs} args - Arguments to update many ConnectionAttributes.
   * @example
   * // Update many ConnectionAttributes
   * const connectionAttribute = await prisma.connectionAttribute.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ConnectionAttributes and only return the `id`
   * const connectionAttributeWithIdOnly = await prisma.connectionAttribute.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ConnectionAttributeUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ConnectionAttribute.
   * @param {ConnectionAttributeUpsertArgs} args - Arguments to update or create a ConnectionAttribute.
   * @example
   * // Update or create a ConnectionAttribute
   * const connectionAttribute = await prisma.connectionAttribute.upsert({
   *   create: {
   *     // ... data to create a ConnectionAttribute
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ConnectionAttribute we want to update
   *   }
   * })
   */
  upsert<T extends ConnectionAttributeUpsertArgs>(args: Prisma.SelectSubset<T, ConnectionAttributeUpsertArgs<ExtArgs>>): Prisma.Prisma__ConnectionAttributeClient<runtime.Types.Result.GetResult<Prisma.$ConnectionAttributePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ConnectionAttributes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeCountArgs} args - Arguments to filter ConnectionAttributes to count.
   * @example
   * // Count the number of ConnectionAttributes
   * const count = await prisma.connectionAttribute.count({
   *   where: {
   *     // ... the filter for the ConnectionAttributes we want to count
   *   }
   * })
  **/
  count<T extends ConnectionAttributeCountArgs>(
    args?: Prisma.Subset<T, ConnectionAttributeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ConnectionAttributeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ConnectionAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ConnectionAttributeAggregateArgs>(args: Prisma.Subset<T, ConnectionAttributeAggregateArgs>): Prisma.PrismaPromise<GetConnectionAttributeAggregateType<T>>

  /**
   * Group by ConnectionAttribute.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAttributeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ConnectionAttributeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ConnectionAttributeGroupByArgs['orderBy'] }
      : { orderBy?: ConnectionAttributeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ConnectionAttributeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetConnectionAttributeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ConnectionAttribute model
 */
readonly fields: ConnectionAttributeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ConnectionAttribute.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ConnectionAttributeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  connection<T extends Prisma.ConnectionDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ConnectionDefaultArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ConnectionAttribute model
 */
export interface ConnectionAttributeFieldRefs {
  readonly id: Prisma.FieldRef<"ConnectionAttribute", 'String'>
  readonly connectionId: Prisma.FieldRef<"ConnectionAttribute", 'String'>
  readonly name: Prisma.FieldRef<"ConnectionAttribute", 'String'>
  readonly value: Prisma.FieldRef<"ConnectionAttribute", 'String'>
  readonly createdAt: Prisma.FieldRef<"ConnectionAttribute", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"ConnectionAttribute", 'DateTime'>
}
    

// Custom InputTypes
/**
 * ConnectionAttribute findUnique
 */
export type ConnectionAttributeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ConnectionAttribute to fetch.
   */
  where: Prisma.ConnectionAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute findUniqueOrThrow
 */
export type ConnectionAttributeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ConnectionAttribute to fetch.
   */
  where: Prisma.ConnectionAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute findFirst
 */
export type ConnectionAttributeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ConnectionAttribute to fetch.
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ConnectionAttributes to fetch.
   */
  orderBy?: Prisma.ConnectionAttributeOrderByWithRelationInput | Prisma.ConnectionAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ConnectionAttributes.
   */
  cursor?: Prisma.ConnectionAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ConnectionAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ConnectionAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ConnectionAttributes.
   */
  distinct?: Prisma.ConnectionAttributeScalarFieldEnum | Prisma.ConnectionAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute findFirstOrThrow
 */
export type ConnectionAttributeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ConnectionAttribute to fetch.
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ConnectionAttributes to fetch.
   */
  orderBy?: Prisma.ConnectionAttributeOrderByWithRelationInput | Prisma.ConnectionAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ConnectionAttributes.
   */
  cursor?: Prisma.ConnectionAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ConnectionAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ConnectionAttributes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ConnectionAttributes.
   */
  distinct?: Prisma.ConnectionAttributeScalarFieldEnum | Prisma.ConnectionAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute findMany
 */
export type ConnectionAttributeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * Filter, which ConnectionAttributes to fetch.
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ConnectionAttributes to fetch.
   */
  orderBy?: Prisma.ConnectionAttributeOrderByWithRelationInput | Prisma.ConnectionAttributeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ConnectionAttributes.
   */
  cursor?: Prisma.ConnectionAttributeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ConnectionAttributes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ConnectionAttributes.
   */
  skip?: number
  distinct?: Prisma.ConnectionAttributeScalarFieldEnum | Prisma.ConnectionAttributeScalarFieldEnum[]
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute create
 */
export type ConnectionAttributeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * The data needed to create a ConnectionAttribute.
   */
  data: Prisma.XOR<Prisma.ConnectionAttributeCreateInput, Prisma.ConnectionAttributeUncheckedCreateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute createMany
 */
export type ConnectionAttributeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ConnectionAttributes.
   */
  data: Prisma.ConnectionAttributeCreateManyInput | Prisma.ConnectionAttributeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ConnectionAttribute createManyAndReturn
 */
export type ConnectionAttributeCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * The data used to create many ConnectionAttributes.
   */
  data: Prisma.ConnectionAttributeCreateManyInput | Prisma.ConnectionAttributeCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ConnectionAttribute update
 */
export type ConnectionAttributeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * The data needed to update a ConnectionAttribute.
   */
  data: Prisma.XOR<Prisma.ConnectionAttributeUpdateInput, Prisma.ConnectionAttributeUncheckedUpdateInput>
  /**
   * Choose, which ConnectionAttribute to update.
   */
  where: Prisma.ConnectionAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute updateMany
 */
export type ConnectionAttributeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ConnectionAttributes.
   */
  data: Prisma.XOR<Prisma.ConnectionAttributeUpdateManyMutationInput, Prisma.ConnectionAttributeUncheckedUpdateManyInput>
  /**
   * Filter which ConnectionAttributes to update
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * Limit how many ConnectionAttributes to update.
   */
  limit?: number
}

/**
 * ConnectionAttribute updateManyAndReturn
 */
export type ConnectionAttributeUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * The data used to update ConnectionAttributes.
   */
  data: Prisma.XOR<Prisma.ConnectionAttributeUpdateManyMutationInput, Prisma.ConnectionAttributeUncheckedUpdateManyInput>
  /**
   * Filter which ConnectionAttributes to update
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * Limit how many ConnectionAttributes to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ConnectionAttribute upsert
 */
export type ConnectionAttributeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * The filter to search for the ConnectionAttribute to update in case it exists.
   */
  where: Prisma.ConnectionAttributeWhereUniqueInput
  /**
   * In case the ConnectionAttribute found by the `where` argument doesn't exist, create a new ConnectionAttribute with this data.
   */
  create: Prisma.XOR<Prisma.ConnectionAttributeCreateInput, Prisma.ConnectionAttributeUncheckedCreateInput>
  /**
   * In case the ConnectionAttribute was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ConnectionAttributeUpdateInput, Prisma.ConnectionAttributeUncheckedUpdateInput>
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute delete
 */
export type ConnectionAttributeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
  /**
   * Filter which ConnectionAttribute to delete.
   */
  where: Prisma.ConnectionAttributeWhereUniqueInput
  relationLoadStrategy?: Prisma.RelationLoadStrategy
}

/**
 * ConnectionAttribute deleteMany
 */
export type ConnectionAttributeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ConnectionAttributes to delete
   */
  where?: Prisma.ConnectionAttributeWhereInput
  /**
   * Limit how many ConnectionAttributes to delete.
   */
  limit?: number
}

/**
 * ConnectionAttribute without action
 */
export type ConnectionAttributeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ConnectionAttribute
   */
  select?: Prisma.ConnectionAttributeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ConnectionAttribute
   */
  omit?: Prisma.ConnectionAttributeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionAttributeInclude<ExtArgs> | null
}
