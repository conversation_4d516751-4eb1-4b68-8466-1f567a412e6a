
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums"
import * as $Class from "./internal/class"
import * as Prisma from "./internal/prismaNamespace"

export * as $Enums from './enums'
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more ChannelAttributes
 * const channelAttributes = await prisma.channelAttribute.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, Log = $Class.LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<ClientOptions, Log, ExtArgs>
export { Prisma }


// file annotations for bundling tools to include these files
path.join(__dirname, "libquery_engine-darwin-arm64.dylib.node")
path.join(process.cwd(), "src/prisma/libquery_engine-darwin-arm64.dylib.node")

/**
 * Model ChannelAttribute
 * 
 */
export type ChannelAttribute = Prisma.ChannelAttributeModel
/**
 * Model ChannelMessage
 * 
 */
export type ChannelMessage = Prisma.ChannelMessageModel
/**
 * Model Channel
 * 
 */
export type Channel = Prisma.ChannelModel
/**
 * Model ConnectionAttribute
 * 
 */
export type ConnectionAttribute = Prisma.ConnectionAttributeModel
/**
 * Model Connection
 * 
 */
export type Connection = Prisma.ConnectionModel
/**
 * Model Identifier
 * 
 */
export type Identifier = Prisma.IdentifierModel
/**
 * Model MessageAttribute
 * 
 */
export type MessageAttribute = Prisma.MessageAttributeModel
/**
 * Model MessageContext
 * 
 */
export type MessageContext = Prisma.MessageContextModel
/**
 * Model Message
 * 
 */
export type Message = Prisma.MessageModel
/**
 * Model RecipientAttribute
 * 
 */
export type RecipientAttribute = Prisma.RecipientAttributeModel
/**
 * Model RecipientMessage
 * 
 */
export type RecipientMessage = Prisma.RecipientMessageModel
/**
 * Model Recipient
 * 
 */
export type Recipient = Prisma.RecipientModel
/**
 * Model Tag
 * 
 */
export type Tag = Prisma.TagModel
/**
 * Model TeamsUser
 * 
 */
export type TeamsUser = Prisma.TeamsUserModel
/**
 * Model Action
 * 
 */
export type Action = Prisma.ActionModel
/**
 * Model LocaleTemplate
 * 
 */
export type LocaleTemplate = Prisma.LocaleTemplateModel
/**
 * Model Template
 * 
 */
export type Template = Prisma.TemplateModel
/**
 * Model Tenant
 * 
 */
export type Tenant = Prisma.TenantModel
/**
 * Model TopicPreference
 * 
 */
export type TopicPreference = Prisma.TopicPreferenceModel
/**
 * Model Topic
 * 
 */
export type Topic = Prisma.TopicModel
/**
 * Model TrackedLink
 * 
 */
export type TrackedLink = Prisma.TrackedLinkModel
/**
 * Model TwoWayMessage
 * 
 */
export type TwoWayMessage = Prisma.TwoWayMessageModel

export type Status = $Enums.Status
export const Status = $Enums.Status
