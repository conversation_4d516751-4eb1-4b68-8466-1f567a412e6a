
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * WARNING: This is an internal file that is subject to change!
 * 
 * 🛑 Under no circumstances should you import this file directly! 🛑
 * 
 * All exports from this file are wrapped under a `Prisma` namespace object in the client.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 * 
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective 
 * model files in the `model` directory!
 */

import * as runtime from "@prisma/client/runtime/library"
import type * as Prisma from "../models"
import { type PrismaClient } from "./class"

export type * from '../models'

export type DMMF = typeof runtime.DMMF

export type PrismaPromise<T> = runtime.Types.Public.PrismaPromise<T>

/**
 * Validator
 */
export const validator = runtime.Public.validator

/**
 * Prisma Errors
 */

export const PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
export type PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError

export const PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
export type PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError

export const PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
export type PrismaClientRustPanicError = runtime.PrismaClientRustPanicError

export const PrismaClientInitializationError = runtime.PrismaClientInitializationError
export type PrismaClientInitializationError = runtime.PrismaClientInitializationError

export const PrismaClientValidationError = runtime.PrismaClientValidationError
export type PrismaClientValidationError = runtime.PrismaClientValidationError

/**
 * Re-export of sql-template-tag
 */
export const sql = runtime.sqltag
export const empty = runtime.empty
export const join = runtime.join
export const raw = runtime.raw
export const Sql = runtime.Sql
export type Sql = runtime.Sql



/**
 * Decimal.js
 */
export const Decimal = runtime.Decimal
export type Decimal = runtime.Decimal

export type DecimalJsLike = runtime.DecimalJsLike

/**
 * Metrics
 */
export type Metrics = runtime.Metrics
export type Metric<T> = runtime.Metric<T>
export type MetricHistogram = runtime.MetricHistogram
export type MetricHistogramBucket = runtime.MetricHistogramBucket

/**
* Extensions
*/
export type Extension = runtime.Types.Extensions.UserArgs
export const getExtensionContext = runtime.Extensions.getExtensionContext
export type Args<T, F extends runtime.Operation> = runtime.Types.Public.Args<T, F>
export type Payload<T, F extends runtime.Operation = never> = runtime.Types.Public.Payload<T, F>
export type Result<T, A, F extends runtime.Operation> = runtime.Types.Public.Result<T, A, F>
export type Exact<A, W> = runtime.Types.Public.Exact<A, W>

export type PrismaVersion = {
  client: string
  engine: string
}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
export const prismaVersion: PrismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

/**
 * Utility Types
 */

export type JsonObject = runtime.JsonObject
export type JsonArray = runtime.JsonArray
export type JsonValue = runtime.JsonValue
export type InputJsonObject = runtime.InputJsonObject
export type InputJsonArray = runtime.InputJsonArray
export type InputJsonValue = runtime.InputJsonValue

export const NullTypes = {
  DbNull: runtime.objectEnumValues.classes.DbNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.DbNull),
  JsonNull: runtime.objectEnumValues.classes.JsonNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.JsonNull),
  AnyNull: runtime.objectEnumValues.classes.AnyNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.AnyNull),
}

/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull

/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull

/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull

type SelectAndInclude = {
  select: any
  include: any
}

type SelectAndOmit = {
  select: any
  omit: any
}

/**
 * From T, pick a set of properties whose keys are in the union K
 */
type Prisma__Pick<T, K extends keyof T> = {
    [P in K]: T[P];
};

export type Enumerable<T> = T | Array<T>;

/**
 * Subset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
 */
export type Subset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never;
};

/**
 * SelectSubset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
 * Additionally, it validates, if both select and include are present. If the case, it errors.
 */
export type SelectSubset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  (T extends SelectAndInclude
    ? 'Please either choose `select` or `include`.'
    : T extends SelectAndOmit
      ? 'Please either choose `select` or `omit`.'
      : {})

/**
 * Subset + Intersection
 * @desc From `T` pick properties that exist in `U` and intersect `K`
 */
export type SubsetIntersection<T, U, K> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  K

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

/**
 * XOR is needed to have a real mutually exclusive union type
 * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
 */
export type XOR<T, U> =
  T extends object ?
  U extends object ?
    (Without<T, U> & U) | (Without<U, T> & T)
  : U : T


/**
 * Is T a Record?
 */
type IsObject<T extends any> = T extends Array<any>
? False
: T extends Date
? False
: T extends Uint8Array
? False
: T extends BigInt
? False
: T extends object
? True
: False


/**
 * If it's T[], return T
 */
export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

/**
 * From ts-toolbelt
 */

type __Either<O extends object, K extends Key> = Omit<O, K> &
  {
    // Merge all but K
    [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
  }[K]

type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

type _Either<
  O extends object,
  K extends Key,
  strict extends Boolean
> = {
  1: EitherStrict<O, K>
  0: EitherLoose<O, K>
}[strict]

export type Either<
  O extends object,
  K extends Key,
  strict extends Boolean = 1
> = O extends unknown ? _Either<O, K, strict> : never

export type Union = any

export type PatchUndefined<O extends object, O1 extends object> = {
  [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
} & {}

/** Helper Types for "Merge" **/
export type IntersectOf<U extends Union> = (
  U extends unknown ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never

export type Overwrite<O extends object, O1 extends object> = {
    [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
} & {};

type _Merge<U extends object> = IntersectOf<Overwrite<U, {
    [K in keyof U]-?: At<U, K>;
}>>;

type Key = string | number | symbol;
type AtStrict<O extends object, K extends Key> = O[K & keyof O];
type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
    1: AtStrict<O, K>;
    0: AtLoose<O, K>;
}[strict];

export type ComputeRaw<A extends any> = A extends Function ? A : {
  [K in keyof A]: A[K];
} & {};

export type OptionalFlat<O> = {
  [K in keyof O]?: O[K];
} & {};

type _Record<K extends keyof any, T> = {
  [P in K]: T;
};

// cause typescript not to expand types and preserve names
type NoExpand<T> = T extends unknown ? T : never;

// this type assumes the passed object is entirely optional
export type AtLeast<O extends object, K extends string> = NoExpand<
  O extends unknown
  ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
    | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
  : never>;

type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
/** End Helper Types for "Merge" **/

export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

export type Boolean = True | False

export type True = 1

export type False = 0

export type Not<B extends Boolean> = {
  0: 1
  1: 0
}[B]

export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
  ? 0 // anything `never` is false
  : A1 extends A2
  ? 1
  : 0

export type Has<U extends Union, U1 extends Union> = Not<
  Extends<Exclude<U1, U>, U1>
>

export type Or<B1 extends Boolean, B2 extends Boolean> = {
  0: {
    0: 0
    1: 1
  }
  1: {
    0: 1
    1: 1
  }
}[B1][B2]

export type Keys<U extends Union> = U extends unknown ? keyof U : never

export type GetScalarType<T, O> = O extends object ? {
  [P in keyof T]: P extends keyof O
    ? O[P]
    : never
} : never

type FieldPaths<
  T,
  U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
> = IsObject<T> extends True ? U : T

export type GetHavingFields<T> = {
  [K in keyof T]: Or<
    Or<Extends<'OR', K>, Extends<'AND', K>>,
    Extends<'NOT', K>
  > extends True
    ? // infer is only needed to not hit TS limit
      // based on the brilliant idea of Pierre-Antoine Mills
      // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
      T[K] extends infer TK
      ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
      : never
    : {} extends FieldPaths<T[K]>
    ? never
    : K
}[keyof T]

/**
 * Convert tuple to union
 */
type _TupleToUnion<T> = T extends (infer E)[] ? E : never
type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
export type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

/**
 * Like `Pick`, but additionally can also accept an array of keys
 */
export type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

/**
 * Exclude all keys with underscores
 */
export type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


export const ModelName = {
  ChannelAttribute: 'ChannelAttribute',
  ChannelMessage: 'ChannelMessage',
  Channel: 'Channel',
  ConnectionAttribute: 'ConnectionAttribute',
  Connection: 'Connection',
  Identifier: 'Identifier',
  MessageAttribute: 'MessageAttribute',
  MessageContext: 'MessageContext',
  Message: 'Message',
  RecipientAttribute: 'RecipientAttribute',
  RecipientMessage: 'RecipientMessage',
  Recipient: 'Recipient',
  Tag: 'Tag',
  TeamsUser: 'TeamsUser',
  Action: 'Action',
  LocaleTemplate: 'LocaleTemplate',
  Template: 'Template',
  Tenant: 'Tenant',
  TopicPreference: 'TopicPreference',
  Topic: 'Topic',
  TrackedLink: 'TrackedLink',
  TwoWayMessage: 'TwoWayMessage'
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]



export interface TypeMapCb<ClientOptions = {}> extends runtime.Types.Utils.Fn<{extArgs: runtime.Types.Extensions.InternalArgs }, runtime.Types.Utils.Record<string, any>> {
  returns: TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
}

export type TypeMap<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
  globalOmitOptions: {
    omit: GlobalOmitOptions
  }
  meta: {
    modelProps: "channelAttribute" | "channelMessage" | "channel" | "connectionAttribute" | "connection" | "identifier" | "messageAttribute" | "messageContext" | "message" | "recipientAttribute" | "recipientMessage" | "recipient" | "tag" | "teamsUser" | "action" | "localeTemplate" | "template" | "tenant" | "topicPreference" | "topic" | "trackedLink" | "twoWayMessage"
    txIsolationLevel: TransactionIsolationLevel
  }
  model: {
    ChannelAttribute: {
      payload: Prisma.$ChannelAttributePayload<ExtArgs>
      fields: Prisma.ChannelAttributeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ChannelAttributeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ChannelAttributeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>
        }
        findFirst: {
          args: Prisma.ChannelAttributeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ChannelAttributeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>
        }
        findMany: {
          args: Prisma.ChannelAttributeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>[]
        }
        create: {
          args: Prisma.ChannelAttributeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>
        }
        createMany: {
          args: Prisma.ChannelAttributeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ChannelAttributeCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>[]
        }
        delete: {
          args: Prisma.ChannelAttributeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>
        }
        update: {
          args: Prisma.ChannelAttributeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>
        }
        deleteMany: {
          args: Prisma.ChannelAttributeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ChannelAttributeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ChannelAttributeUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>[]
        }
        upsert: {
          args: Prisma.ChannelAttributeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelAttributePayload>
        }
        aggregate: {
          args: Prisma.ChannelAttributeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateChannelAttribute>
        }
        groupBy: {
          args: Prisma.ChannelAttributeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ChannelAttributeGroupByOutputType>[]
        }
        count: {
          args: Prisma.ChannelAttributeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ChannelAttributeCountAggregateOutputType> | number
        }
      }
    }
    ChannelMessage: {
      payload: Prisma.$ChannelMessagePayload<ExtArgs>
      fields: Prisma.ChannelMessageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ChannelMessageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ChannelMessageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>
        }
        findFirst: {
          args: Prisma.ChannelMessageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ChannelMessageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>
        }
        findMany: {
          args: Prisma.ChannelMessageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>[]
        }
        create: {
          args: Prisma.ChannelMessageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>
        }
        createMany: {
          args: Prisma.ChannelMessageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ChannelMessageCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>[]
        }
        delete: {
          args: Prisma.ChannelMessageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>
        }
        update: {
          args: Prisma.ChannelMessageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>
        }
        deleteMany: {
          args: Prisma.ChannelMessageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ChannelMessageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ChannelMessageUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>[]
        }
        upsert: {
          args: Prisma.ChannelMessageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelMessagePayload>
        }
        aggregate: {
          args: Prisma.ChannelMessageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateChannelMessage>
        }
        groupBy: {
          args: Prisma.ChannelMessageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ChannelMessageGroupByOutputType>[]
        }
        count: {
          args: Prisma.ChannelMessageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ChannelMessageCountAggregateOutputType> | number
        }
      }
    }
    Channel: {
      payload: Prisma.$ChannelPayload<ExtArgs>
      fields: Prisma.ChannelFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ChannelFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ChannelFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>
        }
        findFirst: {
          args: Prisma.ChannelFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ChannelFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>
        }
        findMany: {
          args: Prisma.ChannelFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>[]
        }
        create: {
          args: Prisma.ChannelCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>
        }
        createMany: {
          args: Prisma.ChannelCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ChannelCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>[]
        }
        delete: {
          args: Prisma.ChannelDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>
        }
        update: {
          args: Prisma.ChannelUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>
        }
        deleteMany: {
          args: Prisma.ChannelDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ChannelUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ChannelUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>[]
        }
        upsert: {
          args: Prisma.ChannelUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ChannelPayload>
        }
        aggregate: {
          args: Prisma.ChannelAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateChannel>
        }
        groupBy: {
          args: Prisma.ChannelGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ChannelGroupByOutputType>[]
        }
        count: {
          args: Prisma.ChannelCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ChannelCountAggregateOutputType> | number
        }
      }
    }
    ConnectionAttribute: {
      payload: Prisma.$ConnectionAttributePayload<ExtArgs>
      fields: Prisma.ConnectionAttributeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ConnectionAttributeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ConnectionAttributeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>
        }
        findFirst: {
          args: Prisma.ConnectionAttributeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ConnectionAttributeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>
        }
        findMany: {
          args: Prisma.ConnectionAttributeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>[]
        }
        create: {
          args: Prisma.ConnectionAttributeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>
        }
        createMany: {
          args: Prisma.ConnectionAttributeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ConnectionAttributeCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>[]
        }
        delete: {
          args: Prisma.ConnectionAttributeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>
        }
        update: {
          args: Prisma.ConnectionAttributeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>
        }
        deleteMany: {
          args: Prisma.ConnectionAttributeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ConnectionAttributeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ConnectionAttributeUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>[]
        }
        upsert: {
          args: Prisma.ConnectionAttributeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionAttributePayload>
        }
        aggregate: {
          args: Prisma.ConnectionAttributeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateConnectionAttribute>
        }
        groupBy: {
          args: Prisma.ConnectionAttributeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ConnectionAttributeGroupByOutputType>[]
        }
        count: {
          args: Prisma.ConnectionAttributeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ConnectionAttributeCountAggregateOutputType> | number
        }
      }
    }
    Connection: {
      payload: Prisma.$ConnectionPayload<ExtArgs>
      fields: Prisma.ConnectionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ConnectionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ConnectionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        findFirst: {
          args: Prisma.ConnectionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ConnectionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        findMany: {
          args: Prisma.ConnectionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>[]
        }
        create: {
          args: Prisma.ConnectionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        createMany: {
          args: Prisma.ConnectionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ConnectionCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>[]
        }
        delete: {
          args: Prisma.ConnectionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        update: {
          args: Prisma.ConnectionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        deleteMany: {
          args: Prisma.ConnectionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ConnectionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ConnectionUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>[]
        }
        upsert: {
          args: Prisma.ConnectionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        aggregate: {
          args: Prisma.ConnectionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateConnection>
        }
        groupBy: {
          args: Prisma.ConnectionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ConnectionGroupByOutputType>[]
        }
        count: {
          args: Prisma.ConnectionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ConnectionCountAggregateOutputType> | number
        }
      }
    }
    Identifier: {
      payload: Prisma.$IdentifierPayload<ExtArgs>
      fields: Prisma.IdentifierFieldRefs
      operations: {
        findUnique: {
          args: Prisma.IdentifierFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.IdentifierFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>
        }
        findFirst: {
          args: Prisma.IdentifierFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.IdentifierFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>
        }
        findMany: {
          args: Prisma.IdentifierFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>[]
        }
        create: {
          args: Prisma.IdentifierCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>
        }
        createMany: {
          args: Prisma.IdentifierCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.IdentifierCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>[]
        }
        delete: {
          args: Prisma.IdentifierDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>
        }
        update: {
          args: Prisma.IdentifierUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>
        }
        deleteMany: {
          args: Prisma.IdentifierDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.IdentifierUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.IdentifierUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>[]
        }
        upsert: {
          args: Prisma.IdentifierUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$IdentifierPayload>
        }
        aggregate: {
          args: Prisma.IdentifierAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateIdentifier>
        }
        groupBy: {
          args: Prisma.IdentifierGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.IdentifierGroupByOutputType>[]
        }
        count: {
          args: Prisma.IdentifierCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.IdentifierCountAggregateOutputType> | number
        }
      }
    }
    MessageAttribute: {
      payload: Prisma.$MessageAttributePayload<ExtArgs>
      fields: Prisma.MessageAttributeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.MessageAttributeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.MessageAttributeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>
        }
        findFirst: {
          args: Prisma.MessageAttributeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.MessageAttributeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>
        }
        findMany: {
          args: Prisma.MessageAttributeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>[]
        }
        create: {
          args: Prisma.MessageAttributeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>
        }
        createMany: {
          args: Prisma.MessageAttributeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.MessageAttributeCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>[]
        }
        delete: {
          args: Prisma.MessageAttributeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>
        }
        update: {
          args: Prisma.MessageAttributeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>
        }
        deleteMany: {
          args: Prisma.MessageAttributeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.MessageAttributeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.MessageAttributeUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>[]
        }
        upsert: {
          args: Prisma.MessageAttributeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageAttributePayload>
        }
        aggregate: {
          args: Prisma.MessageAttributeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateMessageAttribute>
        }
        groupBy: {
          args: Prisma.MessageAttributeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageAttributeGroupByOutputType>[]
        }
        count: {
          args: Prisma.MessageAttributeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageAttributeCountAggregateOutputType> | number
        }
      }
    }
    MessageContext: {
      payload: Prisma.$MessageContextPayload<ExtArgs>
      fields: Prisma.MessageContextFieldRefs
      operations: {
        findUnique: {
          args: Prisma.MessageContextFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.MessageContextFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>
        }
        findFirst: {
          args: Prisma.MessageContextFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.MessageContextFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>
        }
        findMany: {
          args: Prisma.MessageContextFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>[]
        }
        create: {
          args: Prisma.MessageContextCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>
        }
        createMany: {
          args: Prisma.MessageContextCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.MessageContextCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>[]
        }
        delete: {
          args: Prisma.MessageContextDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>
        }
        update: {
          args: Prisma.MessageContextUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>
        }
        deleteMany: {
          args: Prisma.MessageContextDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.MessageContextUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.MessageContextUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>[]
        }
        upsert: {
          args: Prisma.MessageContextUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessageContextPayload>
        }
        aggregate: {
          args: Prisma.MessageContextAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateMessageContext>
        }
        groupBy: {
          args: Prisma.MessageContextGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageContextGroupByOutputType>[]
        }
        count: {
          args: Prisma.MessageContextCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageContextCountAggregateOutputType> | number
        }
      }
    }
    Message: {
      payload: Prisma.$MessagePayload<ExtArgs>
      fields: Prisma.MessageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.MessageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.MessageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        findFirst: {
          args: Prisma.MessageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.MessageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        findMany: {
          args: Prisma.MessageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>[]
        }
        create: {
          args: Prisma.MessageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        createMany: {
          args: Prisma.MessageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.MessageCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>[]
        }
        delete: {
          args: Prisma.MessageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        update: {
          args: Prisma.MessageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        deleteMany: {
          args: Prisma.MessageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.MessageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.MessageUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>[]
        }
        upsert: {
          args: Prisma.MessageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        aggregate: {
          args: Prisma.MessageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateMessage>
        }
        groupBy: {
          args: Prisma.MessageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageGroupByOutputType>[]
        }
        count: {
          args: Prisma.MessageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageCountAggregateOutputType> | number
        }
      }
    }
    RecipientAttribute: {
      payload: Prisma.$RecipientAttributePayload<ExtArgs>
      fields: Prisma.RecipientAttributeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.RecipientAttributeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.RecipientAttributeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>
        }
        findFirst: {
          args: Prisma.RecipientAttributeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.RecipientAttributeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>
        }
        findMany: {
          args: Prisma.RecipientAttributeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>[]
        }
        create: {
          args: Prisma.RecipientAttributeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>
        }
        createMany: {
          args: Prisma.RecipientAttributeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.RecipientAttributeCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>[]
        }
        delete: {
          args: Prisma.RecipientAttributeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>
        }
        update: {
          args: Prisma.RecipientAttributeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>
        }
        deleteMany: {
          args: Prisma.RecipientAttributeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.RecipientAttributeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.RecipientAttributeUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>[]
        }
        upsert: {
          args: Prisma.RecipientAttributeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientAttributePayload>
        }
        aggregate: {
          args: Prisma.RecipientAttributeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRecipientAttribute>
        }
        groupBy: {
          args: Prisma.RecipientAttributeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RecipientAttributeGroupByOutputType>[]
        }
        count: {
          args: Prisma.RecipientAttributeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RecipientAttributeCountAggregateOutputType> | number
        }
      }
    }
    RecipientMessage: {
      payload: Prisma.$RecipientMessagePayload<ExtArgs>
      fields: Prisma.RecipientMessageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.RecipientMessageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.RecipientMessageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>
        }
        findFirst: {
          args: Prisma.RecipientMessageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.RecipientMessageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>
        }
        findMany: {
          args: Prisma.RecipientMessageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>[]
        }
        create: {
          args: Prisma.RecipientMessageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>
        }
        createMany: {
          args: Prisma.RecipientMessageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.RecipientMessageCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>[]
        }
        delete: {
          args: Prisma.RecipientMessageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>
        }
        update: {
          args: Prisma.RecipientMessageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>
        }
        deleteMany: {
          args: Prisma.RecipientMessageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.RecipientMessageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.RecipientMessageUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>[]
        }
        upsert: {
          args: Prisma.RecipientMessageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientMessagePayload>
        }
        aggregate: {
          args: Prisma.RecipientMessageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRecipientMessage>
        }
        groupBy: {
          args: Prisma.RecipientMessageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RecipientMessageGroupByOutputType>[]
        }
        count: {
          args: Prisma.RecipientMessageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RecipientMessageCountAggregateOutputType> | number
        }
      }
    }
    Recipient: {
      payload: Prisma.$RecipientPayload<ExtArgs>
      fields: Prisma.RecipientFieldRefs
      operations: {
        findUnique: {
          args: Prisma.RecipientFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.RecipientFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>
        }
        findFirst: {
          args: Prisma.RecipientFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.RecipientFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>
        }
        findMany: {
          args: Prisma.RecipientFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>[]
        }
        create: {
          args: Prisma.RecipientCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>
        }
        createMany: {
          args: Prisma.RecipientCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.RecipientCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>[]
        }
        delete: {
          args: Prisma.RecipientDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>
        }
        update: {
          args: Prisma.RecipientUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>
        }
        deleteMany: {
          args: Prisma.RecipientDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.RecipientUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.RecipientUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>[]
        }
        upsert: {
          args: Prisma.RecipientUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RecipientPayload>
        }
        aggregate: {
          args: Prisma.RecipientAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRecipient>
        }
        groupBy: {
          args: Prisma.RecipientGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RecipientGroupByOutputType>[]
        }
        count: {
          args: Prisma.RecipientCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RecipientCountAggregateOutputType> | number
        }
      }
    }
    Tag: {
      payload: Prisma.$TagPayload<ExtArgs>
      fields: Prisma.TagFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TagFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TagFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>
        }
        findFirst: {
          args: Prisma.TagFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TagFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>
        }
        findMany: {
          args: Prisma.TagFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>[]
        }
        create: {
          args: Prisma.TagCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>
        }
        createMany: {
          args: Prisma.TagCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TagCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>[]
        }
        delete: {
          args: Prisma.TagDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>
        }
        update: {
          args: Prisma.TagUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>
        }
        deleteMany: {
          args: Prisma.TagDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TagUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TagUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>[]
        }
        upsert: {
          args: Prisma.TagUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TagPayload>
        }
        aggregate: {
          args: Prisma.TagAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTag>
        }
        groupBy: {
          args: Prisma.TagGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TagGroupByOutputType>[]
        }
        count: {
          args: Prisma.TagCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TagCountAggregateOutputType> | number
        }
      }
    }
    TeamsUser: {
      payload: Prisma.$TeamsUserPayload<ExtArgs>
      fields: Prisma.TeamsUserFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TeamsUserFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TeamsUserFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>
        }
        findFirst: {
          args: Prisma.TeamsUserFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TeamsUserFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>
        }
        findMany: {
          args: Prisma.TeamsUserFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>[]
        }
        create: {
          args: Prisma.TeamsUserCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>
        }
        createMany: {
          args: Prisma.TeamsUserCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TeamsUserCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>[]
        }
        delete: {
          args: Prisma.TeamsUserDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>
        }
        update: {
          args: Prisma.TeamsUserUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>
        }
        deleteMany: {
          args: Prisma.TeamsUserDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TeamsUserUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TeamsUserUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>[]
        }
        upsert: {
          args: Prisma.TeamsUserUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeamsUserPayload>
        }
        aggregate: {
          args: Prisma.TeamsUserAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTeamsUser>
        }
        groupBy: {
          args: Prisma.TeamsUserGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TeamsUserGroupByOutputType>[]
        }
        count: {
          args: Prisma.TeamsUserCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TeamsUserCountAggregateOutputType> | number
        }
      }
    }
    Action: {
      payload: Prisma.$ActionPayload<ExtArgs>
      fields: Prisma.ActionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ActionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ActionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>
        }
        findFirst: {
          args: Prisma.ActionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ActionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>
        }
        findMany: {
          args: Prisma.ActionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>[]
        }
        create: {
          args: Prisma.ActionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>
        }
        createMany: {
          args: Prisma.ActionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ActionCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>[]
        }
        delete: {
          args: Prisma.ActionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>
        }
        update: {
          args: Prisma.ActionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>
        }
        deleteMany: {
          args: Prisma.ActionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ActionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ActionUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>[]
        }
        upsert: {
          args: Prisma.ActionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActionPayload>
        }
        aggregate: {
          args: Prisma.ActionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAction>
        }
        groupBy: {
          args: Prisma.ActionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ActionGroupByOutputType>[]
        }
        count: {
          args: Prisma.ActionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ActionCountAggregateOutputType> | number
        }
      }
    }
    LocaleTemplate: {
      payload: Prisma.$LocaleTemplatePayload<ExtArgs>
      fields: Prisma.LocaleTemplateFieldRefs
      operations: {
        findUnique: {
          args: Prisma.LocaleTemplateFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.LocaleTemplateFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>
        }
        findFirst: {
          args: Prisma.LocaleTemplateFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.LocaleTemplateFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>
        }
        findMany: {
          args: Prisma.LocaleTemplateFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>[]
        }
        create: {
          args: Prisma.LocaleTemplateCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>
        }
        createMany: {
          args: Prisma.LocaleTemplateCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.LocaleTemplateCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>[]
        }
        delete: {
          args: Prisma.LocaleTemplateDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>
        }
        update: {
          args: Prisma.LocaleTemplateUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>
        }
        deleteMany: {
          args: Prisma.LocaleTemplateDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.LocaleTemplateUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.LocaleTemplateUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>[]
        }
        upsert: {
          args: Prisma.LocaleTemplateUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocaleTemplatePayload>
        }
        aggregate: {
          args: Prisma.LocaleTemplateAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateLocaleTemplate>
        }
        groupBy: {
          args: Prisma.LocaleTemplateGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LocaleTemplateGroupByOutputType>[]
        }
        count: {
          args: Prisma.LocaleTemplateCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LocaleTemplateCountAggregateOutputType> | number
        }
      }
    }
    Template: {
      payload: Prisma.$TemplatePayload<ExtArgs>
      fields: Prisma.TemplateFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TemplateFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TemplateFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>
        }
        findFirst: {
          args: Prisma.TemplateFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TemplateFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>
        }
        findMany: {
          args: Prisma.TemplateFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>[]
        }
        create: {
          args: Prisma.TemplateCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>
        }
        createMany: {
          args: Prisma.TemplateCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TemplateCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>[]
        }
        delete: {
          args: Prisma.TemplateDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>
        }
        update: {
          args: Prisma.TemplateUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>
        }
        deleteMany: {
          args: Prisma.TemplateDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TemplateUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TemplateUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>[]
        }
        upsert: {
          args: Prisma.TemplateUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TemplatePayload>
        }
        aggregate: {
          args: Prisma.TemplateAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTemplate>
        }
        groupBy: {
          args: Prisma.TemplateGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TemplateGroupByOutputType>[]
        }
        count: {
          args: Prisma.TemplateCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TemplateCountAggregateOutputType> | number
        }
      }
    }
    Tenant: {
      payload: Prisma.$TenantPayload<ExtArgs>
      fields: Prisma.TenantFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TenantFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TenantFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>
        }
        findFirst: {
          args: Prisma.TenantFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TenantFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>
        }
        findMany: {
          args: Prisma.TenantFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>[]
        }
        create: {
          args: Prisma.TenantCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>
        }
        createMany: {
          args: Prisma.TenantCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TenantCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>[]
        }
        delete: {
          args: Prisma.TenantDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>
        }
        update: {
          args: Prisma.TenantUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>
        }
        deleteMany: {
          args: Prisma.TenantDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TenantUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TenantUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>[]
        }
        upsert: {
          args: Prisma.TenantUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TenantPayload>
        }
        aggregate: {
          args: Prisma.TenantAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTenant>
        }
        groupBy: {
          args: Prisma.TenantGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TenantGroupByOutputType>[]
        }
        count: {
          args: Prisma.TenantCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TenantCountAggregateOutputType> | number
        }
      }
    }
    TopicPreference: {
      payload: Prisma.$TopicPreferencePayload<ExtArgs>
      fields: Prisma.TopicPreferenceFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TopicPreferenceFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TopicPreferenceFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>
        }
        findFirst: {
          args: Prisma.TopicPreferenceFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TopicPreferenceFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>
        }
        findMany: {
          args: Prisma.TopicPreferenceFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>[]
        }
        create: {
          args: Prisma.TopicPreferenceCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>
        }
        createMany: {
          args: Prisma.TopicPreferenceCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TopicPreferenceCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>[]
        }
        delete: {
          args: Prisma.TopicPreferenceDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>
        }
        update: {
          args: Prisma.TopicPreferenceUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>
        }
        deleteMany: {
          args: Prisma.TopicPreferenceDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TopicPreferenceUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TopicPreferenceUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>[]
        }
        upsert: {
          args: Prisma.TopicPreferenceUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPreferencePayload>
        }
        aggregate: {
          args: Prisma.TopicPreferenceAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTopicPreference>
        }
        groupBy: {
          args: Prisma.TopicPreferenceGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TopicPreferenceGroupByOutputType>[]
        }
        count: {
          args: Prisma.TopicPreferenceCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TopicPreferenceCountAggregateOutputType> | number
        }
      }
    }
    Topic: {
      payload: Prisma.$TopicPayload<ExtArgs>
      fields: Prisma.TopicFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TopicFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TopicFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>
        }
        findFirst: {
          args: Prisma.TopicFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TopicFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>
        }
        findMany: {
          args: Prisma.TopicFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>[]
        }
        create: {
          args: Prisma.TopicCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>
        }
        createMany: {
          args: Prisma.TopicCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TopicCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>[]
        }
        delete: {
          args: Prisma.TopicDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>
        }
        update: {
          args: Prisma.TopicUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>
        }
        deleteMany: {
          args: Prisma.TopicDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TopicUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TopicUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>[]
        }
        upsert: {
          args: Prisma.TopicUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TopicPayload>
        }
        aggregate: {
          args: Prisma.TopicAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTopic>
        }
        groupBy: {
          args: Prisma.TopicGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TopicGroupByOutputType>[]
        }
        count: {
          args: Prisma.TopicCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TopicCountAggregateOutputType> | number
        }
      }
    }
    TrackedLink: {
      payload: Prisma.$TrackedLinkPayload<ExtArgs>
      fields: Prisma.TrackedLinkFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TrackedLinkFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TrackedLinkFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>
        }
        findFirst: {
          args: Prisma.TrackedLinkFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TrackedLinkFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>
        }
        findMany: {
          args: Prisma.TrackedLinkFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>[]
        }
        create: {
          args: Prisma.TrackedLinkCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>
        }
        createMany: {
          args: Prisma.TrackedLinkCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TrackedLinkCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>[]
        }
        delete: {
          args: Prisma.TrackedLinkDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>
        }
        update: {
          args: Prisma.TrackedLinkUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>
        }
        deleteMany: {
          args: Prisma.TrackedLinkDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TrackedLinkUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TrackedLinkUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>[]
        }
        upsert: {
          args: Prisma.TrackedLinkUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TrackedLinkPayload>
        }
        aggregate: {
          args: Prisma.TrackedLinkAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTrackedLink>
        }
        groupBy: {
          args: Prisma.TrackedLinkGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TrackedLinkGroupByOutputType>[]
        }
        count: {
          args: Prisma.TrackedLinkCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TrackedLinkCountAggregateOutputType> | number
        }
      }
    }
    TwoWayMessage: {
      payload: Prisma.$TwoWayMessagePayload<ExtArgs>
      fields: Prisma.TwoWayMessageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TwoWayMessageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TwoWayMessageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>
        }
        findFirst: {
          args: Prisma.TwoWayMessageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TwoWayMessageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>
        }
        findMany: {
          args: Prisma.TwoWayMessageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>[]
        }
        create: {
          args: Prisma.TwoWayMessageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>
        }
        createMany: {
          args: Prisma.TwoWayMessageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TwoWayMessageCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>[]
        }
        delete: {
          args: Prisma.TwoWayMessageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>
        }
        update: {
          args: Prisma.TwoWayMessageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>
        }
        deleteMany: {
          args: Prisma.TwoWayMessageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TwoWayMessageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TwoWayMessageUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>[]
        }
        upsert: {
          args: Prisma.TwoWayMessageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoWayMessagePayload>
        }
        aggregate: {
          args: Prisma.TwoWayMessageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTwoWayMessage>
        }
        groupBy: {
          args: Prisma.TwoWayMessageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TwoWayMessageGroupByOutputType>[]
        }
        count: {
          args: Prisma.TwoWayMessageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TwoWayMessageCountAggregateOutputType> | number
        }
      }
    }
  }
} & {
  other: {
    payload: any
    operations: {
      $executeRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $executeRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
      $queryRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $queryRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
      $queryRawTyped: {
        args: runtime.UnknownTypedSql,
        result: JsonObject
      }
    }
  }
}

/**
 * Enums
 */

export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
} as const)

export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


export const ChannelAttributeScalarFieldEnum = {
  id: 'id',
  channelId: 'channelId',
  name: 'name',
  value: 'value',
  secure: 'secure'
} as const

export type ChannelAttributeScalarFieldEnum = (typeof ChannelAttributeScalarFieldEnum)[keyof typeof ChannelAttributeScalarFieldEnum]


export const RelationLoadStrategy = {
  query: 'query',
  join: 'join'
} as const

export type RelationLoadStrategy = (typeof RelationLoadStrategy)[keyof typeof RelationLoadStrategy]


export const ChannelMessageScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  status: 'status',
  error: 'error',
  channelId: 'channelId',
  endpointIdentifier: 'endpointIdentifier',
  title: 'title',
  text: 'text',
  transactionId: 'transactionId',
  isRecipientMessage: 'isRecipientMessage',
  sentAt: 'sentAt',
  aborted: 'aborted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type ChannelMessageScalarFieldEnum = (typeof ChannelMessageScalarFieldEnum)[keyof typeof ChannelMessageScalarFieldEnum]


export const ChannelScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  externalId: 'externalId',
  description: 'description',
  service: 'service',
  channelType: 'channelType',
  provider: 'provider',
  displayName: 'displayName',
  enabled: 'enabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type ChannelScalarFieldEnum = (typeof ChannelScalarFieldEnum)[keyof typeof ChannelScalarFieldEnum]


export const ConnectionAttributeScalarFieldEnum = {
  id: 'id',
  connectionId: 'connectionId',
  name: 'name',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type ConnectionAttributeScalarFieldEnum = (typeof ConnectionAttributeScalarFieldEnum)[keyof typeof ConnectionAttributeScalarFieldEnum]


export const ConnectionScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  service: 'service',
  enabled: 'enabled',
  showInPreferences: 'showInPreferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  channelId: 'channelId'
} as const

export type ConnectionScalarFieldEnum = (typeof ConnectionScalarFieldEnum)[keyof typeof ConnectionScalarFieldEnum]


export const IdentifierScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  type: 'type',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type IdentifierScalarFieldEnum = (typeof IdentifierScalarFieldEnum)[keyof typeof IdentifierScalarFieldEnum]


export const MessageAttributeScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  name: 'name',
  value: 'value'
} as const

export type MessageAttributeScalarFieldEnum = (typeof MessageAttributeScalarFieldEnum)[keyof typeof MessageAttributeScalarFieldEnum]


export const MessageContextScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  name: 'name',
  value: 'value'
} as const

export type MessageContextScalarFieldEnum = (typeof MessageContextScalarFieldEnum)[keyof typeof MessageContextScalarFieldEnum]


export const MessageScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  recipients: 'recipients',
  tags: 'tags',
  topic: 'topic',
  importance: 'importance',
  isNotificationMessage: 'isNotificationMessage',
  conversationId: 'conversationId',
  category: 'category',
  sendAt: 'sendAt',
  status: 'status',
  error: 'error',
  title: 'title',
  message: 'message',
  shortMessage: 'shortMessage',
  plainText: 'plainText',
  apiUser: 'apiUser',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type MessageScalarFieldEnum = (typeof MessageScalarFieldEnum)[keyof typeof MessageScalarFieldEnum]


export const RecipientAttributeScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  name: 'name',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type RecipientAttributeScalarFieldEnum = (typeof RecipientAttributeScalarFieldEnum)[keyof typeof RecipientAttributeScalarFieldEnum]


export const RecipientMessageScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  recipientId: 'recipientId',
  acknowledged: 'acknowledged',
  acknowledgedAt: 'acknowledgedAt',
  actionTaken: 'actionTaken',
  actionTakenAt: 'actionTakenAt',
  idUsed: 'idUsed',
  status: 'status',
  error: 'error',
  relevant: 'relevant'
} as const

export type RecipientMessageScalarFieldEnum = (typeof RecipientMessageScalarFieldEnum)[keyof typeof RecipientMessageScalarFieldEnum]


export const RecipientScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  enabled: 'enabled',
  timezone: 'timezone',
  locale: 'locale',
  digest: 'digest',
  defaultService: 'defaultService',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type RecipientScalarFieldEnum = (typeof RecipientScalarFieldEnum)[keyof typeof RecipientScalarFieldEnum]


export const TagScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  externalId: 'externalId',
  displayName: 'displayName',
  description: 'description',
  enabled: 'enabled',
  selectable: 'selectable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type TagScalarFieldEnum = (typeof TagScalarFieldEnum)[keyof typeof TagScalarFieldEnum]


export const TeamsUserScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  userPrincipalName: 'userPrincipalName',
  email: 'email',
  conversationId: 'conversationId',
  msTenantId: 'msTenantId'
} as const

export type TeamsUserScalarFieldEnum = (typeof TeamsUserScalarFieldEnum)[keyof typeof TeamsUserScalarFieldEnum]


export const ActionScalarFieldEnum = {
  id: 'id',
  label: 'label',
  url: 'url',
  localeTemplateId: 'localeTemplateId'
} as const

export type ActionScalarFieldEnum = (typeof ActionScalarFieldEnum)[keyof typeof ActionScalarFieldEnum]


export const LocaleTemplateScalarFieldEnum = {
  id: 'id',
  locale: 'locale',
  templateId: 'templateId',
  title: 'title',
  fullMessage: 'fullMessage',
  shortMessage: 'shortMessage',
  plainText: 'plainText',
  variables: 'variables',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type LocaleTemplateScalarFieldEnum = (typeof LocaleTemplateScalarFieldEnum)[keyof typeof LocaleTemplateScalarFieldEnum]


export const TemplateScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  externalId: 'externalId',
  displayName: 'displayName',
  description: 'description',
  isNotificationTemplate: 'isNotificationTemplate'
} as const

export type TemplateScalarFieldEnum = (typeof TemplateScalarFieldEnum)[keyof typeof TemplateScalarFieldEnum]


export const TenantScalarFieldEnum = {
  id: 'id',
  name: 'name',
  enabled: 'enabled',
  language: 'language',
  storeMessageForDays: 'storeMessageForDays',
  archive: 'archive',
  encryptionKeyId: 'encryptionKeyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type TenantScalarFieldEnum = (typeof TenantScalarFieldEnum)[keyof typeof TenantScalarFieldEnum]


export const TopicPreferenceScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  topicId: 'topicId',
  optedIn: 'optedIn'
} as const

export type TopicPreferenceScalarFieldEnum = (typeof TopicPreferenceScalarFieldEnum)[keyof typeof TopicPreferenceScalarFieldEnum]


export const TopicScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  externalId: 'externalId',
  displayName: 'displayName',
  description: 'description',
  enabled: 'enabled',
  defaultCategory: 'defaultCategory',
  channelType: 'channelType',
  defaultService: 'defaultService',
  visibleInPreferences: 'visibleInPreferences',
  orderSequence: 'orderSequence',
  userMustOptIn: 'userMustOptIn',
  channelAlwaysOn: 'channelAlwaysOn',
  userPreferenceRoles: 'userPreferenceRoles',
  roles: 'roles',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type TopicScalarFieldEnum = (typeof TopicScalarFieldEnum)[keyof typeof TopicScalarFieldEnum]


export const TrackedLinkScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  hash: 'hash',
  url: 'url',
  clickCount: 'clickCount',
  firstClick: 'firstClick',
  lastClick: 'lastClick',
  messageId: 'messageId',
  createdAt: 'createdAt'
} as const

export type TrackedLinkScalarFieldEnum = (typeof TrackedLinkScalarFieldEnum)[keyof typeof TrackedLinkScalarFieldEnum]


export const TwoWayMessageScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  recipientServiceId: 'recipientServiceId',
  tenantServiceId: 'tenantServiceId',
  conversationId: 'conversationId',
  channel: 'channel',
  direction: 'direction',
  subject: 'subject',
  message: 'message',
  reaction: 'reaction',
  category: 'category',
  recipientId: 'recipientId',
  tenantMessageId: 'tenantMessageId',
  transactionId: 'transactionId',
  error: 'error',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type TwoWayMessageScalarFieldEnum = (typeof TwoWayMessageScalarFieldEnum)[keyof typeof TwoWayMessageScalarFieldEnum]


export const SortOrder = {
  asc: 'asc',
  desc: 'desc'
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


export const QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
} as const

export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


export const NullsOrder = {
  first: 'first',
  last: 'last'
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]



/**
 * Field references 
 */


/**
 * Reference to a field of type 'String'
 */
export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


/**
 * Reference to a field of type 'String[]'
 */
export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


/**
 * Reference to a field of type 'Boolean'
 */
export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


/**
 * Reference to a field of type 'Status'
 */
export type EnumStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Status'>
    


/**
 * Reference to a field of type 'Status[]'
 */
export type ListEnumStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Status[]'>
    


/**
 * Reference to a field of type 'DateTime'
 */
export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


/**
 * Reference to a field of type 'DateTime[]'
 */
export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


/**
 * Reference to a field of type 'Int'
 */
export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


/**
 * Reference to a field of type 'Int[]'
 */
export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


/**
 * Reference to a field of type 'Float'
 */
export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


/**
 * Reference to a field of type 'Float[]'
 */
export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    

/**
 * Batch Payload for updateMany & deleteMany & createMany
 */
export type BatchPayload = {
  count: number
}


export type Datasource = {
  url?: string
}
export type Datasources = {
  db?: Datasource
}

export const defineExtension = runtime.Extensions.defineExtension as unknown as runtime.Types.Extensions.ExtendsHook<"define", TypeMapCb, runtime.Types.Extensions.DefaultArgs>
export type DefaultPrismaClient = PrismaClient
export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
export interface PrismaClientOptions {
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasources?: Datasources
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasourceUrl?: string
  /**
   * @default "colorless"
   */
  errorFormat?: ErrorFormat
  /**
   * @example
   * ```
   * // Defaults to stdout
   * log: ['query', 'info', 'warn', 'error']
   * 
   * // Emit as events
   * log: [
   *   { emit: 'stdout', level: 'query' },
   *   { emit: 'stdout', level: 'info' },
   *   { emit: 'stdout', level: 'warn' }
   *   { emit: 'stdout', level: 'error' }
   * ]
   * ```
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
   */
  log?: (LogLevel | LogDefinition)[]
  /**
   * The default values for transactionOptions
   * maxWait ?= 2000
   * timeout ?= 5000
   */
  transactionOptions?: {
    maxWait?: number
    timeout?: number
    isolationLevel?: TransactionIsolationLevel
  }
  /**
   * Instance of a Driver Adapter, e.g., like one provided by `@prisma/adapter-planetscale`
   */
  adapter?: runtime.SqlDriverAdapterFactory | null
  /**
   * Global configuration for omitting model fields by default.
   * 
   * @example
   * ```
   * const prisma = new PrismaClient({
   *   omit: {
   *     user: {
   *       password: true
   *     }
   *   }
   * })
   * ```
   */
  omit?: GlobalOmitConfig
}
export type GlobalOmitConfig = {
  channelAttribute?: Prisma.ChannelAttributeOmit
  channelMessage?: Prisma.ChannelMessageOmit
  channel?: Prisma.ChannelOmit
  connectionAttribute?: Prisma.ConnectionAttributeOmit
  connection?: Prisma.ConnectionOmit
  identifier?: Prisma.IdentifierOmit
  messageAttribute?: Prisma.MessageAttributeOmit
  messageContext?: Prisma.MessageContextOmit
  message?: Prisma.MessageOmit
  recipientAttribute?: Prisma.RecipientAttributeOmit
  recipientMessage?: Prisma.RecipientMessageOmit
  recipient?: Prisma.RecipientOmit
  tag?: Prisma.TagOmit
  teamsUser?: Prisma.TeamsUserOmit
  action?: Prisma.ActionOmit
  localeTemplate?: Prisma.LocaleTemplateOmit
  template?: Prisma.TemplateOmit
  tenant?: Prisma.TenantOmit
  topicPreference?: Prisma.TopicPreferenceOmit
  topic?: Prisma.TopicOmit
  trackedLink?: Prisma.TrackedLinkOmit
  twoWayMessage?: Prisma.TwoWayMessageOmit
}

/* Types for Logging */
export type LogLevel = 'info' | 'query' | 'warn' | 'error'
export type LogDefinition = {
  level: LogLevel
  emit: 'stdout' | 'event'
}

export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
  GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
  : never

export type QueryEvent = {
  timestamp: Date
  query: string
  params: string
  duration: number
  target: string
}

export type LogEvent = {
  timestamp: Date
  message: string
  target: string
}
/* End Types for Logging */


export type PrismaAction =
  | 'findUnique'
  | 'findUniqueOrThrow'
  | 'findMany'
  | 'findFirst'
  | 'findFirstOrThrow'
  | 'create'
  | 'createMany'
  | 'createManyAndReturn'
  | 'update'
  | 'updateMany'
  | 'updateManyAndReturn'
  | 'upsert'
  | 'delete'
  | 'deleteMany'
  | 'executeRaw'
  | 'queryRaw'
  | 'aggregate'
  | 'count'
  | 'runCommandRaw'
  | 'findRaw' 
  | 'groupBy'

/**
 * These options are being passed into the middleware as "params"
 */
export type MiddlewareParams = {
  model?: ModelName
  action: PrismaAction
  args: any
  dataPath: string[]
  runInTransaction: boolean
}

/**
 * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
 */
export type Middleware<T = any> = (
  params: MiddlewareParams,
  next: (params: MiddlewareParams) => runtime.Types.Utils.JsPromise<T>,
) => runtime.Types.Utils.JsPromise<T>

/**
 * `PrismaClient` proxy available in interactive transactions.
 */
export type TransactionClient = Omit<DefaultPrismaClient, runtime.ITXClientDenyList>

