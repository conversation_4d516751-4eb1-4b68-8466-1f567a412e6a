/**
 * Base64URL Encoding/Decoding Utilities
 *
 * This module provides functions for encoding/decoding data using the base64url format
 * as specified in RFC 4648. The base64url format is designed to be URL and filename safe
 * by using different characters than standard base64 encoding.
 *
 * Key differences from standard base64:
 * - '+' is replaced with '-'
 * - '/' is replaced with '_'
 * - Padding '=' characters are removed
 *
 * This format is commonly used in:
 * - JSON Web Tokens (JWT)
 * - URL parameters
 * - Filenames
 * - Any context where the output needs to be URL-safe
 */

/**
 * Converts a Buffer to a URL-safe base64 string
 *
 * @param buffer - The binary data to encode
 * @returns A URL-safe base64 encoded string without padding
 *
 * @example
 * const data = Buffer.from('Hello, world!');
 * const encoded = base64urlEncode(data);
 * // Result: "SGVsbG8sIHdvcmxkIQ"
 */
export const base64urlEncode = (buffer: Buffer): string => {
   return buffer.toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');
};

/**
 * Converts a base64url string back to a Buffer
 *
 * @param base64url - The base64url encoded string to decode
 * @returns The decoded binary data as a Buffer
 * @throws {Error} If the input string is not properly formatted
 *
 * @example
 * const decoded = base64urlDecode('SGVsbG8sIHdvcmxkIQ');
 * const text = decoded.toString('utf-8');
 * // Result: "Hello, world!"
 */
export const base64urlDecode = (base64url: string): Buffer => {
   // Convert from base64url to base64 by reversing the character substitutions
   base64url = base64url.replace(/-/g, '+').replace(/_/g, '/');

   // Add padding if necessary
   while (base64url.length % 4) {
      base64url += '=';
   }

   return Buffer.from(base64url, 'base64');
};
