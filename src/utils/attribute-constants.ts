export const AttributeConstants = {
   ACCEPT_URL: 'accept-url',
   ACCESS_TOKEN: 'access-token',
   API_KEY: 'api-key',
   AZURE_SMS_CONNECTION: 'azure-sms-connection',
   CANVAS: 'canvas',
   CANVAS_HOST: 'canvas-host',
   CANVAS_ID: 'canvas-id',
   CLIENT_ID: 'client-id',
   CLIENT_SECRET: 'client-secret',
   CONNECT: 'connect',
   COUNTRY_CODE: 'country-code',
   EMAIL: 'email',
   ESENDEX: 'esendex',
   ESENDEX_KEY: 'esendex-key',
   ESENDEX_NUMBER: 'esendex-number',
   HOSTNAME: 'hostname',
   MODO: 'modo',
   MODO_APPLICATION_ID: 'modo-application-id',
   MODO_AUTHORIZATION: 'modo-authorization',
   MODO_BANNER: 'modo-banner',
   MODO_CHANNEL: 'modo-channel',
   MODO_END_AT: 'modo-end-at',
   MODO_FILTER_KEY: 'modo-filter-key',
   MODO_GROUP_ATTRIBUTE: 'modo-group-attribute',
   MODO_ID: 'modo-id',
   MODO_PUSH: 'modo-push',
   MODO_START_AT: 'modo-start-at',
   MODO_STYLE: 'modo-style',
   MODO_TARGET: 'modo-target',
   MS_TEAMS: 'ms-teams',
   MS_TEAMS_WEBHOOK: 'ms-teams-webhook',
   NO_PROVIDER: 'no-provider',
   OPRID: 'oprid',
   PASSWORD: 'password',
   PHONE: 'phone',
   PORT: 'port',
   PROVIDER: 'provider',
   REFRESH_TOKEN: 'refresh-token',
   REQUEST_ID: 'request-id',
   SECURE: 'secure',
   SENDGRID: 'sendgrid',
   SENDGRID_KEY: 'sendgrid-key',
   SERVICE_ACCOUNT: 'service-account',
   SLACK: 'slack',
   SLACK_WEBHOOK: 'slack-webhook',
   SMS: 'sms',
   SMTP: 'smtp',
   SMTP_FROM: 'smtp-from',
   SMTP_HOST: 'smtp-host',
   SMTP_PASSWORD: 'smtp-password',
   SMTP_PORT: 'smtp-port',
   SMTP_SECURE: 'smtp-secure',
   SMTP_SUBJECT: 'smtp-subject',
   SMTP_TO: 'smtp-to',
   SMTP_USER: 'smtp-user',
   TEAMS_BOT_ID: 'teams-bot-id',
   TEAMS_BOT_PASSWORD: 'teams-bot-password',
   TEAMS_CONVERSATION_ID: 'conversation-id',
   TENANT_ID: 'tenant-id',
   TEST: 'test',
   TOKEN: 'token',
   TWILIO: 'twilio',
   TWILIO_NUMBER: 'twilio-number',
   TWILIO_SID: 'twilio-sid',
   TWILIO_TOKEN: 'twilio-token',
   UPN: 'upn',
   USERNAME: 'username',
   WEBHOOK_URL: 'webhook-url',
   WHATSAPP: 'whatsapp',
   X_MESSAGE_ID: 'x-message-id',
   X_SLACK_UNIQUE_ID: 'x-slack-unique-id'
};
