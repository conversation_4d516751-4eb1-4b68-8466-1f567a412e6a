/**
 * Prisma Client Multi-Tenant Management
 *
 * This module provides utilities for managing Prisma client instances in a multi-tenant environment.
 * It handles:
 * - Caching of Prisma client instances per tenant
 * - Fetching tenant-specific database connection strings from a secure vault
 * - Configuration of Prisma clients based on environment settings
 * - Debug and optimization features configuration
 */

import { Prisma, PrismaClient } from '@prisma/client';
import { withOptimize } from '@prisma/extension-optimize';

import { SecretVaultFactory } from '../secrets/index.js';

/**
 * Cache of Prisma client instances, keyed by tenant ID
 * This prevents creating multiple client instances for the same tenant
 */
const tenantMap = new Map<string, PrismaClient>();

/**
 * Shared secret vault instance for retrieving database connection strings
 * Uses caching (true) to improve performance
 */
const secrets = SecretVaultFactory.getSecretVault(true);

/**
 * Retrieves or creates a Prisma client instance for the specified tenant
 *
 * @param tenantId - The unique identifier of the tenant
 * @returns A configured Prisma client instance for the tenant
 * @throws Error if the database URL for the tenant cannot be retrieved
 */
export async function getPrismaClient(tenantId: string): Promise<PrismaClient> {
   // Check if a client for this tenant already exists in the cache
   const client = tenantMap.get(tenantId);

   if (client) {
      return client;
   }

   const url: string = await secrets.getSecret(`${tenantId}-db-url`);

   if (!url) {
      throw new Error(`Missing prisma url for tenant: ${tenantId}`);
   }

   // Configure Prisma client options
   const prismaOptions: Prisma.PrismaClientOptions = {
      datasourceUrl: url,
      log: [] // Default: no logging
   };

   // Enable detailed logging in debug mode
   if (process.env.PRISMA_DEBUG === 'true') {
      prismaOptions.log = ['query', 'info', 'warn', 'error'];
   }

   const prismaClient: PrismaClient = new PrismaClient(prismaOptions);

   // Apply Prisma Optimize extension if enabled and configured
   // This extension improves query performance through analysis
   if (process.env.PRISMA_OPTIMIZE === 'true' && process.env.PRISMA_OPTIMIZE_API_KEY) {
      prismaClient.$extends(
         withOptimize({ apiKey: process.env.PRISMA_OPTIMIZE_API_KEY })
      );
   }

   /* used in development only */
   if (process.env.PRISMA_DEBUG === 'true') {
      // Add query logging to help with debugging
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore - The Prisma types don't fully expose the query event interface
      prismaClient.$on('query', (e: Prisma.QueryEvent) => {
         // eslint-disable-next-line no-console
         console.log(`Params: ${e.params}`);
      });
   }

   // Cache the client for future use
   tenantMap.set(tenantId, prismaClient);

   return prismaClient;
}
