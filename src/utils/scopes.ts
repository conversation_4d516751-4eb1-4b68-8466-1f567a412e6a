export enum Scopes {
   ADMIN_READ = 'admin.read',
   ADMIN_WRITE = 'admin.write',
   CHANNEL_READ = 'channel.read',
   CHANNEL_WRITE = 'channel.write',
   LINK_READ = 'link.read',
   LINK_WRITE = 'link.write',
   MESSAGE_READ = 'message.read',
   MESSAGE_WRITE = 'message.write',
   MODO_READ = 'modo.read',
   MODO_WRITE = 'modo.write',
   RECIPIENT_READ = 'recipient.read',
   RECIPIENT_WRITE = 'recipient.write',
   TAG_READ = 'tag.read',
   TAG_WRITE = 'tag.write',
   TOPIC_READ = 'topic.read',
   TOPIC_WRITE = 'topic.write'
}
