import { AppConfigurationClient } from '@azure/app-configuration';
import { DefaultAzureCredential } from '@azure/identity';

const azureCredential = new DefaultAzureCredential();

export class AzureAppConfig {
   private static readonly _REFRESH_INTERVAL_ = 5 * 60 * 1000; // 5 minute;

   private _client: AppConfigurationClient;
   private _map: Map<string, [date: Date, value: string]>;

   constructor() {
      this._map = new Map<string, [date: Date, value: string]>();

      try {
         this._client = new AppConfigurationClient(process.env.CONFIGURATION_URL ?? '', azureCredential);
      } catch (error) {
         console.error(error);
         throw error;
      }
   }

   private async _getRemoteValue(key: string): Promise<string> {
      try {
         const setting = await this._client.getConfigurationSetting({
            key: `${process.env.CONFIGURATION_PREFIX ?? 'prefix-missing'}:${key}`
         });

         const value = setting.value ?? '';
         this._map.set(key, [new Date(), value]);

         return value;
      } catch (error) {
         console.error(error);

         return '';
      }
   }

   public async getValue(key: string): Promise<string> {
      if (this._map.has(key)) {
         const [date, value] = this._map.get(key)!;

         if (date.getTime() + AzureAppConfig._REFRESH_INTERVAL_ > new Date().getTime()) {
            return value;
         }
      }

      const remoteValue = await this._getRemoteValue(key);
      this._map.set(key, [new Date(), remoteValue]);

      return remoteValue;
   }
}
