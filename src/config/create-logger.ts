import { redactionPlugin } from '@loglayer/plugin-redaction';
import { LogLayerTransport } from '@loglayer/transport';
import { ConsolaTransport } from '@loglayer/transport-consola';
import { DataDogTransport } from '@loglayer/transport-datadog';
import { createConsola } from 'consola';
import { FastifyBaseLogger } from 'fastify';
import { LogLayer } from 'loglayer';
import { serializeError } from 'serialize-error';

const getTransport = (): LogLayerTransport => {
   if (process.env.LOG_TRANSPORT?.toLocaleLowerCase() === 'datadog') {
      return new DataDogTransport({
         level: 'trace',
         options: {
            ddClientConf: {
               authMethods: {
                  apiKeyAuth: process.env.DATADOG_API_KEY ?? ''
               }
            },
            ddServerConf: {
               site: process.env.DATADOG_SITE ?? ''
            },
            ddsource: process.env.DATADOG_SOURCE ?? '',
            ddtags: process.env.DATADOG_TAGS ?? '',
            service: process.env.DATADOG_SERVICE ?? ''
         }
      });
   }

   return new ConsolaTransport({
      logger: createConsola({
         level: 5
      })
   });
};

class LogLayerLogger implements FastifyBaseLogger {
   private _logger: LogLayer;

   constructor() {
      this._logger = new LogLayer({
         errorSerializer: serializeError,
         plugins: [
            redactionPlugin({
               paths: ['api_key', 'authorization', 'client_secret', 'password', 'token']
            })
         ],
         transport: getTransport()
      });
      this.level = 'trace';
   }

   level: string;

   fatal(obj: unknown, msg?: string) {
      const metadata = obj && typeof obj === 'object' ? obj : { value: obj };

      if (Object.hasOwn(metadata, 'error')) {
         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
         const error = Object.getOwnPropertyDescriptor(metadata, 'error')?.value;

         if (msg) {
            this._logger.withError(error).withMetadata(metadata).fatal(msg);
         } else {
            this._logger.withError(error).withMetadata(metadata);
         }
      }

      if (msg) {
         this._logger.withMetadata(metadata).fatal(msg);
      } else {
         this._logger.metadataOnly(metadata);
      }
   }

   error(obj: unknown, msg?: string) {
      const metadata = obj && typeof obj === 'object' ? obj : { value: obj };

      if (Object.hasOwn(metadata, 'error')) {
         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
         const error = Object.getOwnPropertyDescriptor(metadata, 'error')?.value;

         if (msg) {
            this._logger.withError(error).withMetadata(metadata).error(msg);
         } else {
            this._logger.withError(error).withMetadata(metadata);
         }
      }

      if (msg) {
         this._logger.withMetadata(metadata).error(msg);
      } else {
         this._logger.metadataOnly(metadata);
      }
   }

   warn(obj: unknown, msg?: string) {
      const metadata = obj && typeof obj === 'object' ? obj : { value: obj };

      if (msg) {
         this._logger.withMetadata(metadata).warn(msg);
      } else {
         this._logger.metadataOnly(metadata);
      }
   }

   info(obj: unknown, msg?: string) {
      const metadata = obj && typeof obj === 'object' ? obj : { value: obj };

      if (msg) {
         this._logger.withMetadata(metadata).info(msg);
      } else {
         this._logger.metadataOnly(metadata);
      }
   }

   debug(obj: unknown, msg?: string) {
      const metadata = obj && typeof obj === 'object' ? obj : { value: obj };

      if (msg) {
         this._logger.withMetadata(metadata).debug(msg);
      } else {
         this._logger.metadataOnly(metadata);
      }
   }

   trace(obj: unknown, msg?: string) {
      const metadata = obj && typeof obj === 'object' ? obj : { value: obj };

      if (msg) {
         this._logger.withMetadata(metadata).trace(msg);
      } else {
         this._logger.metadataOnly(metadata);
      }
   }

   silent() {}

   child(): FastifyBaseLogger {
      return new LogLayerLogger();
   }
}

export function createLogger(): FastifyBaseLogger {
   return new LogLayerLogger();
}
