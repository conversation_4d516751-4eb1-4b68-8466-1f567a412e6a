import { AppConfig } from './app-config.js';
import { AzureAppConfig } from './azure-config.js';
import { LocalAppConfig } from './local-config.js';

export type ConfigurationType = 'azure' | 'local';

export class AppConfigFactory {
   private static _appConfig: AppConfig | undefined;

   static clear(): void {
      this._appConfig = undefined;
   }

   static create(): AppConfig {
      if (this._appConfig) {
         return this._appConfig;
      }

      const type = (process.env.CONFIGURATION_TYPE ?? 'local').toLocaleLowerCase();

      switch (type) {
         case 'azure':
            this._appConfig = new AzureAppConfig();
            break;
         case 'local':
            this._appConfig = new LocalAppConfig();
            break;
         default:
            this._appConfig = new LocalAppConfig();
            break;
      }

      return this._appConfig;
   }
}
