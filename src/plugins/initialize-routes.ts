import { FastifyPluginCallbackTypebox } from '@fastify/type-provider-typebox';
import { FastifyPluginOptions } from 'fastify';

import { Route } from '../interfaces/route.interface.js';
import { ChannelRoute } from '../modules/channel/channel.route.js';
import { EsendexWebhookRoute } from '../modules/esendex-webhook/esendex-webhook.route.js';
import { HeartbeatRoute } from '../modules/heartbeat/heartbeat.route.js';
import { LinkRoute } from '../modules/link/link.route.js';
import { MessageRoute } from '../modules/message/message.route.js';
import { ModoRoute } from '../modules/modo/modo.route.js';
import { RecipientRoute } from '../modules/recipient/recipient.route.js';
import { ShortLinkRoute } from '../modules/short-link/short-link.route.js';
import { TagRoute } from '../modules/tag/tag.route.js';
import { TeamsBotRoute } from '../modules/teams-bot/teams-bot.route.js';
import { TopicRoute } from '../modules/topic/topic.route.js';
import { TwilioWebhookRoute } from '../modules/twilio-webhook/twilio-webhook.route.js';
import { TwoWayMessageRoute } from '../modules/two-way-message/two-way-message.route.js';

export const initializeRoutes: FastifyPluginCallbackTypebox<FastifyPluginOptions> = (server, options, done) => {
   const routes: Route[] = [
      new ChannelRoute(),
      new EsendexWebhookRoute(),
      new HeartbeatRoute(),
      new LinkRoute(),
      new MessageRoute(),
      new ModoRoute(),
      new RecipientRoute(),
      new ShortLinkRoute(),
      new TagRoute(),
      new TeamsBotRoute(),
      new TopicRoute(),
      new TwilioWebhookRoute(),
      new TwoWayMessageRoute()
   ];

   routes.forEach((route: Route) => {
      server.register(route.initializeRoutes.bind(route));
   });
   done();
};
