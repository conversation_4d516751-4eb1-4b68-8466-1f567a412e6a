import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { fastifyPlugin } from 'fastify-plugin';
import { B2BClient, ClientError } from 'stytch';

export const authenticate = fastifyPlugin((fastify: FastifyInstance, _: unknown, next: () => void) => {
   const projectId = process.env.STYTCH_PROJECT_ID ?? '';
   const secret = process.env.STYTCH_SECRET ?? '';

   const client = new B2BClient({
      // eslint-disable-next-line camelcase
      project_id: projectId,
      secret
   });

   const authenticateFunction = (
      requiredScope: string[]
   ): ((request: FastifyRequest, reply: FastifyReply) => Promise<void>) => {
      // eslint-disable-next-line consistent-return
      return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
         const token = request.headers.authorization ?? '';

         if (!token.startsWith('Bearer ')) {
            return await reply.code(401).send({ message: 'Missing a Bearer token' });
         }

         const items = token.split(' ');

         if (items.length !== 2) {
            return await reply.code(401).send({ message: 'Invalid Bearer token' });
         }

         try {
            // eslint-disable-next-line camelcase
            const { scopes, custom_claims } = await client.m2m.authenticateToken({
               // eslint-disable-next-line camelcase
               access_token: items[1]
            });

            // we have at least one matching scope
            const filteredArray = scopes.filter((value) => requiredScope.includes(value));

            if (filteredArray.length === 0) {
               return await reply.code(403).send({
                  // eslint-disable-next-line max-len
                  message: `Access Denied: The provided access token does not include the required scope(s) to perform this action. 
One of these scopes is required: [${requiredScope.join(', ')}]`
               });
            }

            // eslint-disable-next-line camelcase
            if (!custom_claims.tenant) {
               return await reply.code(401).send({ message: 'Invalid token' });
            }

            // eslint-disable-next-line camelcase
            request.tenantId = custom_claims.tenant as string;
            // eslint-disable-next-line camelcase
            request.user = custom_claims.user as string;

            try {
               const prisma = await getPrismaClient(request.tenantId);
               const tenant = await prisma.tenant.findUnique({
                  where: {
                     id: request.tenantId
                  }
               });

               if (!tenant) {
                  request.log.info(
                     {
                        data: { error: 'Tenant not found in database' },
                        name: 'authenticate',
                        tenantId: request.tenantId,
                        type: 'jwt-plugin'
                     },
                     `'Tenant not found in database' : ${request.tenantId}`
                  );

                  return await reply.code(401).send({ message: 'Invalid tenant' });
               }
            } catch (error) {
               request.log.error(
                  {
                     error,
                     name: 'authenticate',
                     tenantId: request.tenantId,
                     type: 'jwt-plugin'
                  },
                  `error getting tenant from database: ${request.tenantId}`
               );

               return await reply.code(401).send({ message: 'Invalid tenant' });
            }
         } catch (exception) {
            if (!(exception instanceof ClientError && exception.code === 'jwt_invalid')) {
               request.log.warn(
                  {
                     error: exception,
                     name: 'authenticate',
                     type: 'jwt-plugin'
                  },
                  'oauth2-token - authenticateToken error'
               );
            }

            return reply.code(401).send({ message: 'Invalid token' });
         }
      };
   };

   fastify.decorate('authenticate', authenticateFunction);
   next();
});
