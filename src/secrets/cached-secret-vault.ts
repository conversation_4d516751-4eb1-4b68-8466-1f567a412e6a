import { LRUCache } from 'lru-cache';

import { SecretVault } from './secret-vault.js';

export const LRUOptions: LRUCache.Options<string, string, unknown> = {
   allowStale: false,
   max: 500 /* 15 minutes */,
   ttl: 1000 * 60 * 15
};

export class CachedSecretVault implements SecretVault {
   private static readonly _SECRET_CACHE_ = new LRUCache<string, string, unknown>(LRUOptions);
   private _vault: SecretVault;

   constructor(vault: SecretVault) {
      this._vault = vault;
   }

   async clearCache(): Promise<void> {
      CachedSecretVault._SECRET_CACHE_.clear();
      await this._vault.clearCache();
   }

   async deleteSecret(key: string): Promise<void> {
      CachedSecretVault._SECRET_CACHE_.delete(key);
      await this._vault.deleteSecret(key);
   }

   async getSecret(name: string): Promise<string> {
      if (CachedSecretVault._SECRET_CACHE_.has(name)) {
         return CachedSecretVault._SECRET_CACHE_.get(name) ?? '';
      }

      const secret = await this._vault.getSecret(name);
      CachedSecretVault._SECRET_CACHE_.set(name, secret);

      return secret;
   }

   async setSecret(key: string, secret: string): Promise<void> {
      CachedSecretVault._SECRET_CACHE_.set(key, secret);
      await this._vault.setSecret(key, secret);
   }
}
