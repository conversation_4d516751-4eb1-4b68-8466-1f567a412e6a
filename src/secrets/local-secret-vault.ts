/* eslint-disable @typescript-eslint/require-await */
import { SecretVault } from './secret-vault.js';

export class LocalSecretVault implements SecretVault {
   private _secrets: Map<string, string> = new Map<string, string>();

   async clearCache(): Promise<void> {
      this._secrets.clear();
   }

   async deleteSecret(key: string): Promise<void> {
      this._secrets.delete(key);
   }

   async getSecret(name: string): Promise<string> {

      return this._secrets.get(name) ?? '';
   }

   async setSecret(key: string, secret: string): Promise<void> {
      this._secrets.set(key, secret);
   }
}
