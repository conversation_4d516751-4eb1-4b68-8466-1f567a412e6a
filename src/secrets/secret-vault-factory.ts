import { AzureSecretVault } from './azure-secret-vault.js';
import { CachedSecretVault } from './cached-secret-vault.js';
import { LocalSecretVault } from './local-secret-vault.js';
import { SecretVault } from './secret-vault.js';

type SecretVaultType = 'AZURE' | 'LOCAL';

export class SecretVaultFactory {
   private static _secretVault: SecretVault | undefined;
   private static readonly _VAULT_TYPES_: SecretVaultType[] = ['AZURE', 'LOCAL'];

   static getSecretVault(cache: boolean): SecretVault {

      if (!this._secretVault) {
         const processVaultType = process.env.SECRET_VAULT_TYPE?.toUpperCase() as SecretVaultType;
         const vaultType = this._VAULT_TYPES_.includes(processVaultType) ? processVaultType : 'LOCAL';

         switch (vaultType.toLocaleUpperCase()) {
            case 'AZURE': {
               const azureVault = new AzureSecretVault();

               if (!cache) {
                  this._secretVault = azureVault;
                  break;
               }

               this._secretVault = new CachedSecretVault(azureVault);
               break;
            }

            default:
               if (!cache) {
                  this._secretVault = new LocalSecretVault();
                  break;
               }

               this._secretVault = new CachedSecretVault(new LocalSecretVault());
               break;
         }
      }

      return this._secretVault;
   }

   static resetSecretVault(): void {
      this._secretVault = undefined;
   }
}
