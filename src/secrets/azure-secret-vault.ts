import { DefaultAzureCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';

import { SecretVault } from './secret-vault.js';

export class AzureSecretVault implements SecretVault {
   private _client: SecretClient;

   constructor() {
      this._client = new SecretClient(process.env.AZURE_VAULT_URL ?? '', new DefaultAzureCredential());
   }

   async clearCache(): Promise<void> {
      // No cache to clear for Azure Key Vault
   }

   async deleteSecret(key: string): Promise<void> {
      try {
         void await this._client.beginDeleteSecret(key);
      } catch {
         // ignore error if secret does not exist
      }
   }

   async getSecret(name: string): Promise<string> {

      try {
         const secret = await this._client.getSecret(name);

         return secret.value ?? '';
      } catch {
         // doesn't exist, return empty string
         return '';
      }
   }

   async setSecret(key: string, secret: string): Promise<void> {
      await this._client.setSecret(key, secret);
   }
}
