import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { TwoWayMessageController } from './two-way-message.controller.js';
import {
   CreateTwoWayMessageBulkSchema,
   CreateTwoWaySchema,
   GetTwoWayMessageSchema,
   GetTwoWayMessagesSchema,
   UpdateTwoWaySchema
} from './two-way-message.schema.js';

export class TwoWayMessageRoute implements Route {
   public path = '/two-way-message';
   private _messageController: TwoWayMessageController;
   private _readScopes = [Scopes.ADMIN_READ, Scopes.ADMIN_WRITE, Scopes.MESSAGE_READ, Scopes.MESSAGE_WRITE];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.CHANNEL_WRITE];
   constructor() {
      this._messageController = new TwoWayMessageController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._messageController.createMessage,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateTwoWaySchema,
         url: this.path
      });

      fastify.route({
         handler: this._messageController.createMessagesBulk,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateTwoWayMessageBulkSchema,
         url: `${this.path}/bulk`
      });

      fastify.route({
         handler: this._messageController.updateMessage,
         method: 'patch',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: UpdateTwoWaySchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._messageController.getMessage,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTwoWayMessageSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._messageController.getMessages,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTwoWayMessagesSchema,
         url: this.path
      });

      done();
   }
}
