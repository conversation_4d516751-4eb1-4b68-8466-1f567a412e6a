import { Static } from '@fastify/type-provider-typebox';

import {
   CreatedTwoWayMessageBody,
   CreatedTwoWayMessageBulkBody,
   CreateTwoWayMessageBody,
   CreateTwoWayMessageBulkBody,
   GetTwoWayMessageParams,
   GetTwoWayMessagesBody,
   GetTwoWayMessagesParams,
   UpdateTwoWayMessageBody
} from './two-way-message.schema.js';

export type BulkCreatedTwoWayMessage = Static<typeof CreatedTwoWayMessageBulkBody>;
export type CreateTwoWayMessage = Static<typeof CreateTwoWayMessageBody>;
export type CreateTwoWayMessageBulk = Static<typeof CreateTwoWayMessageBulkBody>;
export type GetTwoWayMessage = Static<typeof CreatedTwoWayMessageBody>;
export type GetTwoWayMessages = Static<typeof GetTwoWayMessagesBody>;
export type UpdateTwoWayMessage = Static<typeof UpdateTwoWayMessageBody>;
export type GetTwoWayMessageParams = Static<typeof GetTwoWayMessageParams>;
export type GetTwoWayMessagesParams = Static<typeof GetTwoWayMessagesParams>;
