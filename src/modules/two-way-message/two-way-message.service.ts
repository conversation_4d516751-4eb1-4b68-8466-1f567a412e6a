import { Prisma } from '@prisma/client';
import { TwoWayMessage } from '@prisma/client';
import { Value } from '@sinclair/typebox/value';
import { AttributeConstants, getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyBaseLogger, FastifyReply } from 'fastify';
import { v7 as uuid } from 'uuid';

import { Result } from './result.js';
import { sendEsendexSms } from './send-esendex-sms.js';
import { sendTwilioSms } from './send-twilio-sms.js';
import { CreateTwoWayMessage, CreateTwoWayMessageBulk, UpdateTwoWayMessage } from './two-way-message.interface.js';
import { CreatedTwoWayMessageBody } from './two-way-message.schema.js';

type GetMessagesOptions = {
   orderBy?: { id: 'asc' | 'desc' };
   take?: number;
   where: {
      createdAt: {
         gt: Date;
      };
      id?: {
         gte: string;
      };
      tenantId: string;
   };
};

type ChannelType = Prisma.ChannelGetPayload<{
   include: { attributes: true };
}>;

export class TwoWayMessageService {
   public async createTwoWayMessage(reply: FastifyReply, tenantId: string, body: CreateTwoWayMessage) {
      const prisma = await getPrismaClient(tenantId);
      let channel = await prisma.channel.findUnique({
         include: { attributes: true },
         where: { id: body.channel, tenantId }
      });

      channel ??= await prisma.channel.findFirst({
         include: { attributes: true },
         where: { externalId: { equals: body.channel, mode: 'insensitive' }, tenantId }
      });

      if (!channel) {
         return reply.code(400).send({
            error: 'Bad Request',
            message: 'Channel not found',
            statusCode: 400
         });
      }

      let result: Result;

      switch (channel.service) {
         case AttributeConstants.SMS:
            if (!body.recipientServiceId) {
               return reply.code(400).send({
                  error: 'Bad Request',
                  message: 'Recipient service ID (recipientServiceId) for SMS messages is required',
                  statusCode: 400
               });
            }

            result = await this._sendSms(channel, body.recipientServiceId, body.message, reply.log);
            break;
         case AttributeConstants.TEST:
            result = {
               code: 200,
               message: 'Message sent successfully',
               success: true
            };
            break;
         default:
            return reply.code(400).send({
               error: 'Bad Request',
               message: 'Channel not supported',
               statusCode: 400
            });
      }

      if (!result.success) {
         if (result.code >= 500) {
            return reply.code(500).send({
               error: 'Internal Server Error',
               message: result.message,
               statusCode: 500
            });
         } else {
            return reply.code(400).send({
               error: 'Bad Request',
               message: result.message,
               statusCode: 400
            });
         }
      }

      const response = await prisma.twoWayMessage.create({
         data: {
            category: body.category,
            channel: body.channel,
            conversationId: body.conversationId,
            direction: 'O',
            id: uuid(),
            message: body.message,
            recipientId: body.recipientId,
            recipientServiceId: body.recipientServiceId?.replace('+', ''),
            tenantId,
            tenantMessageId: body.tenantMessageId,
            tenantServiceId: result.tenantServiceId,
            transactionId: result.transactionId
         }
      });

      return reply.code(201).send(Value.Clean(CreatedTwoWayMessageBody, response));
   }

   public async createTwoWayMessagesBulk(reply: FastifyReply, tenantId: string, body: CreateTwoWayMessageBulk) {
      const prisma = await getPrismaClient(tenantId);

      const promises = body.items.map(async (item) => {
         let channel = await prisma.channel.findUnique({
            include: { attributes: true },
            where: { id: item.channel, tenantId }
         });

         channel ??= await prisma.channel.findFirst({
            include: { attributes: true },
            where: { externalId: { equals: item.channel, mode: 'insensitive' }, tenantId }
         });

         if (!channel) {
            return {
               error: 'Channel not found',
               id: '',
               success: false,
               tenantMessageId: item.tenantMessageId
            };
         }

         let result: Result;

         switch (channel.service) {
            case 'sms':
               if (!item.recipientServiceId) {
                  return {
                     error: 'Recipient service ID (recipientServiceId) for SMS messages is required',
                     id: '',
                     success: false,
                     tenantMessageId: item.tenantMessageId
                  };
               }

               result = await this._sendSms(channel, item.recipientServiceId, item.message, reply.log);
               break;
            case 'test':
               result = {
                  code: 200,
                  message: 'Message sent successfully',
                  success: true
               };
               break;
            default:
               return {
                  error: `Channel ${channel.service} not supported`,
                  id: '',
                  success: false,
                  tenantMessageId: item.tenantMessageId
               };
         }

         if (!result.success) {
            return {
               error: result.message,
               id: '',
               success: false,
               tenantMessageId: item.tenantMessageId
            };
         }

         const created = await prisma.twoWayMessage.create({
            data: {
               category: item.category,
               channel: item.channel,
               conversationId: item.conversationId,
               direction: 'O',
               id: uuid(),
               message: item.message,
               recipientId: item.recipientId,
               recipientServiceId: item.recipientServiceId?.replace('+', ''),
               tenantId,
               tenantMessageId: item.tenantMessageId,
               tenantServiceId: result.tenantServiceId,
               transactionId: result.transactionId
            }
         });

         return {
            error: '',
            id: created.id,
            success: true,
            tenantMessageId: item.tenantMessageId
         };
      });

      const messages = await Promise.all(promises);

      return reply.code(200).send({ items: messages });
   }

   public async sendTwoWayMessage(reply: FastifyReply, tenantId: string, body: CreateTwoWayMessage) {
      const prisma = await getPrismaClient(tenantId);
      let channel = await prisma.channel.findUnique({
         include: { attributes: true },
         where: { id: body.channel, tenantId }
      });

      channel ??= await prisma.channel.findFirst({
         include: { attributes: true },
         where: { externalId: { equals: body.channel, mode: 'insensitive' }, tenantId }
      });

      if (!channel) {
         return reply.code(400).send({
            error: 'Bad Request',
            message: 'Channel not found',
            statusCode: 400
         });
      }

      let result: Result;

      switch (channel.service) {
         case 'sms':
            if (!body.recipientServiceId) {
               return reply.code(400).send({
                  error: 'Bad Request',
                  message: 'Recipient service ID (recipientServiceId) for SMS messages is required',
                  statusCode: 400
               });
            }

            result = await this._sendSms(channel, body.recipientServiceId, body.message, reply.log);
            break;
         case 'test':
            result = {
               code: 200,
               message: 'Message sent successfully',
               success: true
            };
            break;
         default:
            return reply.code(400).send({
               error: 'Bad Request',
               message: 'Channel not supported',
               statusCode: 400
            });
      }

      if (!result.success) {
         if (result.code >= 500) {
            return reply.code(500).send({
               error: 'Internal Server Error',
               message: result.message,
               statusCode: 500
            });
         } else {
            return reply.code(400).send({
               error: 'Bad Request',
               message: result.message,
               statusCode: 400
            });
         }
      }

      const response = await prisma.twoWayMessage.create({
         data: {
            category: body.category,
            channel: body.channel,
            conversationId: body.conversationId,
            direction: 'O',
            id: uuid(),
            message: body.message,
            recipientId: body.recipientId,
            recipientServiceId: body.recipientServiceId?.replace('+', ''),
            tenantId,
            tenantMessageId: body.tenantMessageId,
            tenantServiceId: result.tenantServiceId,
            transactionId: result.transactionId
         }
      });

      return reply.code(201).send(Value.Clean(CreatedTwoWayMessageBody, response));
   }

   public async updateTwoWayMessage(reply: FastifyReply, tenantId: string, id: string, body: UpdateTwoWayMessage) {
      const prisma = await getPrismaClient(tenantId);
      const item = await prisma.twoWayMessage.findUnique({ where: { id, tenantId } });

      if (!item) {
         return reply.code(404).send({
            error: 'Not Found',
            message: 'Two-way message not found',
            statusCode: 404
         });
      }

      const updatedData: Partial<TwoWayMessage> = {};
      updatedData.category = body.category ?? item.category;

      if ('category' in body && body.category === '') {
         updatedData.category = '';
      }

      updatedData.conversationId = body.conversationId ?? item.conversationId;

      if ('conversationId' in body && body.conversationId === '') {
         updatedData.conversationId = '';
      }

      updatedData.tenantMessageId = body.tenantMessageId ?? item.tenantMessageId;

      if ('tenantMessageId' in body && body.tenantMessageId === '') {
         updatedData.tenantMessageId = '';
      }

      const updated = await prisma.twoWayMessage.update({
         data: updatedData,
         where: { id, tenantId }
      });

      return reply.send(Value.Clean(CreatedTwoWayMessageBody, updated));
   }

   public async getTwoWayMessage(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);
      const item = await prisma.twoWayMessage.findUnique({ where: { id, tenantId } });

      return reply.send(item);
   }

   public async getTwoWayMessages(reply: FastifyReply, tenantId: string, since: Date, key?: string, size: number = 50) {
      const options: GetMessagesOptions = {
         orderBy: { id: 'asc' },
         where: {
            createdAt: {
               gt: since
            },
            tenantId
         }
      };

      if (key) {
         options.where = {
            ...options.where,
            id: {
               gte: key
            }
         };
      }

      const prisma = await getPrismaClient(tenantId);
      const count = await prisma.twoWayMessage.count(options);
      options.take = size + 1;
      const items = await prisma.twoWayMessage.findMany(options);
      let nextKey = '';

      if (items.length > size) {
         const last = items[size];
         nextKey = last.id;
         items.pop();
      }

      const response = {
         items,
         pagination: {
            // eslint-disable-next-line camelcase
            next_key: nextKey,
            // eslint-disable-next-line camelcase
            page_size: size,
            // eslint-disable-next-line camelcase
            total_records: count
         }
      };

      return reply.send(response);
   }

   private async _sendSms(channel: ChannelType, to: string, text: string, logger: FastifyBaseLogger): Promise<Result> {
      const fromAttribute = channel.attributes.find((a) => a.name === 'phone');

      if (!fromAttribute?.value) {
         return {
            code: 400,
            message: 'From phone number not found',
            success: false
         };
      }

      const from: string = fromAttribute.value;
      const provider = channel.provider;

      let result: Result;

      switch (provider) {
         case 'esendex':
            result = await sendEsendexSms(to, from, text, channel.tenantId, channel.id, logger);
            break;

         case 'twilio': {
            const sidAttribute = channel.attributes.find((a) => a.name === AttributeConstants.TWILIO_SID);

            if (!sidAttribute?.value) {
               return {
                  code: 400,
                  message: 'Missing Twilio sid attribute',
                  success: false
               };
            }

            const sid: string = sidAttribute.value;

            result = await sendTwilioSms(to, from, text, channel.tenantId, channel.id, sid, logger);
            break;
         }

         default:
            return {
               code: 500,
               message: 'Provider not supported',
               success: false
            };
      }

      return result;
   }
}
