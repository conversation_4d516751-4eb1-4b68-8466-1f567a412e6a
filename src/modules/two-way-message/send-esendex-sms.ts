import { AttributeConstants, SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { FastifyBaseLogger } from 'fastify';

import { Result } from './result.js';

const url = 'https://messaging.esendex.us/Messaging.svc/SendMessage';

const Errors: Record<number, string> = {
   400: 'The number provided is invalid, wrong format, or it cannot send to the destination. (InvalidDID)',
   401: 'The provided license key is invalid. (InvalidLicenseKey)',
   402: 'The account has been suspended. (AccountSuspended)',
   403: 'No messages can be sent to the destination phone number from the license key. (StopOnPhoneNumber)',
   404: 'Proper number could not be established or MessageID provided is invalid. (MissingDIDOrInvalidMessageID)',
   405: 'ScheduledDateTime not in UTC. (InvalidDateTime)',
   406: 'Destination phone number invalid. (InvalidDestination)',
   411: 'No message body provided. (NoMessage)',
   413: 'Message is too long. (MessageExceedsLimit)',
   506: 'Message failed to submit to provider. (Failed)',
   507: 'Message cancelled. (Cancelled)',
   508: 'Message submitted to provider but failed by delivery receipt. (FailedByProvider)'
};

export const sendEsendexSms = async (
   to: string,
   from: string,
   text: string,
   tenantId: string,
   channelId: string,
   logger: FastifyBaseLogger
): Promise<Result> => {
   const result: Result = {
      code: 0,
      message: '',
      success: false
   };

   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);
   const key = await secretVault.getSecret(`${tenantId}-${channelId}-${AttributeConstants.ESENDEX_KEY}`);

   if (!key) {
      result.message = `Esendex key not found for tenant ${tenantId}`;

      return result;
   }

   const cleanTo = to.replace('+', '');
   const cleanFrom = from.replace('+', '');
   const data = {
      Body: text,
      Concatenate: true,
      From: cleanFrom,
      IsUnicode: true,
      LicenseKey: key,
      To: [cleanTo],
      UseMMS: false
   };

   result.tenantServiceId = cleanFrom;

   // don't log the license key
   // eslint-disable-next-line no-unused-vars
   const { LicenseKey, ...logData } = data;
   logger.debug({
      data: logData,
      message: 'esendex - sending',
      name: 'sendEsendexSms',
      tenantId,
      type: 'two-way-message'
   });

   let response: Response;

   try {
      response = await fetch(url, {
         body: JSON.stringify(data),
         headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json'
         },
         method: 'POST'
      });

      const responseText = await response.text();
      logger.debug({
         data: responseText,
         message: 'esendex - response',
         name: 'sendEsendexSms',
         tenantId,
         type: 'two-way-message'
      });

      // bad request, or backend server issue
      if (response.status >= 400) {
         result.code = response.status;
         result.message = 'Internal server error';

         logger.info({
            code: response.status,
            data: `status:${response.status}: response: ${responseText}`,
            message: 'esendex - server issue',
            name: 'sendEsendexSms',
            tenantId,
            type: 'two-way-message'
         });

         return result;
      }

      // TODO: test this
      const responseData = JSON.parse(responseText) as Array<{ MessageID: string; MessageStatus: number }>;

      const messageStatus: number = responseData[0].MessageStatus;
      result.code = messageStatus;

      if (messageStatus >= 400) {
         result.message = Errors[`${messageStatus}`];

         return result;
      }

      result.success = true;
      result.transactionId = responseData[0].MessageID;
      result.message = 'Message sent successfully';
   } catch (err) {
      const error = err as Error;
      logger.info({
         data: error,
         message: 'esendex - exception',
         name: 'sendEsendexSms',
         tenantId,
         type: 'two-way-message'
      });
      result.message = 'Internal server error';
   }

   return result;
};
