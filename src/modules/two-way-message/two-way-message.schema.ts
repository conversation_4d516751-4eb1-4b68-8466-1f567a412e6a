import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { PaginationResponse } from '../shared/pagination/pagination.schema.js';

export const CreateTwoWayMessageBody = Type.Object({
   category: Type.Optional(Type.String({ maxLength: 30 })),
   channel: Type.String(),
   conversationId: Type.Optional(Type.String()),
   message: Type.String(),
   recipientId: Type.Optional(Type.String()),
   recipientServiceId: Type.Optional(Type.String()),
   subject: Type.Optional(Type.String()),
   tenantMessageId: Type.Optional(Type.String())
});

export const CreatedTwoWayMessageBody = Type.Object({
   category: Type.String({ maxLength: 30 }),
   channel: Type.String(),
   conversationId: Type.String({ default: '' }),
   createdAt: Type.String({ format: 'date-time' }),
   direction: Type.String({ enum: ['I', 'O'] }),
   error: Type.String({ default: '' }),
   id: Type.String({ format: 'uuid' }),
   message: Type.String(),
   reaction: Type.String({ default: '' }),
   recipientId: Type.String({ default: '' }),
   recipientServiceId: Type.String({ default: '' }),
   subject: Type.String({ default: '' }),
   tenantMessageId: Type.String({ default: '' }),
   tenantServiceId: Type.String({ default: '' }),
   updatedAt: Type.String({ format: 'date-time' })
});

export const UpdateTwoWayMessageBody = Type.Object({
   category: Type.Optional(Type.String({ maxLength: 30 })),
   conversationId: Type.Optional(Type.String()),
   tenantMessageId: Type.Optional(Type.String())
});

export const CreateTwoWaySchema: FastifySchema = {
   body: CreateTwoWayMessageBody,
   description: 'Create a two-way message',
   hide: true,
   response: {
      201: CreatedTwoWayMessageBody
   }
};

export const UpdateTwoWaySchema: FastifySchema = {
   body: UpdateTwoWayMessageBody,
   description: 'Update the category, conversation ID, and/or tenant message ID of a two-way message',
   hide: true,
   response: {
      201: CreatedTwoWayMessageBody
   }
};

export const GetTwoWayMessagesBody = Type.Object({
   items: Type.Array(CreatedTwoWayMessageBody),
   pagination: PaginationResponse
});

export const GetTwoWayMessageParams = Type.Object({
   id: Type.String()
});

export const GetTwoWayMessagesParams = Type.Object({
   key: Type.Optional(Type.String()),
   since: Type.String({ format: 'date-time' }),
   size: Type.Optional(Type.Number({ default: 50, maximum: 100, minimum: 1 }))
});

export const GetTwoWayMessageSchema: FastifySchema = {
   description: 'Get a two-way message',
   params: GetTwoWayMessageParams,
   response: {
      200: CreatedTwoWayMessageBody
   }
};

export const GetTwoWayMessagesSchema: FastifySchema = {
   description: 'Get two-way messages',
   querystring: GetTwoWayMessagesParams,
   response: {
      200: GetTwoWayMessagesBody
   }
};

export const CreateTwoWayMessageBulkItemBody = Type.Object({
   category: Type.String({ maxLength: 30 }),
   channel: Type.String(),
   conversationId: Type.Optional(Type.String()),
   message: Type.String(),
   recipientId: Type.Optional(Type.String()),
   recipientServiceId: Type.Optional(Type.String()),
   subject: Type.Optional(Type.String()),
   tenantMessageId: Type.String()
});

export const CreateTwoWayMessageBulkBody = Type.Object({
   items: Type.Array(CreateTwoWayMessageBulkItemBody)
});

export const CreatedTwoWayMessageBulkItemBody = Type.Object({
   error: Type.String({ default: '' }),
   id: Type.Optional(Type.String({ format: 'uuid' })),
   success: Type.Boolean(),
   tenantMessageId: Type.String()
});

export const CreatedTwoWayMessageBulkBody = Type.Object({
   items: Type.Array(CreatedTwoWayMessageBulkItemBody)
});

export const CreateTwoWayMessageBulkSchema: FastifySchema = {
   body: CreateTwoWayMessageBulkBody,
   description: 'Create two-way messages in bulk',
   hide: false,
   response: {
      200: CreatedTwoWayMessageBulkBody
   }
};
