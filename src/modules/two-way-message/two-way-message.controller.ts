import { FastifyReply, FastifyRequest } from 'fastify';

import {
   CreateTwoWayMessage,
   CreateTwoWayMessageBulk,
   GetTwoWayMessageParams,
   GetTwoWayMessagesParams,
   UpdateTwoWayMessage
} from './two-way-message.interface.js';
import { TwoWayMessageService } from './two-way-message.service.js';

export class TwoWayMessageController {
   public createMessage = async (req: FastifyRequest<{ Body: CreateTwoWayMessage }>, reply: FastifyReply) => {
      await this._messageService.createTwoWayMessage(reply, req.tenantId, req.body);
   };

   public createMessagesBulk = async (req: FastifyRequest<{ Body: CreateTwoWayMessageBulk }>, reply: FastifyReply) => {
      await this._messageService.createTwoWayMessagesBulk(reply, req.tenantId, req.body);
   };

   public updateMessage = async (
      req: FastifyRequest<{ Body: UpdateTwoWayMessage; Params: GetTwoWayMessageParams }>,
      reply: FastifyReply
   ) => {
      await this._messageService.updateTwoWayMessage(reply, req.tenantId, req.params.id, req.body);
   };

   public getMessage = async (req: FastifyRequest<{ Params: GetTwoWayMessageParams }>, reply: FastifyReply) => {
      await this._messageService.getTwoWayMessage(reply, req.tenantId, req.params.id);
   };

   public getMessages = async (req: FastifyRequest<{ Querystring: GetTwoWayMessagesParams }>, reply: FastifyReply) => {
      await this._messageService.getTwoWayMessages(
         reply,
         req.tenantId,
         new Date(req.query.since),
         req.query.key,
         req.query.size
      );
   };

   private _messageService = new TwoWayMessageService();
}
