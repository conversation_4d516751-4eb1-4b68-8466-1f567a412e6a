import { AttributeConstants, SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { FastifyBaseLogger } from 'fastify';
import twilio from 'twilio';

import { Result } from './result.js';

export const sendTwilioSms = async (
   to: string,
   from: string,
   text: string,
   tenantId: string,
   channelId: string,
   sid: string,
   logger: FastifyBaseLogger
): Promise<Result> => {
   const result: Result = {
      code: 0,
      message: '',
      success: false
   };

   const secretVault: SecretVault = SecretVaultFactory.getSecretVault(true);
   const token = await secretVault.getSecret(`${tenantId}-${channelId}-${AttributeConstants.TWILIO_TOKEN}`);

   if (!token) {
      result.message = 'Twilio token not found';

      return result;
   }

   let cleanTo = to.replace('+', '');

   if (!cleanTo.startsWith('+')) {
      cleanTo = `+${cleanTo}`;
   }

   let cleanFrom = from;

   if (!cleanFrom.startsWith('+')) {
      cleanFrom = `+${cleanFrom}`;
   }

   const toSend = {
      body: text,
      from: cleanFrom,
      to: cleanTo
      // TODO: status callback ?
      // this only handle status updates, not message responses -- not supported yet
      // statusCallback: `${baseUrl}/v1/api/twilio-sms/${tenantId}/webhook`
   };

   result.tenantServiceId = cleanFrom.replace('+', '');

   logger.debug({
      data: toSend,
      message: 'twilio - sending',
      name: 'sendTwilioSms',
      tenantId,
      type: 'two-way-message'
   });

   try {
      const client: twilio.Twilio = twilio(sid, token);
      const response = await client.messages.create(toSend);

      logger.debug({ data: response, message: 'twilio - response', name: 'twilio-sms', tenantId, type: 'channel' });

      if (response.status !== 'failed' && response.status !== 'undelivered') {
         result.success = true;
         result.transactionId = response.sid;

         return result;
      }

      result.success = false;
      result.code = response.errorCode || 0;

      if (response.errorCode === 21610) {
         logger.info({
            data: { code: response.errorCode, error: 'Recipient has blocked SMS', phoneNumber: to, tenantId },
            message: 'twilio - error - 21610',
            name: 'twilio-sms',
            type: 'channel'
         });
         result.message = 'Recipient has blocked SMS';

         return result;
      }

      if (response.errorCode === 21408) {
         logger.info({
            data: { code: response.errorCode, error: 'Invalid region', phoneNumber: to, tenantId },
            message: 'twilio - error - 21408',
            name: 'twilio-sms',
            tenantId,
            type: 'channel'
         });
         result.message = 'Invalid region';

         return result;
      }

      if (response.errorCode === 21211) {
         logger.info({
            data: { code: response.errorCode, error: response.errorMessage, phoneNumber: to, tenantId },
            message: 'twilio - error - 21211',
            name: 'twilio-sms',
            tenantId,
            type: 'channel'
         });
         result.message = response.errorMessage;

         return result;
      }

      logger.info({
         data: { code: response.errorCode, error: response.errorMessage, phoneNumber: to, tenantId },
         message: `twilio - error - ${response.errorCode}`,
         name: 'twilio-sms',
         tenantId,
         type: 'channel'
      });

      result.message = response.errorMessage;

      return result;
   } catch (err) {
      const error = err as Error;
      logger.info({
         data: { error: error.message, fullError: err, phoneNumber: to, tenantId },
         message: 'twilio - error - no code',
         name: 'twilio-sms',
         tenantId,
         type: 'channel'
      });

      result.success = false;
      result.message = error.message;

      return result;
   }
};
