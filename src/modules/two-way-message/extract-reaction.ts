export const extractReaction = (message: string): string => {
   const text = message.toLowerCase();

   // Define a map of reaction patterns and their corresponding emojis
   const reactionMap: Record<string, string> = {
      'disliked “': '👎',
      'emphasized “': '‼️',
      'laughed at “': '😆',
      'liked “': '👍',
      'loved “': '❤️',
      'questioned “': '❓'
   };

   // Check for known reaction patterns
   for (const [pattern, emoji] of Object.entries(reactionMap)) {
      if (text.startsWith(pattern)) {
         return emoji;
      }
   }

   // Handle special cases
   if (text.startsWith('removed ') || text.startsWith(' removed')) {
      return ' ';
   }

   // Handle custom reactions (note: that is not a space character)
   if (text.startsWith(' ')) {
      const match = /(.*?) to “/.exec(message);

      if (match) {
         return match[1].replace(/[\u200A\u200B]/g, '').trim();
      }
   }

   if (text.startsWith('reacted ')) {
      const match = / (.*?) /.exec(message);

      if (match) {
         return decodeURIComponent(match[1]);
      }
   }

   // If no reaction is found, return an empty string
   return '';
};
