/* eslint-disable camelcase */
import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { ErrorResponseSchema } from '../shared/error/error.schema.js';

export const PaginationResponse = Type.Object({
   created_max: Type.Optional(Type.String({ format: 'date-time' })),
   created_min: Type.Optional(Type.String({ format: 'date-time' })),
   next_key: Type.Optional(Type.String()),
   page_size: Type.Number(),
   total_records: Type.Number(),
   updated_max: Type.Optional(Type.String({ format: 'date-time' })),
   updated_min: Type.Optional(Type.String({ format: 'date-time' }))
});

export const CreateMessageBody = Type.Object({
   attributes: Type.Optional(
      Type.Array(
         Type.Object({
            name: Type.String(),
            value: Type.Optional(Type.String({ default: '' }))
         })
      )
   ),
   awaitingApproval: Type.Optional(Type.Boolean({ default: false })),
   category: Type.Optional(Type.String({ maxLength: 30 })),
   context: Type.Optional(
      Type.Array(
         Type.Object({
            name: Type.String(),
            value: Type.String()
         })
      )
   ),
   conversationId: Type.Optional(Type.String()),
   importance: Type.Optional(Type.String({ default: '1', enum: ['1', '5', '9'] })),
   isNotificationMessage: Type.Optional(Type.Boolean({ default: false })),
   message: Type.Optional(Type.String()),
   plainText: Type.Optional(Type.String({ default: '' })),
   recipients: Type.Optional(Type.Array(Type.String())),
   sendAt: Type.Optional(Type.String({ format: 'date-time' })),
   shortMessage: Type.Optional(Type.String({ default: '' })),
   tags: Type.Optional(Type.Array(Type.String())),
   title: Type.String(),
   topic: Type.String()
   // trackLinks: Type.Optional(Type.Boolean({ default: false })),
});

export const CreateMessagesBody = Type.Object({
   messages: Type.Array(
      Type.Object({
         correlationId: Type.String(),
         message: CreateMessageBody
      })
   )
});

export const CreatedMessageBody = Type.Object({
   attributes: Type.Array(
      Type.Object({
         name: Type.String(),
         value: Type.String()
      })
   ),
   category: Type.String({ maxLength: 30 }),
   context: Type.Array(
      Type.Object({
         name: Type.String(),
         value: Type.String()
      })
   ),
   conversationId: Type.String(),
   createdAt: Type.String({ format: 'date-time' }),
   error: Type.String({ default: '' }),
   id: Type.String({ format: 'uuid' }),
   importance: Type.String({ default: '1', enum: ['1', '5', '9'] }),
   isNotificationMessage: Type.Boolean(),
   recipients: Type.Array(Type.String()),
   sendAt: Type.String({ format: 'date-time' }),
   status: Type.String({
      enum: ['PROCESSING', 'AWAITING_APPROVAL', 'SCHEDULED', 'RETRYING', 'APPROVAL_DENIED', 'ERROR', 'SENT']
   }),
   tags: Type.Array(Type.String()),
   title: Type.String(),
   topic: Type.String(),
   // trackLinks: Type.Boolean(),
   updatedAt: Type.String({ format: 'date-time' })
});

export const CreatedMessagesBody = Type.Object({
   messages: Type.Array(
      Type.Union([
         Type.Object({ correlationId: Type.String(), id: Type.String() }),
         Type.Object({ correlationId: Type.String(), error: ErrorResponseSchema })
      ]),
      { maxItems: 50, minItems: 1 }
   )
});

export const GetMessagesBody = Type.Object({
   items: Type.Array(CreatedMessageBody),
   pagination: PaginationResponse
});

export const GetMessageParams = Type.Object({
   id: Type.String()
});

export const GetMessagesParams = Type.Object({
   created_max: Type.Optional(Type.String({ format: 'date-time' })),
   created_min: Type.Optional(Type.String({ format: 'date-time' })),
   key: Type.Optional(Type.String()),
   size: Type.Optional(Type.Number({ default: 50, maximum: 100, minimum: 1 })),
   status: Type.Optional(
      Type.String({
         enum: ['PROCESSING', 'PENDING_APPROVAL', 'SCHEDULED', 'RETRYING', 'APPROVAL_DENIED', 'ERROR', 'SENT']
      })
   ),
   updated_max: Type.Optional(Type.String({ format: 'date-time' })),
   updated_min: Type.Optional(Type.String({ format: 'date-time' }))
});

export const CreateMessageSchema: FastifySchema = {
   body: CreateMessageBody,
   description: 'Create a message',
   response: {
      201: CreatedMessageBody
   }
};

export const CreateMessagesSchema: FastifySchema = {
   body: CreateMessagesBody,
   description: 'Create multiple messages',
   response: {
      200: CreatedMessagesBody
   }
};

export const GetMessagesSchema: FastifySchema = {
   description: 'Get messages',
   params: GetMessagesParams,
   querystring: GetMessagesParams,
   response: {
      200: GetMessagesBody
   }
};

export const GetMessageSchema: FastifySchema = {
   description: 'Get a message by ID',
   params: GetMessageParams,
   response: {
      200: CreatedMessageBody
   }
};

export const ApproveMessageSchema: FastifySchema = {
   description: 'Approve a message',
   params: GetMessageParams,
   response: {
      200: CreatedMessageBody
   }
};

export const DenyMessageSchema: FastifySchema = {
   description: 'Deny a message',
   params: GetMessageParams,
   response: {
      200: CreatedMessageBody
   }
};
