import { Static } from '@fastify/type-provider-typebox';

import {
   CreatedMessageBody,
   CreatedMessagesBody,
   CreateMessageBody,
   CreateMessagesBody,
   GetMessageParams
} from './message.schema.js';

export type CreateMessage = Static<typeof CreateMessageBody>;
export type CreateMessages = Static<typeof CreateMessagesBody>;
export type CreatedMessage = Static<typeof CreatedMessageBody>;
export type CreatedMessages = Static<typeof CreatedMessagesBody>;
export type GetMessageParams = Static<typeof GetMessageParams>;
