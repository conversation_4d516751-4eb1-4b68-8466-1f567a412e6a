import { Value } from '@sinclair/typebox/value';
import { CryptoService, getPrismaClient, QueueFactory } from '@x-signal-inc/messaging-common';
import { FastifyBaseLogger, FastifyReply } from 'fastify';
import { LRUCache } from 'lru-cache';
import { v7 as uuid } from 'uuid';

import { ErrorResponse } from '../shared/error/error.schema.js';

import { CreatedMessage, CreateMessage, CreateMessages } from './message.interface.js';
import { CreatedMessageBody } from './message.schema.js';

export class MessageService {
   private _cryptoService: CryptoService = CryptoService.getInstance();
   private _outgoingQueue = process.env.OUTGOING_QUEUE ?? '';
   private _serviceBusHostname = process.env.QUEUE_HOSTNAME ?? '';
   private _encryptionKeyIdCache = new LRUCache<string, string>({
      allowStale: false,
      max: 500,
      ttl: 1000 * 60 * 15
   });

   private async _getEncryptionKeyId(tenantId: string): Promise<string> {
      if (this._encryptionKeyIdCache.has(tenantId)) {
         return this._encryptionKeyIdCache.get(tenantId)!;
      }

      const prisma = await getPrismaClient(tenantId);
      const tenant = await prisma.tenant.findFirst({
         select: { encryptionKeyId: true },
         where: { id: tenantId }
      });

      if (!tenant?.encryptionKeyId) {
         throw new Error(`Encryption key not found for tenant ${tenantId}`);
      }

      this._encryptionKeyIdCache.set(tenantId, tenant.encryptionKeyId);

      return tenant.encryptionKeyId;
   }

   // TODO: lower complexity
   // eslint-disable-next-line complexity
   private async _createMessage(
      log: FastifyBaseLogger,
      tenantId: string,
      user: string,
      body: CreateMessage
   ): Promise<CreatedMessage | ErrorResponse> {
      const prisma = await getPrismaClient(tenantId);

      if ((!body.recipients || body.recipients.length === 0) && (!body.tags || body.tags.length === 0)) {
         return {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'No recipients or tags are provided',
            statusCode: 400
         };
      }

      if (
         (body.recipients && body.recipients.length > 0 && body.tags && body.tags.length > 0) ||
         (body.tags && body.tags.length > 0 && body.recipients && body.recipients.length > 0)
      ) {
         return {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'Only one of recipients, service groups, or tags can be provided',
            statusCode: 400
         };
      }

      if (body.isNotificationMessage && !body.message) {
         return {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'Message is required when notificationMessage is provided',
            statusCode: 400
         };
      }

      if (body.isNotificationMessage && !body.shortMessage && !body.plainText) {
         return {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'Short message or plain text is required when notificationMessage is true',
            statusCode: 400
         };
      }

      if (!body.message && !body.shortMessage && !body.plainText) {
         return {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'Message, short message, or plain text is required.',
            statusCode: 400
         };
      }

      let topic = await prisma.topic.findFirst({
         where: {
            id: body.topic,
            tenantId
         }
      });

      if (!topic) {
         topic = await prisma.topic.findFirst({
            where: {
               externalId: {
                  equals: body.topic,
                  mode: 'insensitive'
               },
               tenantId
            }
         });

         if (topic) {
            body.topic = topic.id;
         }
      }

      if (!topic) {
         return {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: `Topic ${body.topic} not found`,
            statusCode: 400
         };
      }

      const message = await prisma.message.create({
         data: {
            apiUser: user,
            attributes: {
               create: body.attributes
            },
            category: body.category,
            context: {
               create: body.context
            },
            conversationId: body.conversationId,
            id: uuid(),
            importance: body.importance,
            isNotificationMessage: body.isNotificationMessage,
            message: body.message
               ? await this._cryptoService.encrypt(tenantId, await this._getEncryptionKeyId(tenantId), body.message)
               : '',
            plainText: body.plainText
               ? await this._cryptoService.encrypt(tenantId, await this._getEncryptionKeyId(tenantId), body.plainText)
               : '',
            recipients: body.recipients,
            sendAt: body.sendAt,
            shortMessage: body.shortMessage
               ? await this._cryptoService.encrypt(tenantId, await this._getEncryptionKeyId(tenantId), body.shortMessage)
               : '',
            status: body.awaitingApproval ? 'AWAITING_APPROVAL' : 'PROCESSING',
            tags: body.tags,
            tenantId,
            title: body.title,
            topic: body.topic
         },
         include: {
            attributes: true,
            context: true
         }
      });

      if (body.awaitingApproval) {
         return Value.Clean(CreatedMessageBody, message) as CreatedMessage;
      }

      let scheduledEnqueueTimeUtc = new Date();

      if (body.sendAt && new Date(body.sendAt) > new Date()) {
         scheduledEnqueueTimeUtc = new Date(body.sendAt);
         message.status = 'SCHEDULED';
         await prisma.message.update({
            data: {
               status: 'SCHEDULED'
            },
            where: {
               id: message.id
            }
         });
      }

      try {
         const queue = QueueFactory.getQueue('outgoing', this._serviceBusHostname, this._outgoingQueue);
         await queue.enqueue({ id: message.id, messageId: message.id, tenantId }, scheduledEnqueueTimeUtc);

         return Value.Clean(CreatedMessageBody, message) as CreatedMessage;
      } catch (error) {
         log.error(
            {
               data: error,
               ids: { messageId: message.id, tenantId },
               name: 'sendServiceMessage',
               type: 'function'
            },
            error instanceof Error ? error.message : 'unknown error'
         );

         return {
            code: 'INTERNAL_SERVER_ERROR',
            error: 'Internal Server Error',
            message: 'Failed to create message',
            statusCode: 500
         };
      }
   }

   public async createMessage(reply: FastifyReply, tenantId: string, user: string, body: CreateMessage) {
      const response = await this._createMessage(reply.log, tenantId, user, body);

      if ('statusCode' in response && 'error' in response) {
         return reply.status(response.statusCode).send(response);
      }

      return reply.status(201).send(response);
   }

   public async createMessages(reply: FastifyReply, tenantId: string, user: string, body: CreateMessages) {
      const promises = body.messages.map(async (message) => {
         const created = await this._createMessage(reply.log, tenantId, user, message.message);

         if ('statusCode' in created && 'error' in created) {
            return { correlationId: message.correlationId, error: created };
         }

         return { correlationId: message.correlationId, id: created.id };
      });

      const response = await Promise.all(promises);

      return await reply.status(200).send({ messages: response });
   }

   public async getMessage(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);

      const message = await prisma.message.findFirst({
         include: {
            attributes: true,
            context: true
         },
         where: {
            id
         }
      });

      if (!message) {
         return reply.status(404).send({
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Message ${id} not found`,
            statusCode: 404
         });
      }

      if( message.plainText) {
         message.plainText = await this._cryptoService.decrypt(tenantId, message.plainText);
      }

      if (message.message) {
         message.message = await this._cryptoService.decrypt(tenantId, message.message);
      }

      if (message.shortMessage) {
         message.shortMessage = await this._cryptoService. decrypt(tenantId, message.shortMessage);
      }

      return reply.status(200).send(message);
   }
}
