import { FastifyReply, FastifyRequest } from 'fastify';

import { CreateMessage, CreateMessages, GetMessageParams } from './message.interface.js';
import { MessageService } from './message.service.js';

export class MessageController {
   public createMessage = async (req: FastifyRequest<{ Body: CreateMessage }>, reply: FastifyReply) => {
      await this._messageService.createMessage(reply, req.tenantId, req.user, req.body);
   };

   public createMessages = async (req: FastifyRequest<{ Body: CreateMessages }>, reply: FastifyReply) => {
      await this._messageService.createMessages(reply, req.tenantId, req.user, req.body);
   };

   public getMessage = async (req: FastifyRequest<{ Params: GetMessageParams }>, reply: FastifyReply) => {
      await this._messageService.getMessage(reply, req.tenantId, req.params.id);
   };

   public getMessages = async (req: FastifyRequest<{ Body: CreateMessage }>, reply: FastifyReply) => {};
   public searchMessages = async (req: FastifyRequest<{ Body: CreateMessage }>, reply: FastifyReply) => {};
   public approveMessage = async (req: FastifyRequest<{ Body: CreateMessage }>, reply: FastifyReply) => {};
   public denyMessage = async (req: FastifyRequest<{ Body: CreateMessage }>, reply: FastifyReply) => {};

   private _messageService = new MessageService();
}
