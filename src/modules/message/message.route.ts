import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { MessageController } from './message.controller.js';
import {
   ApproveMessageSchema,
   CreateMessageSchema,
   CreateMessagesSchema,
   DenyMessageSchema,
   GetMessageSchema,
   GetMessagesSchema
} from './message.schema.js';

export class MessageRoute implements Route {
   public path = '/message';
   private _messageController: MessageController;
   private _readScopes = [Scopes.ADMIN_READ, Scopes.ADMIN_WRITE, Scopes.MESSAGE_READ, Scopes.MESSAGE_WRITE];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.MESSAGE_WRITE];

   constructor() {
      this._messageController = new MessageController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._messageController.createMessage,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateMessageSchema,
         url: this.path
      });

      fastify.route({
         handler: this._messageController.createMessages,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateMessagesSchema,
         url: '/messages'
      });

      fastify.route({
         handler: this._messageController.getMessages,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetMessagesSchema,
         url: this.path
      });

      fastify.route({
         handler: this._messageController.getMessage,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetMessageSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._messageController.searchMessages,
         method: 'post',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetMessagesSchema,
         url: `${this.path}/search`
      });

      fastify.route({
         handler: this._messageController.getMessage,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetMessageSchema,
         url: `${this.path}/:id/text`
      });

      fastify.route({
         handler: this._messageController.approveMessage,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: ApproveMessageSchema,
         url: `${this.path}/:id/approve`
      });

      fastify.route({
         handler: this._messageController.denyMessage,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: DenyMessageSchema,
         url: `${this.path}/:id/deny`
      });

      done();
   }
}
