import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { ChannelServices } from '../channel/channel-services.js';
import { PaginationResponse } from '../shared/pagination/pagination.schema.js';

const Identifiers = Type.Array(
   Type.Object({
      type: Type.String(),
      value: Type.String()
   })
);

const CreateConnection = Type.Object({
   attributes: Type.Array(
      Type.Object({
         name: Type.String(),
         value: Type.String()
      })
   ),
   channel: Type.Optional(Type.String({ format: 'uuid' })),
   enabled: Type.Optional(Type.Boolean({ default: true })),
   service: Type.String({ enum: ChannelServices }),
   showInPreferences: Type.Optional(Type.Boolean({ default: true }))
});

const UpdateConnection = Type.Object({
   attributes: Type.Array(
      Type.Object({
         id: Type.Optional(Type.String({ format: 'uuid' })),
         name: Type.String(),
         value: Type.String()
      })
   ),
   channel: Type.Optional(Type.String({ format: 'uuid' })),
   enabled: Type.Optional(Type.Boolean()),
   id: Type.Optional(Type.String({ format: 'uuid' })),
   service: Type.String({ enum: ChannelServices }),
   showInPreferences: Type.Optional(Type.Boolean())
});

const CreatedConnection = Type.Object({
   attributes: Type.Array(
      Type.Object({
         id: Type.String({ format: 'uuid' }),
         name: Type.String(),
         value: Type.String()
      })
   ),
   channel: Type.Optional(Type.String()),
   enabled: Type.Boolean(),
   id: Type.String({ format: 'uuid' }),
   service: Type.String({ enum: ChannelServices }),
   showInPreferences: Type.Boolean()
});

export const ReplacePreferencesBody = Type.Object({
   preferences: Type.Array(
      Type.Object({
         connections: Type.Array(Type.String({ format: 'uuid' })),
         optedIn: Type.Optional(Type.Boolean({ default: false })),
         topic: Type.String({ format: 'uuid' })
      })
   )
});

const UpdatePreference = Type.Object({
   connections: Type.Array(Type.String({ format: 'uuid' })),
   id: Type.Optional(Type.String({ format: 'uuid' })),
   optedIn: Type.Optional(Type.Boolean()),
   topic: Type.String({ format: 'uuid' })
});

const CreatedPreference = Type.Object({
   connections: Type.Array(Type.String({ format: 'uuid' })),
   id: Type.String({ format: 'uuid' }),
   optedIn: Type.Optional(Type.Boolean()),
   topic: Type.String({ format: 'uuid' })
});

export const CreateRecipientBody = Type.Object({
   attributes: Type.Optional(
      Type.Array(
         Type.Object({
            name: Type.String(),
            value: Type.Optional(Type.String({ default: '' }))
         })
      )
   ),
   connections: Type.Optional(Type.Array(CreateConnection, { default: [] })),
   enabled: Type.Optional(Type.Boolean()),
   identifiers: Identifiers,
   locale: Type.Optional(Type.String()),
   tags: Type.Optional(Type.Array(Type.String(), { default: [] })),
   timezone: Type.Optional(Type.String())
});

export const CreatedRecipientBody = Type.Object({
   attributes: Type.Array(
      Type.Object({
         name: Type.String(),
         value: Type.Optional(Type.String())
      })
   ),
   connections: Type.Array(CreatedConnection),
   createdAt: Type.String({ format: 'date-time' }),
   enabled: Type.Boolean(),
   id: Type.String({ format: 'uuid' }),
   identifiers: Type.Array(
      Type.Object({
         id: Type.String({ format: 'uuid' }),
         type: Type.String(),
         value: Type.String()
      })
   ),
   locale: Type.String(),
   preferences: Type.Array(CreatedPreference),
   tags: Type.Array(Type.String()),
   timezone: Type.String(),
   updatedAt: Type.String({ format: 'date-time' })
});

export const UpdateRecipientBody = Type.Object({
   attributes: Type.Optional(
      Type.Array(
         Type.Object({
            id: Type.Optional(Type.String({ format: 'uuid' })),
            name: Type.String(),
            value: Type.Optional(Type.String())
         })
      )
   ),
   connections: Type.Optional(Type.Array(UpdateConnection)),
   enabled: Type.Optional(Type.Boolean()),
   id: Type.Optional(Type.String({ format: 'uuid' })),
   identifiers: Type.Optional(
      Type.Array(
         Type.Object({
            id: Type.Optional(Type.String({ format: 'uuid' })),
            type: Type.String(),
            value: Type.String()
         })
      )
   ),
   isServiceAccount: Type.Optional(Type.Boolean()),
   locale: Type.Optional(Type.String()),
   preferences: Type.Optional(Type.Array(UpdatePreference)),
   tags: Type.Optional(Type.Array(Type.String())),
   timezone: Type.Optional(Type.String())
});

export const BulkResponseBody = Type.Object({
   correlationId: Type.String(),
   error: Type.String({ default: '' }),
   id: Type.Optional(Type.String({ format: 'uuid' })),
   success: Type.Boolean()
});

export const BulkResponsesBody = Type.Object({
   items: Type.Array(BulkResponseBody)
});

export const UpsertIdentifierBody = Type.Object({
   correlationId: Type.String(),
   identifiers: Identifiers,
   reenable: Type.Boolean({ default: false })
});

export const UpsertIdentifiersBody = Type.Object({
   items: Type.Array(UpsertIdentifierBody)
});

export const BulkUpsertRecipientBody = Type.Intersect([
   UpdateRecipientBody,
   Type.Object({
      correlationId: Type.String()
   })
]);

export const BulkUpsertBody = Type.Object({
   items: Type.Array(BulkUpsertRecipientBody),
   useDummyData: Type.Optional(Type.Boolean({ default: false }))
});

export const GetRecipientsBody = Type.Object({
   items: Type.Array(CreatedRecipientBody),
   pagination: PaginationResponse
});

export const GetRecipientParams = Type.Object({
   id: Type.String()
});

export const GetRecipientsParams = Type.Object({
   key: Type.Optional(Type.String()),
   size: Type.Optional(Type.Number({ default: 50, maximum: 100, minimum: 1 }))
});

export const GetRecipientSchema: FastifySchema = {
   description: 'Get a recipient by ID',
   params: GetRecipientParams,
   response: {
      200: CreatedRecipientBody
   }
};

export const GetRecipientsSchema: FastifySchema = {
   description: 'Get recipients',
   querystring: GetRecipientsParams,
   response: {
      200: GetRecipientsBody
   }
};

export const CreateRecipientSchema: FastifySchema = {
   body: CreateRecipientBody,
   description: 'Create a recipient',
   response: {
      201: CreatedRecipientBody
   }
};

export const DeleteRecipientSchema: FastifySchema = {
   description: 'Delete a recipient by ID',
   params: GetRecipientParams,
   response: {
      204: Type.Null()
   }
};

export const UpdateRecipientSchema: FastifySchema = {
   body: UpdateRecipientBody,
   description: 'Update a recipient by ID',
   params: GetRecipientParams,
   response: {
      200: CreatedRecipientBody
   }
};

export const ReplacePreferencesSchema: FastifySchema = {
   body: ReplacePreferencesBody,
   description: 'Replace a recipient preferences',
   params: GetRecipientParams,
   response: {
      200: CreatedRecipientBody
   }
};

export const BulkUpsertSchema: FastifySchema = {
   body: BulkUpsertBody,
   description: 'Bulk upsert recipients',
   response: {
      200: BulkResponsesBody
   }
};

export const UpsertIdentifiersSchema: FastifySchema = {
   body: UpsertIdentifiersBody,
   description: 'Upsert identifiers for recipients',
   response: {
      200: BulkResponsesBody
   }
};
