import { FastifyReply, FastifyRequest } from 'fastify';

import {
   <PERSON>ulk<PERSON>psert,
   CreateRecipient,
   GetRecipientParams,
   GetRecipientsParams,
   ReplacePreferences,
   UpdateRecipient,
   UpsertIdentifiers
} from './recipient.interface.js';
import { RecipientService } from './recipient.service.js';

export class RecipientController {
   public createRecipient = async (req: FastifyRequest<{ Body: CreateRecipient }>, reply: FastifyReply) => {
      await this._recipientService.createRecipient(reply, req.tenantId, req.body);
   };

   public getRecipients = async (req: FastifyRequest<{ Querystring: GetRecipientsParams }>, reply: FastifyReply) => {
      await this._recipientService.getRecipients(reply, req.tenantId, false, req.query.key, req.query.size);
   };

   public getRecipient = async (req: FastifyRequest<{ Params: GetRecipientParams }>, reply: FastifyReply) => {
      await this._recipientService.getRecipientById(reply, req.tenantId, req.params.id);
   };

   public getServiceAccounts = async (req: FastifyRequest<{ Params: GetRecipientParams }>, reply: FastifyReply) => {
      await this._recipientService.getRecipients(reply, req.tenantId, true, req.params.id);
   };

   public deleteRecipient = async (req: FastifyRequest<{ Params: GetRecipientParams }>, reply: FastifyReply) => {
      await this._recipientService.deleteRecipient(reply, req.tenantId, req.params.id);
   };

   public updateRecipient = async (
      req: FastifyRequest<{ Body: UpdateRecipient; Params: GetRecipientParams }>,
      reply: FastifyReply
   ) => {
      await this._recipientService.updateRecipient(reply, req.tenantId, req.params.id, req.body);
   };

   public upsertIdentifiers = async (
      req: FastifyRequest<{ Body: UpsertIdentifiers; Params: GetRecipientParams }>,
      reply: FastifyReply
   ) => {
      await this._recipientService.upsertIdentifiers(reply, req.tenantId, req.body);
   };

   public bulkUpsert = async (
      req: FastifyRequest<{ Body: BulkUpsert; Params: GetRecipientParams }>,
      reply: FastifyReply
   ) => {
      await this._recipientService.bulkUpsert(reply, req.tenantId, req.body);
   };

   public replacePreferences = async (
      req: FastifyRequest<{ Body: ReplacePreferences; Params: GetRecipientParams }>,
      reply: FastifyReply
   ) => {
      await this._recipientService.replacePreferences(reply, req.tenantId, req.params.id, req.body);
   };

   private _recipientService = new RecipientService();
}
