import { Static } from '@fastify/type-provider-typebox';

import {
   BulkResponseBody,
   BulkResponsesBody,
   BulkUpsertBody,
   CreatedRecipientBody,
   CreateRecipientBody,
   GetRecipientParams,
   GetRecipientsBody,
   GetRecipientsParams,
   ReplacePreferencesBody,
   UpdateRecipientBody,
   UpsertIdentifierBody,
   UpsertIdentifiersBody
} from './recipient.schema.js';

export type BulkResponse = Static<typeof BulkResponseBody>;
export type BulkResponses = Static<typeof BulkResponsesBody>;
export type BulkUpsert = Static<typeof BulkUpsertBody>;
export type ReplacePreferences = Static<typeof ReplacePreferencesBody>;
export type CreateRecipient = Static<typeof CreateRecipientBody>;
export type CreatedRecipient = Static<typeof CreatedRecipientBody>;
export type GetRecipients = Static<typeof GetRecipientsBody>;
export type GetRecipientParams = Static<typeof GetRecipientParams>;
export type GetRecipientsParams = Static<typeof GetRecipientsParams>;
export type UpdateRecipient = Static<typeof UpdateRecipientBody>;
export type UpsertIdentifier = Static<typeof UpsertIdentifierBody>;
export type UpsertIdentifiers = Static<typeof UpsertIdentifiersBody>;
