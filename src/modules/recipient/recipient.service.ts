import { Prisma, PrismaClient } from '@prisma/client';
import { Value } from '@sinclair/typebox/value';
import { AttributeConstants, CryptoService, getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';
import { LRUCache } from 'lru-cache';
import { v7 as uuid } from 'uuid';
import validator from 'validator';

import { RecipientPaginationOptions } from '../../interfaces/pagination-options.js';

import {
   BulkResponse,
   BulkUpsert,
   CreatedRecipient,
   CreateRecipient,
   ReplacePreferences,
   UpdateRecipient,
   UpsertIdentifiers
} from './recipient.interface.js';
import { CreateRecipientBody } from './recipient.schema.js';

type Connection = {
   attributes: Array<{
      name: string;
      value: string;
   }>;

   service: string;
};

type Recipient = Prisma.RecipientGetPayload<{
   include: {
      attributes: true;
      connections: {
         include: {
            attributes: true;
         };
      };
      identifiers: true;
      preferences: {
         include: {
            connections: { select: { id: true } };
            optedIn: true;
            topic: { select: { id: true } };
         };
      };
      tags: { select: { id: true } };
   };
}>;

type Attribute = {
   name: string;
   value: string | undefined;
};

export class RecipientService {
   private _cryptoService: CryptoService = CryptoService.getInstance();

   private _encryptionKeyIdCache = new LRUCache<string, string>({
      allowStale: false,
      max: 500,
      ttl: 1000 * 60 * 15
   });

   private async _getEncryptionKeyId(tenantId: string): Promise<string> {
      if (this._encryptionKeyIdCache.has(tenantId)) {
         return this._encryptionKeyIdCache.get(tenantId)!;
      }

      const prisma = await getPrismaClient(tenantId);
      const tenant = await prisma.tenant.findFirst({
         select: { encryptionKeyId: true },
         where: { id: tenantId }
      });

      if (!tenant) {
         throw new Error(`Encryption key not found for tenant ${tenantId}`);
      }

      this._encryptionKeyIdCache.set(tenantId, tenant.encryptionKeyId);

      return tenant.encryptionKeyId;
   }

   getRecipientById = async (reply: FastifyReply, tenantId: string, recipientId: string) => {
      const prisma = await getPrismaClient(tenantId);
      const recipient = await prisma.recipient.findFirst({
         include: {
            attributes: true,
            connections: {
               include: {
                  attributes: true
               }
            },
            identifiers: true,
            preferences: {
               include: {
                  connections: { select: { id: true } },
                  topic: { select: { id: true } }
               }
            },
            tags: { select: { id: true } }
         },
         where: {
            OR: [
               { id: recipientId },
               { identifiers: { some: { value: { equals: recipientId, mode: 'insensitive' } } } }
            ],
            tenantId
         }
      });

      if (!recipient) {
         return reply.status(404).send({
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: 'Recipient not found',
            statusCode: 404
         });
      }

      const converted = await this._convertToRecipient(tenantId, recipient);

      return reply.send(converted);
   };

   getRecipients = async (
      reply: FastifyReply,
      tenantId: string,
      onlyServiceAccounts?: boolean,
      key?: string,
      size: number = 50
   ) => {
      const options: RecipientPaginationOptions = {
         orderBy: { id: 'asc' },
         where: {
            tenantId
         }
      };

      if (key) {
         options.where = {
            ...options.where,
            id: {
               gte: key
            },
            tenantId
         };
      }

      const serviceAccount: string = AttributeConstants.SERVICE_ACCOUNT;

      if (onlyServiceAccounts) {
         options.where = {
            ...options.where,
            identifiers: {
               some: {
                  type: serviceAccount
               }
            }
         };
      }

      const prisma = await getPrismaClient(tenantId);
      const count = await prisma.recipient.count({ where: { ...options.where } });
      options.take = size + 1;

      const recipients = await prisma.recipient.findMany({
         ...options,
         include: {
            attributes: true,
            connections: { include: { attributes: true } },
            identifiers: true,
            preferences: { include: { connections: { select: { id: true } }, topic: { select: { id: true } } } },
            tags: true
         }
      });
      let nextKey = '';

      if (recipients.length > size) {
         const last = recipients[size];
         nextKey = last.id;
         recipients.pop();
      }

      const converted = await Promise.all(recipients.map((recipient) => this._convertToRecipient(tenantId, recipient)));

      const response = {
         items: converted,
         pagination: {
            // eslint-disable-next-line camelcase
            next_key: nextKey,
            // eslint-disable-next-line camelcase
            page_size: size,
            // eslint-disable-next-line camelcase
            total_records: count
         }
      };

      return reply.send(response);
   };

   private _replaceWithDummyData = (prisma: PrismaClient, service: string, data: Attribute[]): void => {
      switch (service) {
         case AttributeConstants.EMAIL:
            for (const attribute of data) {
               if (attribute.name === AttributeConstants.EMAIL) {
                  attribute.value = '<EMAIL>';
               }
            }

            break;
         case AttributeConstants.SMS:
         case AttributeConstants.WHATSAPP:
            for (const attribute of data) {
               if (attribute.name === AttributeConstants.PHONE) {
                  attribute.value = '15555555555';
               }
            }

            break;
         case AttributeConstants.CANVAS:
         case AttributeConstants.MODO:
            for (const attribute of data) {
               attribute.value = '-99999';
            }

            break;
      }
   };

   // TODO: Refactor this method to reduce complexity
   // eslint-disable-next-line complexity
   private _validateRecipient = async (
      log: FastifyReply['log'],
      tenantId: string,
      body: CreateRecipient | UpdateRecipient,
      existing?: Recipient
   ): Promise<{
      error?: {
         code: string;
         error: string;
         message: string;
         statusCode: number;
      };
      valid: boolean;
   }> => {
      try {
         // must have at least one identifier
         if (body.identifiers && body.identifiers.length === 0) {
            return {
               error: {
                  code: 'FST_ERR_VALIDATION',
                  error: 'Bad Request',
                  message: 'No identifiers provided',
                  statusCode: 400
               },
               valid: false
            };
         }

         const prisma = await getPrismaClient(tenantId);

         if (body.identifiers) {
            // check for duplicate identifier types
            if (this._hasDuplicateIdentifierTypes(body.identifiers)) {
               return {
                  error: {
                     code: 'FST_ERR_VALIDATION',
                     error: 'Bad Request',
                     message: 'Duplicate identifier types',
                     statusCode: 400
                  },
                  valid: false
               };
            }

            // check if a service account and other identifier type is provided
            if (body.identifiers.some((identifier) => identifier.type === 'service-account')) {
               if (body.identifiers.some((identifier) => identifier.type !== 'service-account')) {
                  return {
                     error: {
                        code: 'FST_ERR_VALIDATION',
                        error: 'Bad Request',
                        message: 'Service account identifier cannot be mixed with other identifier types',
                        statusCode: 400
                     },
                     valid: false
                  };
               }
            }

            // identifiers must be unique across recipients in the tenant
            const identifiers = body.identifiers.map((item): string => item.value.toLowerCase());
            const ids = [...new Set(identifiers)];
            const where: Prisma.RecipientWhereInput = {
               identifiers: {
                  some: {
                     value: {
                        in: ids,
                        mode: 'insensitive'
                     }
                  }
               },
               tenantId
            };

            if (existing) {
               where.NOT = {
                  id: existing.id
               };
            }

            const other = await prisma.recipient.findFirst({
               where
            });

            if (other) {
               return {
                  error: {
                     code: 'FST_ERR_CONFLICT',
                     error: 'Conflict',
                     message: 'Recipient already exists with one of the given identifiers',
                     statusCode: 409
                  },
                  valid: false
               };
            }
         }

         if (body.attributes) {
            const [hasDuplicateAttributes, duplicateAttributeName] = this._hasDuplicateAttributes(body.attributes);

            if (hasDuplicateAttributes) {
               return {
                  error: {
                     code: 'FST_ERR_CONFLICT',
                     error: 'Bad Request',
                     message: `Duplicate attribute name "${duplicateAttributeName}"`,
                     statusCode: 400
                  },
                  valid: false
               };
            }
         }

         if (existing && 'preferences' in body) {
            const topicsIds = new Set();

            for (const preference of body.preferences!) {
               if (topicsIds.has(preference.topic)) {
                  return {
                     error: {
                        code: 'FST_ERR_VALIDATION',
                        error: 'Bad Request',
                        message: `Duplicate topic preference ${preference.topic}`,
                        statusCode: 400
                     },
                     valid: false
                  };
               }

               topicsIds.add(preference.topic);
            }

            for (const preference of body.preferences!) {
               for (const connection of preference.connections) {
                  const existingPreference = await prisma.connection.findFirst({
                     where: {
                        id: connection,
                        recipientId: existing.id
                     }
                  });

                  if (!existingPreference) {
                     return {
                        error: {
                           code: 'FST_ERR_VALIDATION',
                           error: 'Bad Request',
                           message: `Connection ${connection} does not exist for recipient ${existing.id}`,
                           statusCode: 400
                        },
                        valid: false
                     };
                  }
               }
            }
         }

         if (body.connections) {
            const uniquePairs = new Set<string>();

            for (const connection of body.connections) {
               const value = connection.service.toLowerCase();

               if (uniquePairs.has(value)) {
                  return {
                     error: {
                        code: 'FST_ERR_VALIDATION',
                        error: 'Bad Request',
                        message: `Duplicate service (${connection.service})`,
                        statusCode: 400
                     },
                     valid: false
                  };
               }

               uniquePairs.add(value);

               const [hasDuplicateAttributes, duplicateAttributeName] = this._hasDuplicateAttributes(
                  connection.attributes
               );

               if (hasDuplicateAttributes) {
                  return {
                     error: {
                        code: 'FST_ERR_CONFLICT',
                        error: 'Bad Request',
                        // eslint-disable-next-line max-len
                        message: `Duplicate attribute name "${duplicateAttributeName}" for channel ${connection.service}`,
                        statusCode: 400
                     },
                     valid: false
                  };
               }

               const [valid, error] = await this._validateConnection(prisma, connection);

               if (!valid) {
                  return {
                     error: {
                        code: 'FST_ERR_VALIDATION',
                        error: 'Bad Request',
                        message: error ?? 'unknown error',
                        statusCode: 400
                     },
                     valid: false
                  };
               }
            }
         }

         if (existing && 'preferences' in body && body.connections) {
            for (const preference of body.preferences!) {
               for (const connection of preference.connections) {
                  const existingConnection = body.connections.find((c) => c.id === connection);

                  if (!existingConnection) {
                     return {
                        error: {
                           code: 'FST_ERR_VALIDATION',
                           error: 'Bad Request',
                           message: `Connection ${connection} does not exist for recipient ${existing.id}`,
                           statusCode: 400
                        },
                        valid: false
                     };
                  }
               }
            }
         }

         return { valid: true };
      } catch (error) {
         log.error(error);

         return {
            error: {
               code: 'FST_ERR_INTERNAL_SERVER_ERROR',
               error: 'Internal Server Error',
               message: 'Internal Server Error',
               statusCode: 500
            },
            valid: false
         };
      }
   };

   // eslint-disable-next-line complexity
   private _validateConnection = async (prisma: PrismaClient, connection: Connection): Promise<[boolean, string?]> => {
      const service = connection.service;

      if ('channel' in connection && connection.channel) {
         const exists = await prisma.channel.findFirst({
            where: {
               id: connection.channel
            }
         });

         if (!exists) {
            return [false, 'Channel not found'];
         }
      }

      switch (service) {
         case AttributeConstants.EMAIL: {
            const email = connection.attributes.find((a) => a.name === AttributeConstants.EMAIL);

            if (!email) {
               return [false, `${AttributeConstants.EMAIL} attribute is required for service ${service}`];
            }

            if (!validator.isEmail(email.value)) {
               return [false, `${email.value} is invalid for service ${service}`];
            }

            return [true];
         }

         case AttributeConstants.CANVAS: {
            const canvas = connection.attributes.find((a) => a.name === AttributeConstants.CANVAS_ID);

            if (!canvas) {
               return [false, `${AttributeConstants.CANVAS_ID} attribute is required for service ${service}`];
            }

            return [true];
         }

         case AttributeConstants.MODO: {
            const modo = connection.attributes.find((a) => a.name === AttributeConstants.MODO_ID);

            if (!modo) {
               return [false, `${AttributeConstants.MODO_ID} attribute is required for service ${service}`];
            }

            return [true];
         }

         case AttributeConstants.MS_TEAMS: {
            const conversationId = connection.attributes.find((a) => a.name === AttributeConstants.UPN);

            if (!conversationId) {
               return [false, `${AttributeConstants.UPN} attribute is required for service ${service}`];
            }

            return [true];
         }

         case AttributeConstants.MS_TEAMS_WEBHOOK: {
            const webhook = connection.attributes.find((a) => a.name === AttributeConstants.WEBHOOK_URL);

            if (!webhook) {
               return [false, `${AttributeConstants.WEBHOOK_URL} attribute is required for service ${service}`];
            }

            return [true];
         }

         case AttributeConstants.SLACK_WEBHOOK: {
            const webhook = connection.attributes.find((a) => a.name === AttributeConstants.WEBHOOK_URL);

            if (!webhook) {
               return [false, `${AttributeConstants.WEBHOOK_URL} attribute is required for service ${service}`];
            }

            return [true];
         }

         case AttributeConstants.SMS: {
            const phone = connection.attributes.find((a) => a.name === AttributeConstants.PHONE);

            if (!phone) {
               return [false, `${AttributeConstants.PHONE} attribute is required for service ${service}`];
            }

            if (!validator.isMobilePhone(phone.value)) {
               return [false, `${phone.value} is invalid for service `];
            }

            return [true];
         }

         case AttributeConstants.TEST:
            return [true];

         default:
            return [false, `Service (${service}) not supported`];
      }
   };

   private _hasDuplicateIdentifierTypes = (identifiers: Array<{ type: string; value: string }>): boolean => {
      const typeSet = new Set();

      for (const identifier of identifiers) {
         if (typeSet.has(identifier.type)) {
            return true;
         }

         typeSet.add(identifier.type);
      }

      return false;
   };

   private _hasDuplicateAttributes = (data: Array<{ name: string; value?: string }>): [boolean, string?] => {
      const unique = new Set();

      for (const item of data) {
         if (unique.has(item.name)) {
            return [true, item.name];
         }

         unique.add(item.name);
      }

      return [false];
   };

   private _createRecipient = async (
      tenantId: string,
      body: CreateRecipient,
      prisma: PrismaClient
   ): Promise<CreatedRecipient> => {
      const recipientId = uuid();

      await prisma.recipient.create({
         data: {
            attributes: {
               create: body.attributes
            },
            enabled: body.enabled,
            id: recipientId,
            identifiers: {
               create: body.identifiers
            },
            locale: body.locale,
            tags: {
               connect: body.tags?.map((tag) => ({ id: tag }))
            },
            tenantId,
            timezone: body.timezone
         }
      });

      if (body.connections) {
         const promises = body.connections.map(async (connection) => {
            const attributes = await Promise.all(
               connection.attributes.map(async (attribute) => {
                  const keyId = await this._getEncryptionKeyId(tenantId);
                  const encryptedValue = await this._cryptoService.encrypt(tenantId, keyId, attribute.value);

                  return {
                     id: uuid(),
                     name: attribute.name,
                     value: encryptedValue
                  };
               })
            );

            return prisma.connection.create({
               data: {
                  attributes: {
                     createMany: {
                        data: attributes
                     }
                  },
                  enabled: connection.enabled,
                  id: uuid(),
                  recipientId,
                  service: connection.service,
                  showInPreferences: connection.showInPreferences
               }
            });
         });

         await Promise.all(promises);
      }

      const created = await prisma.recipient.findUniqueOrThrow({
         include: {
            attributes: true,
            connections: {
               include: {
                  attributes: true
               }
            },
            identifiers: true,
            preferences: {
               include: {
                  connections: { select: { id: true } },
                  topic: { select: { id: true } }
               }
            },
            tags: { select: { id: true } }
         },
         where: {
            id: recipientId
         }
      });

      return await this._convertToRecipient(tenantId, created);
   };

   public async createRecipient(reply: FastifyReply, tenantId: string, body: CreateRecipient) {
      const prisma = await getPrismaClient(tenantId);

      // must have at least one identifier
      if (body.identifiers.length === 0) {
         return reply.status(400).send({
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'No identifiers provided',
            statusCode: 400
         });
      }

      const validation = await this._validateRecipient(reply.log, tenantId, body);

      if (validation.error) {
         return reply.status(validation.error.statusCode).send(validation.error);
      }

      // If all validations pass, create the recipient
      const recipient = await this._createRecipient(tenantId, body, prisma);

      return reply.status(201).send(recipient);
   }

   public async deleteRecipient(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);
      const existing = await prisma.recipient.findUnique({
         where: { id, tenantId }
      });

      if (!existing) {
         return reply.status(404).send({
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: 'Recipient not found',
            statusCode: 404
         });
      }

      await prisma.recipient.delete({
         where: { id, tenantId }
      });

      return reply.status(204).send();
   }

   public async upsertIdentifiers(reply: FastifyReply, tenantId: string, body: UpsertIdentifiers) {
      // TODO: can we make this async? the problem is that the same identifier can be in more than item
      // and the identifier is unique across recipients (a recipient can the same identifier value for different types)
      const prisma = await getPrismaClient(tenantId);

      const returnValues: BulkResponse[] = [];

      for (const item of body.items) {
         try {
            const identifiers = item.identifiers.map((identifier) => identifier.value);
            const recipients = await prisma.recipient.findMany({
               where: {
                  identifiers: {
                     some: {
                        value: {
                           in: identifiers
                        }
                     }
                  },
                  tenantId
               }
            });

            if (recipients.length > 1) {
               returnValues.push({
                  correlationId: item.correlationId,
                  error: 'Identifiers are not unique across recipients',
                  success: false
               });
               continue;
            }

            if (recipients.length === 1) {
               const recipient = recipients[0];

               for (const identifier of item.identifiers) {
                  const recipientIdentifier = await prisma.identifier.findFirst({
                     where: {
                        recipientId: recipient.id,
                        type: identifier.type
                     }
                  });

                  if (recipientIdentifier) {
                     await prisma.identifier.update({
                        data: {
                           value: identifier.value
                        },
                        where: {
                           id: recipientIdentifier.id
                        }
                     });
                  } else {
                     await prisma.identifier.create({
                        data: {
                           recipientId: recipient.id,
                           type: identifier.type,
                           value: identifier.value
                        }
                     });
                  }
               }

               returnValues.push({
                  correlationId: item.correlationId,
                  error: '',
                  id: recipient.id,
                  success: true
               });
            } else {
               const created = await prisma.recipient.create({
                  data: {
                     identifiers: {
                        createMany: {
                           data: item.identifiers
                        }
                     },
                     tenantId
                  }
               });

               returnValues.push({
                  correlationId: item.correlationId,
                  error: '',
                  id: created.id,
                  success: true
               });
            }
         } catch (error) {
            reply.log.error(
               {
                  data: { error },
                  name: 'upsertIdentifiers',
                  type: 'recipientService'
               },
               'recipientService - upsertIdentifiers error'
            );
            const value: BulkResponse = {
               correlationId: item.correlationId,
               error: 'server error adding identifiers',
               success: false
            };
            returnValues.push(value);
         }
      }

      return reply.send({ items: returnValues });
   }

   public async replacePreferences(reply: FastifyReply, tenantId: string, id: string, body: ReplacePreferences) {
      const prisma = await getPrismaClient(tenantId);
      const existing = await prisma.recipient.findUnique({
         where: { id, tenantId }
      });

      if (!existing) {
         return reply.status(404).send({
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: 'Recipient not found',
            statusCode: 404
         });
      }

      await prisma.topicPreference.deleteMany({
         where: { recipientId: id }
      });

      for (const preference of body.preferences) {
         const data = {
            connections: {
               connect: preference.connections.map((connection) => ({ id: connection }))
            },
            optedIn: preference.optedIn,
            recipientId: id,
            topicId: preference.topic
         };

         await prisma.topicPreference.create({
            data
         });
      }

      try {
         const updated = await prisma.recipient.findUniqueOrThrow({
            include: {
               attributes: true,
               connections: {
                  include: {
                     attributes: true
                  }
               },
               identifiers: true,
               preferences: {
                  include: {
                     connections: { select: { id: true } },
                     topic: { select: { id: true } }
                  }
               },
               tags: { select: { id: true } }
            },
            where: { id, tenantId }
         });

         return await reply.send(await this._convertToRecipient(tenantId, updated));
      } catch (error) {
         reply.log.error(
            {
               data: { error },
               name: 'replacePreferences',
               type: 'recipientService'
            },
            'recipientService - replacePreferences error'
         );

         return reply.status(500).send({
            code: 'FST_ERR_INTERNAL_SERVER_ERROR',
            error: 'Internal Server Error',
            message: 'Internal Server Error',
            statusCode: 500
         });
      }
   }

   public async updateRecipient(reply: FastifyReply, tenantId: string, id: string, body: UpdateRecipient) {
      const prisma = await getPrismaClient(tenantId);
      const existing = await prisma.recipient.findUnique({
         include: {
            attributes: true,
            connections: {
               include: {
                  attributes: true
               }
            },
            identifiers: true,
            preferences: {
               include: {
                  connections: { select: { id: true } },
                  topic: { select: { id: true } }
               }
            },
            tags: { select: { id: true } }
         },
         where: { id, tenantId }
      });

      if (!existing) {
         return reply.status(404).send({
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: 'Recipient not found',
            statusCode: 404
         });
      }

      const validation = await this._validateRecipient(reply.log, tenantId, body, existing);

      if (validation.error) {
         return reply.status(validation.error.statusCode).send(validation.error);
      }

      // If all validations pass, update the recipient
      const recipient = await this._updateRecipient(tenantId, id, body, existing, prisma);

      return reply.send(recipient);
   }

   // TODO: simplify this method
   public async bulkUpsert(reply: FastifyReply, tenantId: string, body: BulkUpsert) {
      const prisma = await getPrismaClient(tenantId);

      const returnValues: BulkResponse[] = [];

      /* doing this sequentially, when doing in async, we were getting a deadlock
         TODO: fix deadlock issue and make this async */
      for (const item of body.items) {
         try {
            if (body.useDummyData) {
               for (const connection of item.connections ?? []) {
                  this._replaceWithDummyData(prisma, connection.service, connection.attributes);
               }
            }

            const correlationId = item.correlationId;

            /* Recipient ID was given */
            if (item.id) {
               const existing = await prisma.recipient.findUnique({
                  include: {
                     attributes: true,
                     connections: {
                        include: {
                           attributes: true
                        }
                     },
                     identifiers: true,
                     preferences: {
                        include: {
                           connections: { select: { id: true } },
                           topic: { select: { id: true } }
                        }
                     },
                     tags: { select: { id: true } }
                  },
                  where: { id: item.id, tenantId }
               });

               if (!existing) {
                  returnValues.push({
                     correlationId,
                     error: 'Recipient not found',
                     success: false
                  });
                  continue;
               }

               const validation = await this._validateRecipient(reply.log, tenantId, item, existing);

               if (validation.error) {
                  returnValues.push({
                     correlationId,
                     error: validation.error.message,
                     success: false
                  });
                  continue;
               }

               await this._updateRecipient(tenantId, item.id, item, existing, prisma);

               returnValues.push({
                  correlationId,
                  error: '',
                  id: item.id,
                  success: true
               });
               continue;
            }

            /* See if the recipient already exists */
            const identifiers = item.identifiers?.map((identifier) => identifier.value) ?? [];
            const existing = await prisma.recipient.findMany({
               include: {
                  attributes: true,
                  connections: {
                     include: {
                        attributes: true
                     }
                  },
                  identifiers: true,
                  preferences: {
                     include: {
                        connections: { select: { id: true } },
                        topic: { select: { id: true } }
                     }
                  },
                  tags: { select: { id: true } }
               },
               where: { identifiers: { some: { value: { in: identifiers } } }, tenantId }
            });

            if (existing.length > 1) {
               returnValues.push({
                  correlationId,
                  error: 'Identifiers are not unique across recipients',
                  success: false
               });
               continue;
            }

            if (existing.length === 1) {
               const validation = await this._validateRecipient(reply.log, tenantId, item, existing[0]);

               if (validation.error) {
                  returnValues.push({
                     correlationId,
                     error: validation.error.message,
                     success: false
                  });
                  continue;
               }

               await this._updateRecipient(tenantId, existing[0].id, item, existing[0], prisma);

               returnValues.push({
                  correlationId,
                  error: '',
                  id: existing[0].id,
                  success: true
               });
               continue;
            }

            /* Create the recipient */
            const validation = await this._validateRecipient(reply.log, tenantId, item);

            if (validation.error) {
               returnValues.push({
                  correlationId,
                  error: validation.error.message,
                  success: false
               });
               continue;
            }

            const recipient = Value.Clean(CreateRecipientBody, item) as CreateRecipient;
            const created = await this._createRecipient(tenantId, recipient, prisma);

            returnValues.push({
               correlationId,
               error: '',
               id: created.id,
               success: true
            });
            continue;
         } catch (error) {
            reply.log.error(
               {
                  data: { error },
                  name: 'bulkUpsert',
                  type: 'recipientService'
               },
               'recipientService - bulkUpsert error'
            );

            returnValues.push({
               correlationId: item.correlationId,
               error: 'server error adding upserting recipient',
               success: false
            });
            continue;
         }
      }

      return reply.send({ items: returnValues });
   }

   private async _convertToRecipient(tenantId: string, data: Recipient): Promise<CreatedRecipient> {
      const converted: CreatedRecipient = {
         attributes: data.attributes,
         connections: await Promise.all(
            data.connections.map(async (connection) => ({
               attributes: await Promise.all(
                  connection.attributes.map(async (attribute) => ({
                     id: attribute.id,
                     name: attribute.name,
                     value: await this._cryptoService.decrypt(tenantId, attribute.value)
                  }))
               ),
               channelId: connection.channelId,
               createdAt: connection.createdAt,
               enabled: connection.enabled,
               id: connection.id,
               service: connection.service,
               showInPreferences: connection.showInPreferences,
               updatedAt: connection.updatedAt
            }))
         ),
         createdAt: data.createdAt.toISOString(),
         enabled: data.enabled,
         id: data.id,
         identifiers: data.identifiers,
         locale: data.locale,
         preferences: data.preferences.map((preference) => ({
            connections: preference.connections.map((connection) => connection.id),
            id: preference.id,
            optedIn: preference.optedIn,
            topic: preference.topicId
         })),
         tags: data.tags.map((tag) => tag.id),
         timezone: data.timezone,
         updatedAt: data.updatedAt.toISOString()
      };

      return converted;
   }

   public async _updateRecipient(
      tenantId: string,
      id: string,
      body: UpdateRecipient,
      existing: Recipient,
      prisma: PrismaClient
   ): Promise<object> {
      const recipientData: Prisma.RecipientUpdateInput = {};

      if (body.timezone) {
         recipientData.timezone = body.timezone;
      } else {
         recipientData.timezone = existing.timezone;
      }

      if (body.locale) {
         recipientData.locale = body.locale;
      } else {
         recipientData.locale = existing.locale;
      }

      if ('enabled' in body) {
         recipientData.enabled = body.enabled;
      } else {
         recipientData.enabled = existing.enabled;
      }

      await prisma.$transaction(async (trx) => {
         await trx.recipient.update({ data: recipientData, where: { id, tenantId } });

         if (body.identifiers) {
            await trx.identifier.deleteMany({
               where: { recipientId: existing.id }
            });

            await trx.identifier.createMany({
               data: body.identifiers.map((identifier) => ({
                  id: identifier.id ?? uuid(),
                  recipientId: existing.id,
                  type: identifier.type,
                  value: identifier.value
               }))
            });
         }

         if (body.attributes) {
            await trx.recipientAttribute.deleteMany({
               where: { recipientId: existing.id }
            });

            await trx.recipientAttribute.createMany({
               data: body.attributes.map((attribute) => ({
                  id: attribute.id ?? uuid(),
                  name: attribute.name,
                  recipientId: existing.id,
                  value: attribute.value
               }))
            });
         }

         if (body.tags) {
            for (const tag of existing.tags) {
               await trx.tag.update({
                  data: {
                     recipients: {
                        disconnect: { id: existing.id }
                     }
                  },
                  where: { id: tag.id }
               });
            }

            for (const tag of body.tags) {
               await trx.tag.update({
                  data: {
                     recipients: {
                        connect: { id: existing.id }
                     }
                  },
                  where: { id: tag }
               });
            }
         }

         if (body.preferences) {
            await trx.topicPreference.deleteMany({
               where: { recipientId: existing.id }
            });

            for (const preference of body.preferences) {
               const connections = preference.connections.map((connection) => ({
                  id: connection
               }));

               preference.id ??= uuid();

               await trx.topicPreference.upsert({
                  create: {
                     connections: {
                        connect: connections
                     },
                     id: preference.id,
                     optedIn: preference.optedIn,
                     recipientId: existing.id,
                     topicId: preference.topic
                  },
                  update: {
                     connections: {
                        connect: connections
                     },
                     optedIn: preference.optedIn,
                     topicId: preference.topic
                  },
                  where: {
                     id: preference.id,
                     recipientId: existing.id
                  }
               });
            }
         }

         if (body.connections) {
            await trx.connection.deleteMany({
               where: { recipientId: existing.id }
            });

            for (const connection of body.connections) {
               connection.id ??= uuid();

               await trx.connection.create({
                  data: {
                     enabled: connection.enabled,
                     id: connection.id,
                     recipientId: existing.id,
                     service: connection.service,
                     showInPreferences: connection.showInPreferences
                  }
               });

               const attributes = await Promise.all(
                  connection.attributes.map(async (attribute) => ({
                     id: attribute.id ?? uuid(),
                     name: attribute.name,
                     value: await this._cryptoService.encrypt(
                        tenantId,
                        await this._getEncryptionKeyId(tenantId),
                        attribute.value
                     )
                  }))
               );

               for (const attribute of attributes) {
                  await trx.connectionAttribute.create({
                     data: {
                        connectionId: connection.id,
                        id: attribute.id,
                        name: attribute.name,
                        value: attribute.value
                     }
                  });
               }
            }
         }
      });

      const recipient = await prisma.recipient.findUniqueOrThrow({
         include: {
            attributes: true,
            connections: {
               include: {
                  attributes: true
               }
            },
            identifiers: true,
            preferences: {
               include: {
                  connections: { select: { id: true } },
                  topic: { select: { id: true } }
               }
            },
            tags: { select: { id: true } }
         },
         where: { id, tenantId }
      });

      return await this._convertToRecipient(tenantId, recipient);
   }
}
