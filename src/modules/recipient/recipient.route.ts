import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { RecipientController } from './recipient.controller.js';
import {
   BulkUpsertSchema,
   CreateRecipientSchema,
   DeleteRecipientSchema,
   GetRecipientSchema,
   GetRecipientsSchema,
   ReplacePreferencesSchema,
   UpdateRecipientSchema,
   UpsertIdentifiersSchema
} from './recipient.schema.js';

export class RecipientRoute implements Route {
   public path = '/recipient';
   private _recipientController: RecipientController;
   private _readScopes = [Scopes.ADMIN_READ, Scopes.ADMIN_WRITE, Scopes.RECIPIENT_READ, Scopes.RECIPIENT_WRITE];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.RECIPIENT_WRITE];

   constructor() {
      this._recipientController = new RecipientController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._recipientController.createRecipient,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateRecipientSchema,
         url: this.path
      });

      fastify.route({
         handler: this._recipientController.getRecipients,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetRecipientsSchema,
         url: this.path
      });

      fastify.route({
         handler: this._recipientController.getServiceAccounts,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetRecipientsSchema,
         url: '/service-accounts'
      });

      fastify.route({
         handler: this._recipientController.getRecipient,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetRecipientSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._recipientController.deleteRecipient,
         method: 'delete',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: DeleteRecipientSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._recipientController.upsertIdentifiers,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: UpsertIdentifiersSchema,
         url: `${this.path}/upsert-identifiers`
      });

      fastify.route({
         handler: this._recipientController.bulkUpsert,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: BulkUpsertSchema,
         url: `${this.path}/bulk-upsert`
      });

      fastify.route({
         handler: this._recipientController.replacePreferences,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: ReplacePreferencesSchema,
         url: `${this.path}/:id/preferences`
      });

      fastify.route({
         handler: this._recipientController.updateRecipient,
         method: 'patch',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: UpdateRecipientSchema,
         url: `${this.path}/:id`
      });

      /*

 fastify.route({
               method: 'post',
               url: `${this.path}/:id/tag{:tagId}`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.addConnection
            });

            fastify.route({
               method: 'post',
               url: `${this.path}/:id/connections`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.addConnection
            });

            fastify.route({
               method: 'patch',
               url: `${this.path}/:id/connections/:connectionId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.replaceConnection
            });

            fastify.route({
               method: 'delete',
               url: `${this.path}/:id/connections/:connectionId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.deleteConnection
            });

          fastify.route({
               method: 'post',
               url: `${this.path}/:id/attributes`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.addAttributes
            });

            fastify.route({
               method: 'patch',
               url: `${this.path}/:id/attributes/:attributeId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.replaceAttributes
            });

            fastify.route({
               method: 'delete',
               url: `${this.path}/:id/attributes/:attributeId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.deleteAttributes
            });

               fastify.route({
               method: 'post',
               url: `${this.path}/:id/identifiers`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.addIdentifiers
            });

            fastify.route({
               method: 'patch',
               url: `${this.path}/:id/identifiers/:identifierId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.replaceIdentifiers
            });

            fastify.route({
               method: 'delete',
               url: `${this.path}/:id/identifiers/:identifierId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.deleteIdentifiers
            });

            fastify.route({
               method: 'post',
               url: `${this.path}/:id/preferences`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.addIdentifiers
            });

            fastify.route({
               method: 'patch',
               url: `${this.path}/:id/preferences/:preferencesId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.replaceIdentifiers
            });

            fastify.route({
               method: 'delete',
               url: `${this.path}/:id/preferences/:preferencesId`,
               schema: GetRecipientSchema,
               preHandler: fastify.authenticate(this._readScopes),
               handler: this._recipientController.deleteIdentifiers
            });

*/

      done();
   }
}
