import { TableClient } from '@azure/data-tables';
import { DefaultAzureCredential } from '@azure/identity';
import { TrackedLink } from '@prisma/client';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';

type LinkEntity = {
   hash: string;
   partitionKey: string;
   rowKey: string;
   tenantId: string;
   url: string;
};

export class ShortLinkService {
   private _tableClient: TableClient;
   private _tableName = process.env.SHORT_LINK_TABLE_NAME ?? '';
   private _tableUrl = process.env.SHORT_LINK_TABLE_URL ?? '';

   constructor() {
      const credential = new DefaultAzureCredential();
      this._tableClient = new TableClient(this._tableUrl, this._tableName, credential);
   }

   private async _getEntity(
      partitionKey: string,
      rowKey: string
   ): Promise<LinkEntity | null> {
      try {
         return await this._tableClient.getEntity(partitionKey, rowKey) as LinkEntity;
      } catch (error: unknown) {

         if ((error as {statusCode?: number}).statusCode === 404) {
            return null; // Entity does not exist
         }

         throw error; // Re-throw other errors
      }
   }

   public async getLinkByHash(reply: FastifyReply, hash: string) {
      const link = await this._getEntity(hash, '');

      if (!link) {
         return reply.status(404).send('Not found');
      }

      if (link.tenantId) {
         const tenantId = link.tenantId;
         const prisma = await getPrismaClient(tenantId);
         const trackedLink = await prisma.trackedLink.findFirst({
            where: {
               hash
            }
         });

         if (trackedLink) {
            const data: Partial<TrackedLink> = {
               clickCount: trackedLink.clickCount + 1,
               firstClick: trackedLink.firstClick ?? new Date(),
               lastClick: new Date()
            };

            await prisma.trackedLink.update({
               data,
               where: {
                  id: trackedLink.id,
                  tenantId
               }
            });
         }
      }

      return reply.redirect(link.url, 302);
   }
}
