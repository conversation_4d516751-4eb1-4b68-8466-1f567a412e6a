import { FastifyReply, FastifyRequest } from 'fastify';

import { GetByHash } from './short-link.interface.js';
import { ShortLinkService } from './short-link.service.js';

export class ShortLinkController {
   public getLinkByHash = async (req: FastifyRequest<{ Params: GetByHash }>, reply: FastifyReply) => {
      await this._linkService.getLinkByHash(reply, req.params.hash);
   };

   private _linkService = new ShortLinkService();
}
