import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';

import { ShortLinkController } from './short-link.controller.js';
import { GetLinkByHashSchema } from './short-link.schema.js';

export class ShortLinkRoute implements Route {
   public path = '/';

   private _linkController: ShortLinkController;

   constructor() {
      this._linkController = new ShortLinkController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._linkController.getLinkByHash,
         method: 'get',
         schema: GetLinkByHashSchema,
         url: '/:hash'
      });

      done();
   }
}
