import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

export const TwilioWebhookBody = Type.Object({
   AccountSid: Type.String(), // Your Twilio account identifier
   Body: Type.String(), // The content of the SMS message
   From: Type.String(), // The phone number that sent the message (e.g. +**********)
   MessageSid: Type.String(), // Unique identifier for the message
   MessagingServiceSid: Type.String(), // Unique identifier for the message service
   NumMedia: Type.Number(), // Number of media attachments
   NumSegments: Type.Number(), // Number of segments for long messages
   SmsSid: Type.String(), // Another identifier for the SMS
   To: Type.String() // Your Twilio phone number that received the message
});

export const TwilioWebhookParams = Type.Object({
   tenantId: Type.String()
});

export const TwilioWebhookSchema: FastifySchema = {
   body: TwilioWebhookBody,
   hide: true,
   params: TwilioWebhookParams
};
