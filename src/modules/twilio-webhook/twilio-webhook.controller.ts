import { FastifyReply, FastifyRequest } from 'fastify';

import { TwilioWebhook, TwilioWebhookParams } from './twilio-webhook.interface.js';
import { TwilioWebhookService } from './twilio-webhook.service.js';

export class TwilioWebhookController {
   public setResponse = async (
      req: FastifyRequest<{ Body: TwilioWebhook; Params: TwilioWebhookParams }>,
      reply: FastifyReply
   ) => {
      return await this._webhookService.setResponse(reply, req.params.tenantId, req.body);
   };

   private _webhookService = new TwilioWebhookService();
}
