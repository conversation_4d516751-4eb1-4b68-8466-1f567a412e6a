import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';
import { v7 as uuid7 } from 'uuid';

import { extractReaction } from '../two-way-message/extract-reaction.js';

import { TwilioWebhook } from './twilio-webhook.interface.js';

export class TwilioWebhookService {
   private static readonly _DAYS_BACK_ = 7;
   private static readonly _MS_PER_DAY_ = 24 * 60 * 60 * 1000;
   private static readonly _DATE_OFFSET_ = TwilioWebhookService._DAYS_BACK_ * TwilioWebhookService._MS_PER_DAY_;

   public async setResponse(reply: FastifyReply, tenantId: string, body: TwilioWebhook) {
      try {
         // TODO: two-way
         //  get recipientId from fromPhoneNumber

         const reaction = extractReaction(body.Body);

         if (reaction) {
            if (await this._updateReaction(tenantId, body, reaction)) {
               return await reply.status(200).send();
            }
         }

         const from = this._formatPhoneNumber(body.From);
         const to = this._formatPhoneNumber(body.To);

         const prisma = await getPrismaClient(tenantId);
         const lastMessage = await prisma.twoWayMessage.findFirst({
            orderBy: {
               createdAt: 'desc'
            },
            where: {
               createdAt: {
                  gte: new Date(new Date().getTime() - TwilioWebhookService._DATE_OFFSET_)
               },
               direction: 'O',
               recipientServiceId: from,
               tenantId,
               tenantServiceId: to
            }
         });

         const category = lastMessage ? lastMessage.category : '';

         // TODO: add ML categorization

         await prisma.twoWayMessage.create({
            data: {
               category,
               channel: 'TWILIO-WEBHOOK',
               direction: 'I',
               id: uuid7(),
               message: body.Body,
               recipientServiceId: from,
               tenantId,
               tenantServiceId: to
            }
         });

         return await reply.status(200).send();
      } catch (error) {
         const errorMessage = error instanceof Error ? error.message : 'Unknown error';
         reply.log.error(
            {
               data: { error: errorMessage },
               name: 'setResponse',
               type: 'TwilioWebhookService'
            },
            'twilioWebhookService - setResponse error'
         );

         return reply.status(500).send({
            // eslint-disable-next-line camelcase
            error_message: 'Internal Server Error',
            // eslint-disable-next-line camelcase
            error_type: 'internal_error',
            // eslint-disable-next-line camelcase
            status_code: 500
         });
      }
   }

   private _formatPhoneNumber(number: string): string {
      return number.replace(/^\+/, '');
   }

   private async _updateReaction(tenantId: string, body: TwilioWebhook, reaction: string): Promise<boolean> {
      const matches = /“(.*?)”/.exec(body.Body);

      if (!matches) {
         return false;
      }

      let message = matches[1].trim();

      if (message.endsWith('…')) {
         message = message.slice(0, -3);
      }

      const prisma = await getPrismaClient(tenantId);
      const matched = await prisma.twoWayMessage.findFirst({
         orderBy: {
            createdAt: 'desc'
         },
         select: { id: true },
         where: {
            direction: 'O',
            message: {
               mode: 'insensitive',
               startsWith: message
            },
            recipientServiceId: this._formatPhoneNumber(body.From),
            tenantId
         }
      });

      if (!matched) {
         return false;
      }

      if (reaction) {
         await prisma.twoWayMessage.update({
            data: {
               reaction
            },
            where: {
               id: matched.id
            }
         });

         return true;
      }

      return false;
   }
}
