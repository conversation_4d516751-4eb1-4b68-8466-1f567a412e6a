import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';

import { TwilioWebhookController } from './twilio-webhook.controller.js';
import { TwilioWebhookSchema } from './twilio-webhook.schema.js';

export class TwilioWebhookRoute implements Route {
   public path = '/twilio-sms/:tenantId/webhook';
   private _webhookController: TwilioWebhookController;

   constructor() {
      this._webhookController = new TwilioWebhookController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.post(this.path, { schema: TwilioWebhookSchema }, this._webhookController.setResponse);
      done();
   }
}
