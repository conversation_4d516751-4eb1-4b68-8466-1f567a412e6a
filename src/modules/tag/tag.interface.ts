import { Static } from '@fastify/type-provider-typebox';

import {
   CreatedTagBody,
   CreateTagBody,
   GetTagParams,
   GetTagRecipientsParams,
   GetTagsBody,
   GetTagsParams,
   RecipientIdsBody,
   RecipientsBody,
   UpdateTagBody
} from './tag.schema.js';

export type CreateTag = Static<typeof CreateTagBody>;
export type CreatedTag = Static<typeof CreatedTagBody>;
export type GetTag = Static<typeof CreatedTagBody>;
export type GetTags = Static<typeof GetTagsBody>;
export type GetTagParams = Static<typeof GetTagParams>;
export type GetTagsParams = Static<typeof GetTagsParams>;
export type GetTagRecipientsParams = Static<typeof GetTagRecipientsParams>;
export type RecipientIds = Static<typeof RecipientIdsBody>;
export type Recipients = Static<typeof RecipientsBody>;
export type UpdateTag = Static<typeof UpdateTagBody>;
