import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { TagController } from './tag.controller.js';
import { CreateTagSchema, DeleteTagSchema, GetTagSchema, GetTagsSchema, UpdateTagSchema } from './tag.schema.js';
import {
   AddTagRecipientsSchema,
   GetTagRecipientsSchema,
   RemoveTagRecipientsSchema,
   ReplaceTagRecipientsSchema
} from './tag.schema.js';

export class TagRoute implements Route {
   public path = '/tag';
   private _tagController: TagController;
   private _readScopes = [Scopes.ADMIN_READ, Scopes.ADMIN_WRITE, Scopes.TAG_READ, Scopes.TAG_WRITE];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.TAG_WRITE];

   constructor() {
      this._tagController = new TagController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._tagController.createTag,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateTagSchema,
         url: this.path
      });

      fastify.route({
         handler: this._tagController.getTag,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTagSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._tagController.getTags,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTagsSchema,
         url: this.path
      });

      fastify.route({
         handler: this._tagController.updateTag,
         method: 'patch',
         preHandler: fastify.authenticate(this._readScopes),
         schema: UpdateTagSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._tagController.deleteTag,
         method: 'delete',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: DeleteTagSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._tagController.addTagRecipients,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: AddTagRecipientsSchema,
         url: `${this.path}/:id/recipients/add`
      });

      fastify.route({
         handler: this._tagController.removeTagRecipients,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: RemoveTagRecipientsSchema,
         url: `${this.path}/:id/recipients/remove`
      });

      fastify.route({
         handler: this._tagController.replaceTagRecipients,
         method: 'put',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: ReplaceTagRecipientsSchema,
         url: `${this.path}/:id/recipients`
      });

      fastify.route({
         handler: this._tagController.getTagRecipients,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTagRecipientsSchema,
         url: `${this.path}/:id/recipients`
      });

      done();
   }
}
