// eslint-disable-next-line simple-import-sort/imports
import { Tag } from '@prisma/client';
import { Value } from '@sinclair/typebox/value';
import { FastifyReply } from 'fastify';
import { v7 as uuid } from 'uuid';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { DefaultPaginationOptions } from '../../interfaces/pagination-options.js';

import { CreateTag, UpdateTag } from './tag.interface.js';
import { CreatedTagBody } from './tag.schema.js';

export class TagService {
   public async createTag(reply: FastifyReply, tenantId: string, body: CreateTag) {
      const re = /^\S+$/;

      if (!re.test(body.externalId ?? '')) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"externalId" cannot contain whitespace',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const prisma = await getPrismaClient(tenantId);
      const exists = await prisma.tag.findFirst({
         where: { externalId: { equals: body.externalId, mode: 'insensitive' }, tenantId }
      });

      if (exists) {
         const error = {
            code: 'FST_ERR_CONFLICT',
            error: 'Conflict',
            message: `Tag with externalId {${body.externalId}} already exists`,
            statusCode: 409
         };

         return reply.code(409).send(error);
      }

      const tag = await prisma.tag.create({
         data: {
            description: body.description ?? '',
            displayName: body.displayName,
            enabled: body.enabled ?? true,
            externalId: body.externalId,
            id: uuid(),
            selectable: body.selectable ?? false,
            tenantId
         }
      });

      return reply.code(201).send(Value.Clean(CreatedTagBody, tag));
   }

   public async updateTag(reply: FastifyReply, tenantId: string, id: string, body: UpdateTag) {
      const prisma = await getPrismaClient(tenantId);
      const item = await prisma.tag.findUnique({ where: { id, tenantId } });

      if (!item) {
         return reply.code(404).send({
            error: 'Not Found',
            message: 'Tag not found',
            statusCode: 404
         });
      }

      const updatedData: Partial<Tag> = {};

      if ('name' in body && body.name === '') {
         return reply.code(400).send({
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"displayName" cannot be empty',
            statusCode: 400
         });
      }

      updatedData.displayName = body.displayName ?? item.displayName;

      updatedData.description = body.description ?? item.description;

      if ('description' in body && body.description === '') {
         updatedData.description = '';
      }

      if (body.enabled !== undefined) {
         updatedData.enabled = body.enabled ?? item.enabled;
      }

      if (body.selectable !== undefined) {
         updatedData.selectable = body.selectable ?? item.selectable;
      }

      const updated = await prisma.tag.update({
         data: updatedData,
         where: { id, tenantId }
      });

      return reply.send(Value.Clean(CreatedTagBody, updated));
   }

   public async getTag(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);
      let tag = await prisma.tag.findUnique({ where: { id, tenantId } });

      tag ??= await prisma.tag.findFirst({ where: { displayName: id, tenantId } });

      if (!tag) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Tag {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      return reply.code(200).send(Value.Clean(CreatedTagBody, tag));
   }

   public async getTags(reply: FastifyReply, tenantId: string, key?: string, size: number = 50) {
      const options: DefaultPaginationOptions = {
         orderBy: { id: 'asc' },
         where: {
            tenantId
         }
      };

      if (key) {
         options.where = {
            ...options.where,
            id: {
               gte: key
            },
            tenantId
         };
      }

      const prisma = await getPrismaClient(tenantId);
      const count = await prisma.tag.count({ where: { ...options.where } });
      options.take = size + 1;

      const tags = await prisma.tag.findMany({ ...options });
      let nextKey = '';

      if (tags.length > size) {
         const last = tags[size];
         nextKey = last.id;
         tags.pop();
      }

      const response = {
         items: tags,
         pagination: {
            // eslint-disable-next-line camelcase
            next_key: nextKey,
            // eslint-disable-next-line camelcase
            page_size: size,
            // eslint-disable-next-line camelcase
            total_records: count
         }
      };

      return reply.send(response);
   }

   public async deleteTag(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);

      const exists = await prisma.tag.findUnique({ where: { id, tenantId } });

      if (!exists) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Tag {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      await prisma.tag.delete({ where: { id, tenantId } });

      return reply.code(204).send();
   }

   public async getTagRecipients(reply: FastifyReply, tenantId: string, id: string, key?: string, size: number = 50) {
      const prisma = await getPrismaClient(tenantId);
      const exists = await prisma.tag.findUnique({
         include: { recipients: { select: { id: true } } },
         where: { id, tenantId }
      });

      if (!exists) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Tag {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      const all = exists.recipients.map((r) => r.id).sort();
      let nextKey = '';

      let filtered: string[] = [];

      if (!key) {
         filtered = all.slice(0, size);

         if (all.length > size) {
            nextKey = all[size];
         }
      } else {
         const index = all.indexOf(key);
         const nextIndex = index + size;
         filtered = all.slice(index, nextIndex);

         if (nextIndex < all.length) {
            nextKey = all[nextIndex];
         }
      }

      const response = {
         items: filtered,
         pagination: {
            // eslint-disable-next-line camelcase
            next_key: nextKey,
            // eslint-disable-next-line camelcase
            page_size: size,
            // eslint-disable-next-line camelcase
            total_records: all.length
         }
      };

      return reply.send(response);
   }

   public async addTagRecipients(reply: FastifyReply, tenantId: string, id: string, recipients: string[]) {
      if (recipients.length === 0) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'At least one tag is required',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const prisma = await getPrismaClient(tenantId);
      await prisma.tag.update({
         data: {
            recipients: {
               connect: recipients.map((recipient) => ({ id: recipient }))
            }
         },
         where: { id, tenantId }
      });

      return reply.send();
   }

   public async removeTagRecipients(reply: FastifyReply, tenantId: string, id: string, recipients: string[]) {
      if (recipients.length === 0) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: 'At least one tag is required',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const prisma = await getPrismaClient(tenantId);
      await prisma.tag.update({
         data: {
            recipients: {
               disconnect: recipients.map((recipient) => ({ id: recipient }))
            }
         },
         where: { id, tenantId }
      });

      return reply.send();
   }

   public async replaceTagRecipients(reply: FastifyReply, tenantId: string, id: string, recipients: string[]) {
      const prisma = await getPrismaClient(tenantId);
      await prisma.tag.update({
         data: {
            recipients: {
               deleteMany: {}
            }
         },
         where: { id, tenantId }
      });
      await prisma.tag.update({
         data: {
            recipients: {
               connect: recipients.map((recipient) => ({ id: recipient }))
            }
         },
         where: { id, tenantId }
      });

      return reply.send();
   }
}
