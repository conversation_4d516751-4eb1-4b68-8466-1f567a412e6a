import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { PaginationResponse } from '../shared/pagination/pagination.schema.js';

export const CreateTagBody = Type.Object({
   description: Type.Optional(Type.String()),
   displayName: Type.String({ maxLength: 30, minLength: 1, pattern: '^[a-zA-Z0-9-_]+$' }),
   enabled: Type.Optional(Type.Boolean({ default: true })),
   externalId: Type.Optional(Type.String({ pattern: '^[a-zA-Z0-9-_]+$' })),
   selectable: Type.Optional(Type.Boolean({ default: false }))
});

export const CreatedTagBody = Type.Object({
   createdAt: Type.String({ format: 'date-time' }),
   description: Type.String(),
   displayName: Type.String(),
   enabled: Type.Boolean(),
   externalId: Type.String(),
   id: Type.String({ format: 'uuid' }),
   selectable: Type.Boolean(),
   updatedAt: Type.String({ format: 'date-time' })
});

export const GetTagsBody = Type.Object({
   items: Type.Array(CreatedTagBody),
   pagination: PaginationResponse
});

export const RecipientIdsBody = Type.Array(Type.String({ format: 'uuid' }));
export const RecipientsBody = Type.Object({
   items: RecipientIdsBody
});

export const GetTagRecipientsBody = Type.Object({
   items: RecipientIdsBody,
   pagination: PaginationResponse
});

export const UpdateTagBody = Type.Object({
   description: Type.Optional(Type.String()),
   displayName: Type.Optional(Type.String({ maxLength: 30, minLength: 1, pattern: '^[a-zA-Z0-9-_]+$' })),
   enabled: Type.Optional(Type.Boolean()),
   selectable: Type.Optional(Type.Boolean())
});

export const GetTagRecipientsParams = Type.Object({
   key: Type.Optional(Type.String()),
   size: Type.Optional(Type.Number({ default: 100, maximum: 1000, minimum: 1 }))
});

export const GetTagParams = Type.Object({
   id: Type.String()
});

export const GetTagsParams = Type.Object({
   key: Type.Optional(Type.String()),
   size: Type.Optional(Type.Number({ default: 50, maximum: 100, minimum: 1 }))
});

export const CreateTagSchema: FastifySchema = {
   body: CreateTagBody,
   description: 'Create a tag',
   response: {
      201: CreatedTagBody
   }
};

export const GetTagSchema: FastifySchema = {
   description: 'Get a tag by ID',
   params: GetTagParams,
   response: {
      200: CreatedTagBody
   }
};

export const GetTagByExternalIdSchema: FastifySchema = {
   description: 'Get a tag by name',
   params: GetTagParams,
   response: {
      200: CreatedTagBody
   }
};

export const GetTagsSchema: FastifySchema = {
   description: 'Get tags',
   params: GetTagsParams,
   querystring: GetTagsParams,
   response: {
      200: GetTagsBody
   }
};

export const GetTagRecipientsSchema: FastifySchema = {
   // eslint-disable-next-line @stylistic/quotes
   description: "Get a tag's recipients",
   querystring: GetTagsParams,
   response: {
      200: GetTagRecipientsBody
   }
};

export const ReplaceTagRecipientsSchema: FastifySchema = {
   body: RecipientsBody,
   // eslint-disable-next-line @stylistic/quotes
   description: "Replace a tag's recipients",
   params: GetTagParams,
   response: {
      200: Type.Null()
   }
};

export const AddTagRecipientsSchema: FastifySchema = {
   body: RecipientsBody,
   description: 'Add recipients to a tag',
   params: GetTagParams,
   response: {
      200: Type.Null()
   }
};

export const RemoveTagRecipientsSchema: FastifySchema = {
   body: RecipientsBody,
   description: 'Remove recipients from a tag',
   params: GetTagParams,
   response: {
      200: Type.Null()
   }
};

export const UpdateTagSchema: FastifySchema = {
   body: UpdateTagBody,
   description: 'Update a tag',
   params: GetTagParams,
   response: {
      200: CreatedTagBody
   }
};

export const DeleteTagSchema: FastifySchema = {
   description: 'Delete a tag',
   params: GetTagParams,
   response: {
      204: Type.Null()
   }
};
