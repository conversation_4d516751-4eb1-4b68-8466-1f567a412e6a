import { FastifyReply, FastifyRequest } from 'fastify';

import {
   CreateTag,
   GetTagParams,
   GetTagRecipientsParams,
   GetTagsParams,
   Recipients,
   UpdateTag
} from './tag.interface.js';
import { TagService } from './tag.service.js';

export class TagController {
   public createTag = async (req: FastifyRequest<{ Body: CreateTag }>, reply: FastifyReply) => {
      await this._tagService.createTag(reply, req.tenantId, req.body);
   };

   public updateTag = async (req: FastifyRequest<{ Body: UpdateTag; Params: GetTagParams }>, reply: FastifyReply) => {
      await this._tagService.updateTag(reply, req.tenantId, req.params.id, req.body);
   };

   public getTag = async (req: FastifyRequest<{ Params: GetTagParams }>, reply: FastifyReply) => {
      await this._tagService.getTag(reply, req.tenantId, req.params.id);
   };

   public getTags = async (req: FastifyRequest<{ Querystring: GetTagsParams }>, reply: FastifyReply) => {
      await this._tagService.getTags(reply, req.tenantId, req.query.key, req.query.size);
   };

   public deleteTag = async (req: FastifyRequest<{ Params: GetTagParams }>, reply: FastifyReply) => {
      await this._tagService.deleteTag(reply, req.tenantId, req.params.id);
   };

   public getTagRecipients = async (
      req: FastifyRequest<{ Params: GetTagParams; Querystring: GetTagRecipientsParams }>,
      reply: FastifyReply
   ) => {
      await this._tagService.getTagRecipients(reply, req.tenantId, req.params.id, req.query.key, req.query.size);
   };

   public addTagRecipients = async (
      req: FastifyRequest<{ Body: Recipients; Params: GetTagParams }>,
      reply: FastifyReply
   ) => {
      await this._tagService.addTagRecipients(reply, req.tenantId, req.params.id, req.body.items);
   };

   public removeTagRecipients = async (
      req: FastifyRequest<{ Body: Recipients; Params: GetTagParams }>,
      reply: FastifyReply
   ) => {
      await this._tagService.removeTagRecipients(reply, req.tenantId, req.params.id, req.body.items);
   };

   public replaceTagRecipients = async (
      req: FastifyRequest<{ Body: Recipients; Params: GetTagParams }>,
      reply: FastifyReply
   ) => {
      await this._tagService.replaceTagRecipients(reply, req.tenantId, req.params.id, req.body.items);
   };

   private _tagService = new TagService();
}
