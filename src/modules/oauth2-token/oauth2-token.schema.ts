/* eslint-disable camelcase */
import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { ErrorResponseSchema } from '../shared/error/error.schema.js';

export const OAuth2TokenBody = Type.Object({
   client_id: Type.String(),
   client_secret: Type.String(),
   grant_type: Type.String()
});

const OAuth2TokenResponse = Type.Object({
   access_token: Type.String(),
   expires_in: Type.Number(),
   request_id: Type.Optional(Type.String()),
   token_type: Type.String()
});

export const OAuth2TokenSchema: FastifySchema = {
   body: OAuth2TokenBody,
   consumes: ['application/x-www-form-urlencoded'],
   description: 'OAuth2 token request',
   response: {
      200: OAuth2TokenResponse,
      400: ErrorResponseSchema,
      500: ErrorResponseSchema
   }
};
