import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';

import { OAuth2TokenController } from './oauth2-token.controller.js';
import { OAuth2TokenSchema } from './oauth2-token.schema.js';

export class OAuth2TokenRoute implements Route {
   public path = '/oauth2/token';
   private _tokenController: OAuth2TokenController;

   constructor() {
      this._tokenController = new OAuth2TokenController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.post(this.path, { schema: OAuth2TokenSchema }, this._tokenController.getToken);
      done();
   }
}
