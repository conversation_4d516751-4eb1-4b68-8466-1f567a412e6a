import { FastifyReply } from 'fastify';
import { jwtDecode } from 'jwt-decode';
import { B2BClient, StytchError } from 'stytch';

import { OAuth2Token } from './oauth2-token.interface.js';

type Token = {
   access_token: string;
   expires_in: number;
   request_id: string;
   status_code: number;
   token_type: string;
};

export class OAuth2TokenService {
   private _client: B2BClient;
   private _cache: Map<string, [token: Token, timestamp: number]>;

   constructor() {
      this._cache = new Map<string, [token: Token, timestamp: number]>();
      this._client = new B2BClient({
         // eslint-disable-next-line camelcase
         project_id: process.env.STYTCH_PROJECT_ID ?? '',
         secret: process.env.STYTCH_SECRET ?? ''
      });
   }

   public async getToken(reply: FastifyReply, body: OAuth2Token) {
      try {
         if (this._cache.has(body.client_id)) {
            const [token, timestamp] = this._cache.get(body.client_id)!;

            const tokenDecoded = jwtDecode(token.access_token);

            const exp = tokenDecoded.exp ?? 3600;
            // eslint-disable-next-line camelcase
            token.expires_in = Math.floor((new Date(exp * 1000).getTime() - Date.now()) / 1000);

            // cache for 15 minutes
            if (Date.now() - timestamp < 1000 * 60 * 15) {
               return token;
            }
         }

         const token = (await this._client.m2m.token({
            // eslint-disable-next-line camelcase
            client_id: body.client_id,
            // eslint-disable-next-line camelcase
            client_secret: body.client_secret,
            scopes: ['admin.write']
         })) as Token;

         this._cache.set(body.client_id, [token, Date.now()]);

         return token;
      } catch (error) {
         if (error instanceof StytchError) {
            const stytchError = error;

            return reply.status(400).send({
               error: stytchError.error_type,
               message: stytchError.error_message,
               requestId: stytchError.request_id,
               statusCode: stytchError.status_code
            });
         }

         reply.log.error(error);

         return reply.status(500).send({
            error: 'Internal Server Error',
            message: '',
            requestId: '',
            statusCode: 500
         });
      }
   }
}
