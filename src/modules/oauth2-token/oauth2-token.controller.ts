import { FastifyReply, FastifyRequest } from 'fastify';

import { OAuth2Token } from './oauth2-token.interface.js';
import { OAuth2TokenService } from './oauth2-token.service.js';

export class OAuth2TokenController {
   public getToken = async (req: FastifyRequest<{ Body: OAuth2Token }>, reply: FastifyReply) => {
      return await this._tokenService.getToken(reply, req.body);
   };

   private _tokenService = new OAuth2TokenService();

}
