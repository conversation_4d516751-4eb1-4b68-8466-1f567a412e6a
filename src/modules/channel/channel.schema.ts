import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { CreateAttributeBody, CreatedAttributeBody } from '../shared/attribute/attribute.schema.js';
import { PaginationResponse } from '../shared/pagination/pagination.schema.js';

import { ChannelProvider } from './channel-provider.js';
import { ChannelServices } from './channel-services.js';

export const CreateChannelBody = Type.Object({
   attributes: Type.Array(CreateAttributeBody),
   channelType: Type.String({ maxLength: 30, minLength: 1 }),
   description: Type.Optional(Type.String()),
   displayName: Type.String({ maxLength: 30, minLength: 1 }),
   enabled: Type.Optional(Type.Boolean({ default: true })),

   externalId: Type.Optional(Type.String({ pattern: '^[a-zA-Z0-9-_]+$' })),
   provider: Type.Optional(Type.String({ enum: ChannelProvider })),
   service: Type.String({ enum: ChannelServices })
});

export const CreatedChannelBody = Type.Object({
   attributes: Type.Array(CreatedAttributeBody),
   channelType: Type.String(),
   description: Type.String(),
   displayName: Type.String(),
   enabled: Type.Boolean(),
   externalId: Type.String(),
   id: Type.String({ format: 'uuid' }),
   provider: Type.Optional(Type.String({ enum: ChannelProvider })),
   service: Type.String({ enum: ChannelServices })
});

/* TODO: Support updates
   export const UpdateChannelBody = Type.Object({
   attributes: Type.Optional(Type.Array(UpdateAttributeBody)),
   description: Type.Optional(Type.String()),
   displayName: Type.Optional(Type.String({ maxLength: 30, minLength: 1 })),
   enabled: Type.Optional(Type.Boolean()),
   externalId: Type.Optional(Type.String({ pattern: '^[a-zA-Z0-9-_]+$' })),
   id: Type.String({ format: 'uuid' }),
   provider: Type.Optional(Type.String({ enum: ChannelProvider })),
   service: Type.Optional(Type.String({ enum: ChannelServices })),
   type: Type.Optional(Type.String({ maxLength: 30, minLength: 1 }))
});*/

export const GetChannelsBody = Type.Object({
   items: Type.Array(CreatedChannelBody),
   pagination: PaginationResponse
});

export const GetChannelParams = Type.Object({
   id: Type.String()
});

export const GetChannelsParams = Type.Object({
   key: Type.Optional(Type.String()),
   service: Type.Optional(Type.String()),
   size: Type.Optional(Type.Number({ default: 50, maximum: 100, minimum: 1 }))
});

export const CreateChannelSchema: FastifySchema = {
   body: CreateChannelBody,
   description: 'Create a channel',
   response: {
      201: CreatedChannelBody
   }
};

export const GetChannelSchema: FastifySchema = {
   description: 'Get a channel by ID',
   params: GetChannelParams,
   response: {
      200: CreatedChannelBody
   }
};

export const GetChannelByExternalIdSchema: FastifySchema = {
   description: 'Get a channel by name',
   params: GetChannelParams,
   response: {
      200: CreatedChannelBody
   }
};

export const GetChannelsSchema: FastifySchema = {
   description: 'Get channels',
   querystring: GetChannelsParams,
   response: {
      200: GetChannelsBody
   }
};

export const UpdateChannelSchema: FastifySchema = {
   body: CreateChannelBody,
   description: 'Replace a channel',
   response: {
      200: CreatedChannelBody
   }
};

export const DeleteChannelSchema: FastifySchema = {
   description: 'Delete a channel',
   params: GetChannelParams,
   response: {
      204: Type.Null()
   }
};
