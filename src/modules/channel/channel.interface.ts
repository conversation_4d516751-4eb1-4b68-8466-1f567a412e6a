import { Static } from '@fastify/type-provider-typebox';

import {
   CreateC<PERSON><PERSON>Body,
   CreatedChannelBody,
   GetChannelParams,
   GetChannelsBody,
   GetChannelsParams
} from './channel.schema.js';

export type CreateChannel = Static<typeof CreateChannelBody>;
export type CreatedChannel = Static<typeof CreatedChannelBody>;
export type GetChannel = Static<typeof CreatedChannelBody>;
export type GetChannels = Static<typeof GetChannelsBody>;
export type GetChannelParams = Static<typeof GetChannelParams>;
export type GetChannelsParams = Static<typeof GetChannelsParams>;
