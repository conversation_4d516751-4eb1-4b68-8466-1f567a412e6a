import { FastifyReply, FastifyRequest } from 'fastify';

import { AttributeParams, UpdateAttribute } from '../shared/attribute/attribute.interface.js';

import { CreateChannel, CreatedChannel, GetChannelParams, GetChannelsParams } from './channel.interface.js';
import { ChannelService } from './channel.service.js';

export class ChannelController {
   public createChannel = async (req: FastifyRequest<{ Body: CreateChannel }>, reply: FastifyReply) => {
      await this._channelService.createChannel(reply, req.tenantId, req.body);
   };

   public getChannel = async (req: FastifyRequest<{ Params: GetChannelParams }>, reply: FastifyReply) => {
      await this._channelService.getChannel(reply, req.tenantId, req.params.id);
   };

   public getChannels = async (req: FastifyRequest<{ Querystring: GetChannelsParams }>, reply: FastifyReply) => {
      await this._channelService.getChannels(reply, req.tenantId, req.query.service, req.query.key, req.query.size);
   };

   public replaceChannel = async (
      req: FastifyRequest<{ Body: CreatedChannel; Params: GetChannelParams }>,
      reply: FastifyReply
   ) => {
      await this._channelService.replaceChannel(reply, req.tenantId, req.params.id, req.body);
   };

   public deleteChannel = async (req: FastifyRequest<{ Params: GetChannelParams }>, reply: FastifyReply) => {
      await this._channelService.deleteChannel(reply, req.tenantId, req.params.id);
   };

   public getChannelAttribute = async (req: FastifyRequest<{ Params: AttributeParams }>, reply: FastifyReply) => {
      await this._channelService.getAttribute(reply, req.tenantId, req.params.channelId, req.params.attributeId);
   };

   public updateChannelAttribute = async (
      req: FastifyRequest<{ Body: UpdateAttribute; Params: AttributeParams }>,
      reply: FastifyReply
   ) => {
      await this._channelService.updateAttribute(
         req.tenantId,
         req.params.channelId,
         req.params.attributeId,
         req.body,
         reply
      );
   };

   public deleteChannelAttribute = async (req: FastifyRequest<{ Params: AttributeParams }>, reply: FastifyReply) => {
      await this._channelService.deleteAttribute(reply, req.tenantId, req.params.channelId, req.params.attributeId);
   };

   private _channelService = new ChannelService();
}
