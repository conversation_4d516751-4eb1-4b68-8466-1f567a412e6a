import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { ChannelController } from './channel.controller.js';
import {
   CreateChannelSchema,
   DeleteChannelSchema,
   GetChannelSchema,
   GetChannelsSchema,
   UpdateChannelSchema
} from './channel.schema.js';

export class ChannelRoute implements Route {
   public path = '/channel';
   private _channelController: ChannelController;
   private _readScopes = [Scopes.ADMIN_READ, Scopes.ADMIN_WRITE, Scopes.CHANNEL_READ, Scopes.CHANNEL_WRITE];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.CHANNEL_WRITE];

   constructor() {
      this._channelController = new ChannelController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._channelController.createChannel,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateChannelSchema,
         url: this.path
      });

      fastify.route({
         handler: this._channelController.getChannel,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetChannelSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._channelController.getChannels,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetChannelsSchema,
         url: this.path
      });

      fastify.route({
         handler: this._channelController.replaceChannel,
         method: 'put',
         preHandler: fastify.authenticate(this._readScopes),
         schema: UpdateChannelSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._channelController.deleteChannel,
         method: 'delete',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: DeleteChannelSchema,
         url: `${this.path}/:id`
      });

      /* TODO: Do we still need this?
      fastify.route({
         handler: this._channelController.getChannelAttribute,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetAttributeSchema,
         url: `${this.path}/:channelId/attribute/:attributeId`
      });

      fastify.route({
         handler: this._channelController.updateChannelAttribute,
         method: 'patch',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: UpdateAttributeSchema,
         url: `${this.path}/:channelId/attribute/:attributeId`
      });

      fastify.route({
         handler: this._channelController.deleteChannelAttribute,
         method: 'delete',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: GetAttributeSchema,
         url: `${this.path}/:channelId/attribute/:attributeId`
      });
      */

      done();
   }
}
