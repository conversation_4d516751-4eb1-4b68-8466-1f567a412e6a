import { Channel } from '@prisma/client';
import { Value } from '@sinclair/typebox/value';
import { AttributeConstants, getPrismaClient, SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';
import { v7 as uuid } from 'uuid';
import validator from 'validator';

import { CreateAttribute, UpdateAttribute } from '../shared/attribute/attribute.interface.js';
import { CreatedAttributeBody } from '../shared/attribute/attribute.schema.js';

import { CreateChannel } from './channel.interface.js';
import { CreatedChannelBody } from './channel.schema.js';

type getChannelsOptions = {
   include: { attributes: boolean };
   orderBy: { id: 'asc' | 'desc' };
   take?: number;
   where?: {
      id?: {
         gte: string;
      };
      service?: string;
      tenantId: string;
   };
};

type ChannelAttribute = {
   id: string;
   name: string;
   secure: boolean;
   value: string;
};

export class ChannelService {
   private _vault: SecretVault;

   private _validateAttributes = async (
      tenantId: string,
      channelId: string,
      attributes: CreateAttribute[] | UpdateAttribute[],
      service: string,
      provider: string
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> => {
      const cleanedAttributes = attributes.map((a) => {
         return {
            name: a.name.trim().toLowerCase(),
            value: a.value
         };
      });

      switch (service) {
         case AttributeConstants.CANVAS:
            return this._validateCanvasAttribute(tenantId, channelId, cleanedAttributes);

         case AttributeConstants.EMAIL: {
            switch (provider) {
               case AttributeConstants.SENDGRID:
                  return this._validateSendgridAttribute(tenantId, channelId, cleanedAttributes);
               case AttributeConstants.SMTP:
                  return this._validateSmtpAttribute(tenantId, channelId, cleanedAttributes);
               default:
                  return [false, `Provider ${provider} not found`, []];
            }
         }

         case AttributeConstants.MODO:
            return this._validateModoAttribute(tenantId, channelId, cleanedAttributes);

         case AttributeConstants.MS_TEAMS:
            return this._validateMsTeamsAttribute(tenantId, channelId, cleanedAttributes);

         case AttributeConstants.SMS: {
            switch (provider) {
               case AttributeConstants.ESENDEX:
                  return this._validateEsendexAttribute(tenantId, channelId, cleanedAttributes);
               case AttributeConstants.TWILIO:
                  return this._validateTwilioAttribute(tenantId, channelId, cleanedAttributes);
               default:
                  return [false, `Provider ${provider} not found`, []];
            }
         }

         case AttributeConstants.TEST:
            return [true, '', []];

         case AttributeConstants.WEBHOOK_URL:
            return this._validateWebhookAttribute(tenantId, channelId, cleanedAttributes);
         default:
            return [false, `Service ${service} not found`, []];
      }
   };

   constructor() {
      this._vault = SecretVaultFactory.getSecretVault(true);
   }

   public async createChannel(reply: FastifyReply, tenantId: string, body: CreateChannel) {
      const prisma = await getPrismaClient(tenantId);
      const re = /^\S+$/;

      if (!re.test(body.externalId ?? '')) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"externalId" cannot contain whitespace',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const exists = await prisma.channel.findFirst({
         where: { externalId: { equals: body.externalId, mode: 'insensitive' }, tenantId }
      });

      if (exists) {
         const error = {
            code: 'FST_ERR_CONFLICT',
            error: 'Conflict',
            message: `Channel with externalId {${body.externalId}} already exists`,
            statusCode: 409
         };

         return reply.code(409).send(error);
      }

      const id = uuid();

      const result = await this._validateAttributes(tenantId, id, body.attributes, body.service, body.provider ?? '');

      if (!result[0]) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: result[1],
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const attributes = result[2];

      const existing = await prisma.channel.findFirst({
         where: { channelType: body.channelType.toLowerCase(), service: body.service, tenantId }
      });

      if (existing) {
         const error = {
            code: 'FST_ERR_CONFLICT',
            error: 'Conflict',
            message: `Channel with service (${body.service}) and type (${body.channelType}) already exists`,
            statusCode: 409
         };

         return reply.code(409).send(error);
      }

      if (!body.provider) {
         switch (body.service) {
            case AttributeConstants.TEST:
               body.provider = AttributeConstants.NO_PROVIDER;
               break;
            case AttributeConstants.MODO:
               body.provider = AttributeConstants.MODO;
               break;
            case AttributeConstants.CANVAS:
               body.provider = AttributeConstants.CANVAS;
               break;
            case AttributeConstants.SLACK:
            case AttributeConstants.SLACK_WEBHOOK:
               body.provider = AttributeConstants.SLACK;
               break;
            case AttributeConstants.MS_TEAMS:
            case AttributeConstants.MS_TEAMS_WEBHOOK:
               body.provider = AttributeConstants.MS_TEAMS;
               break;
         }
      }

      const channel = await prisma.channel.create({
         data: {
            attributes: {
               create: attributes
            },
            channelType: body.channelType.toLowerCase(),
            description: body.description,
            displayName: body.displayName,
            enabled: body.enabled,
            externalId: body.externalId ?? '',
            id,
            provider: body.provider ?? '',
            service: body.service,
            tenantId
         },
         include: { attributes: true }
      });

      return reply.code(201).send(Value.Clean(CreatedChannelBody, channel));
   }

   public async getChannel(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);
      let item = await prisma.channel.findUnique({
         include: { attributes: true },
         where: { id, tenantId }
      });

      item ??= await prisma.channel.findFirst({
         include: { attributes: true },
         where: { externalId: { equals: id, mode: 'insensitive' }, tenantId }
      });

      if (!item) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Channel {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      return reply.send(Value.Clean(CreatedChannelBody, item));
   }

   public async getChannels(reply: FastifyReply, tenantId: string, service?: string, key?: string, size: number = 50) {
      const options: getChannelsOptions = service
         ? {
            include: { attributes: true },
            orderBy: { id: 'asc' },
            where: {
               service,
               tenantId
            }
         }
         : { include: { attributes: true }, orderBy: { id: 'asc' }, where: { tenantId } };

      if (key) {
         options.where = {
            ...options.where,
            id: {
               gte: key
            },
            tenantId
         };
      }

      const prisma = await getPrismaClient(tenantId);
      const count = await prisma.channel.count({ where: { ...options.where } });
      options.take = size + 1;
      const items = await prisma.channel.findMany(options);
      let nextKey = '';

      if (items.length > size) {
         const last = items[size];
         nextKey = last.id;
         items.pop();
      }

      const response = {
         items,
         pagination: {
            // eslint-disable-next-line camelcase
            next_key: nextKey,
            // eslint-disable-next-line camelcase
            page_size: size,
            // eslint-disable-next-line camelcase
            total_records: count
         }
      };

      return reply.send(response);
   }

   public async replaceChannel(reply: FastifyReply, tenantId: string, id: string, body: CreateChannel) {
      const prisma = await getPrismaClient(tenantId);
      const exists = await prisma.channel.findUnique({
         include: { attributes: true },
         where: { id, tenantId }
      });

      if (!exists) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Channel {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      const re = /^\S+$/;

      if (!re.test(body.externalId ?? '')) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"externalId" cannot contain whitespace',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const duplicateExternalId = await prisma.channel.findFirst({
         where: { NOT: { id }, externalId: { equals: body.externalId, mode: 'insensitive' }, tenantId }
      });

      if (duplicateExternalId) {
         const error = {
            code: 'FST_ERR_CONFLICT',
            error: 'Conflict',
            message: `Channel with externalId {${body.externalId}} already exists`,
            statusCode: 409
         };

         return reply.code(409).send(error);
      }

      const existingService = await prisma.channel.findFirst({
         where: { NOT: { id }, channelType: body.channelType.toLowerCase(), service: body.service, tenantId }
      });

      if (existingService) {
         const error = {
            code: 'FST_ERR_CONFLICT',
            error: 'Conflict',
            message: `Channel with service (${body.service}) and type (${body.channelType}) already exists`,
            statusCode: 409
         };

         return reply.code(409).send(error);
      }

      const result = await this._validateAttributes(tenantId, id, body.attributes, body.service, body.provider ?? '');

      if (!result[0]) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: result[1],
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const attributes = result[2];

      const channel = await prisma.$transaction<Channel>(async (trx) => {
         const secureAttributes = await trx.channelAttribute.findMany({ where: { channelId: id, secure: true } });

         for (const attribute of secureAttributes) {
            if (!attributes.some((a: ChannelAttribute) => (a.name === attribute.name))) {
               await this._deleteSecureAttribute(tenantId, id, attribute);
            }
         }

         await trx.channelAttribute.deleteMany({ where: { channelId: id } });

         if (!body.provider) {
            switch (body.service) {
               case AttributeConstants.TEST:
                  body.provider = AttributeConstants.NO_PROVIDER;
                  break;
               case AttributeConstants.MODO:
                  body.provider = AttributeConstants.MODO;
                  break;
               case AttributeConstants.CANVAS:
                  body.provider = AttributeConstants.CANVAS;
                  break;
               case AttributeConstants.SLACK:
               case AttributeConstants.SLACK_WEBHOOK:
                  body.provider = AttributeConstants.SLACK;
                  break;
               case AttributeConstants.MS_TEAMS:
               case AttributeConstants.MS_TEAMS_WEBHOOK:
                  body.provider = AttributeConstants.MS_TEAMS;
                  break;
            }
         }

         return await trx.channel.update({
            data: {
               attributes: {
                  create: attributes
               },
               channelType: body.channelType.toLowerCase(),
               description: body.description,
               displayName: body.displayName,
               enabled: body.enabled,
               externalId: body.externalId,
               provider: body.provider,
               service: body.service,
               tenantId
            },
            include: { attributes: true },
            where: { id }
         });
      });

      return reply.send(Value.Clean(CreatedChannelBody, channel));
   }

   public async deleteChannel(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);

      const exists = await prisma.channel.findUnique({
         include: { attributes: true },
         where: { id, tenantId }
      });

      if (!exists) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Channel {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      const secureAttributes = await prisma.channelAttribute.findMany({ where: { channelId: id, secure: true } });

      for (const attribute of secureAttributes) {
         await this._deleteSecureAttribute(tenantId, id, attribute);
      }

      await prisma.channel.delete({ where: { id, tenantId } });

      return reply.code(204).send();
   }

   public async getAttribute(reply: FastifyReply, tenantId: string, channelId: string, attributeId: string) {
      const prisma = await getPrismaClient(tenantId);
      const attribute = await prisma.channelAttribute.findUnique({
         include: { channel: true },
         where: { channel: { id: channelId, tenantId }, channelId, id: attributeId }
      });

      if (!attribute) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Attribute {${attributeId}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      return reply.code(200).send(Value.Clean(CreatedAttributeBody, attribute));
   }

   public async updateAttribute(
      tenantId: string,
      channelId: string,
      attributeId: string,
      body: UpdateAttribute,
      reply: FastifyReply
   ) {
      const prisma = await getPrismaClient(tenantId);
      const exists = await prisma.channelAttribute.findUnique({
         include: { channel: true },
         where: { channel: { id: channelId, tenantId }, channelId, id: attributeId }
      });

      if (!exists) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Attribute {${attributeId}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      const updated = await prisma.channelAttribute.update({ data: body, where: { id: attributeId } });

      return reply.code(200).send(Value.Clean(CreatedAttributeBody, updated));
   }

   public async deleteAttribute(reply: FastifyReply, tenantId: string, channelId: string, attributeId: string) {
      const prisma = await getPrismaClient(tenantId);
      const exists = await prisma.channelAttribute.findUnique({
         include: { channel: true },
         where: { channel: { id: channelId, tenantId }, channelId, id: attributeId }
      });

      if (!exists) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Attribute {${attributeId}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      await prisma.channelAttribute.delete({ where: { id: attributeId } });

      return reply.code(204).send();
   }

   private async _setSecret(tenantId: string, channelId: string, secretName: string, secretValue: string) {
      await this._vault.setSecret(`${tenantId}-${channelId}-${secretName}`, secretValue);
   }

   private async _getSecret(tenantId: string, channelId: string, secretName: string): Promise<string> {
      try {
         return await this._vault.getSecret(`${tenantId}-${channelId}-${secretName}`);
      } catch {
         return '';
      }
   }

   private async _createSecureAttribute(
      tenantId: string,
      channelId: string,
      attribute: ChannelAttribute
   ): Promise<string> {
      if (attribute.value && !attribute.value.includes('******')) {
         const currentValue = await this._getSecret(tenantId, channelId, attribute.name);

         if (currentValue !== attribute.value) {
            await this._setSecret(tenantId, channelId, attribute.name, attribute.value || '');
         }

         return `${attribute.value.substring(0, 4)}******`;
      }

      return attribute.value;
   }

   private async _deleteSecureAttribute(
      tenantId: string,
      channelId: string,
      attribute: ChannelAttribute
   ): Promise<void> {
      await this._vault.deleteSecret(`${tenantId}-${channelId}-${attribute.name}`);
   }

   private async _validateCanvasAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> {
      const attributes: ChannelAttribute[] = [];

      let foundClientSecret = false;
      let foundRefreshToken = false;
      let foundHostname = false;
      let foundClientId = false;

      for (const a of attribute) {
         const id = 'id' in a && a.id ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.HOSTNAME:
               foundHostname = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.REFRESH_TOKEN:
               foundRefreshToken = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.CLIENT_SECRET:
               foundClientSecret = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.CLIENT_ID:
               foundClientId = true;
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundHostname) {
         return [false, `Attribute ${AttributeConstants.CANVAS_HOST} not found`, []];
      }

      if (!foundRefreshToken) {
         return [false, `Attribute ${AttributeConstants.REFRESH_TOKEN} not found`, []];
      }

      if (!foundClientSecret) {
         return [false, `Attribute ${AttributeConstants.CLIENT_SECRET} not found`, []];
      }

      if (!foundClientId) {
         return [false, `Attribute ${AttributeConstants.CLIENT_ID} not found`, []];
      }

      return [true, '', attributes];
   }

   private async _validateEsendexAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> {
      const attributes: ChannelAttribute[] = [];

      let foundPhone = false;
      let foundKey = false;

      for (const a of attribute) {
         const id = 'id' in a && a.id ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.PHONE:
               if (!validator.isMobilePhone(a.value)) {
                  return [false, `Attribute ${AttributeConstants.PHONE} is not a valid phone number`, []];
               }

               foundPhone = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.ESENDEX_KEY:
               foundKey = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundPhone) {
         return [false, `Attribute ${AttributeConstants.PHONE} not found`, []];
      }

      if (!foundKey) {
         return [false, `Attribute ${AttributeConstants.ESENDEX_KEY} not found`, []];
      }

      return [true, '', attributes];
   }

   private async _validateTwilioAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> {
      const attributes: ChannelAttribute[] = [];

      let foundPhone = false;
      let foundSid = false;
      let foundToken = false;

      for (const a of attribute) {
         const id = 'id' in a ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.PHONE:
               if (!validator.isMobilePhone(a.value)) {
                  return [false, `Attribute ${AttributeConstants.PHONE} is not a valid phone number`, []];
               }

               foundPhone = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.TWILIO_SID:
               foundSid = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.TWILIO_TOKEN:
               foundToken = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundPhone) {
         return [false, `Attribute ${AttributeConstants.PHONE} not found`, []];
      }

      if (!foundSid) {
         return [false, `Attribute ${AttributeConstants.TWILIO_SID} not found`, []];
      }

      if (!foundToken) {
         return [false, `Attribute ${AttributeConstants.TWILIO_TOKEN} not found`, []];
      }

      return [true, '', attributes];
   }

   // eslint-disable-next-line complexity
   private async _validateModoAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> {
      const attributes: ChannelAttribute[] = [];

      let foundAuthorization = false;
      let foundApplication = false;
      let foundTarget = false;
      let foundChannel = false;
      let foundFilter = false;
      let foundPush = false;
      let foundStyle = false;
      let foundGroup = false;
      let foundBanner = false;

      for (const a of attribute) {
         const id = 'id' in a ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.MODO_AUTHORIZATION:
               foundAuthorization = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_APPLICATION_ID:
               foundApplication = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_TARGET:
               if (!validator.isIn(a.value, ['test', 'production'])) {
                  return [false, `Attribute ${AttributeConstants.MODO_TARGET} must be test or production`, []];
               }

               foundTarget = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_CHANNEL:
               foundChannel = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_FILTER_KEY:
               foundFilter = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_PUSH:
               if (!validator.isBoolean(a.value)) {
                  return [false, `Attribute ${AttributeConstants.MODO_PUSH} must be true or false`, []];
               }

               foundPush = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_STYLE:
               if (!validator.isIn(a.value, ['information', 'warning', 'alarm'])) {
                  return [
                     false,
                     `Attribute ${AttributeConstants.MODO_STYLE} must be information, warning or alarm`,
                     []
                  ];
               }

               foundStyle = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_GROUP_ATTRIBUTE:
               foundGroup = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.MODO_BANNER:
               foundBanner = true;
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundAuthorization) {
         return [false, `Attribute ${AttributeConstants.MODO_AUTHORIZATION} not found`, []];
      }

      if (!foundApplication) {
         return [false, `Attribute ${AttributeConstants.MODO_APPLICATION_ID} not found`, []];
      }

      if (!foundBanner) {
         return [false, `Attribute ${AttributeConstants.MODO_BANNER} not found`, []];
      }

      if (!foundGroup) {
         return [false, `Attribute ${AttributeConstants.MODO_GROUP_ATTRIBUTE} not found`, []];
      }

      if (!foundTarget) {
         return [false, `Attribute ${AttributeConstants.MODO_TARGET} not found`, []];
      }

      if (!foundStyle) {
         return [false, `Attribute ${AttributeConstants.MODO_STYLE} not found`, []];
      }

      if (!foundPush) {
         return [false, `Attribute ${AttributeConstants.MODO_PUSH} not found`, []];
      }

      if (!foundFilter) {
         return [false, `Attribute ${AttributeConstants.MODO_FILTER_KEY} not found`, []];
      }

      if (!foundChannel) {
         return [false, `Attribute ${AttributeConstants.MODO_CHANNEL} not found`, []];
      }

      return [true, '', attributes];
   }

   private _validateWebhookAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): [success: boolean, error: string, attributes: ChannelAttribute[]] {
      const attributes: ChannelAttribute[] = [];

      let foundUrl = false;

      for (const a of attribute) {
         const id = 'id' in a ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.WEBHOOK_URL:
               if (!validator.isURL(a.value)) {
                  return [false, `Attribute ${AttributeConstants.WEBHOOK_URL} is not a valid URL`, []];
               }

               foundUrl = true;
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundUrl) {
         return [false, `Attribute ${AttributeConstants.WEBHOOK_URL} not found`, []];
      }

      return [true, '', attributes];
   }

   private async _validateSendgridAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> {
      const attributes: ChannelAttribute[] = [];

      let foundKey = false;
      let foundFrom = false;

      for (const a of attribute) {
         const id = 'id' in a ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.EMAIL:
               if (!validator.isEmail(a.value)) {
                  return [false, `Attribute ${AttributeConstants.EMAIL} is not a valid email address`, []];
               }

               foundFrom = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.SENDGRID_KEY:
               foundKey = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundKey) {
         return [false, `Attribute ${AttributeConstants.SENDGRID_KEY} not found`, []];
      }

      if (!foundFrom) {
         return [false, `Attribute ${AttributeConstants.EMAIL} not found`, []];
      }

      return [true, '', attributes];
   }

   private async _validateSmtpAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): Promise<[success: boolean, error: string, attributes: ChannelAttribute[]]> {
      const attributes: ChannelAttribute[] = [];

      let foundUsername = false;
      let foundPassword = false;
      let foundHostname = false;
      let foundPort = false;
      let foundTls = false;
      let foundFrom = false;

      for (const a of attribute) {
         const id = 'id' in a ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.SMTP_USER:
               foundUsername = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.SMTP_PASSWORD:
               foundPassword = true;
               channelAttribute.secure = true;
               channelAttribute.value = await this._createSecureAttribute(tenantId, channelId, channelAttribute);
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.SMTP_HOST:
               foundHostname = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.SMTP_PORT:
               if (!validator.isInt(a.value)) {
                  return [false, `Attribute ${a.name} is not an integer`, []];
               }

               foundPort = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.SMTP_FROM:
               if (!validator.isEmail(a.value)) {
                  return [false, `Attribute ${AttributeConstants.SMTP_FROM} is not a valid email address`, []];
               }

               foundFrom = true;
               attributes.push(channelAttribute);
               break;
            case AttributeConstants.SMTP_SECURE:
               foundTls = true;
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundUsername) {
         return [false, `Attribute ${AttributeConstants.SMTP_USER} not found`, []];
      }

      if (!foundPassword) {
         return [false, `Attribute ${AttributeConstants.SMTP_PASSWORD} not found`, []];
      }

      if (!foundHostname) {
         return [false, `Attribute ${AttributeConstants.SMTP_HOST} not found`, []];
      }

      if (!foundPort) {
         return [false, `Attribute ${AttributeConstants.SMTP_PORT} not found`, []];
      }

      if (!foundFrom) {
         return [false, `Attribute ${AttributeConstants.SMTP_FROM} not found`, []];
      }

      if (!foundTls) {
         return [false, `Attribute ${AttributeConstants.SMTP_SECURE} not found`, []];
      }

      return [true, '', attributes];
   }

   private _validateMsTeamsAttribute(
      tenantId: string,
      channelId: string,
      attribute: CreateAttribute[] | UpdateAttribute[]
   ): [success: boolean, error: string, attributes: ChannelAttribute[]] {
      const attributes: ChannelAttribute[] = [];

      let foundTenantId = false;

      for (const a of attribute) {
         const id = 'id' in a ? a.id : uuid();

         if (!a.value) {
            return [false, `Attribute ${a.name} has no value`, []];
         }

         const channelAttribute: ChannelAttribute = {
            id,
            name: a.name,
            secure: false,
            value: a.value
         };

         switch (a.name) {
            case AttributeConstants.TENANT_ID:
               foundTenantId = true;
               attributes.push(channelAttribute);
               break;
         }
      }

      if (!foundTenantId) {
         return [false, `Attribute ${AttributeConstants.TENANT_ID} not found`, []];
      }

      return [true, '', attributes];
   }
}
