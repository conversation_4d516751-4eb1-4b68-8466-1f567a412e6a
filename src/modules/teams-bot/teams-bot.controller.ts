import { FastifyReply, FastifyRequest } from 'fastify';

import { MessageActivity } from './teams-bot.interface.js';
import { TeamsBotService } from './teams-bot.service.js';

export class TeamsBotController {
   public processMessage = async (req: FastifyRequest<{ Body: MessageActivity }>, reply: FastifyReply) => {
      await this._teamsBotService.processMessage(req, reply);
   };

   private _teamsBotService = new TeamsBotService();
}
