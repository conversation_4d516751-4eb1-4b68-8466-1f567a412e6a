import { TeamsActivityHandler } from 'botbuilder';
import { FastifyBaseLogger } from 'fastify';

import { getLogger } from '../../../utils/getLogger.js';

export class TeamsBot extends TeamsActivityHandler {
   private _logger: FastifyBaseLogger;

   constructor() {
      super();
      this._logger = getLogger();

      // Handle when members are added to the conversation
      this.onMembersAdded(async (context, next) => {
         this._logger.trace({ context, function: 'TeamsBot - onMembersAdded' }, 'TeamsBot - onMembersAdded');

         const membersAdded = context.activity.membersAdded ?? [];

         for (const member of membersAdded) {
            // Skip if the member added is the bot itself
            if (member.id !== context.activity.recipient.id) {
               // Check if this is a personal scope conversation (1:1 chat)
               const isPersonalScope = context.activity.conversation.conversationType === 'personal';

               if (isPersonalScope) {
                  // Send a welcome message in personal scope
                  await context.sendActivity(
                     // eslint-disable-next-line max-len
                     `Hi ${member.name || 'there'}! Welcome from the xSIGNAL Bot! 👋\n\nI'm here to deliver important notifications to you.`
                  );
               }
            }
         }

         await next();
      });

      // NOTE: we aren't using this right now, we've disabled two-way
      this.onMessage(async (context, next) => {
         const tenantId = context.activity.conversation.tenantId;
         const conversationId = context.activity.conversation.id;
         const userAadObjectId = context.activity.from.aadObjectId;

         this._logger.trace(
            {
               data: { conversationId, text: context.activity.text, userAadObjectId },
               tenantId
            },
            'TeamsBot - onMessage'
         );

         if (!userAadObjectId || !tenantId || !conversationId) {
            this._logger.info({ context, function: 'TeamsBot - onMessage', tenantId }, 'Invalid message context');

            return;
         }

         await context.sendActivity(
            'Hi! Please note that I am a notification-only bot and you cannot interact with me (yet)'
         );

         await next();
      });
   }
}
