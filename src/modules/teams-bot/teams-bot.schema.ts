import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

const ChannelAccount = Type.Object({
   id: Type.String(),
   name: Type.Optional(Type.String()),
   role: Type.Optional(Type.String())
});

const ConversationAccount = Type.Object({
   conversationType: Type.Optional(Type.String()),
   id: Type.String(),
   isGroup: Type.Optional(Type.Boolean()),
   name: Type.Optional(Type.String())
});

const Attachment = Type.Object({
   content: Type.Optional(Type.Unknown()),
   contentType: Type.String(),
   contentUrl: Type.Optional(Type.String()),
   name: Type.Optional(Type.String()),
   thumbnailUrl: Type.Optional(Type.String())
});

const CardAction = Type.Object({
   title: Type.Optional(Type.String()),
   type: Type.String(),
   value: Type.Optional(Type.Any())
});

const SuggestedActions = Type.Object({
   actions: Type.Array(CardAction)
});

export const MessageActivityBody = Type.Object({
   attachments: Type.Optional(Type.Array(Attachment)),
   channelData: Type.Optional(Type.Any()),
   channelId: Type.String(),
   conversation: ConversationAccount,
   entities: Type.Optional(Type.Array(Type.Unknown())),
   from: ChannelAccount,
   id: Type.Optional(Type.String()),
   inputHint: Type.Optional(Type.String()),
   localTimestamp: Type.Optional(Type.String()),
   locale: Type.Optional(Type.String()),
   recipient: Type.Optional(ChannelAccount),
   replyToId: Type.Optional(Type.String()),
   speak: Type.Optional(Type.String()),
   suggestedActions: Type.Optional(SuggestedActions),
   text: Type.Optional(Type.String()),
   textFormat: Type.Optional(Type.String()),
   timestamp: Type.Optional(Type.String()),
   type: Type.String()
});

export const MessageActivitySchema: FastifySchema = {
   body: MessageActivityBody,
   description: 'Receive a message activity from Microsoft Teams',
   response: {
      200: Type.Object({
         message: Type.String(),
         status: Type.String()
      })
   }
};
