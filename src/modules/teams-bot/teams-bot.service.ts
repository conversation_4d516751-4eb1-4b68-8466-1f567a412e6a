/* eslint-disable @typescript-eslint/no-explicit-any */
import { IncomingMessage, ServerResponse } from 'node:http';

import { TurnContext } from 'botbuilder';
import { FastifyReply, FastifyRequest } from 'fastify';

import { notificationApp } from './internal/initialize.js';
import { TeamsBot } from './internal/teams-bot.js';

type ExpressRequest = IncomingMessage & {
   body: any;
   headers: Record<string, string>;
   params: any;
   query: any;
   url: string;
};

type ExpressResponse = ServerResponse & {
   header: (name: string, value: string) => void;
   send: (body: any) => void;
   status: (code: number) => void;
};

const convertRequest = (request: FastifyRequest): ExpressRequest => {
   const rawRequest = request.raw;

   // Create an Express-like request
   const expressRequest = rawRequest as unknown as ExpressRequest;

   // Add the parsed body from Fastify
   expressRequest.body = request.body;
   expressRequest.query = request.query;
   expressRequest.headers = request.headers as Record<string, string>;
   expressRequest.params = request.params ?? {};
   expressRequest.url = request.url;

   return expressRequest;
};

const convertResponse = (reply: FastifyReply): ExpressResponse => {
   const response = reply.raw as ServerResponse;

   // Add methods directly to the response object
   const expressResponse = response as unknown as ExpressResponse;

   expressResponse.header = (name: string, value: string) => {
      response.setHeader(name, value);
   };

   expressResponse.send = (body: any) => {
      response.write(body);
      response.end();
   };

   expressResponse.status = (code: number) => {
      response.statusCode = code;
   };

   return expressResponse;
};

export class TeamsBotService {
   private _teamsBot: TeamsBot = new TeamsBot();

   public async processMessage(request: FastifyRequest, reply: FastifyReply) {
      await notificationApp.requestHandler(
         convertRequest(request),
         convertResponse(reply),
         async (context: TurnContext) => {
            await this._teamsBot.run(context);
         }
      );
   }
}
