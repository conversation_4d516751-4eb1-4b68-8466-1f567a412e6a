import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';

import { TeamsBotController } from './teams-bot.controller.js';
import { MessageActivitySchema } from './teams-bot.schema.js';

export class TeamsBotRoute implements Route {
   public path = '/teams/messages';
   private _teamsBotController: TeamsBotController;

   constructor() {
      this._teamsBotController = new TeamsBotController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._teamsBotController.processMessage,
         method: 'post',
         schema: MessageActivitySchema,
         url: this.path
      });

      done();
   }
}
