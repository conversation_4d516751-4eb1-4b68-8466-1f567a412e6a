import { GetTableEntityResponse, TableClient, TableEntityResult } from '@azure/data-tables';
import { DefaultAzureCredential } from '@azure/identity';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import baseX from 'base-x';
import { FastifyReply } from 'fastify';
import { v7 as uuid } from 'uuid';

const base = baseX('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ');

export class LinkService {
   private _tableClient: TableClient;
   private _tableName = process.env.SHORT_LINK_TABLE_NAME ?? '';
   private _tableUrl = process.env.SHORT_LINK_TABLE_URL ?? '';

   constructor() {
      const credential = new DefaultAzureCredential();
      this._tableClient = new TableClient(this._tableUrl, this._tableName, credential);
   }

   public async getLinkById(reply: FastifyReply, tenantId: string, id: string): Promise<void> {
      const prisma = await getPrismaClient(tenantId);

      const link = await prisma.trackedLink.findUnique({
         where: {
            id,
            tenantId
         }
      });

      return reply.status(200).send(link);
   }

   private async _getEntity(
      partitionKey: string,
      rowKey: string
   ): Promise<GetTableEntityResponse<TableEntityResult<Record<string, unknown>>> | null> {
      try {
         return await this._tableClient.getEntity(partitionKey, rowKey);
      } catch (error: unknown) {

         if ((error as {statusCode?: number}).statusCode === 404) {
            return null; // Entity does not exist
         }

         throw error; // Re-throw other errors
      }
   }

   private async _createHash(url: string, tracking: boolean): Promise<string> {
      const encoder = new TextEncoder();
      let data: Uint8Array;

      if (tracking) {
         data = encoder.encode(`${url}:${uuid()}`);
      } else {
         data = encoder.encode(url);
      }

      const fullHashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(fullHashBuffer));
      const truncatedBuffer = hashArray.slice(0, 24);

      return base.encode(truncatedBuffer);
   }

   public async createTrackedLink(reply: FastifyReply, tenantId: string, url: string): Promise<void> {
      const prisma = await getPrismaClient(tenantId);

      if (!url.toLocaleLowerCase().startsWith('http')) {
         const error = {
            code: 'FST_ERR_INVALID_URL',
            error: 'Invalid URL',
            message: `Invalid URL: ${url}`,
            statusCode: 400
         };

         return reply.status(400).send(error);
      }

      const hash = await this._createHash(url, true);
      const id = uuid();

      await prisma.trackedLink.create({
         data: {
            hash,
            id,
            tenantId,
            url
         }
      });

      await this._tableClient.createEntity({
         partitionKey: hash,
         rowKey: '',
         tenantId,
         url
      });

      return await reply.status(201).send({ hash, id });
   }

   public async createShortLink(reply: FastifyReply, url: string): Promise<void> {

      if (!url.toLocaleLowerCase().startsWith('http')) {
         const error = {
            code: 'FST_ERR_INVALID_URL',
            error: 'Invalid URL',
            message: `Invalid URL: ${url}`,
            statusCode: 400
         };

         return reply.status(400).send(error);
      }

      const hash = await this._createHash(url, false);

      const shortLink = await this._getEntity(hash, '');

      if (!shortLink) {
         await this._tableClient.createEntity({
            partitionKey: hash,
            rowKey: '',
            tenantId: '',
            url
         });
      }

      return await reply.status(201).send({ hash });
   }
}
