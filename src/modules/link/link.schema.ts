import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

export const LinkBody = Type.Object({
   url: Type.String({ format: 'uri' })
});

export const GetById = Type.Object({
   id: Type.String()
});

export const GetTrackedLink = Type.Object({
   clickCount: Type.Optional(Type.Number()),
   firstClick: Type.Optional(Type.String()),
   hash: Type.String(),
   id: Type.String(),
   lastClick: Type.Optional(Type.String()),
   url: Type.String({ format: 'uri' })
});

export const CreatedTrackedLink = Type.Object({
   hash: Type.String(),
   id: Type.String()
});

export const CreatedShortLink = Type.Object({
   hash: Type.String()
});

export const CreateTrackedLinkSchema: FastifySchema = {
   body: LinkBody,
   hide: true,
   response: {
      201: CreatedTrackedLink
   }
};

export const CreateShortLinkSchema: FastifySchema = {
   body: LinkBody,
   hide: true,
   response: {
      201: CreatedShortLink
   }
};

export const GetTrackedLinkSchema: FastifySchema = {
   params: GetById,
   response: {
      200: GetTrackedLink
   }
};

export const GetLinkByIdSchema: FastifySchema = {
   hide: true,
   params: GetById,
   response: {
      302: {}
   }
};
