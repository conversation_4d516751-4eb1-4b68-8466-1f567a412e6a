import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { LinkController } from './link.controller.js';
import {
   CreateShortLinkSchema,
   CreateTrackedLinkSchema,
   GetLinkByIdSchema,
   GetTrackedLinkSchema
} from './link.schema.js';

export class LinkRoute implements Route {
   public path = '/tracked-link';
   private _linkController: LinkController;
   private _readScopes = [Scopes.ADMIN_WRITE, Scopes.LINK_READ];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.LINK_WRITE];

   constructor() {
      this._linkController = new LinkController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._linkController.getLinkById,
         method: 'get',
         schema: GetLinkByIdSchema,
         url: `${this.path}/:tenantId/:id`
      });

      fastify.route({
         handler: this._linkController.getLinkById,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTrackedLinkSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._linkController.createTrackedLink,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateTrackedLinkSchema,
         url: this.path
      });

      fastify.route({
         handler: this._linkController.createShortLink,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateShortLinkSchema,
         url: '/short-link'
      });

      done();
   }
}
