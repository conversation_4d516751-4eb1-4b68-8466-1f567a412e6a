import { FastifyReply, FastifyRequest } from 'fastify';

import { GetById, Link } from './link.interface.js';
import { LinkService } from './link.service.js';

export class LinkController {
   public getLinkById = async (req: FastifyRequest<{ Params: GetById }>, reply: FastifyReply) => {
      await this._linkService.getLinkById(reply, req.tenantId, req.params.id);
   };

   public createTrackedLink = async (req: FastifyRequest<{ Body: Link }>, reply: FastifyReply) => {
      await this._linkService.createTrackedLink(reply, req.tenantId, req.body.url);
   };

   public createShortLink = async (req: FastifyRequest<{ Body: Link }>, reply: FastifyReply) => {
      await this._linkService.createShortLink(reply, req.body.url);
   };

   private _linkService = new LinkService();
}
