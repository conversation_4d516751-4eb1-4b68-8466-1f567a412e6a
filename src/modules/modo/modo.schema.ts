import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

export const ModoRecipient = Type.Object({
   attributes: Type.Any(),
   id: Type.String()
});

export const ModoRecipientsBody = Type.Object({
   channelId: Type.String(),
   recipients: Type.Array(ModoRecipient)
});

export const GetModoRecipientQuery = Type.Object({
   channelId: Type.String()
});

export const GetModoRecipientParams = Type.Object({
   id: Type.String()
});

export const GetModoRecipientsQuery = Type.Object({
   channelId: Type.String(),
   page: Type.Optional(Type.Number({ default: 1 })),
   pageSize: Type.Optional(Type.Number({ default: 100 }))
});

export const DeleteModoRecipientParams = Type.Object({
   id: Type.String()
});

export const DeleteModoRecipientQuery = Type.Object({
   channelId: Type.String(),
   disable: Type.Optional(Type.Boolean({ default: false }))
});

export const ModoUpsertRecipientsSchema: FastifySchema = {
   body: ModoRecipientsBody,
   hide: true
};

export const ModoDeleteRecipientSchema: FastifySchema = {
   hide: true,
   params: DeleteModoRecipientParams,
   querystring: DeleteModoRecipientQuery
};

export const ModoGetRecipientSchema: FastifySchema = {
   hide: true,
   params: GetModoRecipientParams,
   querystring: GetModoRecipientQuery
};

export const ModoGetRecipientsSchema: FastifySchema = {
   hide: true,
   querystring: GetModoRecipientsQuery
};
