import { PrismaClient } from '@prisma/client';
import { AttributeConstants, SecretVault, SecretVaultFactory } from '@x-signal-inc/messaging-common';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';
import { LRUCache } from 'lru-cache';

import { ModoRecipients } from './modo.interface.js';

type ModoOptions = {
   applicationId: string;
   jwt: string;
   target: string;
};

const LRUOptions: LRUCache.Options<string, ModoOptions, unknown> = {
   allowStale: false,
   max: 500 /* 5 minutes */,
   ttl: 1000 * 60 * 5
};

export class ModoService {
   private static readonly _MODO_BASE_URL_ = 'https://communicate.modolabs.net/api';
   private _vault: SecretVault;
   private _cache = new LRUCache<string, ModoOptions>(LRUOptions);

   private _getModoOptions = async (
      prisma: PrismaClient,
      tenantId: string,
      channelId: string
   ): Promise<ModoOptions | null> => {
      let options: ModoOptions | undefined = this._cache.get(tenantId);

      if (options) {
         return options;
      }

      const channel = await prisma.channel.findUnique({
         include: { attributes: true },
         where: { id: channelId, tenantId }
      });

      if (!channel) {
         return null;
      }

      const attributes = channel.attributes;
      const applicationName: string = AttributeConstants.MODO_APPLICATION_ID;
      const targetName: string = AttributeConstants.MODO_TARGET;

      const applicationId = attributes.find((a) => a.name === applicationName)?.value;
      const target = attributes.find((a) => a.name === targetName)?.value;

      const secretId = `${tenantId}-${channel.id}-${AttributeConstants.MODO_AUTHORIZATION}`;
      const jwt = await this._vault.getSecret(secretId);

      if (!applicationId || !jwt || !target) {
         return null;
      }

      options = { applicationId, jwt, target };
      this._cache.set(tenantId, options);

      return options;
   };

   constructor() {
      this._vault = SecretVaultFactory.getSecretVault(true);
   }

   async deleteRecipient(
      reply: FastifyReply,
      tenantId: string,
      id: string,
      channelId: string,
      disable: boolean
   ): Promise<void> {
      const prisma = await getPrismaClient(tenantId);
      const options = await this._getModoOptions(prisma, tenantId, channelId);

      if (!options) {
         const error = {
            code: 'FST_ERR_INTERNAL_SERVER_ERROR',
            error: 'Missing channel configuration',
            message: 'Modo credentials are missing',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      // eslint-disable-next-line max-len
      const url = `${ModoService._MODO_BASE_URL_}/applications/${options.applicationId}/${options.target}/recipients/${id}`;

      const response = await fetch(url, {
         headers: {
            Accept: 'application/vnd.modo.communicate.v2',
            Authorization: `Bearer ${options.jwt}`
         },
         method: 'DELETE'
      });

      if (response.status >= 400) {
         if (response.status === 404) {
            const error = {
               code: 'FST_ERR_NOT_FOUND',
               error: 'Not Found',
               message: `Recipient {${id}} not found`,
               statusCode: 404
            };

            return reply.code(404).send(error);
         }

         return reply.code(404).send(await response.json());
      }

      if (disable) {
         const recipient = await prisma.recipient.findFirst({
            where: { identifiers: { some: { id } } }
         });

         if (recipient) {
            await prisma.recipient.update({
               data: { enabled: false },
               where: { id: recipient.id }
            });
         }
      }

      return reply.code(200).send(await response.json());
   }

   async upsertRecipients(reply: FastifyReply, tenantId: string, body: ModoRecipients) {
      const prisma = await getPrismaClient(tenantId);
      const options = await this._getModoOptions(prisma, tenantId, body.channelId);

      if (!options) {
         const error = {
            code: 'FST_ERR_INTERNAL_SERVER_ERROR',
            error: 'Missing channel configuration',
            message: 'Modo credentials are missing',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      const url = `${ModoService._MODO_BASE_URL_}/applications/${options.applicationId}/${options.target}/recipients`;
      const response = await fetch(url, {
         body: JSON.stringify(body),
         headers: {
            Accept: 'application/vnd.modo.communicate.v2',
            Authorization: `Bearer ${options.jwt}`,
            'content-type': 'application/json'
         },
         method: 'PATCH'
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const responseJson = await response.json();

      if (response.status >= 400) {
         return reply.code(response.status).send(responseJson);
      }

      return reply.code(200).send(responseJson);
   }

   async getRecipients(reply: FastifyReply, tenantId: string, channelId: string, page: number, pageSize: number) {
      const prisma = await getPrismaClient(tenantId);
      const options = await this._getModoOptions(prisma, tenantId, channelId);

      if (!options) {
         const error = {
            code: 'FST_ERR_INTERNAL_SERVER_ERROR',
            error: 'Missing channel configuration',
            message: 'Modo credentials are missing',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      // eslint-disable-next-line max-len
      const url = `${ModoService._MODO_BASE_URL_}/applications/${options.applicationId}/${options.target}/recipients?page_number=${page}&per_page=${pageSize}`;
      const response = await fetch(url, {
         headers: {
            Accept: 'application/vnd.modo.communicate.v2',
            Authorization: `Bearer ${options.jwt}`,
            'Content-Type': 'application/json'
         },
         method: 'GET'
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const responseJson = await response.json();

      if (response.status >= 400) {
         return reply.code(404).send(responseJson);
      }

      return reply.code(200).send(responseJson);
   }

   async getRecipient(reply: FastifyReply, tenantId: string, id: string, channelId: string) {
      const prisma = await getPrismaClient(tenantId);
      const options = await this._getModoOptions(prisma, tenantId, channelId);

      if (!options) {
         const error = {
            code: 'FST_ERR_INTERNAL_SERVER_ERROR',
            error: 'Missing channel configuration',
            message: 'Modo credentials are missing',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      // eslint-disable-next-line max-len
      const url = `${ModoService._MODO_BASE_URL_}/applications/${options.applicationId}/${options.target}/recipients/${id}`;
      const response = await fetch(url, {
         headers: {
            Accept: 'application/vnd.modo.communicate.v2',
            Authorization: `Bearer ${options.jwt}`,
            'Content-Type': 'application/json'
         },
         method: 'GET'
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const responseJson = await response.json();

      if (response.status >= 400) {
         return reply.code(404).send(responseJson);
      }

      return reply.code(200).send(responseJson);
   }
}
