import { Static } from '@fastify/type-provider-typebox';

import {
   DeleteModoRecipientParams,
   DeleteModoRecipientQuery,
   GetModoRecipientParams,
   GetModoRecipientQuery,
   GetModoRecipientsQuery,
   ModoRecipientsBody
} from './modo.schema.js';

export type ModoRecipients = Static<typeof ModoRecipientsBody>;
export type DeleteModoRecipientParams = Static<typeof DeleteModoRecipientParams>;
export type DeleteModoRecipientQuery = Static<typeof DeleteModoRecipientQuery>;
export type GetModoRecipientParams = Static<typeof GetModoRecipientParams>;
export type GetModoRecipientQuery = Static<typeof GetModoRecipientQuery>;
export type GetModoRecipientsQuery = Static<typeof GetModoRecipientsQuery>;
