import { FastifyReply, FastifyRequest } from 'fastify';

import {
   DeleteModoRecipientParams,
   DeleteModoRecipientQuery,
   GetModoRecipientParams,
   GetModoRecipientQuery,
   GetModoRecipientsQuery,
   ModoRecipients
} from './modo.interface.js';
import { ModoService } from './modo.service.js';

export class ModoController {
   public deleteRecipient = async (
      req: FastifyRequest<{ Params: DeleteModoRecipientParams; Querystring: DeleteModoRecipientQuery }>,
      reply: FastifyReply
   ) => {
      await this._modoService.deleteRecipient(
         reply,
         req.tenantId,
         req.params.id,
         req.query.channelId,
         req.query.disable ?? false
      );
   };

   public upsertRecipients = async (req: FastifyRequest<{ Body: ModoRecipients }>, reply: FastifyReply) => {
      await this._modoService.upsertRecipients(reply, req.tenantId, req.body);
   };

   public getRecipient = async (
      req: FastifyRequest<{ Params: GetModoRecipientParams; Querystring: GetModoRecipientQuery }>,
      reply: FastifyReply
   ) => {
      await this._modoService.getRecipient(reply, req.tenantId, req.params.id, req.query.channelId);
   };

   public getRecipients = async (req: FastifyRequest<{ Querystring: GetModoRecipientsQuery }>, reply: FastifyReply) => {
      await this._modoService.getRecipients(
         reply,
         req.tenantId,
         req.query.channelId,
         req.query.page ?? 1,
         req.query.pageSize ?? 100
      );
   };

   private _modoService = new ModoService();
}
