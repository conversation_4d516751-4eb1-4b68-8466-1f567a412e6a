import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { ModoController } from './modo.controller.js';
import {
   ModoDeleteRecipientSchema,
   ModoGetRecipientSchema,
   ModoGetRecipientsSchema,
   ModoUpsertRecipientsSchema
} from './modo.schema.js';

export class ModoRoute implements Route {
   public path = '/modo-recipient';
   private _modoController: ModoController;
   private _readScopes = [Scopes.ADMIN_WRITE, Scopes.MODO_READ];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.MODO_WRITE];

   constructor() {
      this._modoController = new ModoController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._modoController.deleteRecipient,
         method: 'delete',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: ModoDeleteRecipientSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._modoController.getRecipients,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: ModoGetRecipientsSchema,
         url: this.path
      });

      fastify.route({
         handler: this._modoController.getRecipient,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: ModoGetRecipientSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._modoController.upsertRecipients,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: ModoUpsertRecipientsSchema,
         url: this.path
      });

      done();
   }
}
