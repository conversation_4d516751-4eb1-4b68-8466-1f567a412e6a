import { Static } from '@fastify/type-provider-typebox';

import {
   CreatedTopicBody,
   CreateTopicBody,
   GetTopicParams,
   GetTopicsBody,
   GetTopicsParams,
   UpdateTopicBody
} from './topic.schema.js';

export type CreateTopic = Static<typeof CreateTopicBody>;
export type CreatedTopic = Static<typeof CreatedTopicBody>;
export type GetTopic = Static<typeof CreatedTopicBody>;
export type GetTopics = Static<typeof GetTopicsBody>;
export type GetTopicParams = Static<typeof GetTopicParams>;
export type GetTopicsParams = Static<typeof GetTopicsParams>;
export type UpdateTopic = Static<typeof UpdateTopicBody>;
