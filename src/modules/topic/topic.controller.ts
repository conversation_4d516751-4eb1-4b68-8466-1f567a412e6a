import { FastifyReply, FastifyRequest } from 'fastify';

import { CreateTopic, GetTopicParams, GetTopicsParams, UpdateTopic } from './topic.interface.js';
import { TopicService } from './topic.service.js';

export class TopicController {
   public createTopic = async (req: FastifyRequest<{ Body: CreateTopic }>, reply: FastifyReply) => {
      await this._topicService.createTopic(reply, req.tenantId, req.body);
   };

   public updateTopic = async (
      req: FastifyRequest<{ Body: UpdateTopic; Params: GetTopicParams }>,
      reply: FastifyReply
   ) => {
      await this._topicService.updateTopic(reply, req.tenantId, req.params.id, req.body);
   };

   public getTopic = async (req: FastifyRequest<{ Params: GetTopicParams }>, reply: FastifyReply) => {
      await this._topicService.getTopic(reply, req.tenantId, req.params.id);
   };

   public getTopics = async (req: FastifyRequest<{ Querystring: GetTopicsParams }>, reply: FastifyReply) => {
      await this._topicService.getTopics(reply, req.tenantId, req.query.key, req.query.size);
   };

   public deleteTopic = async (req: FastifyRequest<{ Params: GetTopicParams }>, reply: FastifyReply) => {
      await this._topicService.deleteTopic(reply, req.tenantId, req.params.id);
   };

   private _topicService = new TopicService();
}
