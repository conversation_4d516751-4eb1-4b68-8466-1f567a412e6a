import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

import { ChannelServices } from '../channel/channel-services.js';
import { PaginationResponse } from '../shared/pagination/pagination.schema.js';

export const CreateTopicBody = Type.Object({
   channelAlwaysOn: Type.Optional(Type.Boolean({ default: false })),
   channelType: Type.String({ maxLength: 30, minLength: 1 }),
   defaultCategory: Type.Optional(Type.String({ default: '' })),
   defaultService: Type.String({ enum: ChannelServices }),
   description: Type.Optional(Type.String()),
   displayName: Type.String({ maxLength: 30, minLength: 1 }),
   enabled: Type.Optional(Type.Boolean({ default: true })),
   externalId: Type.Optional(Type.String({ pattern: '^[a-zA-Z0-9-_]+$' })),
   orderSequence: Type.Optional(Type.Number({ default: 1 })),
   roles: Type.Optional(Type.Array(Type.String())),
   userMustOptIn: Type.Optional(Type.Boolean({ default: false })),
   userPreferenceRoles: Type.Optional(Type.Array(Type.String())),
   visibleInPreferences: Type.Optional(Type.Boolean({ default: true }))
});

export const CreatedTopicBody = Type.Object({
   channelAlwaysOn: Type.Boolean(),
   channelType: Type.String(),
   defaultCategory: Type.String(),
   defaultService: Type.String({ enum: ChannelServices }),
   description: Type.String(),
   displayName: Type.String(),
   enabled: Type.Boolean({ default: true }),
   externalId: Type.String({ pattern: '^[a-zA-Z0-9-_]+$' }),
   id: Type.String({ format: 'uuid' }),
   orderSequence: Type.Number({ default: 1 }),
   roles: Type.Array(Type.String()),
   userMustOptIn: Type.Boolean({ default: false }),
   userPreferenceRoles: Type.Array(Type.String()),
   visibleInPreferences: Type.Boolean({ default: true })
});

export const GetTopicsBody = Type.Object({
   items: Type.Array(CreatedTopicBody),
   pagination: PaginationResponse
});

export const UpdateTopicBody = Type.Object({
   channelAlwaysOn: Type.Optional(Type.Boolean()),
   channelType: Type.Optional(Type.String()),
   defaultCategory: Type.Optional(Type.String()),
   defaultService: Type.Optional(Type.String({ enum: ChannelServices })),
   description: Type.Optional(Type.String()),
   displayName: Type.Optional(Type.String({ maxLength: 30, minLength: 1 })),
   enabled: Type.Optional(Type.Boolean({ default: true })),
   externalId: Type.Optional(Type.String({ pattern: '^[a-zA-Z0-9-_]+$' })),
   orderSequence: Type.Optional(Type.Number({ default: 1 })),
   roles: Type.Optional(Type.Array(Type.String())),
   type: Type.Optional(Type.String({ maxLength: 30, minLength: 1 })),
   userMustOptIn: Type.Optional(Type.Boolean({ default: false })),
   userPreferenceRoles: Type.Optional(Type.Array(Type.String())),
   visibleInPreferences: Type.Optional(Type.Boolean({ default: true }))
});

export const GetTopicParams = Type.Object({
   id: Type.String()
});

export const GetTopicsParams = Type.Object({
   key: Type.Optional(Type.String()),
   size: Type.Optional(Type.Number({ default: 50, maximum: 100, minimum: 1 }))
});

export const CreateTopicSchema: FastifySchema = {
   body: CreateTopicBody,
   description: 'Create a topic',
   response: {
      201: CreatedTopicBody
   }
};

export const GetTopicSchema: FastifySchema = {
   description: 'Get a topic by ID',
   params: GetTopicParams,
   response: {
      200: CreatedTopicBody
   }
};

export const GetTopicByExternalIdSchema: FastifySchema = {
   description: 'Get a topic by name',
   params: GetTopicParams,
   response: {
      200: CreatedTopicBody
   }
};

export const GetTopicsSchema: FastifySchema = {
   description: 'Get topics',
   params: GetTopicsParams,
   querystring: GetTopicsParams,
   response: {
      200: GetTopicsBody
   }
};

export const UpdateTopicSchema: FastifySchema = {
   body: UpdateTopicBody,
   description: 'Update a topic',
   params: GetTopicParams,
   response: {
      200: CreatedTopicBody
   }
};

export const DeleteTopicSchema: FastifySchema = {
   description: 'Delete a topic',
   params: GetTopicParams,
   response: {
      204: Type.Null()
   }
};
