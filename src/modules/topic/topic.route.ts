import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';
import { Scopes } from '../../utils/scopes.js';

import { TopicController } from './topic.controller.js';
import {
   CreateTopicSchema,
   DeleteTopicSchema,
   GetTopicSchema,
   GetTopicsSchema,
   UpdateTopicSchema
} from './topic.schema.js';

export class TopicRoute implements Route {
   public path = '/topic';
   private _topicController: TopicController;
   private _readScopes = [Scopes.ADMIN_READ, Scopes.ADMIN_WRITE, Scopes.TOPIC_READ, Scopes.TOPIC_WRITE];
   private _writeScopes = [Scopes.ADMIN_WRITE, Scopes.TOPIC_WRITE];

   constructor() {
      this._topicController = new TopicController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.route({
         handler: this._topicController.createTopic,
         method: 'post',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: CreateTopicSchema,
         url: this.path
      });

      fastify.route({
         handler: this._topicController.getTopic,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTopicSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._topicController.getTopics,
         method: 'get',
         preHandler: fastify.authenticate(this._readScopes),
         schema: GetTopicsSchema,
         url: this.path
      });

      fastify.route({
         handler: this._topicController.updateTopic,
         method: 'patch',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: UpdateTopicSchema,
         url: `${this.path}/:id`
      });

      fastify.route({
         handler: this._topicController.deleteTopic,
         method: 'delete',
         preHandler: fastify.authenticate(this._writeScopes),
         schema: DeleteTopicSchema,
         url: `${this.path}/:id`
      });

      done();
   }
}
