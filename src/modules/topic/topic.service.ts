import { Prisma } from '@prisma/client';
import { Value } from '@sinclair/typebox/value';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';
import { v7 as uuid } from 'uuid';

import { DefaultPaginationOptions } from '../../interfaces/pagination-options.js';

import { CreateTopic, UpdateTopic } from './topic.interface.js';
import { CreatedTopicBody } from './topic.schema.js';

export class TopicService {
   public async createTopic(reply: FastifyReply, tenantId: string, body: CreateTopic) {
      const re = /^\S+$/;

      if (!re.test(body.externalId ?? '')) {
         const error = {
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"externalId" cannot contain whitespace',
            statusCode: 400
         };

         return reply.code(400).send(error);
      }

      if (body.displayName.trim() === '') {
         return reply.code(400).send({
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"displayName" cannot be empty',
            statusCode: 400
         });
      }

      const prisma = await getPrismaClient(tenantId);
      const exists = await prisma.topic.findFirst({
         where: {
            externalId: {
               equals: body.externalId,
               mode: 'insensitive'
            },
            tenantId
         }
      });

      if (exists) {
         const error = {
            code: 'FST_ERR_CONFLICT',
            error: 'Conflict',
            message: `Topic with externalId {${body.externalId}} already exists`,
            statusCode: 409
         };

         return reply.code(409).send(error);
      }

      const topic = await prisma.topic.create({
         data: {
            channelAlwaysOn: body.channelAlwaysOn,
            channelType: body.channelType,
            defaultCategory: body.defaultCategory,
            defaultService: body.defaultService,
            description: body.description,
            displayName: body.displayName,
            enabled: body.enabled,
            externalId: body.externalId,
            id: uuid(),
            orderSequence: body.orderSequence,
            roles: body.roles,
            tenantId,
            userMustOptIn: body.userMustOptIn,
            userPreferenceRoles: body.userPreferenceRoles,
            visibleInPreferences: body.visibleInPreferences
         }
      });

      return reply.code(201).send(Value.Clean(CreatedTopicBody, topic));
   }

   // TODO: Refactor this method to reduce complexity
   public async updateTopic(reply: FastifyReply, tenantId: string, id: string, body: UpdateTopic) {
      const prisma = await getPrismaClient(tenantId);
      const item = await prisma.topic.findUnique({ where: { id, tenantId } });

      if (!item) {
         return reply.code(404).send({
            error: 'Not Found',
            message: 'Topic not found',
            statusCode: 404
         });
      }

      const updatedData: Prisma.TopicUncheckedUpdateInput = {};

      if ('displayName' in body && body.displayName?.trim() === '') {
         return reply.code(400).send({
            code: 'FST_ERR_VALIDATION',
            error: 'Bad Request',
            message: '"displayName" cannot be empty',
            statusCode: 400
         });
      }

      updatedData.displayName = (body.displayName ?? item.displayName).trim();
      updatedData.externalId = body.externalId ?? item.externalId;
      updatedData.description = body.description ?? item.description;
      updatedData.defaultCategory = body.defaultCategory ?? item.defaultCategory;
      updatedData.defaultService = body.defaultService ?? item.defaultService;
      updatedData.orderSequence = body.orderSequence ?? item.orderSequence;
      updatedData.roles = body.roles ?? item.roles;
      updatedData.userPreferenceRoles = body.userPreferenceRoles ?? item.userPreferenceRoles;
      updatedData.channelType = body.channelType ?? item.channelType;

      if ('description' in body && body.description === '') {
         updatedData.description = '';
      }

      if (body.channelAlwaysOn !== undefined) {
         updatedData.channelAlwaysOn = body.channelAlwaysOn;
      }

      if (body.enabled !== undefined) {
         updatedData.enabled = body.enabled;
      }

      if (body.visibleInPreferences !== undefined) {
         updatedData.visibleInPreferences = body.visibleInPreferences;
      }

      if (body.userMustOptIn !== undefined) {
         updatedData.userMustOptIn = body.userMustOptIn;
      }

      const updated = await prisma.topic.update({
         data: updatedData,
         where: { id, tenantId }
      });

      return reply.send(Value.Clean(CreatedTopicBody, updated));
   }

   public async getTopic(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);
      let topic = await prisma.topic.findUnique({
         where: { id, tenantId }
      });

      topic ??= await prisma.topic.findFirst({
         where: { displayName: id, tenantId }
      });

      if (!topic) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Topic {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      return reply.code(200).send(Value.Clean(CreatedTopicBody, topic));
   }

   public async getTopics(reply: FastifyReply, tenantId: string, key?: string, size: number = 50) {
      const options: DefaultPaginationOptions = {
         orderBy: { id: 'asc' },
         where: {
            tenantId
         }
      };

      if (key) {
         options.where = {
            ...options.where,
            id: {
               gte: key
            },
            tenantId
         };
      }

      const prisma = await getPrismaClient(tenantId);
      const count = await prisma.topic.count({ where: { ...options.where } });
      options.take = size + 1;

      const topics = await prisma.topic.findMany({
         ...options
      });

      let nextKey = '';

      if (topics.length > size) {
         const last = topics[size];
         nextKey = last.id;
         topics.pop();
      }

      const response = {
         items: topics,
         pagination: {
            // eslint-disable-next-line camelcase
            next_key: nextKey,
            // eslint-disable-next-line camelcase
            page_size: size,
            // eslint-disable-next-line camelcase
            total_records: count
         }
      };

      return reply.send(response);
   }

   public async deleteTopic(reply: FastifyReply, tenantId: string, id: string) {
      const prisma = await getPrismaClient(tenantId);
      const existing = await prisma.topic.findUnique({ where: { id, tenantId } });

      if (!existing) {
         const error = {
            code: 'FST_ERR_NOT_FOUND',
            error: 'Not Found',
            message: `Topic {${id}} not found`,
            statusCode: 404
         };

         return reply.code(404).send(error);
      }

      await prisma.topic.delete({ where: { id, tenantId } });

      return reply.code(204).send();
   }
}
