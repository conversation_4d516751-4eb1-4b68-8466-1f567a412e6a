import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';

import { EsendexWebhookController } from './esendex-webhook.controller.js';
import { EsendexWebhookSchema } from './esendex-webhook.schema.js';

export class EsendexWebhookRoute implements Route {
   public path = '/esendex-sms/:tenantId/webhook';
   private _webhookController: EsendexWebhookController;

   constructor() {
      this._webhookController = new EsendexWebhookController();
   }

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.post(this.path, { schema: EsendexWebhookSchema }, this._webhookController.setResponse);
      done();
   }
}
