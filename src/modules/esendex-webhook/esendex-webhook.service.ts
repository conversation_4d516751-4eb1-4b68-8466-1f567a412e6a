import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { FastifyReply } from 'fastify';
import { v7 as uuid7 } from 'uuid';

import { extractReaction } from '../two-way-message/extract-reaction.js';

import { EsendexWebhook } from './esendex-webhook.interface.js';

export class EsendexWebhookService {
   private static readonly _DAYS_BACK_ = 7;
   private static readonly _MS_PER_DAY_ = 24 * 60 * 60 * 1000;
   private static readonly _DATE_OFFSET_ = EsendexWebhookService._DAYS_BACK_ * EsendexWebhookService._MS_PER_DAY_;

   public async setResponse(reply: FastifyReply, tenantId: string, body: EsendexWebhook) {
      try {
         // TODO: two-way
         // get recipientId from fromPhoneNumber

         if (body.MatchedMessageID) {
            const reaction = extractReaction(body.Message || '');

            if (reaction) {
               if (await this._updateReaction(tenantId, body, reaction)) {
                  return await reply.status(200).send();
               }
            }
         }

         const prisma = await getPrismaClient(tenantId);
         const lastMessage = await prisma.twoWayMessage.findFirst({
            orderBy: {
               createdAt: 'desc'
            },
            where: {
               createdAt: {
                  gte: new Date(new Date().getTime() - EsendexWebhookService._DATE_OFFSET_)
               },
               direction: 'O',
               recipientServiceId: body.FromPhoneNumber,
               tenantId,
               tenantServiceId: body.ToPhoneNumber
            }
         });

         const category = lastMessage ? lastMessage.category : '';

         // TODO: add ML categorization

         await prisma.twoWayMessage.create({
            data: {
               category,
               channel: 'ESENDEX-WEBHOOK',
               direction: 'I',
               id: uuid7(),
               message: body.Message || '',
               recipientServiceId: (body.FromPhoneNumber || '').replace('+', ''),
               tenantId,
               tenantServiceId: (body.ToPhoneNumber || '').replace('+', '')
            }
         });

         return await reply.status(200).send();
      } catch (error) {
         reply.log.error(
            {
               data: { error },
               name: 'setResponse',
               type: 'EsendexWebhookService'
            },
            'esendexWebhookService - setResponse error'
         );

         return await reply.status(500).send({
            // eslint-disable-next-line camelcase
            error_message: 'Internal Server Error',
            // eslint-disable-next-line camelcase
            error_type: 'internal_error',
            // eslint-disable-next-line camelcase
            status_code: 500
         });
      }
   }

   private async _updateReaction(tenantId: string, body: EsendexWebhook, reaction: string): Promise<boolean> {
      const prisma = await getPrismaClient(tenantId);

      const matched = await prisma.twoWayMessage.findFirst({
         orderBy: {
            createdAt: 'desc'
         },
         where: {
            tenantId,
            transactionId: body.MatchedMessageID
         }
      });

      if (!matched) {
         return false;
      }

      if (reaction) {
         await prisma.twoWayMessage.update({
            data: {
               reaction
            },
            where: {
               id: matched.id
            }
         });

         return true;
      }

      return false;
   }
}
