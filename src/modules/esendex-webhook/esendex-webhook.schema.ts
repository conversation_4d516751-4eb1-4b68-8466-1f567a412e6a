import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

export const EsendexWebhookBody = Type.Object({
   FromPhoneNumber: Type.String(),
   MatchedMessageID: Type.String(),
   Message: Type.String(),
   MessageID: Type.String(),
   ReferenceID: Type.String(),
   ResponseReceiveDate: Type.String(),
   ToPhoneNumber: Type.String()
});

export const EsendexWebhookParams = Type.Object({
   tenantId: Type.String()
});

export const EsendexWebhookSchema: FastifySchema = {
   body: EsendexWebhookBody,
   hide: true,
   params: EsendexWebhookParams
};
