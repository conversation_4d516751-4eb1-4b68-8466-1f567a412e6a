import { FastifyReply, FastifyRequest } from 'fastify';

import { EsendexWebhook, EsendexWebhookParams } from './esendex-webhook.interface.js';
import { EsendexWebhookService } from './esendex-webhook.service.js';

export class EsendexWebhookController {
   public setResponse = async (
      req: FastifyRequest<{ Body: EsendexWebhook; Params: EsendexWebhookParams }>,
      reply: FastifyReply
   ) => {
      return await this._webhookService.setResponse(reply, req.params.tenantId, req.body);
   };

   private _webhookService = new EsendexWebhookService();
}
