import { FastifyInstance, FastifyRegisterOptions } from 'fastify';

import { Route } from '../../interfaces/route.interface.js';

import { HeartBeatSchema } from './heartbeat.schema.js';

export class HeartbeatRoute implements Route {
   public path = '/heartbeat';

   public initializeRoutes(fastify: FastifyInstance, opts: FastifyRegisterOptions<unknown>, done: () => void) {
      fastify.get(this.path, { schema: HeartBeatSchema }, () => ({ status: 'ok' }));
      done();
   }
}
