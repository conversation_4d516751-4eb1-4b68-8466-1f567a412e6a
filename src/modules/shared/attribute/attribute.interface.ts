import { Static } from '@fastify/type-provider-typebox';

import { AttributeParams, CreateAttributeBody, CreatedAttributeBody, UpdateAttributeBody } from './attribute.schema.js';

export type AttributeParams = Static<typeof AttributeParams>;
export type CreateAttribute = Static<typeof CreateAttributeBody>;
export type CreatedAttribute = Static<typeof CreatedAttributeBody>;
export type UpdateAttribute = Static<typeof UpdateAttributeBody>;
