import { Type } from '@sinclair/typebox';
import { FastifySchema } from 'fastify';

export const AttributeParams = Type.Object({
   attributeId: Type.String(),
   channelId: Type.String()
});

export const CreateAttributeBody = Type.Object({
   name: Type.String(),
   value: Type.Optional(Type.String({ default: '' }))
});

export const CreatedAttributeBody = Type.Composite([
   CreateAttributeBody,
   Type.Object({ id: Type.String({ format: 'uuid' }), secure: Type.Boolean({ default: false }) })
]);

export const UpdateAttributeBody = Type.Object({
   id: Type.String({ format: 'uuid' }),
   name: Type.String(),
   value: Type.Optional(Type.String())
});

export const GetAttributeSchema: FastifySchema = {
   description: 'Get channel attribute',
   params: AttributeParams,
   response: {
      200: CreatedAttributeBody
   }
};

export const UpdateAttributeSchema: FastifySchema = {
   body: UpdateAttributeBody,
   description: 'Update channel attribute',
   params: AttributeParams,
   response: {
      200: CreatedAttributeBody
   }
};
