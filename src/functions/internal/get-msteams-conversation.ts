/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { ClientSecretCredential } from '@azure/identity';
import { Client } from '@microsoft/microsoft-graph-client';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { Activity, ConversationParameters, MessageFactory } from 'botbuilder';
import { ConnectorClient, MicrosoftAppCredentials } from 'botframework-connector';

const botId = process.env.TEAMS_BOT_ID!;
const botPassword = process.env.TEAMS_BOT_PASSWORD!;

const credentials = new MicrosoftAppCredentials(botId, botPassword);

export const getMsteamsConversation = async (
   tenantId: string,
   msTenantId: string,
   upn: string
): Promise<string | undefined> => {
   const prisma = await getPrismaClient(tenantId);

   let user = await prisma.teamsUser.findFirst({
      where: { tenantId, userPrincipalName: upn }
   });

   if (user?.conversationId) {
      return user.conversationId;
   }

   user = await prisma.teamsUser.findFirst({
      where: { email: upn, tenantId }
   });

   if (user?.conversationId) {
      return user.conversationId;
   }

   const graphCredential = new ClientSecretCredential(msTenantId, botId, botPassword);

   const graphClient = Client.initWithMiddleware({
      authProvider: {
         getAccessToken: async () => {
            const token = await graphCredential.getToken(['https://graph.microsoft.com/.default']);

            return token.token;
         }
      }
      // defaultVersion: 'beta'
   });

   let response = await graphClient.api(`/users/${upn}`).select('id,displayName,mail,userPrincipalName').get();

   if (!response) {
      const responses = await graphClient
         .api('/users')
         .top(1)
         .filter(`mail eq '${upn}'`)
         .select('id,displayName,mail,userPrincipalName')
         .get();

      if (!responses || responses.length === 0) {
         throw new Error(`unable to find user with upn (${upn})`);
      }

      response = responses.value[0];
   }

   const connectorClient = new ConnectorClient(credentials, {
      baseUri: 'https://smba.trafficmanager.net/teams/'
   });

   const message = MessageFactory.text('Hello!') as Activity;

   const conversationParameters: ConversationParameters = {
      activity: message,
      bot: {
         id: botId,
         name: ''
      },
      channelData: {
         tenant: {
            id: msTenantId
         }
      },
      isGroup: false,
      members: [
         {
            aadObjectId: response.id,
            id: response.id,
            name: response.displayName
         }
      ]
   };

   const conversationResponse = await connectorClient.conversations.createConversation(conversationParameters);

   if (!conversationResponse.id) {
      throw new Error(`unable to retrieve conversation for user (${upn})`);
   }

   await prisma.teamsUser.upsert({
      create: {
         conversationId: conversationResponse.id,
         email: response.mail,
         id: response.id,
         msTenantId,
         tenantId,
         userPrincipalName: upn
      },
      update: {
         conversationId: conversationResponse.id,
         email: response.mail,
         userPrincipalName: upn
      },
      where: {
         id: response.id,
         tenantId
      }
   });

   return conversationResponse.id;
};
