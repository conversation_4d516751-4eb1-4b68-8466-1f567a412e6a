import { Tenant } from '@prisma/client';
import { getPrismaClient } from '@x-signal-inc/messaging-common';
import { LRUCache } from 'lru-cache';

const tenantCache = new LRUCache<string, Tenant>({
   max: 500
});

export const getTenant = async (tenantId: string): Promise<Tenant | null> => {
   const prisma = await getPrismaClient(tenantId);

   let tenant: Tenant | null = null;

   if (tenantCache.has(tenantId)) {
      tenant = tenantCache.get(tenantId)!;
   } else {
      tenant = await prisma.tenant.findFirst({
         where: {
            id: tenantId
         }
      });

      if (tenant) {
         tenantCache.set(tenantId, tenant);
      }
   }

   return tenant;
};
