import { app, InvocationContext, LogHookContext } from '@azure/functions';
import { DefaultAzureCredential } from '@azure/identity';
import { ServiceBusClient, ServiceBusMessage } from '@azure/service-bus';
import { BlobServiceClient } from '@azure/storage-blob';
import { AppConfigFactory, Logger, LoggerFactory, Status } from '@x-signal-inc/messaging-common';
import { ServiceChannel, ServiceMessage, ServiceMessageStatus, ServiceResult } from '@x-signal-inc/messaging-common';
import { deserializeError, serializeError } from 'serialize-error';

import {
   sendAzureSms,
   sendCanvas,
   sendEsendexSms,
   sendModo,
   sendSendGrid,
   sendSlackWebhook,
   sendSmtp,
   sendTeams,
   sendTeamsWebhook,
   sendTest,
   sendTwilioSms
} from '../services/index.js';

const oneMinute: number = 1000 * 60;
const maxRetries = parseInt(process.env.MAX_RETRIES ?? '30', 10);

const serviceBusHostname = process.env.QueueHostname ?? '';
const statusQueue = process.env.StatusQueue ?? '';
const outgoingQueue = process.env.IncomingServiceMessageQueue ?? '';
const incomingQueue = process.env.IncomingServiceMessageQueue ?? '';

const config = AppConfigFactory.create();
const serviceBusClient = new ServiceBusClient(serviceBusHostname, new DefaultAzureCredential());

const errorMessage = (context: string, error: unknown): string => {
   if (error instanceof Error) {
      return `${context} - ${error.message}`;
   }

   return `${context} - unknown error`;
};

const sendStatus = async (context: InvocationContext, message: ServiceResult): Promise<void> => {
   const outgoingMessage: ServiceMessageStatus = {
      channelMessageId: message.data.channelMessageId,
      connectionId: message.data.connectionId,
      disable: message.disable,
      message: message.message,
      recipientId: message.data.recipientId,
      sentAt: message.sentAt,
      status: message.data.status,
      success: message.success,
      tenantId: message.data.tenantId,
      transactionId: message.transactionId
   };

   const statusMessage: ServiceBusMessage = {
      body: outgoingMessage,
      contentType: 'application/json'
   };

   context.trace({
      function: 'sendStatus',
      message: 'sending to status queue'
   });

   try {
      const sender = serviceBusClient.createSender(statusQueue);

      try {
         await sender.sendMessages(statusMessage);
      } finally {
         await sender.close();
      }
   } catch (error) {
      context.error({
         error: serializeError(error),
         function: 'sendStatus',
         message: errorMessage('sendStatus', error)
      });
   }
};

const requeue = async (message: ServiceMessage, context: InvocationContext): Promise<void> => {
   message.retries = message.retries ? message.retries + 1 : 1;

   let delay: number;

   if (message.retries <= 6) {
      delay = oneMinute * 5;
   } else if (message.retries <= 20) {
      delay = oneMinute * 15;
   } else {
      delay = oneMinute * 60;
   }

   const serviceBusMessage: ServiceBusMessage = {
      body: message,
      contentType: 'application/json',
      scheduledEnqueueTimeUtc: new Date(Date.now() + delay)
   };

   context.debug({
      data: { channelMessageId: message.channelMessageId, retries: message.retries },
      function: 'requeue',
      message: 'requeueing message'
   });

   try {
      const sender = serviceBusClient.createSender(outgoingQueue);

      try {
         await sender.sendMessages(serviceBusMessage);
      } finally {
         await sender.close();
      }
   } catch (error) {
      context.error({
         data: { channelMessageId: message.channelMessageId },
         error: serializeError(error),
         function: 'requeue',
         message: errorMessage('requeue', error)
      });
   }
};

const handleResult = async (context: InvocationContext, result: ServiceResult): Promise<void> => {
   const success = result.success;
   const message = result.data;

   if (success) {
      message.status = Status.SENT;
      message.error = '';
   } else {
      message.error = result.message;

      if (message.retries > maxRetries) {
         result.recoverable = false;
      }

      if (result.recoverable) {
         message.status = Status.RETRYING;
      } else {
         message.status = Status.ERROR;
      }
   }

   await sendStatus(context, result);

   if (message.status === Status.RETRYING) {
      await requeue(message, context);
   } else {
      if (message.blobText) {
         try {
            const blobServiceClient = new BlobServiceClient(message.blobText.url, new DefaultAzureCredential());
            const containerClient = blobServiceClient.getContainerClient(message.blobText.container);
            await containerClient.deleteBlob(message.blobText.blob, { deleteSnapshots: 'include' });

            message.text = '';
            message.title = '';
         } catch (error) {
            context.error({
               error: serializeError(error),
               function: 'handleResult',
               message: errorMessage('handleResult: delete blob', error)
            });
         }
      }
   }
};

export async function sendServiceMessage(incoming: unknown, context: InvocationContext): Promise<void> {
   const incomingMessage = incoming as ServiceMessage;

   try {
      let result: ServiceResult;

      context.debug({
         data: { channelMessageId: incomingMessage.channelMessageId, retries: incomingMessage.retries },
         function: 'sendServiceMessage',
         message: 'incoming'
      });

      if (incomingMessage.blobText) {
         const blobServiceClient = new BlobServiceClient(incomingMessage.blobText.url, new DefaultAzureCredential());

         const containerClient = blobServiceClient.getContainerClient(incomingMessage.blobText.container);
         const blobClient = containerClient.getBlobClient(incomingMessage.blobText.blob);
         const download = await blobClient.downloadToBuffer();
         const body = Buffer.from(download.buffer).toString('utf-8');

         if (!body) {
            throw new Error('blob is empty');
         }

         incomingMessage.text = body;
      }

      switch (incomingMessage.channel) {
         case ServiceChannel.AZURE_SMS:
            result = await sendAzureSms(incomingMessage, context);
            break;
         case ServiceChannel.CANVAS:
            result = await sendCanvas(incomingMessage, context);
            break;
         case ServiceChannel.ESENDEX_SMS:
            result = await sendEsendexSms(incomingMessage, context);
            break;
         case ServiceChannel.MODO:
            result = await sendModo(incomingMessage, context);
            break;
         case ServiceChannel.MSTEAMS_WEBHOOK:
            result = await sendTeamsWebhook(incomingMessage, context);
            break;
         case ServiceChannel.SENDGRID:
            result = await sendSendGrid(incomingMessage, context);
            break;
         case ServiceChannel.SLACK_WEBHOOK:
            result = await sendSlackWebhook(incomingMessage, context);
            break;
         case ServiceChannel.SMTP:
            result = await sendSmtp(incomingMessage, context);
            break;
         case ServiceChannel.TEST:
            result = sendTest(incomingMessage, context);
            break;
         case ServiceChannel.TWILIO_SMS:
            result = await sendTwilioSms(incomingMessage, context);
            break;
         case ServiceChannel.MSTEAMS:
            result = await sendTeams(incomingMessage, context);
            break;
         case ServiceChannel.SLACK:
         case ServiceChannel.WHATSAPP:
            result = {
               code: '',
               data: incomingMessage,
               disable: false,
               message: `${incomingMessage.channel} channel not supported yet`,
               recoverable: false,
               sentAt: new Date(),
               success: false,
               transactionId: ''
            };

            break;
         case null:
            result = {
               code: '',
               data: incomingMessage,
               disable: false,
               message: 'incomingMessage.channel is null',
               recoverable: false,
               sentAt: new Date(),
               success: false,
               transactionId: ''
            };
      }

      context.debug({
         data: { channelMessageId: result.data.channelMessageId, result: result.success, status: result.message },
         function: 'sendServiceMessage',
         message: 'result received'
      });

      await handleResult(context, result);
   } catch (error) {
      context.error({
         data: { channelMessageId: incomingMessage.channelMessageId },
         error: serializeError(error),
         function: 'sendServiceMessage',
         message: errorMessage('sendServiceMessage', error)
      });
   }
}

app.serviceBusQueue('sendServiceMessage', {
   connection: 'messaging_SERVICEBUS',
   handler: sendServiceMessage,
   queueName: incomingQueue
});

const logger: Logger = LoggerFactory.getLogger('default');

// TODO: figure out a better way to filter logs
function filterMessages(logContext: LogHookContext): boolean {
   const text = logContext.message.toLowerCase();

   return (
      text.startsWith('worker') ||
      text.startsWith('executed') ||
      text.startsWith('load') ||
      text.startsWith('trigger') ||
      text.startsWith('auto-assigned port') ||
      text.startsWith('set "website_run_from_package"')
   );
}

if (process.env.USE_CONTEXT_LOGGING !== 'true') {
   app.hook.log(async (logContext: LogHookContext) => {
      if (filterMessages(logContext)) {
         return;
      }

      let data: object;
      let message: string;
      let error: Error | undefined;

      try {
         const parsed = JSON.parse(logContext.message) as { error?: Error; message: string } & Record<string, unknown>;
         message = parsed.message;
         data = Object.fromEntries(Object.entries(parsed).filter(([key]) => key !== 'message' && key !== 'error'));
         error = deserializeError(parsed.error);
      } catch {
         message = logContext.message;
         data = {
            function: 'sendServiceMessage'
         };
      }

      if (logContext.level === 'critical' || logContext.level === 'error') {
         await logger.error(data, message, error);

         return;
      }

      const logLevel = await config.getValue('logLevel');

      switch (logContext.level) {
         case 'warning':
            if (logLevel === 'warn' || logLevel === 'info' || logLevel === 'debug' || logLevel === 'trace') {
               await logger.warn(data, message);
            }

            break;
         case 'information':
            if (logLevel === 'info' || logLevel === 'debug' || logLevel === 'trace') {
               await logger.info(data, message);
            }

            break;
         case 'debug':
            if (logLevel === 'debug' || logLevel === 'trace') {
               await logger.debug(data, message);
            }

            break;
         case 'trace':
         case 'none':
            await logger.trace(data, message);
            break;
         default:
            await logger.info(data, message);
            break;
      }
   });
}
