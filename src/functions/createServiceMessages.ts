import { app, InvocationContext, LogHookContext } from '@azure/functions';
import { DefaultAzureCredential } from '@azure/identity';
import { ServiceBusClient, ServiceBusMessage } from '@azure/service-bus';
import { BlobServiceClient } from '@azure/storage-blob';
import { Prisma, PrismaClient, RecipientMessage, Topic } from '@prisma/client';
import { CryptoService, Logger, LoggerFactory, Status } from '@x-signal-inc/messaging-common';
import { AppConfigFactory, AttributeConstants, getPrismaClient } from '@x-signal-inc/messaging-common';
import { BlobText, ChannelAttribute, ServiceChannel, ServiceMessage } from '@x-signal-inc/messaging-common';
import { LRUCache } from 'lru-cache';
import { deserializeError, serializeError } from 'serialize-error';
import { v7 as uuid } from 'uuid';

import { getTenant } from '../functions/tenant-cache.js';

import { getMsteamsConversation } from './internal/get-msteams-conversation.js';

const serviceBusHostname = process.env.QueueHostname ?? '';
const outgoingQueue = process.env.IncomingServiceMessageQueue ?? '';
const incomingQueue = process.env.IncomingMessageQueue ?? '';

const config = AppConfigFactory.create();
const serviceBusClient = new ServiceBusClient(serviceBusHostname, new DefaultAzureCredential());
const cryptoService = CryptoService.getInstance();

const encryptionKeyIdCache = new LRUCache<string, string>({
   allowStale: false,
   max: 500,
   ttl: 1000 * 60 * 15
});

type Channel = Prisma.ChannelGetPayload<{
   include: {
      attributes: true;
   };
}>;

type Connection = Prisma.ConnectionGetPayload<{
   include: {
      attributes: true;
   };
}>;

type ConnectionRecipient = Prisma.RecipientGetPayload<{
   include: {
      connections: {
         include: {
            attributes: true;
         };
      };
      preferences: {
         include: {
            connections: {
               include: {
                  attributes: true;
               };
            };
            optedIn: true;
            topic: true;
         };
      };
   };
}>;

type IncomingMessage = {
   messageId: string;
   tenantId: string;
};

type MessageRecipient = {
   idUsed: string;
} & ConnectionRecipient;

type Message = Prisma.MessageGetPayload<{
   include: {
      attributes: true;
   };
}>;

const getEncryptionKeyId = async (tenantId: string): Promise<string> => {
   if (encryptionKeyIdCache.has(tenantId)) {
      return encryptionKeyIdCache.get(tenantId)!;
   }

   const prisma = await getPrismaClient(tenantId);
   const tenant = await prisma.tenant.findFirst({
      select: { encryptionKeyId: true },
      where: { id: tenantId }
   });

   if (!tenant?.encryptionKeyId) {
      throw new Error(`Encryption key not found for tenant ${tenantId}`);
   }

   encryptionKeyIdCache.set(tenantId, tenant.encryptionKeyId);

   return tenant.encryptionKeyId;
};

const decryptMessage = async (tenantId: string, message: Message): Promise<void> => {
   if (message.message) {
      message.message = await cryptoService.decrypt(tenantId, message.message);
   }

   if (message.plainText) {
      message.plainText = await cryptoService.decrypt(tenantId, message.plainText);
   }

   if (message.shortMessage) {
      message.shortMessage = await cryptoService.decrypt(tenantId, message.shortMessage);
   }
};

const errorMessage = (context: string, error: unknown): string => {
   if (error instanceof Error) {
      return `${context} - ${error.message}`;
   }

   return `${context} - unknown error`;
};

const getConnection = (recipient: ConnectionRecipient, service: string): Connection | null => {
   const connection = recipient.connections.find((c) => c.enabled && c.service === service);

   if (connection) {
      return connection;
   }

   // services that don't require a connection
   switch (service) {
      case AttributeConstants.TEST:
         return {
            attributes: [],
            channelId: null,
            createdAt: new Date(),
            enabled: true,
            id: uuid(),
            recipientId: recipient.id,
            service,
            showInPreferences: true,
            updatedAt: new Date()
         };
   }

   return null;
};

const getPreferredMessageFormat = (
   message: Message,
   formatPriority: Array<keyof Pick<Message, 'plainText' | 'shortMessage' | 'message'>>
): string => {
   for (const format of formatPriority) {
      if (message[format]) {
         return message[format];
      }
   }

   return '';
};

const getMessageText = (message: Message, service: string, tenantId: string, context: InvocationContext): string => {
   const formatPriorities: Record<string, Array<keyof Pick<Message, 'plainText' | 'shortMessage' | 'message'>>> = {
      [AttributeConstants.SMS]: ['plainText', 'shortMessage', 'message'],
      [AttributeConstants.EMAIL]: ['message', 'shortMessage', 'plainText'],
      [AttributeConstants.CANVAS]: ['shortMessage', 'message', 'plainText'],
      [AttributeConstants.TEST]: ['message', 'shortMessage', 'plainText'],
      [AttributeConstants.MODO]: ['shortMessage', 'message', 'plainText'],
      [AttributeConstants.MS_TEAMS]: ['shortMessage', 'message', 'plainText'],
      [AttributeConstants.MS_TEAMS_WEBHOOK]: ['shortMessage', 'message', 'plainText'],
      [AttributeConstants.SLACK_WEBHOOK]: ['shortMessage', 'message', 'plainText'],
      [AttributeConstants.WHATSAPP]: ['shortMessage', 'message', 'plainText']
   };

   const priority = formatPriorities[service];
   const text = getPreferredMessageFormat(message, priority);

   if (!text) {
      context.info(
         JSON.stringify({
            data: { message: message.id },
            function: 'createServiceMessages - getMessageText',
            message: `no message text found for message (${message.id})`,
            tenantId
         })
      );
   }

   return text;
};

// The max queue message size is 256kb. If the message is larger than 200kb, we need to send it as a blob
// TODO: On we move to premium tier, the max queue message size will be unlimited
const createBlobText = async (messageText: string): Promise<BlobText | null> => {
   const data = Buffer.from(messageText, 'utf-8');

   if (data.byteLength < 200000) {
      return null;
   }

   const blobText = {
      blob: uuid(),
      container: process.env.BlobContainer ?? '',
      url: process.env.BlobUrl ?? ''
   };

   const blobServiceClient = new BlobServiceClient(blobText.url, new DefaultAzureCredential());
   const containerClient = blobServiceClient.getContainerClient(blobText.container);
   const blobClient = containerClient.getBlockBlobClient(blobText.blob);
   // await blobClient.upload(blob, blob.size);
   await blobClient.uploadData(data, {
      blobHTTPHeaders: {
         blobContentType: 'text/plain'
      }
   });

   return blobText;
};

const createSmsMessage = async (
   tenantId: string,
   message: Message,
   connection: Connection,
   channel: Channel,
   context: InvocationContext
): Promise<ServiceMessage | string> => {
   const messageText = getMessageText(message, channel.service, tenantId, context);
   const to = connection.attributes.find((a) => a.name === AttributeConstants.PHONE)?.value ?? '';
   const from = channel.attributes.find((a) => a.name === AttributeConstants.PHONE)?.value ?? '';
   const provider = channel.provider;

   let service: ServiceChannel;
   const attributes: ChannelAttribute[] = [];

   switch (provider) {
      case AttributeConstants.ESENDEX:
         service = ServiceChannel.ESENDEX_SMS;
         break;

      case AttributeConstants.TWILIO: {
         service = ServiceChannel.TWILIO_SMS;

         const sid = channel.attributes.find((a) => a.name === AttributeConstants.TWILIO_SID)?.value ?? '';

         attributes.push({
            name: AttributeConstants.TWILIO_SID,
            value: sid
         });
         break;
      }

      default:
         context.info(
            JSON.stringify({
               data: { connection: connection.id, message: message.id },
               function: 'createServiceMessages - createSmsMessage',
               message: `unknown provider (${provider}) on channel (${channel.id})`,
               tenantId
            })
         );

         return `unknown provider (${provider}) on channel (${channel.id})`;
   }

   const messageObject = new ServiceMessage({
      attributes,
      blobText: await createBlobText(messageText),
      channel: service,
      channelId: channel.id,
      connectionId: connection.id,
      from,
      importance: message.importance,
      messageId: message.id,
      status: Status.PROCESSING,
      tenantId,
      text: messageText,
      to
   });

   return messageObject;
};

const createTeamsMessage = async (
   tenantId: string,
   message: Message,
   connection: Connection,
   channel: Channel,
   context: InvocationContext
): Promise<ServiceMessage | string> => {
   const messageText = getMessageText(message, channel.service, tenantId, context);
   const upn = connection.attributes.find((a) => a.name === AttributeConstants.UPN)?.value ?? '';
   const msTenantId = channel.attributes.find((a) => a.name === AttributeConstants.TENANT_ID)?.value ?? '';

   if (!upn) {
      return `missing ${AttributeConstants.UPN} on connection (${connection.id})`;
   }

   if (!msTenantId) {
      return `missing ${AttributeConstants.TENANT_ID} on channel (${channel.id})`;
   }

   try {
      const conversationId = await getMsteamsConversation(tenantId, msTenantId, upn);

      return new ServiceMessage({
         attributes: [],
         blobText: await createBlobText(messageText),
         channel: ServiceChannel.MSTEAMS,
         channelId: channel.id,
         connectionId: connection.id,
         conversationId,
         importance: message.importance,
         messageId: message.id,
         status: Status.PROCESSING,
         tenantId,
         text: messageText,
         title: message.title
      });
   } catch (error) {
      return (error as Error).message;
   }
};

const createEmailMessage = async (
   tenantId: string,
   message: Message,
   connection: Connection,
   channel: Channel,
   context: InvocationContext
): Promise<ServiceMessage | string> => {
   const messageText = getMessageText(message, channel.service, tenantId, context);
   const to = connection.attributes.find((a) => a.name === AttributeConstants.EMAIL)?.value ?? '';
   let from: string;

   const provider = channel.provider;

   let service: ServiceChannel;
   const attributes: ChannelAttribute[] = [];

   switch (provider) {
      case AttributeConstants.SENDGRID: {
         service = ServiceChannel.SENDGRID;
         from = channel.attributes.find((a) => a.name === AttributeConstants.EMAIL)?.value ?? '';
         break;
      }

      case AttributeConstants.SMTP: {
         service = ServiceChannel.SMTP;
         from = channel.attributes.find((a) => a.name === AttributeConstants.SMTP_FROM)?.value ?? '';
         const host = channel.attributes.find((a) => a.name === AttributeConstants.SMTP_HOST)?.value ?? '';
         attributes.push({
            name: AttributeConstants.SMTP_HOST,
            value: host
         });

         const port = channel.attributes.find((a) => a.name === AttributeConstants.SMTP_PORT)?.value ?? '';
         attributes.push({
            name: AttributeConstants.SMTP_PORT,
            value: port
         });

         const username = channel.attributes.find((a) => a.name === AttributeConstants.SMTP_USER)?.value ?? '';
         attributes.push({
            name: AttributeConstants.SMTP_USER,
            value: username
         });

         const tls = channel.attributes.find((a) => a.name === AttributeConstants.SMTP_SECURE)?.value ?? '';
         attributes.push({
            name: AttributeConstants.SMTP_SECURE,
            value: tls
         });

         break;
      }

      default:
         context.info(
            JSON.stringify({
               data: { connection: connection.id, message: message.id },
               function: 'createServiceMessages - createSmsMessage',
               message: `unknown provider (${provider}) on channel (${channel.id})`,
               tenantId
            })
         );

         return `unknown provider (${provider}) on channel (${channel.id})`;
   }

   return new ServiceMessage({
      attributes,
      blobText: await createBlobText(messageText),
      channel: service,
      channelId: channel.id,
      connectionId: connection.id,
      endpointIdentifier: to,
      from,
      importance: message.importance,
      messageId: message.id,
      status: Status.PROCESSING,
      tenantId,
      text: messageText,
      title: message.title,
      to
   });
};

// eslint-disable-next-line complexity
const createModoMessage = async (
   tenantId: string,
   message: Message,
   connection: Connection,
   channel: Channel,
   context: InvocationContext
): Promise<ServiceMessage | string> => {
   const messageText = getMessageText(message, channel.service, tenantId, context);

   const to = connection.attributes.find((a) => a.name === AttributeConstants.MODO_ID)?.value;

   let banner = message.attributes.find((a) => a.name === AttributeConstants.MODO_BANNER)?.value ?? '';

   if (!banner) {
      banner = channel.attributes.find((a) => a.name === AttributeConstants.MODO_BANNER)?.value ?? '';
   }

   const attributes: ChannelAttribute[] = [];
   attributes.push({ name: AttributeConstants.MODO_BANNER, value: banner });

   const startAt = message.attributes.find((a) => a.name === AttributeConstants.MODO_START_AT)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_START_AT, value: startAt });

   const endAt = message.attributes.find((a) => a.name === AttributeConstants.MODO_END_AT)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_END_AT, value: endAt });

   const style = channel.attributes.find((a) => a.name === AttributeConstants.MODO_STYLE)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_STYLE, value: style });

   let push = message.attributes.find((a) => a.name === AttributeConstants.MODO_PUSH)?.value ?? '';

   if (!push) {
      push = channel.attributes.find((a) => a.name === AttributeConstants.MODO_PUSH)?.value ?? '';
   }

   attributes.push({ name: AttributeConstants.MODO_PUSH, value: push });

   const applicationId = channel.attributes.find((a) => a.name === AttributeConstants.MODO_APPLICATION_ID)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_APPLICATION_ID, value: applicationId });

   const target = channel.attributes.find((a) => a.name === AttributeConstants.MODO_TARGET)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_TARGET, value: target });

   const modoChannel = channel.attributes.find((a) => a.name === AttributeConstants.MODO_CHANNEL)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_CHANNEL, value: modoChannel });

   const group = channel.attributes.find((a) => a.name === AttributeConstants.MODO_GROUP_ATTRIBUTE)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_GROUP_ATTRIBUTE, value: group });

   const filter = channel.attributes.find((a) => a.name === AttributeConstants.MODO_FILTER_KEY)?.value ?? '';
   attributes.push({ name: AttributeConstants.MODO_FILTER_KEY, value: filter });

   return new ServiceMessage({
      attributes,
      blobText: await createBlobText(messageText),
      channel: ServiceChannel.MODO,
      channelId: channel.id,
      connectionId: connection.id,
      endpointIdentifier: to,
      importance: message.importance,
      messageId: message.id,
      status: Status.PROCESSING,
      tenantId,
      text: messageText,
      title: message.title,
      to
   });
};

// eslint-disable-next-line complexity
const createOutgoingMessage = async (
   tenantId: string,
   channel: Channel | null,
   connection: Connection,
   message: Message,
   context: InvocationContext
): Promise<ServiceMessage | string> => {
   const attributes: ChannelAttribute[] = [];

   const encryptionKeyId = await getEncryptionKeyId(tenantId);

   for (const attribute of connection.attributes) {
      attribute.value = await cryptoService.decrypt(encryptionKeyId, attribute.value);
   }

   if (channel) {
      switch (channel.service) {
         case AttributeConstants.CANVAS: {
            const messageText = getMessageText(message, channel.service, tenantId, context);

            const clientId = channel.attributes.find((a) => a.name === AttributeConstants.CLIENT_ID)?.value ?? '';
            attributes.push({ name: AttributeConstants.CLIENT_ID, value: clientId });

            const hostname = channel.attributes.find((a) => a.name === AttributeConstants.HOSTNAME)?.value ?? '';
            attributes.push({ name: AttributeConstants.HOSTNAME, value: hostname });

            const to = connection.attributes.find((a) => a.name === AttributeConstants.CANVAS_ID)?.value ?? '';

            return new ServiceMessage({
               attributes,
               blobText: await createBlobText(messageText),
               channel: ServiceChannel.CANVAS,
               channelId: channel.id,
               connectionId: connection.id,
               endpointIdentifier: to,
               importance: message.importance,
               messageId: message.id,
               status: Status.PROCESSING,
               tenantId,
               text: messageText,
               title: message.title,
               to
            });
         }

         case AttributeConstants.EMAIL:
            return await createEmailMessage(tenantId, message, connection, channel, context);

         case AttributeConstants.MODO:
            return await createModoMessage(tenantId, message, connection, channel, context);

         case AttributeConstants.MS_TEAMS:
            return await createTeamsMessage(tenantId, message, connection, channel, context);

         case AttributeConstants.MS_TEAMS_WEBHOOK: {
            const messageText = getMessageText(message, channel.service, tenantId, context);
            const webhook = channel.attributes.find((a) => a.name === AttributeConstants.WEBHOOK_URL)?.value ?? '';

            return new ServiceMessage({
               attributes,
               blobText: await createBlobText(messageText),
               channel: ServiceChannel.MSTEAMS_WEBHOOK,
               channelId: channel.id,
               connectionId: connection.id,
               endpointIdentifier: webhook,
               importance: message.importance,
               messageId: message.id,
               status: Status.PROCESSING,
               tenantId,
               text: messageText,
               title: message.title,
               webhook
            });
         }

         case AttributeConstants.SLACK_WEBHOOK: {
            const messageText = getMessageText(message, channel.service, tenantId, context);

            const webhook = channel.attributes.find((a) => a.name === AttributeConstants.WEBHOOK_URL)?.value ?? '';

            return new ServiceMessage({
               attributes,
               blobText: await createBlobText(messageText),
               channel: ServiceChannel.SLACK_WEBHOOK,
               channelId: channel.id,
               connectionId: connection.id,
               endpointIdentifier: webhook,
               importance: message.importance,
               messageId: message.id,
               status: Status.PROCESSING,
               tenantId,
               text: messageText,
               title: message.title,
               webhook
            });
         }

         case AttributeConstants.SMS:
            return await createSmsMessage(tenantId, message, connection, channel, context);

         case AttributeConstants.TEST: {
            const messageText = getMessageText(message, channel.service, tenantId, context);

            for (const attribute of message.attributes) {
               attributes.push({
                  name: attribute.name,
                  value: attribute.value
               });
            }

            return new ServiceMessage({
               attributes,
               blobText: await createBlobText(messageText),
               channel: ServiceChannel.TEST,
               channelId: channel.id,
               connectionId: connection.id,
               endpointIdentifier: '',
               importance: message.importance,
               messageId: message.id,
               status: Status.PROCESSING,
               tenantId,
               text: messageText,
               title: message.title
            });
         }

         case AttributeConstants.WHATSAPP:
            return 'WhatsApp is unsupported';

         default:
            context.info(
               JSON.stringify({
                  data: { connection: connection.id, message: message.id },
                  function: 'createServiceMessages - createOutgoingMessage',
                  message: `unknown service (${channel.service}) on channel (${channel.id})`,
                  tenantId
               })
            );

            return `unknown service (${channel.service}) on channel (${channel.id})`;
      }
   }

   switch (connection.service) {
      case AttributeConstants.MS_TEAMS_WEBHOOK: {
         const messageText = getMessageText(message, connection.service, tenantId, context);
         const webhook = connection.attributes.find((a) => a.name === AttributeConstants.WEBHOOK_URL)?.value ?? '';

         return new ServiceMessage({
            attributes,
            blobText: await createBlobText(messageText),
            channel: ServiceChannel.MSTEAMS_WEBHOOK,
            channelId: '',
            connectionId: connection.id,
            endpointIdentifier: webhook,
            importance: message.importance,
            messageId: message.id,
            status: Status.PROCESSING,
            tenantId,
            text: messageText,
            title: message.title,
            webhook
         });
      }

      case AttributeConstants.SLACK_WEBHOOK: {
         const messageText = getMessageText(message, connection.service, tenantId, context);
         const webhook = connection.attributes.find((a) => a.name === AttributeConstants.WEBHOOK_URL)?.value ?? '';

         return new ServiceMessage({
            attributes,
            blobText: await createBlobText(messageText),
            channel: ServiceChannel.SLACK_WEBHOOK,
            channelId: '',
            connectionId: connection.id,
            endpointIdentifier: webhook,
            importance: message.importance,
            messageId: message.id,
            status: Status.PROCESSING,
            tenantId,
            text: messageText,
            title: message.title,
            webhook
         });
      }

      default:
         return `unknown service (${connection.service})`;
   }
};

const createAndQueueChannelMessage = async (
   prisma: PrismaClient,
   tenantId: string,
   recipient: ConnectionRecipient,
   connection: Connection,
   channel: Channel | null,
   message: Message,
   recipientMessage: RecipientMessage,
   context: InvocationContext
): Promise<boolean> => {
   context.trace(
      JSON.stringify({
         data: {
            channelId: channel?.id,
            connectionId: connection.id,
            messageId: message.id,
            recipientId: recipient.id
         },
         function: 'createServiceMessages',
         message: 'createAndQueueChannelMessage',
         tenantId
      })
   );

   const response: ServiceMessage | string = await createOutgoingMessage(
      tenantId,
      channel,
      connection,
      message,
      context
   );

   let dbChannelID: string | null = null;

   if (response instanceof ServiceMessage) {
      const outgoingMessage = response;
      outgoingMessage.recipientId = recipient.id;
      outgoingMessage.isRecipientMessage = true;

      if (channel) {
         dbChannelID = channel.id;
      }

      const keyId = await getEncryptionKeyId(tenantId);
      const channelMessage = await prisma.channelMessage.create({
         data: {
            channelId: dbChannelID,
            endpointIdentifier: outgoingMessage.endpointIdentifier,
            id: uuid(),
            isRecipientMessage: true,
            messageId: recipientMessage.id,
            text: await cryptoService.encrypt(tenantId, keyId, outgoingMessage.text),
            title: outgoingMessage.title
         }
      });

      outgoingMessage.channelMessageId = channelMessage.id;

      if (outgoingMessage.blobText) {
         outgoingMessage.text = '';
      }

      const logLevel = await config.getValue('logLevel');
      outgoingMessage.logLevel = logLevel;

      try {
         const serviceBusMessage: ServiceBusMessage = {
            body: outgoingMessage,
            contentType: 'application/json'
         };

         const sender = serviceBusClient.createSender(outgoingQueue);

         try {
            context.trace(
               JSON.stringify({
                  data: {
                     channelId: channelMessage.id,
                     channelMessageId: channelMessage.id,
                     connectionId: connection.id,
                     messageId: message.id,
                     recipientId: recipient.id
                  },
                  function: 'createServiceMessages',
                  message: 'sending message to queue',
                  tenantId
               })
            );
            await sender.sendMessages(serviceBusMessage);
         } finally {
            await sender.close();
         }
      } catch (error) {
         context.error(
            JSON.stringify({
               error: serializeError(error),
               function: 'createServiceMessages - createAndQueueChannelMessage',
               message: errorMessage('error adding to queue', error),
               outgoingMessage
            })
         );

         throw error;
      }

      return true;
   }

   await prisma.channelMessage.create({
      data: {
         channelId: dbChannelID,
         endpointIdentifier: '',
         error: response,
         id: uuid(),
         isRecipientMessage: true,
         messageId: recipientMessage.id,
         status: Status.ERROR,
         text: '',
         title: ''
      }
   });

   return false;
};

const sendServiceAccountMessages = async (
   prisma: PrismaClient,
   tenantId: string,
   recipients: MessageRecipient[],
   message: Message,
   context: InvocationContext
): Promise<void> => {
   context.trace(
      JSON.stringify({
         data: { messageId: message.id, recipients: recipients.map((r) => r.id), tenantId },
         function: 'createServiceMessages',
         message: 'sendServiceAccountMessages',
         tenantId
      })
   );

   for (const recipient of recipients) {
      try {
         const recipientMessage = await prisma.recipientMessage.create({
            data: {
               id: uuid(),
               idUsed: recipient.idUsed,
               messageId: message.id,
               recipientId: recipient.id
            }
         });

         let foundConnection = false;

         for (const connection of recipient.connections) {
            if (!connection.enabled) {
               continue;
            }

            let channel: Channel | null = null;

            // set to a specific email channel
            if (connection.channelId && connection.service === AttributeConstants.EMAIL) {
               channel = await prisma.channel.findFirst({
                  include: { attributes: true },
                  where: { enabled: true, id: connection.channelId, tenantId }
               });

               foundConnection = true;
            }

            if (!channel) {
               // sending to a webhook
               switch (connection.service) {
                  case AttributeConstants.SLACK_WEBHOOK:
                  case AttributeConstants.MS_TEAMS_WEBHOOK:
                     foundConnection = true;
                     break;
                  default:
                     continue;
               }
            }

            const success = await createAndQueueChannelMessage(
               prisma,
               tenantId,
               recipient,
               connection,
               channel,
               message,
               recipientMessage,
               context
            );

            if (!success) {
               continue;
            }
         }

         if (!foundConnection) {
            await prisma.recipientMessage.update({
               data: {
                  error: 'Unable to create channel message for recipient - no active connection or channel',
                  status: Status.ERROR
               },
               where: {
                  id: recipientMessage.id
               }
            });

            context.info(
               JSON.stringify({
                  data: { message: message.id },
                  function: 'createServiceMessages - sendServiceAccountMessages',
                  message: `no enabled connection or channel found for recipient (${recipient.id})`,
                  tenantId
               })
            );
         }
      } catch (error: unknown) {
         context.error({
            data: { message: message.id },
            error: serializeError(error),
            function: 'createServiceMessages - sendServiceAccountMessages',
            message: errorMessage('sendServiceAccountMessages', error),
            tenantId
         });
      }
   }
};

const sendTopicAndPreferencesMessages = async (
   prisma: PrismaClient,
   tenantId: string,
   recipients: MessageRecipient[],
   message: Message,
   topic: Topic,
   context: InvocationContext
): Promise<void> => {
   context.trace(
      JSON.stringify({
         data: { messageId: message.id, recipients: recipients.map((r) => r.id), topic: topic.id },
         function: 'createServiceMessages',
         message: 'sendTopicAndPreferencesMessages',
         tenantId
      })
   );

   const topicChannel = await prisma.channel.findFirst({
      include: { attributes: true },
      where: {
         channelType: topic.channelType,
         enabled: true,
         service: topic.defaultService,
         tenantId
      }
   });

   context.trace(
      JSON.stringify({
         data: { messageId: message.id, topicChannel },
         function: 'createServiceMessages',
         message: 'sendTopicAndPreferencesMessages - topicChannel',
         tenantId
      })
   );

   for (const recipient of recipients) {
      const recipientMessage = await prisma.recipientMessage.create({
         data: {
            id: uuid(),
            idUsed: recipient.idUsed,
            messageId: message.id,
            recipientId: recipient.id
         }
      });

      try {
         if (topic.channelAlwaysOn) {
            if (topicChannel) {
               const requiredConnection = getConnection(recipient, topic.defaultService);

               if (requiredConnection) {
                  await createAndQueueChannelMessage(
                     prisma,
                     tenantId,
                     recipient,
                     requiredConnection,
                     topicChannel,
                     message,
                     recipientMessage,
                     context
                  );
               }
            } else {
               context.info(
                  JSON.stringify({
                     data: { message: message.id, topic: topic.id },
                     function: 'sendTopicAndPreferencesMessages',
                     // eslint-disable-next-line max-len
                     message: `always on channel for service: ${topic.defaultService}, type: ${topic.channelType} does not exist or is not enabled`,
                     tenantId
                  })
               );
            }
         }

         const topicPreference = recipient.preferences.find((p) => p.topicId === topic.id);

         context.trace(
            JSON.stringify({
               data: { messageId: message.id, mustOptIn: topic.userMustOptIn, topicPreference },
               function: 'createServiceMessages',
               message: 'sendTopicAndPreferencesMessages - topicPreference',
               tenantId
            })
         );

         if (topic.userMustOptIn && (!topicPreference || !topicPreference.optedIn)) {
            continue;
         }

         if (topicPreference) {
            for (const preferenceConnection of topicPreference.connections) {
               if (!preferenceConnection.enabled) {
                  continue;
               }

               if (preferenceConnection.service === topic.defaultService && topic.channelAlwaysOn) {
                  continue;
               }

               let preferenceChannel = await prisma.channel.findFirst({
                  include: { attributes: true },
                  where: {
                     channelType: topic.channelType,
                     enabled: true,
                     service: preferenceConnection.service,
                     tenantId
                  }
               });

               // we don't have exact match(service and channel type) so match on service
               preferenceChannel ??= await prisma.channel.findFirst({
                  include: { attributes: true },
                  where: {
                     enabled: true,
                     service: preferenceConnection.service,
                     tenantId
                  }
               });

               context.trace(
                  JSON.stringify({
                     data: { messageId: message.id, preferenceChannel },
                     function: 'createServiceMessages',
                     message: 'sendTopicAndPreferencesMessages - preferenceChannel',
                     tenantId
                  })
               );

               if (preferenceChannel) {
                  await createAndQueueChannelMessage(
                     prisma,
                     tenantId,
                     recipient,
                     preferenceConnection,
                     preferenceChannel,
                     message,
                     recipientMessage,
                     context
                  );
               }
            }
         }

         // no preference and no required channel
         if (!topic.channelAlwaysOn && !topicPreference) {
            const defaultConnection = getConnection(recipient, topic.defaultService);

            if (defaultConnection) {
               await createAndQueueChannelMessage(
                  prisma,
                  tenantId,
                  recipient,
                  defaultConnection,
                  topicChannel,
                  message,
                  recipientMessage,
                  context
               );
            }
         }
      } catch (error: unknown) {
         context.error({
            error: serializeError(error),
            function: 'createServiceMessages - sendServiceAccountMessages',
            message: errorMessage('sendServiceAccountMessages', error),
            tenantId
         });
      }
   }
};

const sendRecipientMessages = async (
   prisma: PrismaClient,
   tenantId: string,
   recipients: MessageRecipient[],
   message: Message,
   context: InvocationContext
): Promise<void> => {
   context.trace(
      JSON.stringify({
         data: { messageId: message.id, recipients: recipients.map((r) => r.id) },
         function: 'createServiceMessages',
         message: 'sendRecipientMessages',
         tenantId
      })
   );

   const topic = await prisma.topic.findFirst({
      where: {
         id: message.topic,
         tenantId
      }
   });

   if (!topic) {
      context.error(
         JSON.stringify({
            data: { messageId: message.id, topic: message.topic },
            function: 'createServiceMessages - sendRecipientMessages',
            message: `topic (${message.topic}) not found`,
            tenantId
         })
      );

      await prisma.message.update({
         data: { error: `topic (${message.topic}) not found`, status: Status.ERROR },
         where: { id: message.id, tenantId }
      });

      return;
   }

   await sendTopicAndPreferencesMessages(prisma, tenantId, recipients, message, topic, context);
};

const processRecipients = async (
   prisma: PrismaClient,
   tenantId: string,
   message: Message,
   context: InvocationContext
): Promise<void> => {
   context.trace(
      JSON.stringify({
         data: { messageId: message.id },
         function: 'createServiceMessages',
         message: 'processRecipients',
         tenantId
      })
   );

   const recipients: MessageRecipient[] = [];
   const serviceAccounts: MessageRecipient[] = [];
   const uniqueIds = new Set<string>();

   for (const id of message.recipients) {
      const recipient = await prisma.recipient.findFirst({
         include: {
            connections: { include: { attributes: true } },
            identifiers: true,
            preferences: {
               include: {
                  connections: { include: { attributes: true } },
                  topic: true
               }
            }
         },
         where: {
            enabled: true,
            identifiers: {
               some: {
                  value: {
                     equals: id,
                     mode: 'insensitive'
                  }
               }
            },
            tenantId
         }
      });

      if (!recipient) {
         context.debug(
            JSON.stringify({
               data: { messageId: message.id, recipient: id },
               function: 'createServiceMessages - processRecipients',
               message: 'recipient not found'
            })
         );
         continue;
      }

      if (uniqueIds.has(id)) {
         continue;
      }

      uniqueIds.add(id);

      const messageRecipient = {
         ...recipient,
         idUsed: id
      };

      if (recipient.identifiers.some((i) => i.type === AttributeConstants.SERVICE_ACCOUNT)) {
         serviceAccounts.push(messageRecipient);
      } else {
         recipients.push(messageRecipient);
      }
   }

   if (serviceAccounts.length === 0 && recipients.length === 0) {
      context.debug(
         JSON.stringify({
            data: { messageId: message.id, recipients: message.recipients },
            function: 'createServiceMessages - processRecipients',
            message: 'no recipients not found'
         })
      );

      message.status = Status.ERROR;
      await prisma.message.update({
         data: {
            error: 'No active recipients found',
            status: Status.ERROR
         },
         where: {
            id: message.id
         }
      });

      return;
   }

   if (serviceAccounts.length > 0) {
      await sendServiceAccountMessages(prisma, tenantId, serviceAccounts, message, context);
   }

   if (recipients.length > 0) {
      await sendRecipientMessages(prisma, tenantId, recipients, message, context);
   }
};

const processTags = async (
   prisma: PrismaClient,
   tenantId: string,
   message: Message,
   context: InvocationContext
): Promise<void> => {
   context.trace(
      JSON.stringify({
         data: { messageId: message.id },
         function: 'createServiceMessages',
         message: 'processTags',
         tenantId
      })
   );

   const serviceAttribute: string = AttributeConstants.SERVICE_ACCOUNT;

   const tags = await prisma.tag.findMany({
      include: {
         recipients: {
            include: {
               connections: { include: { attributes: true } },
               identifiers: true,
               preferences: {
                  include: {
                     connections: { include: { attributes: true } },
                     topic: true
                  }
               }
            }
         }
      },
      where: {
         displayName: {
            in: message.tags,
            mode: 'insensitive'
         },
         tenantId
      }
   });

   const recipients: MessageRecipient[] = [];
   const uniqueIds = new Set<string>();

   for (const tag of tags) {
      for (const recipient of tag.recipients) {
         if (recipient.enabled && !uniqueIds.has(recipient.id)) {
            uniqueIds.add(recipient.id);

            const messageRecipient = {
               ...recipient,
               idUsed: recipient.id
            };

            if (!recipient.identifiers.some((i) => i.type === serviceAttribute)) {
               recipients.push(messageRecipient);
            }
         }
      }
   }

   await sendRecipientMessages(prisma, tenantId, recipients, message, context);
};

// TODO: Implement Service Groups and Tags
const createRecipientMessages = async (
   prisma: PrismaClient,
   message: Message,
   context: InvocationContext
): Promise<void> => {
   try {
      context.trace(
         JSON.stringify({
            data: { messageId: message.id },
            function: 'createServiceMessages',
            message: 'createRecipientMessages',
            tenantId: message.tenantId
         })
      );

      const tenantId: string = message.tenantId;

      // tags
      message.tags = [...new Set(message.tags.map((r) => r.trim()))];

      if (message.tags.length > 0) {
         await processTags(prisma, tenantId, message, context);

         return;
      }

      // recipients
      message.recipients = [...new Set(message.recipients.map((r) => r.trim().toLowerCase()))];
      await processRecipients(prisma, tenantId, message, context);
   } catch (error) {
      await prisma.message.update({
         data: {
            error: errorMessage('', error),
            status: message.status
         },
         where: {
            id: message.id
         }
      });
   }
};

export async function createServiceMessages(incoming: unknown, context: InvocationContext): Promise<void> {
   const incomingMessage = incoming as IncomingMessage;

   context.trace(
      JSON.stringify({
         data: incomingMessage,
         function: 'createServiceMessages',
         message: 'incoming message',
         tenantId: incomingMessage.tenantId
      })
   );

   try {
      const prisma = await getPrismaClient(incomingMessage.tenantId);

      const message = await prisma.message.findUnique({
         include: {
            attributes: true
         },
         where: {
            id: incomingMessage.messageId,
            tenantId: incomingMessage.tenantId
         }
      });

      if (!message) {
         context.error(
            JSON.stringify({
               data: incomingMessage,
               function: 'createServiceMessages',
               message: `message ID (${incomingMessage.messageId}) not found`,
               tenantId: incomingMessage.tenantId
            })
         );

         return;
      }

      const tenant = await getTenant(incomingMessage.tenantId);

      if (!tenant) {
         context.error(
            JSON.stringify({
               data: incomingMessage,
               error: `tenant (${incomingMessage.tenantId}) not found`,
               message: `tenant (${incomingMessage.tenantId}) not found`,
               tenantId: incomingMessage.tenantId
            })
         );

         return;
      }

      await decryptMessage(incomingMessage.tenantId, message);

      await createRecipientMessages(prisma, message, context);

      const processedMessage = await prisma.message.findUnique({
         include: {
            recipientMessages: {
               include: { channelMessages: true }
            }
         },
         where: {
            id: incomingMessage.messageId,
            tenantId: incomingMessage.tenantId
         }
      });

      if (!processedMessage) {
         return;
      }

      if (tenant.storeMessageForDays === 0) {
         await prisma.message.update({
            data: {
               message: '',
               plainText: '',
               shortMessage: ''
            },
            where: {
               id: processedMessage.id,
               tenantId: incomingMessage.tenantId
            }
         });
      }

      if (processedMessage.status === Status.ERROR) {
         return;
      }

      const ids = new Set<string>();
      processedMessage.recipientMessages.forEach((recipientMessage) => {
         ids.add(recipientMessage.id);
      });

      for (const recipientMessage of processedMessage.recipientMessages) {
         if (recipientMessage.channelMessages.length === 0) {
            ids.delete(recipientMessage.id);
            await prisma.recipientMessage.update({
               data: {
                  error: 'Unable to create channel message for recipient - no active connection for recipient',
                  status: Status.ERROR
               },
               where: {
                  id: recipientMessage.id
               }
            });
         } else {
            await prisma.recipientMessage.update({
               data: {
                  error: '',
                  status: Status.PROCESSED
               },
               where: {
                  id: recipientMessage.id
               }
            });
         }
      }

      if (processedMessage.recipientMessages.length === 0 || ids.size === 0) {
         await prisma.message.update({
            data: {
               error: 'No active recipients or channels found',
               status: Status.ERROR
            },
            where: {
               id: message.id
            }
         });

         return;
      }

      await prisma.message.update({
         data: {
            error: '',
            status: Status.SENT
         },
         where: {
            id: message.id
         }
      });
   } catch (error: unknown) {
      context.error(
         JSON.stringify({
            data: incomingMessage,
            error: serializeError(error),
            function: 'createServiceMessages',
            message: errorMessage('createServiceMessages', error),
            tenantId: incomingMessage.tenantId
         })
      );
   }
}

app.serviceBusQueue('createServiceMessages', {
   connection: 'messaging_SERVICEBUS',
   handler: createServiceMessages,
   queueName: incomingQueue
});

const logger: Logger = LoggerFactory.getLogger('default');

// TODO: figure out a better way to filter logs
function filterMessages(logContext: LogHookContext): boolean {
   const text = logContext.message.toLowerCase();

   return (
      text.startsWith('worker') ||
      text.startsWith('executed') ||
      text.startsWith('load') ||
      text.startsWith('trigger') ||
      text.startsWith('auto-assigned port') ||
      text.startsWith('set "website_run_from_package"')
   );
}

if (process.env.USE_CONTEXT_LOGGING !== 'true') {
   app.hook.log(async (logContext: LogHookContext) => {
      if (filterMessages(logContext)) {
         return;
      }

      let data: object;
      let message: string;
      let error: Error | undefined;

      try {
         const parsed = JSON.parse(logContext.message) as { error?: object; message: string } & Record<string, unknown>;
         message = parsed.message;
         data = Object.fromEntries(Object.entries(parsed).filter(([key]) => key !== 'message' && key !== 'error'));
         error = deserializeError(parsed.error);
      } catch {
         message = logContext.message;
         data = {
            function: 'createServiceMessages'
         };
      }

      if (logContext.level === 'critical' || logContext.level === 'error') {
         await logger.error(data, message, error);

         return;
      }

      const logLevel = await config.getValue('logLevel');

      switch (logContext.level) {
         case 'warning':
            if (logLevel === 'warn' || logLevel === 'info' || logLevel === 'debug' || logLevel === 'trace') {
               await logger.warn(data, message);
            }

            break;
         case 'information':
            if (logLevel === 'info' || logLevel === 'debug' || logLevel === 'trace') {
               await logger.info(data, message);
            }

            break;
         case 'debug':
            if (logLevel === 'debug' || logLevel === 'trace') {
               await logger.debug(data, message);
            }

            break;
         case 'trace':
         case 'none':
            await logger.trace(data, message);
            break;
         default:
            await logger.info(data, message);
            break;
      }
   });
}
