import { app, InvocationContext } from '@azure/functions';
import { Prisma } from '@prisma/client';
import { getPrismaClient,  ServiceMessageStatus } from '@x-signal-inc/messaging-common';

import {getTenant} from '../functions/tenant-cache.js';

const incomingQueue = process.env.IncomingStatusMessageQueue ?? '';

const errorMessage = (context: string, error: unknown): string => {
   if (error instanceof Error) {
      return `${context} - ${error.message}`;
   }

   return `${context} - unknown error`;
};

export async function updateChannelMessageStatus(incoming: unknown, context: InvocationContext): Promise<void> {
   const incomingMessage = incoming as ServiceMessageStatus;

   try {
      context.debug({
         data: incomingMessage,
         function: 'updateChannelMessageStatus',
         message: 'incoming',
         tenantId: incomingMessage.tenantId
      });

      const prisma = await getPrismaClient(incomingMessage.tenantId);

      const tenant = await getTenant(incomingMessage.tenantId);

      if (!tenant) {
         throw new Error(`Tenant not found: ${incomingMessage.tenantId}`);
      }

      const data: Prisma.ChannelMessageUpdateInput = {
         error: incomingMessage.message,
         sentAt: incomingMessage.sentAt,
         status: incomingMessage.status,
         transactionId: incomingMessage.transactionId
      };

      if(tenant.storeMessageForDays === 0) {
         data.text = '';
      }

      await prisma.channelMessage.update({
         data,
         where: {
            id: incomingMessage.channelMessageId
         }
      });

      if (incomingMessage.disable) {
         await prisma.connection.update({
            data: {
               enabled: false
            },
            where: {
               id: incomingMessage.connectionId,
               recipientId: incomingMessage.recipientId
            }
         });
      }
   } catch (error) {
      context.error({
         data: incomingMessage,
         error,
         function: 'updateChannelMessageStatus',
         message: errorMessage('updateChannelMessageStatus', error),
         tenantId: incomingMessage.tenantId
      });

      throw error;
   }
}

app.serviceBusQueue('updateChannelMessageStatus', {
   connection: 'messaging_SERVICEBUS',
   handler: updateChannelMessageStatus,
   queueName: incomingQueue
});
