{"version": "2.0", "logging": {"logLevel": {"default": "Warning", "Host.Results": "Warning", "Function": "None", "Host.Aggregator": "Warning", "Function.createServiceMessages": "Trace", "Function.updateServiceMessages": "Trace"}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "concurrency": {"dynamicConcurrencyEnabled": true, "snapshotPersistenceEnabled": true}}