model RecipientMessage {
  id          String     @id @default(uuid(7))
  message     Message    @relation(fields: [messageId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  messageId   String     @map("message_id")
  recipient   Recipient? @relation(fields: [recipientId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  recipientId String?    @map("recipient_id")

  acknowledged   <PERSON><PERSON><PERSON>   @default(false)
  acknowledgedAt DateTime? @map("acknowledged_at")
  actionTaken    Boolean   @default(false) @map("action_taken")
  actionTakenAt  DateTime? @map("action_taken_at")
  idUsed         String    @map("id_used")
  status         Status    @default(PROCESSING) // PROCESSING, PROCESSED OR ERROR
  error          String    @default("")
  relevant       <PERSON><PERSON><PERSON>   @default(false) // Whether the message is relevant to the recipient, for ML training

  channelMessages ChannelMessage[]

  @@index([messageId], name: "idx_recipient_message_message_id")
  @@index([recipientId], name: "idx_recipient_message_recipient_id")
  @@map("recipient_message")
}
