model TwoWayMessage {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  // We need either a recipientServiceId and tenantServiceId, or a conversationId
  // ID used by the service to identify the recipient
  recipientServiceId String? @default("")

  // ID used by the service to identify the institution
  tenantServiceId String? @default("")

  // the conversation ID used by the service
  // such as the teams conversation ID
  conversationId String? @default("")

  // what channel was the message sent on
  // note, the outgoing channel is the configured service as
  // named by the tenant
  channel   String
  direction String  @default("I")
  subject   String? @default("")
  message   String
  reaction  String? @default("")
  category  String? @default("")

  // xSIGNAL recipient ID
  recipientId String? @default("")

  // unique ID used by the tenant
  tenantMessageId String? @default("")

  // service transaction id 
  transactionId String? @default("")
  error         String? @default("")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([tenantId, updatedAt], name: "idx_two_way_message_tenant_id_created_at")
  @@map("two_way_message")
}
