model ChannelMessage {
  id                 String           @id @default(uuid(7))
  message            RecipientMessage @relation(fields: [messageId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  messageId          String           @map("message_id")
  status             Status           @default(PROCESSING)
  error              String           @default("") @map("error")
  channel            Channel?         @relation(fields: [channelId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  channelId          String?          @map("channel_id")
  endpointIdentifier String           @map("endpoint_identifier")
  title              String           @default("")
  text               String
  transactionId      String           @default("") @map("transaction_id")

  // sent to a standard recipient, not a service group message (e.g. MODO)
  isRecipientMessage Boolean @default(true) @map("is_recipient_message")

  // when the message was sent
  sentAt  DateTime? @map("sent_at")
  aborted <PERSON><PERSON>an   @default(false)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([messageId], name: "idx_channel_message_message_id")
  @@map("channel_message")
}
