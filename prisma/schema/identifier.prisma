model Identifier {
  id          String    @id @default(uuid(7))
  recipient   Recipient @relation(fields: [recipientId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  recipientId String    @map("recipient_id")

  type  String
  value String

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([recipientId], name: "idx_identifier_recipient_id")
  @@index([value], name: "idx_identifier_value")
  @@map("identifier")
}
