model TopicPreference {
  id String @id @default(uuid(7))

  Recipient   Recipient @relation(fields: [recipientId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  recipientId String

  topic   Topic  @relation(fields: [topicId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  topicId String @map("topic_id")

  optedIn Boolean @default(false) @map("opted_in")

  connections Connection[] @relation("relation_topic_preference_connection")

  @@index([recipientId], name: "idx_topic_preference_recipient_id")
  @@index([topicId], name: "idx_topic_preference_topic_id")
  @@map("topic_preference")
}
