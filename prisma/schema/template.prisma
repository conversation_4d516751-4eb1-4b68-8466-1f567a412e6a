model Action {
  id String @id @default(uuid(7))

  label            String
  url              String
  localeTemplate   LocaleTemplate? @relation(fields: [localeTemplateId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  localeTemplateId String?

  @@map("action")
}

model LocaleTemplate {
  id String @id @default(uuid(7))

  locale     String
  template   Template @relation(fields: [templateId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  templateId String   @map("template_id")

  title        String @default("")
  fullMessage  String @default("") @map("full_message")
  shortMessage String @default("") @map("short_message")
  plainText    String @default("") @map("plain_text")

  actionButtons Action[]
  variables     String[] @default([])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("locale_template")
}

model Template {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  externalId             String           @default("") @map("external_id")
  displayName            String           @map("display_name")
  description            String           @default("")
  isNotificationTemplate Boolean          @default(false) @map("is_notification_template")
  localeTemplates        LocaleTemplate[]

  @@map("template")
}
