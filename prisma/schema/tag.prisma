model Tag {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  externalId  String? @default("") @map("external_id")
  displayName String  @map("display_name")
  description String? @default("")
  enabled     Boolean @default(true)
  selectable  Boolean @default(false) // Can be selected by a recipient in preferences

  recipients Recipient[] @relation("relation_recipient_tags")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([tenantId, externalId], name: "idx_tag_tenant_id_external_id")
  @@map("tag")
}
