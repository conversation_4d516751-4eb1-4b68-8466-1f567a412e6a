model Message {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  recipients String[] @default([])
  tags       String[] @default([])
  // audiences     String[] @default([]) for the future, not used yet

  topic      String
  importance String @default("1")

  // we send notification that they have a message, but the message is not sent yet 
  isNotificationMessage Boolean @default(false) @map("is_notification_message")

  conversationId String    @default("") @map("conversation_id")
  category       String    @default("")
  sendAt         DateTime? @map("send_at")
  status         Status    @default(PROCESSING)
  error          String    @default("")

  title        String @default("")
  message      String @default("")
  shortMessage String @default("") @map("short_message")
  plainText    String @default("") @map("plain_text")

  // the user on the JWT token
  apiUser String @default("") @map("api_key_user")

  // not used yet
  // campaign    Campaign? @relation(fields: [campaignId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  // campaignId  String    @default("") @map("campaign_id")
  // campaignRun Int       @default(0) @map("campaign_sequence_number")
  // actionId    String    @default("") @map("action_id")

  // click tracking
  trackedLinks      TrackedLink[]
  recipientMessages RecipientMessage[]
  attributes        MessageAttribute[]
  context           MessageContext[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([tenantId, updatedAt], name: "idx_message_tenant_id_updated_at")
  @@index([tenantId, status], name: "idx_message_tenant_id_status")
  @@map("message")
}
