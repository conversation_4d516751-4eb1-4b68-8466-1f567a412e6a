model Connection {
  id          String    @id @default(uuid(7))
  recipient   Recipient @relation(fields: [recipientId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  recipientId String    @map("recipient_id")

  service           String
  enabled           <PERSON><PERSON><PERSON> @default(true)
  showInPreferences <PERSON><PERSON><PERSON> @default(true) @map("show_in_preferences")

  // channelType    String  @default("") @map("channel_type")
  // serviceDefault Boolean @default(false) @map("service_default")

  attributes       ConnectionAttribute[]
  topicPreferences TopicPreference[]     @relation("relation_topic_preference_connection")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  Channel   Channel? @relation(fields: [channelId], references: [id])
  channelId String?

  // @@unique([recipientId, service, channelType], name: "idx_connection_recipient_service_type")
  @@unique([recipientId, service], name: "idx_connection_recipient_service")
  @@index([recipientId], name: "idx_connection_recipient_id")
  @@map("connection")
}
