model Tenant {
  id String @id @default(uuid(7))

  name                String   @unique
  enabled             Boolean  @default(true)
  language            String   @default("en")
  storeMessageForDays Int      @default(30) @map("store_message_for_days")
  archive             <PERSON>olean  @default(false)
  encryptionKeyId     String   @default(uuid(7)) @map("encryption_key_id")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  twoWayMessages        TwoWayMessage[]
  channelConfigurations Channel[]
  messages              Message[]
  tags                  Tag[]
  recipients            Recipient[]
  topics                Topic[]
  templates             Template[]
  TeamsUser             TeamsUser[]
  TrackedLinks          TrackedLink[]

  @@map("tenant")
}
