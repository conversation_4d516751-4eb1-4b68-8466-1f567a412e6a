model RecipientAttribute {
  id          String    @id @default(uuid(7))
  recipient   Recipient @relation(fields: [recipientId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  recipientId String    @map("recipient_id")

  name  String
  value String @default("")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([recipientId, name], name: "unique_recipient_attribute_recipient_id_name")
  @@index([recipientId, name], name: "idx_recipient_attribute_recipient_id")
  @@map("recipient_attribute")
}
