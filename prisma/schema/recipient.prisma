model Recipient {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  enabled  Boolean @default(true)
  timezone String  @default("Etc/UTC") //TZ Identifier
  locale   String  @default("en-US")

  //days           Int      @default(127) // bitmask of days of the week 1=Sun, 2=Mon, 4=Tue, 8=Wed, 16=Thu, 32=Fri, 64=Sat
  //after          String   @default("00:00")
  //before         String   @default("23:59")

  digest         Boolean @default(false)
  defaultService String?

  identifiers Identifier[]
  attributes  RecipientAttribute[]
  connections Connection[]
  messages    RecipientMessage[]
  tags        Tag[]                @relation("relation_recipient_tags")
  preferences TopicPreference[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([tenantId], name: "idx_recipient_tenant_id")
  @@map("recipient")
}
