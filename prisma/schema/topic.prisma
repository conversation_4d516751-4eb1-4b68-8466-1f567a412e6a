model Topic {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  externalId      String  @default("") @map("external_id")
  displayName     String
  description     String  @default("")
  enabled         Boolean @default(true)
  defaultCategory String  @default("") @map("default_category")
  channelType     String  @map("channel_type")
  defaultService  String  @map("default_service")

  visibleInPreferences Boolean @default(true) @map("visible_in_preferences")
  orderSequence        Int     @default(1) @map("order_sequence")
  userMustOptIn        Boolean @default(false) @map("user_must_opt_in") //disabled if a channelAlwaysOn is true
  channelAlwaysOn      Boolean @default(false) @map("channel_always_on")

  userPreferenceRoles String[]          @default([]) @map("user_preference_roles") // who can see in their preferences
  roles               String[]          @default([]) // who can use this topic
  topicPreference     TopicPreference[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([tenantId, externalId], name: "idx_topic_tenant_id_external_id")
  @@map("topic")
}
