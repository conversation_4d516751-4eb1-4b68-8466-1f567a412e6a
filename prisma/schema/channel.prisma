model Channel {
  id       String @id @default(uuid(7))
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  externalId  String @default("") @map("external_id")
  description String @default("")
  service     String
  channelType String @map("channel_type")
  provider    String @default("")

  displayName String  @map("display_name") //what is displayed in the channel matrix
  enabled     Boolean @default(true)

  attributes      ChannelAttribute[]
  channelMessages ChannelMessage[]

  createdAt  DateTime     @default(now()) @map("created_at")
  updatedAt  DateTime     @updatedAt @map("updated_at")
  Connection Connection[]

  @@unique([tenantId, id, service, channelType], name: "idx_channel_tenant_id_service_channel_type")
  @@index([tenantId, externalId], name: "idx_channel_tenant_id_external_id")
  @@map("channel")
}
