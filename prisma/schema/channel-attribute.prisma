model ChannelAttribute {
  id        String  @id @default(uuid(7))
  channel   Channel @relation(fields: [channelId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  channelId String  @map("channel_id")
  name      String
  value     String  @default("")
  secure    Boolean @default(false)

  @@unique([channelId, name], name: "unique_channel_attribute_channel_id_name")
  @@index([channelId, name], name: "idx_channel_attribute_channel_id")
  @@map("channel_attribute")
}
