model TeamsUser {
  id       String @id
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId String @map("tenant_id")

  userPrincipalName String  @map("user_principal_name")
  email             String?
  conversationId    String? @map("conversation_id")
  msTenantId        String  @map("ms_tenant_id")

  @@index([tenantId, email], name: "idx_teams_user_tenant_email_id")
  @@index([tenantId, userPrincipalName], name: "idx_teams_user_tenant_upn_id")
  @@map("teams_user")
}
