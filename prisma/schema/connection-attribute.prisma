model ConnectionAttribute {
  id           String     @id @default(uuid(7))
  connection   Connection @relation(fields: [connectionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  connectionId String     @map("connection_id")

  name  String
  value String @default("")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([connectionId, name], name: "unique_connection_attribute_connection_id_name")
  @@index([connectionId, name], name: "idx_connection_attribute_connection_id")
  @@map("connection_attribute")
}
