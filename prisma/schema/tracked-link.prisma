model TrackedLink {
  id         String    @id @default(uuid(7))
  tenant     Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenantId   String    @map("tenant_id")
  hash       String
  url        String
  clickCount Int       @default(0) @map("click_count")
  firstClick DateTime?
  lastClick  DateTime?

  message   Message? @relation(fields: [messageId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  messageId String?  @map("message_id")

  createdAt DateTime @default(now()) @map("created_at")

  @@unique([hash], name: "uq_tracked_link_hash")
  @@index([hash], name: "idx_tracked_link_hash")
  @@index([tenantId, id], name: "idx_tracked_link_tenant_id")
  @@index([messageId], name: "idx_tracked_link_message_id")
  @@map("tracked_link")
}
